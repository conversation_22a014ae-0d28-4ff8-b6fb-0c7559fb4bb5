[unix_http_server]
file=/tmp/supervisor.sock
chmod=0700
username=izen
password=P@ssW0rd#1234

[supervisord]
logfile=/var/log/supervisor/supervisord.log
childlogdir=/var/log/supervisor
logfile_maxbytes=300KB
logfile_backups=10
loglevel=info
pidfile=/tmp/supervisord.pid
nodaemon=false
minfds=1024
minprocs=200
;environment variables
environment=ROOT="/home/<USER>",
LD_LIBRARY_PATH="/home/<USER>/libraries",
ML_MODEL_LOC="/home/<USER>/ml-models"

[inet_http_server]
port=127.0.0.1:9001
username=izen
password=P@ssW0rd#1234

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///tmp/supervisor.sock

; The [include] section can just contain the "files" setting.  This
; setting can list multiple files (separated by whitespace or
; newlines).  It can also contain wildcards.  The filenames are
; interpreted as relative to this file.  Included files *cannot*
; include files themselves.

[program:php-fpm]
command=/usr/local/sbin/php-fpm
autostart=true
autorestart=true

[program:laravel-scheduler]
user=izen ; 환경에 따라 유저를 변경해 줘야 함.(AWS: ec2-user, Oracle: ubuntu)
process_name=%(program_name)s_%(process_num)02d
directory=/var/www/html
command=/bin/sh -c "while true; do php artisan schedule:run; sleep 60; done"
autostart=true
autorestart=true
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/supervisor/laravel-scheduler.log
stdout_logfile_maxbytes=300KB
stdout_logfile_backups=10

[program:laravel-worker]
user=izen ; 환경에 따라 유저를 변경해 줘야 함.(AWS: ec2-user, Oracle: ubuntu)
process_name=%(program_name)s_%(process_num)02d
directory=/var/www/html
command=php artisan queue:work --sleep=3 --tries=3 --max-time=3600 --timeout=3600 --backoff=10
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
numprocs=2 ; 프로세스 수를 늘려서 처리량을 늘릴 수 있음
redirect_stderr=true
stdout_logfile=/var/log/supervisor/laravel-worker.log
stopwaitsecs=3600
stdout_logfile_maxbytes=300KB
stdout_logfile_backups=10