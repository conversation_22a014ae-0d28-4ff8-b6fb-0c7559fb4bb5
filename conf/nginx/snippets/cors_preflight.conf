# OPTIONS 프리플라이트 요청을 Nginx에서 즉시 204로 응답
if ($request_method = 'OPTIONS') {
    add_header Access-Control-Allow-Origin $cors_origin always;
    add_header Access-Control-Allow-Credentials "true" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With, X-CSRF-TOKEN" always;
    add_header Access-Control-Max-Age 86400 always;
    add_header Content-Type "text/plain; charset=utf-8" always;
    add_header Content-Length 0 always;
    return 204;
}
