# 공통 CORS 헤더 스니펫
# 허용된 Origin(whitelist 매핑된 $cors_origin)일 때만 반영됨
add_header Access-Control-Allow-Origin $cors_origin always;
add_header Access-Control-Allow-Credentials "true" always;
add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With, X-CSRF-TOKEN" always;
add_header Access-Control-Expose-Headers "Content-Length, Content-Range, Content-Disposition" always;
