load_module modules/ngx_http_brotli_filter_module.so;
load_module modules/ngx_http_brotli_static_module.so;

user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log warn;
pid        /run/nginx.pid;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
    accept_mutex off;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 로그 형식
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    # SSE 전용 로그 형식
    log_format sse_log '$remote_addr - $remote_user [$time_local] "$request" '
                       '$status $body_bytes_sent rt=$request_time '
                       'connection=$connection connection_requests=$connection_requests '
                       'upstream_addr=$upstream_addr upstream_status=$upstream_status';

    access_log  /var/log/nginx/access.log  main;

    # 기본 성능 설정
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    server_tokens off;

    # 버퍼 크기 설정
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    server_names_hash_bucket_size 128;
    types_hash_max_size 2048;
    types_hash_bucket_size 128;

    # 압축 설정
    gzip on;
    gzip_vary on;
    gzip_min_length 1000;
    gzip_disable "msie6";
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml
        image/svg+xml
        text/event-stream;

    brotli on;
    brotli_static on;
    # Brotli 압축을 적용할 최소 파일 크기 (바이트)
    brotli_min_length 256;
    brotli_comp_level 6;
    brotli_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml
        image/svg+xml
        text/event-stream;

    # 허용된 Origin 목록 매핑
    map $http_origin $cors_origin {
        default "";
        "http://localhost:5173" $http_origin;
        "https://localhost:5173" $http_origin;
        "http://tauri.localhost" $http_origin;
        "https://tauri.localhost" $http_origin;
        "http://127.0.0.1:5173" $http_origin;
        "https://127.0.0.1:5173" $http_origin;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=sse:10m rate=5r/s;
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;


    include /etc/nginx/conf.d/*.conf;
}