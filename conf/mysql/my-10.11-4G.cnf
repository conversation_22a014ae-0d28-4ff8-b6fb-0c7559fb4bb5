[client]
default-character-set = utf8mb4

[mysql]
default-character-set = utf8mb4

[mysqld]
character-set-client-handshake = FALSE
character-set-server           = utf8mb4
collation-server               = utf8mb4_unicode_ci

# 네임서버 조회, resolving 과정 생략
skip-name-resolve

innodb_buffer_pool_size  = 2G
innodb_sort_buffer_size  = 1M # default 1M
sort_buffer_size         = 2M # default 2M

bulk_insert_buffer_size  = 256M

max_allowed_packet       = 1073741824
max_connections          = 200

[mysqldump]
max_allowed_packet=1073741824