[client]
default-character-set = utf8mb4

[mysql]
default-character-set = utf8mb4

[mysqld]
# bind-address = 127.0.0.1
default-time-zone = '+9:00'

# InnoDB 버퍼 풀 크기: 서버 메모리의 50~80% 권장 (예: 64GB 메모리라면 32G~50G)
innodb_buffer_pool_size = 4G
bulk_insert_buffer_size = 256M

# InnoDB 트랜잭션 로그 파일 크기 (복구 시간과 성능에 영향)
innodb_log_file_size = 4G

# 문자셋 및 콜레이션 설정
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 클라이언트 문자셋 무시 설정으로 일관성 유지
skip-character-set-client-handshake

# DNS 역방향 조회 차단 (접속 속도 향상)
skip-name-resolve

# 이벤트 스케줄러 활성화
event_scheduler = ON

# 최대 동시 연결 수 (환경에 맞게 조정)
max_connections = 300

# 연결 대기 시간 (초)
wait_timeout = 300

# 로그 파일 설정
log_warnings = 1
log_output = FILE
log_error = /var/log/mysql/error.log

# 느린 쿼리 로그 활성화 및 설정
slow_query_log = ON
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2.0

[mysqldump]
# 덤프 시 패킷 크기(1GB) 제한을 넉넉히 잡은 것은 큰 BLOB나 대용량 데이터를 덤프 및 복원할 때 좋습니다.
# 단, 너무 크게 잡으면 메모리 사용이 커질 수 있으니 용도에 맞게 조절하세요.
max_allowed_packet=1073741824