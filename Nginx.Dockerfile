FROM alpine:latest

# 필요한 패키지 설치 (nginx, brotli 모듈, bash 등)
RUN apk update && apk add --no-cache nginx nginx-mod-http-brotli bash curl

# brotli 모듈 로드 설정 추가
#RUN sed -i '1i load_module /usr/lib/nginx/modules/ngx_http_brotli_filter_module.so;' /etc/nginx/nginx.conf \
# && sed -i '1i load_module /usr/lib/nginx/modules/ngx_http_brotli_static_module.so;' /etc/nginx/nginx.conf
#
## 커스텀 nginx 설정 복사 (brotli 활성화 포함)
#COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80 443

CMD ["nginx", "-g", "daemon off;"]
