{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2 || ^8.3", "ext-pdo": "*", "ext-zip": "*", "guzzlehttp/guzzle": "^7.2", "http-interop/http-factory-guzzle": "^1.2", "irazasyed/telegram-bot-sdk": "^3.14", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/scout": "^10.10", "laravel/slack-notification-channel": "^3.6.0", "laravel/tinker": "^2.8", "maatwebsite/excel": "^3.1", "meilisearch/meilisearch-php": "^1.9", "predis/predis": "^2.0", "pusher/pusher-php-server": "^7.2", "spatie/laravel-activitylog": "^4.8"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "pestphp/pest": "^3.8", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "Illuminate\\Foundation\\ComposerScripts::postUpdate"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}