<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repair_cost_type_process_mappings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('repair_process_id')->comment('RepairProcess의 ID');
            $table->foreignId('repair_cost_type_id')->comment('수리 유형 ID');
            $table->boolean('is_active')->default(true)->comment('활성화 상태');
            $table->timestamps();

            $table->foreign('repair_process_id')
                ->references('id')
                ->on('repair_processes')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->foreign('repair_cost_type_id')
                ->references('id')
                ->on('repair_cost_types')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            // 1:1 매핑 제약 - 하나의 repair_process_id는 하나의 repair_cost_type_id에만 매핑
            $table->unique('repair_process_id');
            // 인덱스 설정
            $table->index('repair_process_id');
            $table->index('repair_cost_type_id');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_cost_type_process_mappings');
    }
};
