<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('repair_parts_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('repair_parts_id')->comment('수리 구성품 인덱스');
            $table->foreignId('cate4_id')->comment('cate4 인덱스');
            $table->foreignId('cate5_id')->comment('cate5 인덱스');
            $table->foreignId('user_id')->comment('사용자 인덱스');
            $table->unsignedTinyInteger('quantity')->default(0)->comment('사용 개수');
            $table->unsignedInteger('amount')->default(0)->comment('금액');
            $table->timestamps();

            $table->foreign('repair_parts_id')
                ->on('repair_parts')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('cate4_id')
                ->on('cate4')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('cate5_id')
                ->on('cate5')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            // 복합 인덱스 추가 (자주 함께 조회되는 컬럼들)
            $table->index(['repair_parts_id', 'created_at'], 'repair_parts_logs_parts_created_index');
            $table->index(['cate4_id', 'cate5_id'], 'repair_parts_logs_category_index');
            $table->index(['user_id', 'created_at'], 'repair_parts_logs_user_created_index');
            $table->index(['quantity', 'amount'], 'repair_parts_logs_quantity_amount_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_parts_logs');
    }
};
