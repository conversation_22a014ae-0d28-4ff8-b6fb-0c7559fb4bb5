<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cate4', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('cate4_no')->nullable()->comment('정리하기 전 cate4');
            $table->string('name');

            $table->unique('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cate4');
    }
};
