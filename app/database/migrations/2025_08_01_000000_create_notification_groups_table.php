<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('notification_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('그룹명');
            $table->text('description')->comment('그룹 설명');
            $table->foreignId('created_by')->comment('그룹 생성자 ID');
            $table->boolean('is_active')->default(true)->comment('활성 여부');
            $table->timestamps();

            $table->foreign('created_by')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->index('is_active');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('notification_groups');
    }
};
