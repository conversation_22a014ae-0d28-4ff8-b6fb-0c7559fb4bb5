<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pallet_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pallet_id')->comment('팔레트 인덱스');
            $table->foreignId('product_id')->comment('상품 인덱스');
            $table->foreignId('repair_product_id')->nullable()->comment('수리된 상품 인덱스');
            $table->unsignedTinyInteger('status')->default(10)->comment('상태(10: 등록(적재), 90: 삭제(취소))');
            $table->unsignedBigInteger('registered_user_id')->nullable()->comment('검수한 회원 인덱스');
            $table->dateTime('registered_at')->nullable()->comment('등록 일시');
            $table->unsignedMediumInteger('quantity')->default(0)->comment('검수 수량');
            $table->unsignedInteger('amount')->default(0)->comment('판매 단가(상품 등록값과 동일)');
            $table->unsignedBigInteger('process_check_id')->nullable()->comment('진단 내용(상품 상태) 번호');
            $table->unsignedBigInteger('process_repair_id')->nullable()->comment('처리 내용(수리 내역) 번호');
            $table->unsignedBigInteger('process_grade_id')->nullable()->comment('분류 등급(상품 등급) 번호');
            $table->foreignId('repair_symptom_id')->nullable()->comment('증상 내용(상품 상태) 인덱스');
            $table->foreignId('repair_process_id')->nullable()->comment('처리 내용(수리 내역) 인덱스');
            $table->foreignId('repair_grade_id')->nullable()->comment('수리 등급 인덱스');
            $table->unsignedInteger('invoice1')->default(0)->comment('청구금액1(기본)');
            $table->unsignedInteger('invoice2')->default(0)->comment('청구금액2(추가)');
            $table->unsignedInteger('invoice3')->default(0)->comment('청구금액3(추가)');
            $table->unsignedBigInteger('checked_user_id')->nullable()->comment('최종 검수 회원 인덱스');
            $table->unsignedTinyInteger('checked_status')->default(10)->comment('최종 상태(10: 적재, 20: 검수)');
            $table->dateTime('checked_at')->nullable()->comment('최종 검수 일시');
            $table->text('memo')->nullable();
            $table->timestamps();
            $table->dateTime('deleted_at')->nullable()->comment('삭제 일시');

            $table->foreign('pallet_id')
                ->on('pallets')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('product_id')
                ->on('products')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('repair_product_id')
                ->on('repair_products')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('registered_user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('repair_symptom_id')
                ->on('repair_symptoms')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('repair_process_id')
                ->on('repair_processes')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('repair_grade_id')
                ->on('repair_grades')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('checked_user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->index('pallet_id');
            $table->index('product_id');
            $table->index('status');
            $table->index('registered_at');
            $table->index('repair_symptom_id');
            $table->index('repair_process_id');
            $table->index('repair_grade_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pallet_products');
    }
};
