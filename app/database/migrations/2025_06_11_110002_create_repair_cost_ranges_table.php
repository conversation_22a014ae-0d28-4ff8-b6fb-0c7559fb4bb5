<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repair_cost_ranges', function (Blueprint $table) {
            $table->id();
            $table->foreignId('repair_cost_category_id')->comment('수리비 카테고리 인덱스');
            $table->string('range_name', 100)->comment('범위명 (24인치, 32인치, 5만원미만, 10만원미만, 공통 등)');
            $table->decimal('min_value', 10, 2)->nullable()->comment('최소값');
            $table->decimal('max_value', 10, 2)->nullable()->comment('최대값');
            $table->enum('unit', ['inch', 'cm', 'won', 'common'])->default('won')->comment('단위 (inch, cm, won, common)');
            $table->boolean('is_active')->default(true)->comment('활성화 여부');
            $table->timestamps();

            // 외래키 제약조건
            $table->foreign('repair_cost_category_id')
                ->references('id')
                ->on('repair_cost_categories')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            // 인덱스
            $table->index(['repair_cost_category_id', 'is_active'], 'idx_repair_cost_ranges_category');
            $table->index(['min_value', 'max_value'], 'idx_repair_cost_ranges_values');
            $table->index('unit');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_cost_ranges');
    }
};
