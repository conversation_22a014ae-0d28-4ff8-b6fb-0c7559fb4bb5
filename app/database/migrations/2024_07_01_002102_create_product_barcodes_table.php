<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_barcodes', function (Blueprint $table) {
            $table->id();
            $table->string('barcode')->comment('바코드');
            $table->string('wms_sku_id')->nullable()->comment('관리번호');
            $table->string('external_wms_sku_id')->nullable()->comment('외부관리번호');

            // barcode와 wms_sku_id의 복합 unique 인덱스 추가
            $table->unique(['barcode', 'wms_sku_id'], 'product_barcodes_barcode_wms_sku_unique');
            $table->index('barcode');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_barcodes');
    }
};
