<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repair_costs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('repair_cost_range_id')->comment('수리비 범위 인덱스');
            $table->foreignId('repair_cost_type_id')->comment('수리 유형 (5가지)');
            $table->unsignedBigInteger('amount')->default(0)->comment('수리비 금액');
            $table->timestamps();

            // 외래키 제약조건
            $table->foreign('repair_cost_range_id')
                ->references('id')
                ->on('repair_cost_ranges')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->foreign('repair_cost_type_id')
                ->references('id')
                ->on('repair_cost_types')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            // 유니크 제약조건 (하나의 범위에서 같은 수리 유형은 하나의 금액만 가질 수 있음)
            $table->unique(['repair_cost_range_id', 'repair_cost_type_id'], 'uk_repair_costs_range_type');

            // 인덱스
            $table->index(['repair_cost_range_id', 'repair_cost_type_id'], 'idx_repair_costs_range_type');
            $table->index('repair_cost_type_id');
            $table->index('amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_costs');
    }
};
