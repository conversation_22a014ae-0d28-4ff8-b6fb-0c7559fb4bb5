<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repair_process_repair_symptom', function (Blueprint $table) {
            $table->primary(['repair_symptom_id', 'repair_process_id']);
            $table->foreignId('repair_symptom_id');
            $table->foreignId('repair_process_id');
            $table->timestamps();

            $table->foreign('repair_symptom_id')
                ->on('repair_symptoms')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('repair_process_id')
                ->on('repair_processes')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            // 추가 인덱스 (역방향 조회를 위한 인덱스)
            $table->index('repair_process_id', 'repair_process_repair_symptom_process_index');
            $table->index('repair_symptom_id', 'repair_process_repair_symptom_symptom_index');
            $table->index('created_at', 'repair_process_repair_symptom_created_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_process_repair_symptom');
    }
};
