<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_links', function (Blueprint $table) {
            $table->id();
            $table->string('external_wms_sku_id')->nullable()->comment('외부 관리번호');
            $table->string('vendor_item_id')->nullable()->comment('공급처 아이템 ID');
            $table->string('product_id')->nullable()->comment('쿠팡 상품 ID');
            $table->string('item_id')->nullable()->comment('쿠팡 상품의 아이템 ID');

            $table->unique(['external_wms_sku_id', 'vendor_item_id'], 'product_links_external_wms_sku_id_vendor_item_id_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_links');
    }
};
