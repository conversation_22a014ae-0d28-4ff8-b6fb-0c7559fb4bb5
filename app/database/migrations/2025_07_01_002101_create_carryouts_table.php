<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('carryouts', function (Blueprint $table) {
            $table->id();
            $table->unsignedTinyInteger('status')->default(10)->comment('외주 상태(10-반출, 30-반입, 90-취소)');
            $table->foreignId('carryout_user_id')->nullable()->comment('반출 담당 직원');
            $table->date('carryout_at')->nullable()->comment('반출 날짜');
            $table->foreignId('carryin_user_id')->nullable()->comment('반입 담당 직원');
            $table->dateTime('carryin_at')->nullable()->comment('반입 시간');
            $table->foreignId('token_id')->comment('외부업체 접속 토큰 인덱스');
            $table->text('memo')->nullable();
            $table->timestamps();

            $table->foreign('carryout_user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('carryin_user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('token_id')
                ->on('carryout_tokens')
                ->references('id')
                ->onUpdate('cascade');

            $table->index('status');
            $table->index('carryout_user_id');
            $table->index('carryout_at');
            $table->index('carryin_user_id');
            $table->index('carryin_at');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('carryouts');
    }
};
