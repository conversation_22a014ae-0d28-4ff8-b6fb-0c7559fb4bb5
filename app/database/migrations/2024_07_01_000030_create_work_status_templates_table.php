<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_status_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->comment('카테고리 인덱스');
            $table->foreignId('action_id')->comment('액션 인덱스');
            $table->string('template_code')->comment('템플릿 코드');
            $table->string('name')->comment('상태 이름');
            $table->text('description')->nullable()->comment('상태 설명');
            $table->json('conditions')->nullable()->comment('조건부 생성 규칙');
            $table->boolean('is_active')->default(true)->comment('활성 상태');
            $table->foreignId('created_by')->comment('생성자');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('category_id')
                ->references('id')
                ->on('work_categories')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('action_id')
                ->references('id')
                ->on('work_actions')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('created_by')
                ->references('id')
                ->on('users')
                ->onUpdate('cascade');

            $table->unique(['category_id', 'action_id', 'template_code'], 'unique_template');
            $table->index('category_id');
            $table->index('action_id');
            $table->index('created_by');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_status_templates');
    }
};
