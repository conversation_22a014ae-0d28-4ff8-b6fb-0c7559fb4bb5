<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('monitor_size_lookups', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('상품명');
            $table->enum('brand', ['brand', 'general'])->default('general')->comment('브랜드 구분');
            $table->decimal('size', 7, 2)->comment('크기');
            $table->enum('unit', ['INCH', 'CM'])->comment('단위');
            $table->string('name_hash')->comment('상품명 해시');
            $table->timestamps();

            $table->unique('name_hash');
            $table->index('name');
            $table->index('name_hash');
            $table->index('brand');
            $table->index('unit');
            $table->index(['brand', 'unit']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('monitor_size_lookups');
    }
};
