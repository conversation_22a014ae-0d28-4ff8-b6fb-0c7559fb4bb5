<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('zones', function (Blueprint $table) {
            $table->id();
            $table->foreignId('area_id')->comment('지역 인덱스');
            $table->string('name')->comment('존 명');
            $table->string('code')->comment('존 코드');
            $table->text('description')->nullable()->comment('존 설명');
            $table->boolean('is_active')->default(true)->comment('활성 여부');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('area_id')
                ->on('areas')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unique('code');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('zones');
    }
};
