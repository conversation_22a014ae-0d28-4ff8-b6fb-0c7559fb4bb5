<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('warehouse_pallet_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('warehouse_pallet_id')->comment('창고 팔레트 인덱스');
            $table->foreignId('product_id')->comment('상품 인덱스');
            $table->unsignedInteger('quantity')->default(1)->comment('수량');
            $table->enum('status', ['stored', 'exported', 'internal_use', 'internal_parts', 'returned', 'discarded'])
                ->default('stored')
                ->comment('상태');
            $table->foreignId('changed_by')->comment('상태 변경자 인덱스');
            $table->text('description')->nullable()->comment('비고');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('warehouse_pallet_id')
                ->on('warehouse_pallets')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('product_id')
                ->on('products')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('changed_by')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->index('status');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('warehouse_pallet_items');
    }
};
