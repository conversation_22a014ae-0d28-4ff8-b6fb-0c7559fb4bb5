<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('boards', function (Blueprint $table) {
            $table->id();
            $table->string('type')->default('board')->comment('게시판 종류');
            $table->foreignId('user_id')->comment('작성자 인덱스');
            $table->string('name')->comment('작성자 이름');
            $table->string('subject')->comment('제목');
            $table->longText('content')->comment('내용');
            $table->unsignedTinyInteger('status')->default(1)->comment('1: 등록, 9: 삭제');
            $table->dateTime('open_at')->nullable()->comment('예약 게시물');
            $table->enum('f_show', ['Y', 'N'])->default('Y')->comment('보이기 여부');
            $table->enum('f_notice', ['Y', 'N'])->default('N')->comment('공지 여부');
            $table->unsignedBigInteger('hits')->default(0)->comment('조회수');
            $table->string('ip')->comment('작성한 IP');
            $table->string('user_agent')->nullable()->comment('접속 디바이스');
            $table->timestamps();
            $table->dateTime('deleted_at')->nullable()->comment('삭제 시간');

            $table->foreign('user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->index('type');
            $table->index('user_id');
            $table->index('name');
            $table->index('subject');
            $table->index('content');
            $table->index('status');
            $table->index('f_show');
            $table->index('f_notice');
            $table->index('hits');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('boards');
    }
};
