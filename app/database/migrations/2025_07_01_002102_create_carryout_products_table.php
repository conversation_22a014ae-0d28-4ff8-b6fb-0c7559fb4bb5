<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('carryout_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('carryout_id')->comment('반출 인덱스');
            $table->foreignId('req_id')->comment('점검 요청서 인덱스');
            $table->foreignId('product_id')->nullable()->comment('상품 인덱스');
            $table->unsignedTinyInteger('status')->default(10)->comment('상태(10-반출, 30-수리, 90-삭제,취소)');
            $table->foreignId('checked_user_id')->nullable()->comment('반출 담당 직원');
            $table->dateTime('checked_at')->nullable()->comment('반출 시간');
            $table->unsignedBigInteger('process_check_id')->nullable()->comment('진단 내용(상품 상태) 번호');
            $table->unsignedBigInteger('process_repair_id')->nullable()->comment('처리 내용(수리 내역) 번호');
            $table->unsignedBigInteger('process_grade_id')->nullable()->comment('분류 등급(상품 등급) 번호');
            $table->foreignId('repair_symptom_id')->nullable()->comment('증상 내용(상품 상태) 인덱스');
            $table->foreignId('repair_process_id')->nullable()->comment('처리 내용(수리 내역) 인덱스');
            $table->foreignId('repair_grade_id')->nullable()->comment('수리 등급 인덱스');
            $table->unsignedInteger('invoice2')->default(0)->comment('청구금액2(추가)');
            $table->foreignId('token_id')->nullable()->comment('외부업체 접속 토큰 인덱스');
            $table->foreignId('renovator_id')->nullable()->comment('외부업체 접속 토큰 인덱스');
            $table->dateTime('renovate_at')->nullable()->comment('수리 완료 시간');
            $table->foreignId('carryin_user_id')->nullable()->comment('반입 담당 직원');
            $table->dateTime('carryin_at')->nullable()->comment('반입 시간');
            $table->text('memo')->nullable();
            $table->timestamps();

            $table->foreign('carryout_id')
                ->on('carryouts')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('req_id')
                ->on('reqs')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('product_id')
                ->on('products')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('set null');

            $table->foreign('checked_user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('carryin_user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('repair_symptom_id')
                ->on('repair_symptoms')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('repair_process_id')
                ->on('repair_processes')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('repair_grade_id')
                ->on('repair_grades')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('token_id')
                ->on('carryout_tokens')
                ->references('id')
                ->onUpdate('cascade');

            $table->index('carryout_id');
            $table->index('req_id');
            $table->index('product_id');
            $table->index('status');
            $table->index('checked_user_id');
            $table->index('checked_at');
            $table->index('token_id');
            $table->index('renovate_at');
            $table->index('carryin_user_id');
            $table->index('carryin_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('carryout_products');
    }
};
