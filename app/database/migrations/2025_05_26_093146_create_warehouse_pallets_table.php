<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('warehouse_pallets', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['MANAGED', 'WAREHOUSING'])->default('WAREHOUSING')
                ->comment('팔레트 타입(고스트 상품 팔레트, 입고 검수 팔레트)');
            $table->foreignId('position_id')->nullable()->comment('위치 인덱스');
            $table->string('barcode')->nullable()->comment('바코드');
            $table->string('pallet_number')->comment('팔레트 번호');
            $table->string('area_code')->nullable()->comment('지역 코드');
            $table->string('zone_code')->nullable()->comment('존 코드');
            $table->string('floor_code')->nullable()->comment('층 코드');
            $table->string('position_code')->nullable()->comment('위치 코드');
            $table->enum('status', ['available', 'in_use', 'pending', 'completed'])
                ->default('available')
                ->comment('상태(available, in_use, pending, completed)');
            $table->unsignedInteger('max_capacity')->default(0)->comment('최대 용량');
            $table->unsignedInteger('current_capacity')->default(0)->comment('현재 용량');
            $table->foreignId('created_by')->comment('팔레트 만든 직원 인덱스');
            $table->foreignId('shipped_in_by')->nullable()->comment('팔레트를 창고에 적재한 직원 인덱스');
            $table->dateTime('shipped_in_at')->nullable()->comment('창고에 적재한 날');
            $table->foreignId('shipped_out_by')->nullable()->comment('팔레트를 창고에서 출고한 직원');
            $table->dateTime('shipped_out_at')->nullable()->comment('창고에서 출고한 날');
            $table->text('memo')->nullable()->comment('비고');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('position_id')
                ->on('positions')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('created_by')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('shipped_in_by')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('shipped_out_by')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->unique('pallet_number');
            $table->unique('barcode');
            $table->index('type');
            $table->index('status');
            $table->index('updated_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('warehouse_pallets');
    }
};
