<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('repair_product_parts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('repair_product_id')->comment('수리/점검 상품 인덱스');
            $table->foreignId('repair_parts_id')->comment('구성품 인덱스');
            $table->unsignedSmallInteger('quantity')->default(1)->comment('사용 개수');
            $table->unsignedInteger('price')->default(0)->comment('사용시 단가(단가가 변경될 경우 대비)');
            $table->timestamps();

            $table->foreign('repair_product_id')
                ->on('repair_products')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('repair_parts_id')
                ->on('repair_parts')
                ->references('id')
                ->onUpdate('cascade');

            // 복합 인덱스 추가 (자주 함께 조회되는 컬럼들)
            $table->index(['repair_product_id', 'repair_parts_id'], 'repair_product_parts_product_parts_index');
            $table->index(['repair_parts_id', 'quantity'], 'repair_product_parts_parts_quantity_index');
            $table->index(['price', 'quantity'], 'repair_product_parts_price_quantity_index');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('repair_product_parts');
    }
};
