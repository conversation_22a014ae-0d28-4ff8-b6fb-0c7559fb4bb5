<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pallets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('location_id')->comment('팔레트 적재 위치 인덱스');
            $table->foreignId('repair_grade_id')->nullable()->comment('팔레트 등급');
            $table->unsignedTinyInteger('status')->default(10)->comment('상태(10-등록, 20-패킹, 30-출고, 90-폐기)');
            $table->dateTime('registered_at')->nullable()->comment('팔레트 등록 시간');
            $table->unsignedBigInteger('registered_user_id')->nullable()->comment('팔레트 등록 직원 인덱스');
            $table->dateTime('checked_at')->nullable()->comment('팔레트 출고전 점검 시간');
            $table->unsignedBigInteger('checked_user_id')->nullable()->comment('팔레트 출고전 검수 직원 인덱스');
            $table->dateTime('exported_at')->nullable()->comment('팔레트 출고 시간');
            $table->unsignedBigInteger('exported_user_id')->nullable()->comment('팔레트 출고 직원 인덱스');
            $table->text('memo')->nullable();
            $table->timestamps();

            $table->foreign('location_id')
                ->on('locations')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('repair_grade_id')
                ->on('repair_grades')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('registered_user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('checked_user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('exported_user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->index('location_id');
            $table->index('status');
            $table->index('registered_at');
            $table->index('checked_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pallets');
    }
};
