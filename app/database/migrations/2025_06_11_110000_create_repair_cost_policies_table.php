<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repair_cost_policies', function (Blueprint $table) {
            $table->id();
            $table->string('code', 50)->comment('가격 정책');
            $table->string('name', 100)->comment('표시명 (모니터/애플/일반/OS재설치 등)');
            $table->text('description')->nullable()->comment('설명');
            $table->enum('pricing_type', ['size', 'price', 'common'])
                ->default('price')
                ->comment('가격 결정 방식');
            $table->boolean('is_active')->default(true)->comment('활성화 여부');
            $table->unsignedInteger('order_no')->default(1)->comment('정렬 순서');
            $table->timestamps();

            // 인덱스 추가
            $table->unique('code');
            $table->index('pricing_type');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_cost_policies');
    }
};
