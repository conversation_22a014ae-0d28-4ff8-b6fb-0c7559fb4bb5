<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->char('id', 36)->primary()->comment('UUID');
            $table->string('title', 200)->comment('알림 제목');
            $table->text('content')->comment('알림 내용');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal')->comment('우선순위');
            $table->string('action_url', 500)->nullable()->comment('알림 클릭 시 이동할 URL');
            $table->foreignId('sender_id')->comment('발송자 ID');
            $table->enum('target_type', ['all', 'group', 'individual'])->default('all')->comment('알림 대상 유형');
            $table->foreignId('target_id')->nullable()->comment('대상 ID(그룹 또는 개인 ID)');
            $table->enum('status', ['draft', 'sent', 'cancelled'])->default('draft')->comment('알림 상태');
            $table->timestamp('sent_at')->nullable()->comment('발송일시');
            $table->timestamps();

            $table->foreign('sender_id')
                ->references('id')
                ->on('users')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->index('sender_id');
            $table->index(['target_type', 'target_id'], 'idx_notifications_target_polymorphic');
            $table->index('status');
            $table->index('sent_at');
            $table->index('priority');

            // 발송자별 시간순 조회를 위한 복합 인덱스
            $table->index(['sender_id', 'sent_at'], 'idx_notifications_sender_sent');

            // 상태별 시간순 조회를 위한 복합 인덱스 (히스토리 조회 시 status='sent' 필터링용)
            $table->index(['status', 'sent_at'], 'idx_notifications_status_sent');

            // 우선순위별 시간순 조회를 위한 복합 인덱스
            $table->index(['priority', 'sent_at'], 'idx_notifications_priority_sent');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
