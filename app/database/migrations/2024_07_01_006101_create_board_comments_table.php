<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('board_comments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('board_id')->comment('게시물 인덱스');
            $table->foreignId('user_id')->comment('작성자 인덱스');
            $table->string('name')->comment('작성자 이름');
            $table->text('comment')->comment('내용');
            $table->unsignedTinyInteger('status')->default(1)->comment('1: 등록, 9: 삭제');
            $table->string('ip')->comment('작성한 IP');
            $table->string('user_agent')->nullable()->comment('접속 디바이스');
            $table->timestamps();

            $table->foreign('board_id')
                ->on('boards')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->index('board_id');
            $table->index('user_id');
            $table->index('name');
            $table->index('comment');
            $table->index('status');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('board_comments');
    }
};
