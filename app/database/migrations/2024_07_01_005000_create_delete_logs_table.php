<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delete_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->comment('삭제한 직원 인덱스');
            $table->string('deletable_type')->nullable()->comment('삭제된 모델(전체 경로 \App\Models\User 형식)');
            $table->unsignedBigInteger('deletable_id')->comment('해당 테이블의 인덱스');
            $table->text('content')->comment('삭제된 레코드의 값');
            $table->string('ip')->nullable()->comment('삭제한 IP');
            $table->timestamps();

            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onUpdate('cascade');

            $table->index('user_id');
            $table->index(['deletable_type', 'deletable_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delete_logs');
    }
};
