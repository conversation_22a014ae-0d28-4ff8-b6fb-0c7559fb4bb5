<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repair_cost_types', function (Blueprint $table) {
            $table->id();
            $table->string('code', 50)->comment('수리비 유형 코드');
            $table->string('name', 50)->comment('수리비 유형 표시 이름');
            $table->text('description')->nullable()->comment('수리비 유형 설명');
            $table->boolean('is_active')->default(true)->comment('활성화 여부(1: 활성, 0: 비활성)');
            $table->timestamps();

            // 인덱스
            $table->unique('code');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_cost_types');
    }
};
