<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cate5', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('cate5_no')->nullable()->comment('정리하기 전 cate5');
            $table->foreignId('cate4_id');
            $table->string('name');

            $table->foreign('cate4_id')
                ->references('id')
                ->on('cate4')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unique(['cate4_id', 'name']);
            $table->index('cate4_id');
            $table->index('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cate5');
    }
};
