<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();
            $table->string('code')->nullable()->comment('거래코드');
            $table->string('name')->comment('소속사 이름(영어)');
            $table->string('kr_name')->comment('소속사 이름(한글)');
            $table->string('logo_uri')->nullable()->comment('로고위치, 상대경로');
            $table->unsignedTinyInteger('type')->default(0)
                ->comment('타입(0: 일반, 1: 거래처관리, 2: 상품분류관리, 3: 거래처및분류관리)');
            $table->string('f_default')->default('N');
            $table->text('description')->nullable()->comment('회사 설명');
            $table->unsignedTinyInteger('status')->default(0)->comment('상태(0: 유효, 9: 중지)');
            $table->timestamps();

            $table->unique('name');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
