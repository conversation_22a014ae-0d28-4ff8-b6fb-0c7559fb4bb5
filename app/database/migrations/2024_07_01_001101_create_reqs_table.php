<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reqs', function (Blueprint $table) {
            $table->id();
            $table->date('req_at')->comment('요청일');
            $table->unsignedTinyInteger('req_type')->default(9)->comment('요청타입(1: 쿠팡-등록, 2: 쿠팡-애플, 9:미등록)');
            $table->unsignedTinyInteger('status')->default(10)->comment('등록상태(10: 등록, 90: 취소)');
            $table->foreignId('user_id')->nullable()->comment('등록한 직원 인덱스');
            $table->foreignId('checked_user_id')->nullable()->comment('검수한 직원 인덱스');
            $table->dateTime('checked_at')->nullable()->comment('검수일');
            $table->text('memo')->nullable();
            $table->unsignedBigInteger('total_count')->default(0)->comment('총 상품 개수');
            $table->timestamps();

            $table->foreign('user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('checked_user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->index('req_at');
            $table->index('req_type');
            $table->index('user_id');
            $table->index('checked_user_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reqs');
    }
};
