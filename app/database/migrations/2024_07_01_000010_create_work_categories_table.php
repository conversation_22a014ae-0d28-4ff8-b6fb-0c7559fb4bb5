<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_categories', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique()->comment('카테고리 코드 (예: REPAIR, PALLET, CARRYOUT)');
            $table->string('name')->comment('카테고리 이름');
            $table->text('description')->nullable()->comment('카테고리 설명');
            $table->boolean('is_active')->default(true)->comment('활성 상태');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_categories');
    }
};
