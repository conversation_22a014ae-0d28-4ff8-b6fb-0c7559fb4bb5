<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('req_id')->comment('관계: 점검 요청서 인덱스');
            $table->string('qaid')->comment('품질관리번호');
            $table->string('barcode')->comment('바코드');
            $table->foreignId('product_barcode_id')->nullable()->comment('관계: 바코드 인덱스');
            $table->string('name')->comment('상품명');
            $table->foreignId('cate4_id')->nullable()->comment('관계: 카테고리 인덱스');
            $table->foreignId('cate5_id')->nullable()->comment('관계: 카테고리 인덱스');
            $table->foreignId('monitor_size_lookup_id')->nullable()->comment('관계: 모니터 가격 정책 인덱스');
            $table->unsignedMediumInteger('quantity')->default(1)->comment('상품 개수');
            $table->unsignedInteger('amount')->default(0)->comment('판매단가');
            $table->foreignId('user_id')->nullable()->comment('관계: 등록 직원 인덱스');
            $table->unsignedTinyInteger('status')->default(10)
                ->comment('상품 처리 상태(10: 수리대기중(창고), 30: 점검완료, 50: 반출중, 70: 출고완료, 80: 출고보류, 90: 삭제)');
            $table->enum('rg', ['Y', 'N'])->default('N')->comment('RG 상품(Y: RG상품, N: 일반상품)');
            $table->enum('duplicated', ['Y', 'N'])->default('N')->comment('중복 QAID 여부');
            $table->dateTime('checked_at')->nullable()->comment('검수일');
            $table->unsignedTinyInteger('checked_status')->nullable()->comment('입고 검수 여부(10-검수대기, 20-검수완료)');
            $table->foreignId('checked_user_id')->nullable()->comment('관계: 검수한 직원 인덱스');
            $table->foreignId('product_lot_id')->nullable()->comment('관계: Lots 인덱스');
            $table->foreignId('product_vendor_id')->nullable()->comment('관계: 공급처 인덱스');
            $table->foreignId('product_link_id')->nullable()->comment('관계: 쿠팡 연결 인덱스');
            $table->foreignId('return_reason_b_id')->nullable()->comment('관계: 반품 이유 B (b_cate)');
            $table->foreignId('return_reason_m_id')->nullable()->comment('관계: 반품 이유 M (b_cate)');
            $table->boolean('is_locked')->default(false)->comment('상품 잠금 상태');
            $table->unsignedBigInteger('locked_by')->nullable()->comment('잠금한 사용자 ID');
            $table->timestamp('locked_at')->nullable()->comment('잠금 시간');
            $table->text('memo')->nullable();
            $table->timestamps();

            $table->foreign('req_id')
                ->on('reqs')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('product_barcode_id')
                ->on('product_barcodes')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('set null');

            $table->foreign('cate4_id')
                ->on('cate4')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('set null');

            $table->foreign('cate5_id')
                ->on('cate5')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('set null');

            $table->foreign('monitor_size_lookup_id')
                ->on('monitor_size_lookups')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('set null');

            $table->foreign('product_lot_id')
                ->on('product_lots')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('set null');

            $table->foreign('product_vendor_id')
                ->on('product_vendors')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('set null');

            $table->foreign('product_link_id')
                ->on('product_links')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('set null');

            $table->foreign('user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('checked_user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            // 외래키 제약조건 추가
            $table->foreign('return_reason_b_id')
                ->on('return_reason_b')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('set null');

            $table->foreign('return_reason_m_id')
                ->on('return_reason_m')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('set null');

            $table->foreign('locked_by')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->index(['cate4_id', 'cate5_id']);
            $table->index(['qaid', 'req_id'], 'products_qaid_req_id_index');
            $table->index('qaid');
            $table->index('barcode');
            $table->index('cate4_id');
            $table->index('cate5_id');
            $table->index('user_id');
            $table->index('status');
            $table->index('rg');
            $table->index('duplicated');
            $table->index('checked_user_id');
            $table->index('checked_status');
            $table->index('checked_at');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
