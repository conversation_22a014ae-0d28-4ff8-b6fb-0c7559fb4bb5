<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repair_symptoms', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['general', 'apple'])->default('general')->comment('상품 유형(일반, 애플)');
            $table->string('name')->comment('증상 이름');
            $table->string('code')->comment('증상 코드');
            $table->foreignId('default_repair_process_id')->nullable();
            $table->foreignId('default_repair_grade_id')->nullable();
            $table->timestamps();

            $table->foreign('default_repair_process_id')
                ->on('repair_processes')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('default_repair_grade_id')
                ->on('repair_grades')
                ->references('id')
                ->onUpdate('cascade');

            $table->unique('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_symptoms');
    }
};
