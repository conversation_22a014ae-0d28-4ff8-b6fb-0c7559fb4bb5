<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('mem_no')->default(0)->comment('이전 멤버 번호');
            $table->foreignId('company_id')->nullable()->comment('소속 회사 인덱스');
            $table->string('role')->default('Guest')->comment('사용자 권한: Super-Admin, Admin, Manager(입고, 출고, 외주, 점검), Employee, Guest');
            $table->string('username')->comment('사용자 아이디');
            $table->string('caps_id')->nullable()->comment('캡스용 사용자 ID(유일값), 바코드용');
            $table->string('line_number')->nullable()->comment('생산 라인 번호');
            $table->string('name')->comment('사용자 이름');
            $table->enum('part', ['IT', '경영관리', '물류', '생산관리', '일반가전'])->default('일반가전')->comment('IT, 경영관리, 물류, 생산관리, 일반가전');
            $table->enum('position', ['대표', '이사', '공장장', '과장', '팀장', '주임', '소장', '반장', '팀원'])->default('팀원')->comment('대표, 이사, 공장장, 과장, 팀장, 주임, 소장, 반장, 팀원 등');
            $table->string('email')->nullable()->comment('메일 주소');
            $table->dateTime('email_verified_at')->nullable();
            $table->string('cellphone')->nullable()->comment('연락처(휴대폰)');
            $table->string('telephone')->nullable()->comment('연락처(기타)');
            $table->unsignedTinyInteger('status')
                ->default(0)
                ->comment('사용자 상태(1: 활성, 2: 비활성(휴면), 3: 이용불가, 9: 삭제)');
            $table->json('menu')->nullable()->comment('사용자 접근 가능 메뉴');
            $table->dateTime('login_at')->nullable()->comment('사용자 마지막 로그인 시간');
            $table->string('login_ip')->nullable()->comment('사용자 로그인 IP');
            $table->string('login_os')->nullable()->comment('사용자 로그인 OS');
            $table->string('password');
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('company_id')
                ->references('id')
                ->on('companies')
                ->onUpdate('cascade')
                ->onDelete('set null');

            $table->unique('username');
            $table->unique('caps_id');
            $table->index('line_number');
            $table->index('mem_no');
            $table->index('name');
            $table->index('email');
            $table->index('cellphone');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
