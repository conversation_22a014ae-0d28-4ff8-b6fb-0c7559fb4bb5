<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        /**
         * 위치 컬럼들은 나중에 따로 테이블로 빼자.
         * 또한 브랜드 컬럼도 있으면 좋을듯 싶다.
         * 이거 자체만으로도 하나의 시스템이라 지금 당장은 하기 힘드니 추후 다시 제작하자.
         */
        Schema::create('repair_parts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->nullable()->comment('수리 구성품 카테고리 인덱스');
            $table->string('barcode')->nullable()->comment('바코드');
            $table->string('name')->comment('구성품 이름');
            $table->string('model_number')->nullable()->comment('구성품 모델번호');
            $table->unsignedBigInteger('price')->default(0)->comment('구성품 가격');
            $table->unsignedMediumInteger('stock')->default(0)->comment('구성품 재고');
            $table->unsignedMediumInteger('reorder_stock')->default(5)->comment('재고 부족시 알림을 위한 최소 수량');
            $table->unsignedBigInteger('acc_count')->default(0)->comment('누적 사용 개수');
            // 구매불가 컬럼
            $table->enum('is_purchasable', ['Y', 'N'])->default('Y')->comment('구매 가능 여부(Y: 구매 가능, N: 구매 불가)');
            $table->string('location_area')->nullable()->comment('구성품 건물 위치(ex. A동)');
            $table->string('location_zone')->nullable()->comment('구성품 랙의 위치');
            $table->unsignedTinyInteger('location_floor')->default(1)->comment('랙에서의 층');
            $table->unsignedMediumInteger('location_position')->default(1)->comment('층에서의 위치');
            $table->text('memo')->nullable()->comment('메모');
            $table->timestamps();

            $table->foreign('category_id')
                ->on('repair_parts_categories')
                ->references('id')
                ->onUpdate('cascade');

            // 복합 인덱스 추가 (자주 함께 조회되는 컬럼들)
            $table->index(['category_id', 'is_purchasable'], 'repair_parts_category_purchasable_index');
            $table->index(['stock', 'reorder_stock'], 'repair_parts_stock_reorder_index');
            $table->index(['location_area', 'location_zone', 'location_floor'], 'repair_parts_location_index');
            $table->index(['name', 'model_number'], 'repair_parts_name_model_index');

            // 기존 인덱스들
            $table->unique('barcode');
            $table->index('name');
            $table->index('acc_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_parts');
    }
};
