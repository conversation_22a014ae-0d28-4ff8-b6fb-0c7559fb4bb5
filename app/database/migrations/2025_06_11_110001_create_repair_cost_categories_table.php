<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repair_cost_categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('repair_cost_policy_id')->comment('수리비 정책 인덱스');
            $table->foreignId('cate4_id')->nullable()->comment('4차 카테고리 인덱스');
            $table->foreignId('cate5_id')->nullable()->comment('5차 카테고리 인덱스 (nullable)');
            $table->enum('pricing_criteria', ['size', 'price', 'common'])
                ->default('price')
                ->comment('가격 기준 (size/price/common)');
            $table->boolean('is_active')->default(true)->comment('활성화 여부');
            $table->timestamps();

            // 외래키 제약조건
            $table->foreign('repair_cost_policy_id')
                ->references('id')
                ->on('repair_cost_policies')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->foreign('cate4_id')
                ->references('id')
                ->on('cate4')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->foreign('cate5_id')
                ->references('id')
                ->on('cate5')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            // 인덱스
            $table->index(['repair_cost_policy_id', 'cate4_id', 'cate5_id'], 'idx_repair_cost_categories_mapping');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_cost_categories');
    }
};
