<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('notification_recipients', function (Blueprint $table) {
            $table->id();
            $table->foreignUuid('notification_id')->comment('알림 ID');
            $table->foreignId('user_id')->comment('수신자 ID');
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->timestamps();

            $table->foreign('notification_id')
                ->references('id')
                ->on('notifications')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unique(['notification_id', 'user_id']);
            $table->index('notification_id');
            $table->index('user_id');
            $table->index('delivered_at');
            $table->index('read_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('notification_recipients');
    }
};
