<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('faqs', function (Blueprint $table) {
            $table->id();
            $table->string('subject')->comment('제목');
            $table->string('cate1')->comment('분류1');
            $table->string('cate2')->nullable()->comment('분류2');
            $table->string('solution_code')->comment('질문 유형');
            $table->text('content')->comment('질문 답변');
            $table->timestamps();

            $table->index('subject');
            $table->index('content');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('faqs');
    }
};
