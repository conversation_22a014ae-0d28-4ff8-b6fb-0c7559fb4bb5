<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_actions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->comment('카테고리 인덱스');
            $table->string('code')->comment('액션 코드(예: WAITING, COMPLETE, SYMPTOM)');
            $table->string('name')->comment('액션 이름');
            $table->text('description')->nullable()->comment('액션 설명');
            $table->foreignId('parent_id')->nullable()->comment('부모 액션 인덱스(계층 구조)');
            $table->unsignedInteger('sort_order')->default(0)->comment('정렬 순서');
            $table->boolean('is_active')->default(true)->comment('활성 상태');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('category_id')
                ->on('work_categories')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('parent_id')
                ->on('work_actions')
                ->references('id')
                ->onUpdate('cascade');

            $table->unique(['category_id', 'code'], 'unique_category_action');
            $table->index('category_id');
            $table->index('parent_id');
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_actions');
    }
};
