<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('qaid_reprints', function (Blueprint $table) {
            $table->id();
            $table->date('date')->comment('재발행 날짜');
            $table->string('qaid')->comment('재발행된 QAID');
            $table->foreignId('user_id')->comment('재발행한 사용자 ID');
            $table->unsignedTinyInteger('print_count')->default(1)->comment('재발행 횟수');
            $table->timestamps();

            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onUpdate('cascade');

            $table->unique('qaid');
            $table->index('date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('qaid_reprints');
    }
};
