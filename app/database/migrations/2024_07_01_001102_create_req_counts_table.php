<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('req_counts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('req_id')->comment('요청서 인덱스');
            $table->unsignedBigInteger('undelivered')->default(0)->comment('미입고 개수');
            $table->unsignedBigInteger('unchecked')->default(0)->comment('입고(검수)대기 개수');
            $table->unsignedBigInteger('checked')->default(0)->comment('점검대기 개수');
            $table->unsignedBigInteger('carryout')->default(0)->comment('외주반출 수리중 개수');
            $table->unsignedBigInteger('waiting')->default(0)->comment('수리대기(구성품 신청) 개수');
            $table->unsignedBigInteger('repaired')->default(0)->comment('수리/점검완료(창고) 개수');
            $table->unsignedBigInteger('checkout')->default(0)->comment('적재중(점검완료) 개수');
            $table->unsignedBigInteger('exporting')->default(0)->comment('출고대기(마감) 개수');
            $table->unsignedBigInteger('duplicated')->default(0)->comment('중복등록 개수');
            $table->unsignedBigInteger('unlinked')->default(0)->comment('미등록 개수');
            $table->unsignedBigInteger('completed')->default(0)->comment('출고완료 개수');
            $table->unsignedBigInteger('deleted')->default(0)->comment('보류(삭제) 개수');

            $table->foreign('req_id')
                ->on('reqs')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->index('req_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('req_counts');
    }
};
