<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('positions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('floor_id')->comment('층 인덱스');
            $table->string('name')->comment('위치명');
            $table->string('code')->comment('위치 코드');
            $table->unsignedTinyInteger('row')->comment('행');
            $table->unsignedTinyInteger('column')->comment('열');
            $table->text('description')->nullable()->comment('위치 설명');
            $table->boolean('is_active')->default(true)->comment('활성 여부');
            $table->boolean('is_occupied')->default(false)->comment('점유 여부');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('floor_id')
                ->on('floors')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->unique('code');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('positions');
    }
};
