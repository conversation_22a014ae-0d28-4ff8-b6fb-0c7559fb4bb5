<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_attendances', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->comment('사용자 인덱스');
            $table->date('work_date')->comment('근무일자');
            $table->enum('day_type', ['평일', '휴일'])->default('평일')->comment('근무일 명칭');
            $table->time('clock_in')->nullable()->comment('출근 시간');
            $table->time('clock_out')->nullable()->comment('퇴근 시간');
            $table->boolean('is_late')->default(false)->comment('지각 여부');
            $table->time('late_hours')->default('00:00:00')->comment('지각 시간');
            $table->boolean('is_early_leave')->default(false)->comment('조퇴 여부');
            $table->time('early_leave_hours')->default('00:00:00')->comment('조퇴 시간');
            $table->time('regular_hours')->default('00:00:00')->comment('기본 근무 시간');
            $table->time('overtime_hours')->default('00:00:00')->comment('연장 근무 시간');
            $table->time('total_hours')->default('00:00:00')->comment('총 근무 시간');
            $table->timestamps();

            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onUpdate('cascade');

            $table->index('work_date');
            $table->index('day_type');
            $table->unique(['user_id', 'work_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_attendances');
    }
};
