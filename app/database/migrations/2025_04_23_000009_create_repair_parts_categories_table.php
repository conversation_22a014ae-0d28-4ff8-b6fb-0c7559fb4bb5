<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('repair_parts_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('카테고리 이름');
            $table->unsignedBigInteger('parent_id')->nullable()->comment('상위 카테고리 인덱스');
            $table->unsignedInteger('order_no')->default(0)->comment('정렬 순서');
            $table->unsignedInteger('level')->default(1)->comment('카테고리 레벨');
            $table->timestamps();

            // 외래키 제약조건 추가 (자기 참조)
            $table->foreign('parent_id')
                ->on('repair_parts_categories')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('set null');

            // 복합 인덱스 추가 (자주 함께 조회되는 컬럼들)
            $table->index(['parent_id', 'level'], 'repair_parts_categories_parent_level_index');
            $table->index(['level', 'order_no'], 'repair_parts_categories_level_order_index');

            // 기존 단일 인덱스들
            $table->index('parent_id');
            $table->index('order_no');
            $table->index('level');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('repair_parts_categories');
    }
};
