<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('board_stats', function (Blueprint $table) {
            $table->id();
            $table->foreignId('board_id')->comment('게시물 인덱스');
            $table->foreignId('user_id')->comment('작성자 인덱스');
            $table->string('ip')->comment('확인한 IP');
            $table->string('user_agent')->nullable()->comment('접속 디바이스');
            $table->timestamps();

            $table->foreign('board_id')
                ->on('boards')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->index('board_id');
            $table->index('user_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('board_stats');
    }
};
