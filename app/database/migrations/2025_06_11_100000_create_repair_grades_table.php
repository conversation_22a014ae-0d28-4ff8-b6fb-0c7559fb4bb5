<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repair_grades', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_no')->default(0)->comment('정렬 순서');
            $table->string('name')->comment('증상 이름');
            $table->string('code')->comment('증상 코드');
            $table->timestamps();

            $table->unique('code');
            $table->index('order_no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_grades');
    }
};
