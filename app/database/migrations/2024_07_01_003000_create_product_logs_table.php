<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 상품의 현재 상태만 저장
        Schema::create('product_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->nullable()->default(null);
            $table->string('model_type')->nullable()->default(null)->comment('연관 모델');
            $table->unsignedBigInteger('model_id')->nullable()->default(null)->comment('연관 모델 id');
            $table->foreignId('work_status_id')->comment('상태 id');
            $table->foreignId('user_id')->nullable();
            $table->text('memo')->nullable();
            $table->timestamps();

            $table->foreign('product_id')
                ->on('products')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('cascade');

            $table->foreign('work_status_id')
                ->on('work_statuses')
                ->references('id')
                ->onUpdate('cascade');

            $table->foreign('user_id')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade');

            $table->index('product_id');
            $table->index(['model_type', 'model_id', 'work_status_id']);
            $table->index('user_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_logs');
    }
};
