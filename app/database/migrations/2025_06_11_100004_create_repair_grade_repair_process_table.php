<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('repair_grade_repair_process', function (Blueprint $table) {
            $table->primary(['repair_grade_id', 'repair_process_id']);
            $table->foreignId('repair_grade_id');
            $table->foreignId('repair_process_id');
            $table->timestamps();

            $table->foreign('repair_grade_id')
                ->references('id')
                ->on('repair_grades')
                ->onDelete('cascade');

            $table->foreign('repair_process_id')
                ->references('id')
                ->on('repair_processes')
                ->onDelete('cascade');

            // 추가 인덱스 (역방향 조회를 위한 인덱스)
            $table->index('repair_process_id', 'repair_grade_repair_process_process_index');
            $table->index('repair_grade_id', 'repair_grade_repair_process_grade_index');
            $table->index('created_at', 'repair_grade_repair_process_created_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('repair_grade_repair_process');
    }
};
