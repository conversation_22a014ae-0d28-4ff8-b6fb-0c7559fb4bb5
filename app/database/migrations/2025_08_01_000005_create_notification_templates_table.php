<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('notification_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50)->comment('템플릿명');
            $table->string('title', 200)->comment('알림 제목');
            $table->text('content')->comment('알림 내용 (최대 5000자)');
            $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal')->comment('우선순위');
            $table->unsignedInteger('usage_count')->default(0)->comment('사용 횟수');
            $table->foreignId('created_by')->comment('템플릿 생성자 ID');
            $table->timestamps();

            $table->foreign('created_by')
                ->on('users')
                ->references('id')
                ->onUpdate('cascade')
                ->onDelete('restrict');

            $table->unique('name');
            $table->index('name');
            $table->index('priority');
            $table->index('usage_count');
            $table->index('created_by');
            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('notification_templates');
    }
};
