<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('locations', function (Blueprint $table) {
            $table->id();
            $table->string('place')->default('KR-CJ')->comment('위치 지점(국가-도시), 한국-충주');
            $table->string('code')->nullable()->comment('위치 코드');
            $table->string('name')->nullable()->comment('위치 설명');
            $table->enum('enable', ['Y', 'N'])->default('Y')->comment('적재 가능 여부(Y, N)');
            $table->timestamps();

            $table->index('code');
            $table->index('enable');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('locations');
    }
};
