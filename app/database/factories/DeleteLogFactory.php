<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DeleteLog>
 */
class DeleteLogFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => \App\Models\User::factory(),
            'deletable_type' => null,
            'deletable_id' => null,
            'content' => $this->faker->sentence(),
            'ip' => $this->faker->optional()->ipv4(),
        ];
    }
}
