<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductLink>
 */
class ProductLinkFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'vendor_item_id' => $this->faker->optional()->bothify('VENDOR-#####'),
            'product_id' => $this->faker->optional()->bothify('PROD-#####'),
            'item_id' => $this->faker->optional()->bothify('ITEM-#####'),
        ];
    }
}
