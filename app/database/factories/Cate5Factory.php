<?php

namespace Database\Factories;

use App\Models\Cate4;
use App\Models\Cate5;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Cate5>
 */
class Cate5Factory extends Factory
{
    protected $model = Cate5::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $subcategories = [
            '일반형',
            '고급형',
            '프리미엄',
            '게이밍',
            '비즈니스',
            '홈엔터테인먼트',
            '포터블',
            '울트라북',
            '워크스테이션',
            '미니PC',
        ];

        return [
            'cate4_id' => Cate4::factory(),
            'name' => $this->faker->randomElement($subcategories),
        ];
    }

    /**
     * 특정 4차 카테고리에 속한 5차 카테고리 상태
     */
    public function forCate4(Cate4 $cate4): static
    {
        return $this->state(fn (array $attributes) => [
            'cate4_id' => $cate4->id,
        ]);
    }

    /**
     * 일반형 서브카테고리 상태
     */
    public function general(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => '일반형',
        ]);
    }

    /**
     * 고급형 서브카테고리 상태
     */
    public function premium(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => '고급형',
        ]);
    }

    /**
     * 게이밍 서브카테고리 상태
     */
    public function gaming(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => '게이밍',
        ]);
    }

    /**
     * 비즈니스 서브카테고리 상태
     */
    public function business(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => '비즈니스',
        ]);
    }
}
