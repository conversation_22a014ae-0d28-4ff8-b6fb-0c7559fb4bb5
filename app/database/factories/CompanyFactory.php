<?php

namespace Database\Factories;

use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Company>
 */
class CompanyFactory extends Factory
{
    protected $model = Company::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'code' => $this->faker->unique()->word,
            'name' => $this->faker->company,
            'kr_name' => $this->faker->companySuffix,
            'logo_uri' => $this->faker->imageUrl(),
            'type' => 0,
            'f_default' => 'N',
            'description' => $this->faker->sentence,
            'status' => 0,
        ];
    }
}
