<?php

namespace Database\Factories;

use App\Models\RepairParts;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairPartsLog>
 */
class RepairPartsLogFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'repair_parts_id' => RepairParts::factory(),
            'type' => $this->faker->randomElement(['in', 'out']),
            'quantity' => $this->faker->numberBetween(1, 10),
            'memo' => $this->faker->optional()->text(50),
        ];
    }
}
