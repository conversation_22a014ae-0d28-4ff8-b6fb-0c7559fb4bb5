<?php

namespace Database\Factories;

use App\Models\RepairCostTypeProcessMapping;
use App\Models\RepairProcess;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairCostTypeProcessMapping>
 */
class RepairProcessMappingFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = RepairCostTypeProcessMapping::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $repairTypes = RepairCostTypeProcessMapping::getRepairTypeValues();

        return [
            'process_code' => RepairProcess::factory()->create()->code,
            'repair_type' => $this->faker->randomElement($repairTypes),
            'is_active' => $this->faker->boolean(80), // 80% 확률로 활성화
        ];
    }

    /**
     * 수리_부품교체 매핑 상태
     */
    public function parts(): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS,
        ]);
    }

    /**
     * 수리_세척 매핑 상태
     */
    public function cleaning(): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING,
        ]);
    }

    /**
     * 수리_S/W 매핑 상태
     */
    public function software(): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_SOFTWARE,
        ]);
    }

    /**
     * 수리_기타 매핑 상태
     */
    public function other(): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_OTHER,
        ]);
    }

    /**
     * 검수(점검) 매핑 상태
     */
    public function inspection(): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_INSPECTION,
        ]);
    }

    /**
     * 활성화된 매핑 상태
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * 비활성화된 매핑 상태
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * 특정 프로세스 코드로 매핑 생성
     */
    public function withProcessCode(string $processCode): static
    {
        return $this->state(fn (array $attributes) => [
            'process_code' => $processCode,
        ]);
    }
}
