<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ReqCount>
 */
class ReqCountFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'req_id' => \App\Models\Req::factory(),
            'undelivered' => $this->faker->numberBetween(0, 10),
            'unchecked' => $this->faker->numberBetween(0, 10),
            'checked' => $this->faker->numberBetween(0, 10),
            'carryout' => $this->faker->numberBetween(0, 10),
            'waiting' => $this->faker->numberBetween(0, 10),
            'repaired' => $this->faker->numberBetween(0, 10),
            'checkout' => $this->faker->numberBetween(0, 10),
            'exporting' => $this->faker->numberBetween(0, 10),
            'duplicated' => $this->faker->numberBetween(0, 10),
            'unlinked' => $this->faker->numberBetween(0, 10),
            'completed' => $this->faker->numberBetween(0, 10),
            'deleted' => $this->faker->numberBetween(0, 10),
        ];
    }
}
