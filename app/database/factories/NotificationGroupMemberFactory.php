<?php

namespace Database\Factories;

use App\Models\NotificationGroup;
use App\Models\NotificationGroupMember;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class NotificationGroupMemberFactory extends Factory
{
    protected $model = NotificationGroupMember::class;

    public function definition(): array
    {
        return [
            'group_id' => NotificationGroup::factory(),
            'user_id' => User::factory(),
        ];
    }
}