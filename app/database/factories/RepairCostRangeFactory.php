<?php

namespace Database\Factories;

use App\Models\RepairCostCategory;
use App\Models\RepairCostRange;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairCostRange>
 */
class RepairCostRangeFactory extends Factory
{
    protected $model = RepairCostRange::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $unit = $this->faker->randomElement([
            RepairCostRange::UNIT_INCH,
            RepairCostRange::UNIT_WON,
            RepairCostRange::UNIT_COMMON,
        ]);

        // 단위에 따른 범위 값 설정
        [$minValue, $maxValue, $rangeName] = match ($unit) {
            RepairCostRange::UNIT_INCH => $this->generateInchRange(),
            RepairCostRange::UNIT_WON => $this->generateWonRange(),
            RepairCostRange::UNIT_COMMON => [null, null, '공통']
        };

        return [
            'repair_cost_category_id' => RepairCostCategory::factory(),
            'range_name' => $rangeName,
            'min_value' => $minValue,
            'max_value' => $maxValue,
            'unit' => $unit,
            'is_active' => $this->faker->boolean(90), // 90% 확률로 활성화
        ];
    }

    /**
     * 인치 단위 범위 생성
     */
    private function generateInchRange(): array
    {
        $ranges = [
            [0, 24, '24인치 미만'],
            [24, 32, '24-32인치'],
            [32, 43, '32-43인치'],
            [43, 55, '43-55인치'],
            [55, 65, '55-65인치'],
            [65, null, '65인치 이상'],
        ];

        $range = $this->faker->randomElement($ranges);

        return [$range[0], $range[1], $range[2]];
    }

    /**
     * 원화 단위 범위 생성
     */
    private function generateWonRange(): array
    {
        $ranges = [
            [0, 50000, '5만원 미만'],
            [50000, 100000, '5-10만원'],
            [100000, 200000, '10-20만원'],
            [200000, 500000, '20-50만원'],
            [500000, 1000000, '50-100만원'],
            [1000000, null, '100만원 이상'],
        ];

        $range = $this->faker->randomElement($ranges);

        return [$range[0], $range[1], $range[2]];
    }

    /**
     * 인치 단위 범위 상태
     */
    public function inchUnit(): static
    {
        return $this->state(function (array $attributes) {
            [$minValue, $maxValue, $rangeName] = $this->generateInchRange();

            return [
                'unit' => RepairCostRange::UNIT_INCH,
                'min_value' => $minValue,
                'max_value' => $maxValue,
                'range_name' => $rangeName,
            ];
        });
    }

    /**
     * 원화 단위 범위 상태
     */
    public function wonUnit(): static
    {
        return $this->state(function (array $attributes) {
            [$minValue, $maxValue, $rangeName] = $this->generateWonRange();

            return [
                'unit' => RepairCostRange::UNIT_WON,
                'min_value' => $minValue,
                'max_value' => $maxValue,
                'range_name' => $rangeName,
            ];
        });
    }

    /**
     * 공통 단위 범위 상태
     */
    public function commonUnit(): static
    {
        return $this->state(fn (array $attributes) => [
            'unit' => RepairCostRange::UNIT_COMMON,
            'min_value' => null,
            'max_value' => null,
            'range_name' => '공통',
        ]);
    }

    /**
     * 특정 카테고리에 속한 범위 상태
     */
    public function forCategory(RepairCostCategory $category): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_cost_category_id' => $category->id,
        ]);
    }

    /**
     * 24인치 미만 모니터 범위 상태
     */
    public function smallMonitor(): static
    {
        return $this->state(fn (array $attributes) => [
            'range_name' => '24인치 미만',
            'min_value' => 0,
            'max_value' => 24,
            'unit' => RepairCostRange::UNIT_INCH,
            'is_active' => true,
        ]);
    }

    /**
     * 24-32인치 모니터 범위 상태
     */
    public function mediumMonitor(): static
    {
        return $this->state(fn (array $attributes) => [
            'range_name' => '24-32인치',
            'min_value' => 24,
            'max_value' => 32,
            'unit' => RepairCostRange::UNIT_INCH,
            'is_active' => true,
        ]);
    }

    /**
     * 32-43인치 모니터 범위 상태
     */
    public function largeMonitor(): static
    {
        return $this->state(fn (array $attributes) => [
            'range_name' => '32-43인치',
            'min_value' => 32,
            'max_value' => 43,
            'unit' => RepairCostRange::UNIT_INCH,
            'is_active' => true,
        ]);
    }

    /**
     * 5만원 미만 가격 범위 상태
     */
    public function lowPrice(): static
    {
        return $this->state(fn (array $attributes) => [
            'range_name' => '5만원 미만',
            'min_value' => 0,
            'max_value' => 50000,
            'unit' => RepairCostRange::UNIT_WON,
            'is_active' => true,
        ]);
    }

    /**
     * 5-10만원 가격 범위 상태
     */
    public function midPrice(): static
    {
        return $this->state(fn (array $attributes) => [
            'range_name' => '5-10만원',
            'min_value' => 50000,
            'max_value' => 100000,
            'unit' => RepairCostRange::UNIT_WON,
            'is_active' => true,
        ]);
    }

    /**
     * 비활성화 상태
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
