<?php

namespace Database\Factories;

use App\Models\Notification;
use App\Models\NotificationRecipient;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class NotificationRecipientFactory extends Factory
{
    protected $model = NotificationRecipient::class;

    public function definition(): array
    {
        return [
            'notification_id' => Notification::factory(),
            'user_id' => User::factory(),
            'delivered_at' => null,
            'read_at' => null,
        ];
    }

    public function delivered(): static
    {
        return $this->state(fn (array $attributes) => [
            'delivered_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
        ]);
    }

    public function read(): static
    {
        return $this->state(fn (array $attributes) => [
            'delivered_at' => $this->faker->dateTimeBetween('-1 day', '-1 hour'),
            'read_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
        ]);
    }

    public function undelivered(): static
    {
        return $this->state(fn (array $attributes) => [
            'delivered_at' => null,
            'read_at' => null,
        ]);
    }
}