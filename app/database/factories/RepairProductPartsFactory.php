<?php

namespace Database\Factories;

use App\Models\RepairParts;
use App\Models\RepairProduct;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairProductParts>
 */
class RepairProductPartsFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'repair_product_id' => RepairProduct::factory(),
            'repair_parts_id' => RepairParts::factory(),
            'quantity' => $this->faker->numberBetween(1, 5),
            'price' => $this->faker->numberBetween(1000, 50000),
        ];
    }
}
