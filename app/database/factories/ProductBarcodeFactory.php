<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductBarcode>
 */
class ProductBarcodeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'barcode' => $this->faker->unique()->ean13(),
            'wms_sku_id' => $this->faker->optional()->bothify('SKU-#####'),
            'external_wms_sku_id' => $this->faker->optional()->bothify('EXTSKU-#####'),
        ];
    }
}
