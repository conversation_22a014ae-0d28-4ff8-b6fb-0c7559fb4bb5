<?php

namespace Database\Factories;

use App\Models\Cate4;
use App\Models\Cate5;
use App\Models\RepairCostCategory;
use App\Models\RepairCostPolicy;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairCostCategory>
 */
class RepairCostCategoryFactory extends Factory
{
    protected $model = RepairCostCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'repair_cost_policy_id' => RepairCostPolicy::factory(),
            'cate4_id' => Cate4::factory(),
            'cate5_id' => null, // 기본적으로 5차 카테고리는 null
            'pricing_criteria' => $this->faker->randomElement([
                RepairCostCategory::PRICING_CRITERIA_SIZE,
                RepairCostCategory::PRICING_CRITERIA_PRICE,
                RepairCostCategory::PRICING_CRITERIA_COMMON,
            ]),
            'is_active' => $this->faker->boolean(90), // 90% 확률로 활성화
        ];
    }

    /**
     * 크기 기준 카테고리 상태
     */
    public function sizeBasedPricing(): static
    {
        return $this->state(fn (array $attributes) => [
            'pricing_criteria' => RepairCostCategory::PRICING_CRITERIA_SIZE,
        ]);
    }

    /**
     * 가격 기준 카테고리 상태
     */
    public function priceBasedPricing(): static
    {
        return $this->state(fn (array $attributes) => [
            'pricing_criteria' => RepairCostCategory::PRICING_CRITERIA_PRICE,
        ]);
    }

    /**
     * 공통 금액 카테고리 상태
     */
    public function commonPricing(): static
    {
        return $this->state(fn (array $attributes) => [
            'pricing_criteria' => RepairCostCategory::PRICING_CRITERIA_COMMON,
        ]);
    }

    /**
     * 5차 카테고리 포함 상태
     */
    public function withCate5(): static
    {
        return $this->state(fn (array $attributes) => [
            'cate5_id' => Cate5::factory(),
        ]);
    }

    /**
     * 특정 시스템에 속한 카테고리 상태
     */
    public function forSystem(RepairCostPolicy $system): static
    {
        return $this->state(fn (array $attributes) => [
            'repair_cost_policy_id' => $system->id,
            'pricing_criteria' => match ($system->pricing_type) {
                'size' => RepairCostCategory::PRICING_CRITERIA_SIZE,
                'price' => RepairCostCategory::PRICING_CRITERIA_PRICE,
                'common' => RepairCostCategory::PRICING_CRITERIA_COMMON,
                default => RepairCostCategory::PRICING_CRITERIA_PRICE
            },
        ]);
    }

    /**
     * 모니터 카테고리 상태
     */
    public function monitor(): static
    {
        return $this->state(fn (array $attributes) => [
            'pricing_criteria' => RepairCostCategory::PRICING_CRITERIA_SIZE,
            'is_active' => true,
        ]);
    }

    /**
     * 비활성화 상태
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
