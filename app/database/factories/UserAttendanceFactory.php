<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserAttendance>
 */
class UserAttendanceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'work_date' => $this->faker->date(),
            'day_type' => $this->faker->randomElement(['평일', '휴일']),
            'clock_in' => $this->faker->time('H:i:s'),
            'clock_out' => $this->faker->time('H:i:s'),
            'is_late' => $this->faker->boolean(20),
            'late_hours' => '00:00:00',
            'is_early_leave' => $this->faker->boolean(10),
            'early_leave_hours' => '00:00:00',
            'regular_hours' => '08:00:00',
            'overtime_hours' => '00:00:00',
            'total_hours' => '08:00:00',
        ];
    }
}
