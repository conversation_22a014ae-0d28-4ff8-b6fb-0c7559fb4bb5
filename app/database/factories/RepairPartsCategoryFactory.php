<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairPartsCategory>
 */
class RepairPartsCategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->text(20),
            'parent_id' => null,
            'order_no' => $this->faker->numberBetween(1, 100),
            'level' => 1,
        ];
    }

    /**
     * 하위 카테고리로 생성
     */
    public function child(int $parentId): static
    {
        return $this->state(fn (array $attributes) => [
            'parent_id' => $parentId,
            'level' => 2,
        ]);
    }
}
