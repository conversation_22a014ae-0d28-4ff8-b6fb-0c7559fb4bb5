<?php

namespace Database\Factories;

use App\Models\NotificationTemplate;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\NotificationTemplate>
 */
class NotificationTemplateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = NotificationTemplate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->words(2, true).'템플릿',
            'title' => $this->faker->sentence(4),
            'content' => $this->faker->paragraph(3),
            'priority' => $this->faker->randomElement(['low', 'normal', 'high', 'urgent']),
            'usage_count' => $this->faker->numberBetween(0, 100),
            'created_by' => User::factory(),
        ];
    }

    /**
     * 낮은 우선순위 템플릿 상태
     */
    public function lowPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'low',
        ]);
    }

    /**
     * 보통 우선순위 템플릿 상태
     */
    public function normalPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'normal',
        ]);
    }

    /**
     * 높은 우선순위 템플릿 상태
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'high',
        ]);
    }

    /**
     * 긴급 우선순위 템플릿 상태
     */
    public function urgentPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'urgent',
        ]);
    }

    /**
     * 자주 사용되는 템플릿 상태
     */
    public function frequentlyUsed(): static
    {
        return $this->state(fn (array $attributes) => [
            'usage_count' => $this->faker->numberBetween(50, 200),
        ]);
    }

    /**
     * 사용되지 않은 템플릿 상태
     */
    public function unused(): static
    {
        return $this->state(fn (array $attributes) => [
            'usage_count' => 0,
        ]);
    }
}
