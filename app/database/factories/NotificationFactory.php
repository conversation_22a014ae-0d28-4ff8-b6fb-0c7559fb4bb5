<?php

namespace Database\Factories;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class NotificationFactory extends Factory
{
    protected $model = Notification::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(4),
            'content' => $this->faker->paragraph(2),
            'priority' => $this->faker->randomElement(['low', 'normal', 'high']),
            'action_url' => $this->faker->optional()->url(),
            'sender_id' => User::factory(),
            'target_type' => $this->faker->randomElement(['all', 'group', 'individual']),
            'target_id' => null,
            'status' => $this->faker->randomElement(['draft', 'sent', 'cancelled']),
            'sent_at' => $this->faker->optional()->dateTimeBetween('-1 week', 'now')?->format('Y-m-d H:i:s'),
        ];
    }

    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'sent',
            'sent_at' => now()->toDateTimeString(),
        ]);
    }

    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'sent_at' => null,
        ]);
    }
}