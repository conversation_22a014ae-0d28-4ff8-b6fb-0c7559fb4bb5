<?php

namespace Database\Factories;

use App\Models\RepairPartsCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RepairParts>
 */
class RepairPartsFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $names = [
            '노트북 배터리', '노트북 어댑터', '노트북 키보드', '노트북 LCD', '노트북 하드디스크',
            '데스크톱 메모리', '데스크톱 그래픽카드', '데스크톱 CPU', '데스크톱 메인보드', '데스크톱 파워',
            '모니터 케이블', '모니터 스탠드', '모니터 베젤', '모니터 인버터', '모니터 백라이트',
        ];

        $memos = [
            '고품질 부품', '정품 부품', '호환 부품', '수리용 부품', '교체용 부품',
            '긴급 주문', '재고 부족', '신제품', '단종 예정', '할인 상품',
        ];

        return [
            'category_id' => RepairPartsCategory::factory(),
            'barcode' => $this->faker->unique()->ean13(),
            'name' => $names[array_rand($names)],
            'model_number' => $this->faker->optional()->regexify('[A-Z]{2}[0-9]{6}'),
            'price' => $this->faker->numberBetween(1000, 100000),
            'stock' => $this->faker->numberBetween(0, 100),
            'reorder_stock' => $this->faker->numberBetween(5, 20),
            'acc_count' => $this->faker->numberBetween(0, 1000),
            'is_purchasable' => $this->faker->numberBetween(0, 1) ? 'Y' : 'N',
            'location_area' => $this->faker->optional()->numberBetween(0, 1) ? ['A동', 'B동', 'C동'][array_rand(['A동', 'B동', 'C동'])] : null,
            'location_zone' => $this->faker->optional()->numberBetween(0, 1) ? ['A구역', 'B구역', 'C구역'][array_rand(['A구역', 'B구역', 'C구역'])] : null,
            'location_floor' => $this->faker->numberBetween(1, 5),
            'location_position' => $this->faker->numberBetween(1, 50),
            'memo' => $this->faker->optional()->numberBetween(0, 1) ? $memos[array_rand($memos)] : null,
        ];
    }

    /**
     * 구매 가능한 구성품으로 생성
     */
    public function purchasable(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_purchasable' => 'Y',
        ]);
    }

    /**
     * 구매 불가능한 구성품으로 생성
     */
    public function notPurchasable(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_purchasable' => 'N',
        ]);
    }

    /**
     * 재고 부족 상태로 생성
     */
    public function lowStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'stock' => $this->faker->numberBetween(0, 5),
            'reorder_stock' => 10,
        ]);
    }

    /**
     * 품절 상태로 생성
     */
    public function outOfStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'stock' => 0,
        ]);
    }
}
