<?php

namespace App\Services;

use App\Models\CarryoutProduct;
use App\Models\Product;
use App\Models\RepairCostRange;
use App\Models\RepairProduct;
use App\Models\User;
use App\Models\WorkStatus;
use App\Traits\Repair\AppleTrait;
use App\Traits\Repair\GeneralTrait;
use App\Traits\Repair\GradeTrait;
use App\Traits\Repair\ProductPartsTrait;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

class RepairService
{
    use AppleTrait, GeneralTrait;
    use GradeTrait, ProductPartsTrait;

    private CountService $countService;

    private WorkStatusService $workStatusService;

    private ProductLogService $logger;

    private RepairCostService $repairCostService; // 새로운 수리비 서비스 추가

    private MonitorSizeExtractionService $monitorSizeService;

    /**
     * 생성자
     */
    public function __construct(
        CountService $countService,
        WorkStatusService $workStatusService,
        ProductLogService $logger,
        RepairCostService $repairCostService, // 새로운 의존성 주입
        MonitorSizeExtractionService $monitorSizeService
    ) {
        $this->countService = $countService;
        $this->workStatusService = $workStatusService;
        $this->logger = $logger;
        $this->repairCostService = $repairCostService; // 새로운 서비스 할당
        $this->monitorSizeService = $monitorSizeService;
    }

    /**
     * 제품에 대한 표시 가능한 수리비 정보를 조회합니다.
     * checkProduct와 같이 이미 수리 완료된 상품의 비용 정보를 다시 표시할 때 사용됩니다.
     */
    public function getDisplayableRepairCostInfo(Product $product): array
    {
        // 최신 수리 내역 조회
        $repairProduct = $product->repairProduct;

        if (! $repairProduct || ! $repairProduct->repairCost) {
            return [
                'cost_type' => null,
                'cost_unit' => null,
                'cost_range_list' => [],
                'cost_range_selected' => null,
            ];
        }

        $repairCost = $repairProduct->repairCost;

        $amount = $repairCost->amount;
        $costRange = $repairCost->range;
        $costCategory = $costRange?->category;
        $costPolicy = $costCategory?->policy;

        // cost_type 결정: policy의 pricing_type을 우선 사용, 없으면 repairType의 name 사용
        $costPolicyPricingType = $costPolicy->pricing_type ?? 'price';

        $costRangeList = [];
        if ($costCategory && $costRange) {
            // 현재 적용된 범위의 단위와 같은 단위의 범위만 필터링
            $currentUnit = $costRange->unit;

            $costRangeList = $costCategory->ranges()
                ->active()
                ->where('unit', $currentUnit) // 같은 단위의 범위만 필터링
                ->orderBy('min_value')
                ->get()
                ->map(fn (RepairCostRange $range) => [
                    'id' => $range->id,
                    'range' => $this->formatRangeDisplay($range),
                ])->all();
        }

        return [
            'invoiceAmount' => $amount,
            'cost_type' => $costPolicyPricingType,
            'cost_unit' => $costRange?->unit,
            'cost_range_list' => $costRangeList,
            'cost_range_selected' => $costRange?->id,
        ];
    }

    /**
     * 범위를 표시용 형식으로 포맷팅합니다.
     */
    private function formatRangeDisplay(RepairCostRange $range): string
    {
        $minValue = $range->min_value;
        $maxValue = $range->max_value;
        $unit = $range->unit;

        // 단위별 포맷팅
        return match ($unit) {
            'inch' => $this->formatInchRange($minValue, $maxValue),
            'cm' => $this->formatCmRange($minValue, $maxValue),
            'won' => $this->formatWonRange($minValue, $maxValue),
            'common' => $range->range_name ?? '공통',
            default => $range->range_name ?? '기타'
        };
    }

    /**
     * 인치 범위 포맷팅
     */
    private function formatInchRange($minValue, $maxValue): string
    {
        $formattedMin = $this->convertDecimalToInteger($minValue);
        $formattedMax = $this->convertDecimalToInteger($maxValue);

        if ($minValue == 0) {
            return "0인치 ~ {$formattedMax}인치";
        }

        return "{$formattedMin}인치 ~ {$formattedMax}인치";
    }

    /**
     * 센티미터 범위 포맷팅
     */
    private function formatCmRange($minValue, $maxValue): string
    {
        $formattedMin = $this->convertDecimalToInteger($minValue);
        $formattedMax = $this->convertDecimalToInteger($maxValue);

        if ($minValue == 0) {
            return "0cm ~ {$formattedMax}cm";
        }

        return "{$formattedMin}cm ~ {$formattedMax}cm";
    }

    /**
     * 원화 범위 포맷팅
     */
    private function formatWonRange($minValue, $maxValue): string
    {
        $formattedMin = $this->convertDecimalToInteger($minValue);
        $formattedMax = $this->convertDecimalToInteger($maxValue);

        if ($minValue == 0) {
            return '0원 ~ '.number_format($formattedMax).'원';
        }

        return number_format($formattedMin).'원 ~ '.number_format($formattedMax).'원';
    }

    /**
     * 인치 값 포맷팅 (.00인 경우 정수로 표시)
     */
    private function convertDecimalToInteger($value): string
    {
        if ($value == 0) {
            return '0';
        }

        // .00으로 끝나는 경우 정수로 표시
        if (fmod($value, 1) == 0) {
            return (int) $value;
        }

        // 소수점이 있는 경우 그대로 표시
        return $value;
    }

    // ========================================
    // 공개 메서드 (Public Methods)
    // ========================================

    /**
     * 수리내역 저장: waiting
     *
     * @throws Exception|Throwable
     */
    public function waiting(array $data, Product $product): void
    {
        try {
            DB::beginTransaction();

            $user = auth()->user();
            $now = now();
            $data['status'] = RepairProduct::STATUS_WAITING;
            $data['user_id'] = $user->id;
            $data['now'] = $now;

            // 수리대기(구성품 신청) 상태 변경
            if ($user->role === User::ROLE_GUEST) {
                $product->update([
                    'status' => Product::STATUS_CARRIED_OUT_WAITING,
                ]);
            } else {
                $product->update([
                    'status' => Product::STATUS_WAITING,
                ]);
            }

            // 로그 저장을 위한 상태 id
            $data['log_mode'] = 'repair_waiting'; // 로그에 수리 대기 찍히게
            $statusIds = $this->getStatusIds($data);

            foreach ($statusIds as $statusId) {
                $this->logger->addLog($product, 'App\Models\Product', $product->id, $statusId, $user->id);
            }

            $memo = $data['memo'] ?? null;

            // waiting에서는 수리비 계산하지 않고 기본값 설정
            $data['invoice1'] = 0;
            $data['invoice3'] = 0;
            $data['memo'] = $memo;
            $data['repair_cost_info'] = [
                'repair_cost_id' => null,
                'calculation_basis' => '대기 상태 - 완료 시 계산 예정',
                'calculation_details' => null,
            ];

            $repairProduct = $this->createRepairProduct($product, $data);

            if (isset($statusIds[WorkStatus::LINK_REPAIR_WAITING])) {
                $this->logger->addLog($product, 'App\Models\RepairProduct', $repairProduct->id, $statusIds[WorkStatus::LINK_REPAIR_WAITING], $user->id, '수리/점검 대기(구성품 신청 중)');
            }

            // 구성품 처리 추가 - complete 메서드와 동일한 코드
            $this->updateRepairProductParts($repairProduct, $data, $user, $this->workStatusService, $this->logger);

            // 구성품 총 비용 계산 후 invoice2 업데이트
            $invoice2 = $repairProduct->parts_total_cost;
            if ($invoice2 > 0) {
                $repairProduct->update(['invoice2' => $invoice2]);
            }

            // 수리대기(구성품 신청) 카운팅<br>
            // 입고검수완료 : checked - 1, 수리대기(구성품 신청) : waiting + 1
            $counters[$product->req_id] = [
                'checked' => -1,
                'waiting' => 1,
            ];

            $this->countService->multipleUpdate($counters);
            $this->logger->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('product', '수리/점검 저장 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 수리내역 저장
     * waiting, complete 에 따라 처리 되어야 함.
     * 특히 waiting 이었다가 complete 로 바뀌는 경우 등급 같은 것은 로그에 저장하지 않아도 됨
     *
     * @throws Exception|Throwable
     */
    public function complete(array $data, Product $product): void
    {
        try {
            DB::beginTransaction();

            $user = auth()->user();
            $now = now();
            $data['status'] = RepairProduct::STATUS_REPAIRED;
            $data['user_id'] = $user->id;
            $data['now'] = $now;

            // 수리/점검 완료 상품 상태 업데이트
            if ($user->role === User::ROLE_GUEST) {
                // 협력 업체일 경우
                $product->update([
                    'checked_status' => Product::CHECKED_STATUS_CHECKED,
                    'status' => Product::STATUS_CARRIED_OUT_REPAIRED,
                ]);

                $carryoutProduct = CarryoutProduct::where('product_id', $product->id)->first();
                $carryoutProduct?->update([
                    'status' => CarryoutProduct::STATUS_RENOVATED,
                    'renovate_at' => $now,
                    'renovator_id' => $user->id,
                    'repair_symptom_id' => $this->findSymptomIdByCode($data['symptom_code']),
                    'repair_process_id' => $this->findProcessIdByCode($data['process_code']),
                    'repair_grade_id' => $this->findGradeIdByCode($data['grade_code']),
                ]);
            } else {
                // 본사 직원일 경우
                $product->update([
                    'checked_status' => Product::CHECKED_STATUS_CHECKED,
                    'status' => Product::STATUS_REPAIRED,
                ]);
            }

            // 로그 저장을 위한 상태 id
            $data['log_mode'] = 'repair_complete'; // 로그에 수리 완료 찍히게
            $statusIds = $this->getStatusIds($data);

            // 기존 수리 내역이 있는지 확인(repair_product_id)
            // 내역이 있다면 기존에 대기 중이던 내역이므로 waiting -> complete 로 처리
            $repairProductId = $data['repair_product_id'] ?? null;
            $repairProduct = $repairProductId ? RepairProduct::find($repairProductId) : null;

            // ST_XL 등급 확인: 'ST_XL'로 시작하는 경우 모든 수리비 0원 처리
            $isStXlGrade = isset($data['grade_code']) && str_starts_with($data['grade_code'], 'ST_XL');
            $memo = $data['memo'] ?? null;

            if ($isStXlGrade) {
                $invoice1 = 0;
                $invoice3 = 0;
                $repairCostInfo = [
                    'repair_cost_id' => null,
                    'calculation_basis' => "{$data['grade_code']} 등급으로 수리비 면제",
                    'calculation_details' => null,
                ];
            } else {
                // 수리비 계산
                $repairCostResult = $this->calculateRepairCostOnce($product, $data);
                $invoice1 = $repairCostResult['invoice1'];
                $invoice3 = $this->calculateInvoice3($product, $data); // OS 재설치비
                $repairCostInfo = $repairCostResult['repair_cost_info'];

                // OS 설치비가 있는 경우 로그 및 메모 처리
                if ($invoice3 > 0) {
                    $formattedInvoice3 = number_format($invoice3);
                    // 안전한 배열 접근을 위해 isset() 체크 추가
                    if (isset($statusIds[WorkStatus::LINK_REPAIR_OS_REINSTALL])) {
                        $this->logger->addLog($product, 'App\Models\RepairProduct', $product->id, $statusIds[WorkStatus::LINK_REPAIR_OS_REINSTALL], $user->id, "[$product->qaid]$product->name<br>OS 재설치비: $formattedInvoice3 원");
                    }
                    $memo .= "OS설치 : $formattedInvoice3 원";
                }
            }

            // 새 수리 등록 (바로 complete 처리)
            if ($repairProduct === null) {
                foreach ($statusIds as $statusId) {
                    $this->logger->addLog($product, 'App\Models\Product', $product->id, $statusId, $user->id);
                }

                // 수리내역 저장
                $data['invoice1'] = $invoice1;
                $data['invoice3'] = $invoice3;
                $data['memo'] = $memo;
                $data['repair_cost_info'] = $repairCostInfo;

                $repairProduct = $this->createRepairProduct($product, $data);

                // 카운터 업데이트
                $counters[$product->req_id] = [
                    'checked' => -1,
                    'repaired' => 1,
                ];
            } else {
                // 기존 대기중인 수리내역 완료 처리
                if ($repairProduct->status === RepairProduct::STATUS_REPAIRED) {
                    throw new Exception('이미 수리/점검 완료된 상품입니다. 확인 부탁드립니다.');
                }

                if (isset($statusIds[WorkStatus::LINK_REPAIR_PROCESS5])) {
                    $this->logger->addLog($product, 'App\Models\Product', $product->id, $statusIds[WorkStatus::LINK_REPAIR_PROCESS5], $user->id, $memo);
                }
                if (isset($statusIds[WorkStatus::LINK_REPAIR_COMPLETE])) {
                    $this->logger->addLog($product, 'App\Models\Product', $product->id, $statusIds[WorkStatus::LINK_REPAIR_COMPLETE], $user->id, '수리/점검 완료');
                }

                // 수리 제품 정보 업데이트
                $repairProduct->update([
                    'status' => RepairProduct::STATUS_REPAIRED,
                    'completed_user_id' => $user->id,
                    'completed_at' => $now,
                    'repair_symptom_id' => $this->findSymptomIdByCode($data['symptom_code']),
                    'repair_process_id' => $this->findProcessIdByCode($data['process_code']),
                    'repair_grade_id' => $this->findGradeIdByCode($data['grade_code']),
                    'invoice1' => $invoice1,
                    'invoice3' => $invoice3,
                    'repair_cost_id' => $repairCostInfo['repair_cost_id'],
                    'calculation_basis' => $repairCostInfo['calculation_basis'],
                    'calculation_details' => $repairCostInfo['calculation_details'] ?? null,
                    'memo' => $memo,
                ]);

                // 카운터 업데이트
                $counters[$product->req_id] = [
                    'waiting' => -1,
                    'repaired' => 1,
                ];
            }

            // 점검/수리 완료 로그
            if (isset($statusIds[WorkStatus::LINK_REPAIR_COMPLETE])) {
                $this->logger->addLog($product, 'App\Models\RepairProduct', $repairProduct->id, $statusIds[WorkStatus::LINK_REPAIR_COMPLETE], $user->id, '점검/수리 완료');
            }

            // 구성품 총 비용(invoice2) 처리
            if (! $isStXlGrade) {
                $this->updateRepairProductParts($repairProduct, $data, $user, $this->workStatusService, $this->logger);

                $invoice2 = $repairProduct->parts_total_cost;

                // invoice2 업데이트 및 로그 기록
                if ($invoice2 > 0) {
                    $repairProduct->update(['invoice2' => $invoice2]);

                    $formattedInvoice2 = number_format($invoice2);
                    $this->logger->addLog(
                        $product,
                        'App\Models\RepairProductParts',
                        $repairProduct->id,
                        $statusIds[WorkStatus::LINK_REPAIR_ADD_PARTS],
                        $user->id,
                        "[$product->qaid]$product->name<br>구성품 총 비용: $formattedInvoice2 원"
                    );
                }
            }

            $this->countService->multipleUpdate($counters);
            $this->logger->save();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            SimpleLogService::error('product', '수리/점검 저장 실패', [
                'product_id' => $product->id,
                'qaid' => $product->qaid,
                'data' => $data,
            ], $e);
            throw $e;
        }
    }

    /**
     * 새로운 수리비 시스템을 사용한 OS 재설치비 계산 (invoice3)
     */
    public function calculateInvoice3(Product $product, array $data): int
    {
        try {
            if ($data['os_reinstall']) {
                // RepairCostService를 통한 OS 재설치비 계산
                $amount = $this->repairCostService->calculateOsInstallCost($product);

                if ($amount === null) {
                    SimpleLogService::warning('repair', '새로운 OS 재설치비 계산 실패, 기존 시스템으로 폴백', [
                        'product_id' => $product->id,
                        'qaid' => $product->qaid,
                    ]);
                }

                return $amount ?? 0;
            }

            return 0;
        } catch (Exception $e) {
            SimpleLogService::error('repair', '새로운 OS 재설치비 계산 실패', [
                'product_id' => $product->id,
                'qaid' => $product->qaid,
                'error' => $e->getMessage(),
            ], $e);

            return 0;
        }
    }

    /**
     * 수리비 계산을 한 번만 수행하고 결과를 반환합니다.
     */
    public function calculateRepairCostOnce(Product $product, array $data): array
    {
        try {
            // 모니터 제품인 경우 monitorSizeLookup 관계 미리 로드
            if ($this->monitorSizeService->isMonitorProduct($product) && ! $product->relationLoaded('monitorSizeLookup')) {
                $product->load('monitorSizeLookup');
            }

            // 프로세스 코드로 프로세스 ID 찾기
            $processId = $this->findProcessIdByCode($data['process_code']);
            if (! $processId) {
                SimpleLogService::critical('repair', '프로세스 코드에 해당하는 프로세스 ID를 찾을 수 없음', [
                    'product_id' => $product->id,
                    'process_code' => $data['process_code'] ?? 'unknown',
                ]);

                return ['invoice1' => 0, 'repair_cost_info' => ['repair_cost_id' => null, 'calculation_basis' => '계산 실패', 'calculation_details' => null]];
            }

            // RepairCostService를 통한 수리비 계산
            $costResult = $this->repairCostService->calculateRepairCost($product, $processId);

            return [
                'invoice1' => $costResult['amount'] ?? 0,
                'repair_cost_info' => [
                    'repair_cost_id' => $costResult['details']['cost_id'] ?? null,
                    'calculation_basis' => $costResult['basis'] ?? '계산 실패',
                    'calculation_details' => $costResult['details'] ?? null,
                ],
            ];
        } catch (Exception $e) {
            SimpleLogService::emergency('repair', '새로운 수리비 계산 실패 (once)', [
                'product_id' => $product->id,
                'process_code' => $data['process_code'] ?? 'unknown',
                'error' => $e->getMessage(),
            ], $e);

            return ['invoice1' => 0, 'repair_cost_info' => ['repair_cost_id' => null, 'calculation_basis' => '계산 실패', 'calculation_details' => null]];
        }
    }

    // ========================================
    // 기존 메서드들 (하위 호환성 유지)
    // ========================================

    /**
     * 동적 WorkStatus 시스템을 사용하여 상태 ID 목록 생성
     *
     * @throws Exception
     */
    private function getStatusIds(array $data): array
    {
        try {
            /**
             * 로그를 찍기 위해 $data['log_mode']를 사용<br>
             * 점검/수리 완료(repair_complete), 대기(repair_waiting),
             * 출고 등록(pallet_product_registered), 출고 적재(pallet_product_inspect)
             */
            $isParts = is_array($data['add_parts']) && ! empty($data['add_parts']);

            return $this->workStatusService->getStatusIds([
                'symptom_code' => $data['symptom_code'],
                'process_code' => $data['process_code'],
                'grade_code' => $data['grade_code'],
                'os_reinstall' => ! empty($data['os_reinstall']),
                'add_cost' => ! empty($data['add_cost']),
                'add_parts' => $isParts,
                'log_mode' => $data['log_mode'],
            ]);
        } catch (Exception $e) {
            SimpleLogService::error('work_status', '동적 WorkStatus 생성 실패', [
                'data' => $data,
            ], $e);

            // 폴백: 기존 WorkStatusService 사용
            return $this->workStatusService->getStatusIds($data);
        }
    }

    // ========================================
    // 비공개 메서드 (Private Methods)
    // ========================================

    /**
     * 수리 내역 저장
     */
    private function createRepairProduct(Product $product, array $data): RepairProduct
    {
        $repairProduct = new RepairProduct;

        $repairProduct->product_id = $product->id;
        $repairProduct->status = $data['status'];
        if ($data['status'] === RepairProduct::STATUS_REPAIRED) {
            $repairProduct->completed_user_id = $data['user_id'];
            $repairProduct->completed_at = $data['now'];
        } else {
            $repairProduct->waiting_user_id = $data['user_id'];
            $repairProduct->waiting_at = $data['now'];
        }
        $repairProduct->amount = $product->amount;
        $repairProduct->repair_symptom_id = $this->findSymptomIdByCode($data['symptom_code']);
        $repairProduct->repair_process_id = $this->findProcessIdByCode($data['process_code']);
        $repairProduct->repair_grade_id = $this->findGradeIdByCode($data['grade_code']);
        $repairProduct->invoice1 = $data['invoice1'];
        $repairProduct->invoice3 = $data['invoice3'];
        $repairProduct->is_os_install = $data['os_reinstall'] === true ? 1 : 0;
        $repairProduct->memo = $data['memo'];

        // 수리비 정보 저장 - 새로운 시스템 (repair_cost_id 사용)
        if (isset($data['repair_cost_info'])) {
            $repairProduct->repair_cost_id = $data['repair_cost_info']['repair_cost_id'] ?? null;
            $repairProduct->calculation_basis = $data['repair_cost_info']['calculation_basis'] ?? null;
            $repairProduct->calculation_details = $data['repair_cost_info']['calculation_details'] ?? null;
        } else {
            // 수리비 정보가 없는 경우 기본값 설정
            $repairProduct->repair_cost_id = null;
            $repairProduct->calculation_basis = null;
            $repairProduct->calculation_details = null;
        }

        $repairProduct->save();

        return $repairProduct;
    }
}
