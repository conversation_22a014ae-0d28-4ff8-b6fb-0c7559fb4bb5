<?php

namespace App\Services;

use App\Exceptions\ProductException;
use App\Models\CarryoutProduct;
use App\Models\Pallet;
use App\Models\PalletProduct;
use App\Models\Product;
use App\Models\ProductBarcode;
use App\Models\ProductLog;
use App\Models\ProductVendor;
use App\Models\Req;
use App\Models\User;
use App\Models\WorkStatus;
use App\Traits\Product\SearchTrait as ProductSearchTrait;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Throwable;

class ProductService
{
    use ProductSearchTrait;

    protected CountService $countService;

    protected WorkStatusService $workStatusService;

    public function __construct(
        CountService $countService,
        WorkStatusService $workStatusService
    ) {
        $this->countService = $countService;
        $this->workStatusService = $workStatusService;
    }

    /**
     * 벤더 등록
     */
    private function createOrGetVendor(array $data): ProductVendor
    {
        return ProductVendor::firstOrCreate(['name' => $data['vendor']]);
    }

    /**
     * 바코드 등록
     */
    private function createOrGetBarcode(array $data): ProductBarcode
    {
        return ProductBarcode::firstOrCreate(
            ['barcode' => $data['barcode']],
            [
                'wms_sku_id' => '-',
                'external_wms_sku_id' => '-',
            ]
        );
    }

    /**
     * QAID를 이용해 상품의 정보를 가져옵니다.
     */
    public function findByQaid(string $qaid): Product
    {
        return Product::with([
            'returnReasonB',
            'returnReasonM',
        ])
            ->without([
                'lot',
                'vendor',
                'palletProducts',
                'carryoutProducts',
            ])
            ->leftJoin('reqs', 'products.req_id', '=', 'reqs.id')
            ->where('qaid', $qaid)
            ->select('products.*')
            ->orderByDesc('reqs.req_at')
            ->first();
    }

    /**
     * 상품의 QAID를 이용해 상품의 정보를 가져옵니다.(삭제된 상품 제외)
     *
     * @param  string  $qaid  상품의 QAID (유일한 값)
     * @return Builder|Model 요청된 상품의 정보를 반환합니다. 상품이 존재하지 않는 경우 null을 반환합니다.
     */
    public function getProductByQaid(string $qaid): Model|Builder
    {
        return Product::where('qaid', $qaid)
            ->where('status', '!=', Product::STATUS_DELETED)
            ->first();
    }

    /**
     * 요청서 인덱스와 QAID를 이용해 상품의 정보를 가져옵니다.
     *
     * @param  int  $reqId  요청서 인덱스
     * @param  string  $qaid  상품의 QAID (유일한 값)
     * @return Product|null 요청된 상품의 정보를 반환합니다. 상품이 존재하지 않는 경우 null을 반환합니다.
     */
    public function getProductByReqIdAndQaid(int $reqId, string $qaid): ?Product
    {
        return Product::where('req_id', $reqId)
            ->where('qaid', $qaid)
            ->first();
    }

    /**
     * 미등록 상품 등록<br>
     * unlinked 카운터만 1 증가
     *
     * @throws Throwable
     */
    public function storeUnlinkedProduct(array $data, User $user): void
    {
        try {
            DB::beginTransaction();

            $vendor = $this->createOrGetVendor($data);

            $now = now();
            $product = Product::create([
                'req_id' => Req::UNLINKED_ID,
                'qaid' => $data['qaid'],
                'barcode' => $data['barcode'],
                'name' => $data['name'],
                'cate4_id' => $data['cate4'],
                'cate5_id' => $data['cate5'],
                'quantity' => 1,
                'amount' => $data['amount'],
                'user_id' => $user->id,
                'status' => Product::STATUS_REGISTERED,
                'rg' => 'N',
                'duplicated' => 'N',
                'product_lot_id' => Req::UNLINKED_LOT_ID, // 미등록 상품은 1번 고정
                'product_vendor_id' => $vendor->id,
                'checked_at' => $now,
                'checked_status' => Product::CHECKED_STATUS_CHECKED,
                'checked_user_id' => $user->id,
                'memo' => $data['memo'] ?? null,
            ]);

            $countData = [
                'unlinked' => 1,
            ];
            // 미등록 상품 카운터 업데이트
            $countService = new CountService;
            $countService->update(Req::UNLINKED_ID, $countData);

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_PRODUCT_UNLINKED_CREATE, // 미등록 상품 등록
                WorkStatus::LINK_INSPECTION_COMPLETE, // 검수완료
            ]);

            $insertData = [
                [
                    'product_id' => $product->id,
                    'model_type' => 'App\Models\Product',
                    'model_id' => $product->id,
                    'work_status_id' => $statusIds[WorkStatus::LINK_PRODUCT_UNLINKED_CREATE],
                    'user_id' => $user->id,
                    'memo' => '미등록 상품이 등록되었습니다.',
                    'created_at' => $now,
                    'updated_at' => $now,
                ],
                [
                    'product_id' => $product->id,
                    'model_type' => 'App\Models\Product',
                    'model_id' => $product->id,
                    'work_status_id' => $statusIds[WorkStatus::LINK_INSPECTION_COMPLETE],
                    'user_id' => $user->id,
                    'memo' => '미등록 상품 검수 완료',
                    'created_at' => $now,
                    'updated_at' => $now,
                ],
            ];

            // 상태 변환/통계::미등록상품 수정
            ProductLog::insert($insertData);

            DB::commit();

            SimpleLogService::info('unlinked', '[미등록 상품]등록 성공', [
                'product_id' => $product->id,
                'qaid' => $product->qaid,
                'barcode' => $product->barcode,
                'product_name' => $product->name,
                'vendor' => $vendor->name,
            ]);
        } catch (Exception $e) {
            // 에러 발생 시 롤백
            DB::rollBack();

            SimpleLogService::error('product', '[미등록 상품]등록 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 미등록 상품의 정보를 수정(업데이트)<br>
     * 미등록 상품 수정시 카운터는 수정되지 않음
     *
     * @throws Throwable
     */
    public function updateUnlinkedProduct(array $data, User $user): void
    {
        try {
            DB::beginTransaction();

            $vendor = $this->createOrGetVendor($data);

            $oldQaid = null;
            $product = Product::find($data['id']);

            if ($product->qaid !== $data['qaid']) {
                $oldQaid = $product->qaid;
            }

            $product->update([
                'qaid' => $data['qaid'],
                'barcode' => $data['barcode'],
                'name' => $data['name'],
                'cate4_id' => $data['cate4'],
                'cate5_id' => $data['cate5'],
                'amount' => $data['amount'],
                'user_id' => $user->id,
                'status' => $data['status'],
                'product_vendor_id' => $vendor->id,
                'memo' => $data['memo'] ?? null,
            ]);

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_PRODUCT_UNLINKED_UPDATE, // 미등록 상품 수정
            ]);

            // 상태 변환/통계::미등록상품 수정 및 검수완료
            $now = now();
            ProductLog::insert([
                'product_id' => $product->id,
                'model_type' => 'App\Models\Product',
                'model_id' => $product->id,
                'work_status_id' => $statusIds[WorkStatus::LINK_PRODUCT_UNLINKED_UPDATE],
                'user_id' => $user->id,
                'memo' => '미등록 상품의 정보가 수정되었습니다.',
                'created_at' => $now,
                'updated_at' => $now,
            ]);

            DB::commit();

            SimpleLogService::info('unlinked', '[미등록 상품]수정 성공', [
                'product_id' => $product->id,
                'qaid' => $product->qaid,
                'barcode' => $product->barcode,
                'product_name' => $product->name,
                'vendor' => $vendor->name,
            ]);
        } catch (Exception $e) {
            // 에러 발생 시 롤백
            DB::rollBack();

            SimpleLogService::error('product', '[미등록 상품]수정 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 상품의 검수상태 업데이트(검수대기 -> 검수완료)
     *
     * @throws Exception|Throwable
     */
    public function checkedStatusChecked(array $data, User $user): Model|Builder|Product
    {
        try {
            DB::beginTransaction();

            $product = Product::where('req_id', $data['reqId'])
                ->where('qaid', $data['qaid'])
                ->where('checked_status', Product::CHECKED_STATUS_UNCHECKED)
                ->where('status', Product::STATUS_REGISTERED)
                ->first();

            if ($product === null) {
                throw ProductException::notFound($data['qaid']);
            }

            if ($product->duplicated === Product::IS_DUPLICATED_Y) {
                throw ProductException::duplicated($product->qaid);
            }

            // Req에 카운터 저장
            $countData = [
                'unchecked' => -1,
                'checked' => 1,
            ];
            $countService = new CountService;
            $reqCount = $countService->update($data['reqId'], $countData);

            $now = now();
            $product->checked_at = $now;
            $product->checked_status = Product::CHECKED_STATUS_CHECKED;
            $product->checked_user_id = $user->id;
            $product->save();

            // 검수대기 상품이 없다면 Req의 상태를 [등록 -> 완료] 상태로 변경
            if ($reqCount->checked === 1) {
                $product->req->update([
                    'status' => Req::STATUS_CHECKED,
                    'checked_user_id' => $user->id,
                    'checked_at' => $now,
                ]);
            }

            // 페이지에 맞는 상태 id를 가져옴
            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_INSPECTION_COMPLETE, // 검수완료
            ]);

            // 상태 변환/통계::검수완료
            ProductLog::insert([
                'product_id' => $product->id,
                'model_type' => 'App\Models\Product',
                'model_id' => $product->id,
                'work_status_id' => $statusIds[WorkStatus::LINK_INSPECTION_COMPLETE],
                'user_id' => $user->id,
                'memo' => null,
                'created_at' => $now,
                'updated_at' => $now,
            ]);

            DB::commit();

            SimpleLogService::info('product', '상품 QAID ['.$product->qaid.']: 검수대기 -> 검수완료 상태 업데이트 성공');

            return $product;
        } catch (Exception $e) {
            DB::rollBack();

            // 원본 메시지: 쿠팡PL팔레트 출고에 실패하였습니다.
            $subject = "상품 검수대기 -> 검수완료 상태 업데이트 실패[req_id: {$data['reqId']}, qaid: {$data['qaid']}, user: $user->username($user->name)]";

            SimpleLogService::error('product', $subject, [], $e);

            throw $e;
        }
    }

    /**
     * 체크된 상품 삭제: 각 상태에 따른 카운터 업데이트
     */
    private function updateDeleteCounter(Product $product, array $counter): array
    {
        $reqId = $product->req_id;

        $counter[$reqId]['deleted']++;

        if ($reqId === Req::UNLINKED_ID) {
            $counter[$reqId]['unlinked']--;
        } else {
            $productStatus = $product->status;
            $productCheckedStatus = $product->checked_status;

            $palletProduct = $product->palletProducts->first();
            if ($palletProduct !== null) {
                $palletStatus = $palletProduct->pallet->status;
                $palletProductStatus = $palletProduct->status;
            } else {
                $palletStatus = null;
                $palletProductStatus = null;
            }

            // 입고검수대기, 검수완료, 점검완료, 출고대기
            if ($productStatus === Product::STATUS_REGISTERED
                && $productCheckedStatus === Product::CHECKED_STATUS_UNCHECKED) {
                // CountService::getUncheckedCount() 에서 참고
                $counter[$reqId]['unchecked']--;
            } elseif ($productStatus === Product::STATUS_REGISTERED
                && $productCheckedStatus === Product::CHECKED_STATUS_CHECKED) {
                // CountService::getCheckedCount() 에서 참고
                $counter[$reqId]['checked']--;
            } elseif ($productStatus === Product::STATUS_CHECKED_ON_PALLET
                && ($palletProduct !== null
                    && $palletProductStatus === PalletProduct::STATUS_REGISTERED
                    && $palletStatus === Pallet::STATUS_LOADED)) {
                // CountService::getCheckoutCount() 에서 참고
                $counter[$reqId]['checkout']--;
            } elseif ($productStatus === Product::STATUS_CHECKED_ON_PALLET
                && ($palletProduct !== null
                    && $palletProductStatus === PalletProduct::STATUS_REGISTERED
                    && $palletStatus === Pallet::STATUS_CLOSED)) {
                // CountService::getExportingCount() 에서 참고
                $counter[$reqId]['exporting']--;
            }

            // 중복상품일 경우
            if ($product->duplicated === Product::IS_DUPLICATED_Y) {
                $counter[$reqId]['duplicated']--;
            }
        }

        return $counter;
    }

    /**
     * 삭제 체크된 상품들의 상태를 삭제로 변경(실제 삭제하는것은 아님)
     *
     * @throws Throwable
     */
    public function destroyProducts(array $ids, User $user): void
    {
        // 페이지에 맞는 상태 id를 가져옴
        $statusIds = $this->workStatusService->getIds([
            WorkStatus::LINK_PRODUCT_DELETE, // 상품 삭제
        ]);

        try {
            DB::beginTransaction();

            $now = now();

            $countService = new CountService;
            $counters = []; // 각종 카운터 저장용

            foreach ($ids as $id) {
                $product = $this->findProductById($id);

                $productStatus = $product->status;
                if ($productStatus === Product::STATUS_REGISTERED ||
                    $productStatus === Product::STATUS_CHECKED_ON_PALLET) {
                    $counters = $countService->initCounter($product->req_id, $counters);
                    $counters = $this->updateDeleteCounter($product, $counters);

                    // 삭제결과 저장
                    $product->status = Product::STATUS_DELETED;
                    $product->checked_user_id = $user->id;
                    $product->checked_at = $now;
                    $product->checked_status = Product::CHECKED_STATUS_CHECKED;
                    $product->save();

                    // 상태 변환/통계::상품 삭제
                    ProductLog::insert([
                        'product_id' => $product->id,
                        'model_type' => 'App\Models\Product',
                        'model_id' => $product->id,
                        'work_status_id' => $statusIds[WorkStatus::LINK_PRODUCT_DELETE],
                        'user_id' => $user->id,
                        'memo' => '상품이 삭제되었습니다.',
                        'created_at' => $now,
                        'updated_at' => $now,
                    ]);

                    // 팔레트에도 상품이 있었다면 삭제처리 해 준다.
                    $palletProduct = $product->palletProducts()->first();
                    if ($palletProduct !== null) {
                        $palletProduct->update([
                            'status' => PalletProduct::STATUS_DELETED,
                        ]);

                        // 상태 변환/통계::상품 삭제(팔레트)
                        ProductLog::insert([
                            'product_id' => $product->id,
                            'model_type' => 'App\Models\PalletProduct',
                            'model_id' => $palletProduct->id,
                            'work_status_id' => $statusIds[WorkStatus::LINK_PRODUCT_DELETE],
                            'user_id' => $user->id,
                            'memo' => '상품이 삭제되면서 팔레트에 들어있던 상품이 삭제되었습니다.',
                            'created_at' => $now,
                            'updated_at' => $now,
                        ]);
                    }

                    // 외주 상품에서도 취소처리 해 준다.
                    $carryoutProduct = $product->carryoutProducts()->first();
                    if ($carryoutProduct !== null) {
                        $carryoutProduct->update([
                            'status' => CarryoutProduct::STATUS_CANCELED,
                        ]);

                        // 상태 변환/통계::상품 삭제(외주)
                        ProductLog::insert([
                            'product_id' => $product->id,
                            'model_type' => 'App\Models\CarryoutProduct',
                            'model_id' => $carryoutProduct->id,
                            'work_status_id' => $statusIds[WorkStatus::LINK_PRODUCT_DELETE],
                            'user_id' => $user->id,
                            'memo' => '상품이 삭제되면서 외주가 취소되었습니다.',
                            'created_at' => $now,
                            'updated_at' => $now,
                        ]);
                    }

                    SimpleLogService::notice('product', '상품 삭제(상태) 성공', ['product' => $product]);
                } else {
                    SimpleLogService::notice('product', '삭제할 수 없는 상품', [
                        'product_id' => $product->id,
                        'qaid' => $product->qaid,
                        'status' => $product->status,
                        '$this->checked_status' => $product->checked_status,
                        'user' => $user->username.'('.$user->name.')',
                    ]);

                    throw new Exception("등록된 점검대상 상품[$product->qaid]의 삭제에 실패하였습니다.");
                }
            }

            // 카운트 저장
            $countService->multipleUpdate($counters);

            DB::commit();
        } catch (Exception $e) {
            // 에러 발생 시 롤백
            DB::rollBack();

            SimpleLogService::error('product', '상품 삭제 실패', [], $e);

            throw $e;
        }

    }

    /**
     * 미등록 상품, 고스트 상품(회사에서 자체 보유하고 있는 상품)을 제외하고 중복된 상품을 찾아서 duplicated를 Y로 변경
     *
     * @throws Throwable
     */
    public function markDuplicateProducts(): void
    {
        try {
            DB::beginTransaction();

            // 중복된 상품을 찾아서 duplicated를 Y로 변경
            DB::table('products')
                ->where('duplicated', Product::IS_DUPLICATED_N)
                ->whereIn('id', function ($query) {
                    $query->select('p.id')
                        ->from('products as p')
                        ->whereNotIn('p.req_id', [Req::UNLINKED_ID, Req::GHOST_ID])
                        ->whereIn('p.qaid', function ($subQuery) {
                            $subQuery->select('qaid')
                                ->from('products as inner_p')
                                ->where('inner_p.status', '!=', Product::STATUS_DELETED)
                                ->groupBy('inner_p.qaid')
                                ->havingRaw('COUNT(*) > 1');
                        });
                })
                ->update(['duplicated' => Product::IS_DUPLICATED_Y]);

            // 반대로 중복이 아닌 상품을 찾아서 duplicated를 N으로 변경
            DB::table('products')
                ->where('duplicated', Product::IS_DUPLICATED_Y)
                ->whereNotIn('id', function ($query) {
                    $query->select('p.id')
                        ->from('products as p')
                        ->whereNotIn('p.req_id', [Req::UNLINKED_ID, Req::GHOST_ID])
                        ->whereIn('p.qaid', function ($subQuery) {
                            $subQuery->select('qaid')
                                ->from('products as inner_p')
                                ->where('inner_p.status', '!=', Product::STATUS_DELETED)
                                ->groupBy('inner_p.qaid')
                                ->havingRaw('COUNT(*) > 1');
                        });
                })
                ->update(['duplicated' => Product::IS_DUPLICATED_N]);

            DB::commit();

            SimpleLogService::info('product', '[스케줄러-중복상품 처리]완료');
        } catch (Throwable $e) {
            // 에러 발생 시 롤백
            DB::rollBack();

            SimpleLogService::error('product', '[스케줄러-중복상품 처리]실패', [], $e);

            throw new Exception('[스케줄러-중복상품 처리]실패: '.$e->getMessage());
        }
    }
}
