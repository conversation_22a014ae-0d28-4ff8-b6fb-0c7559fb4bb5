<?php

namespace App\Services;

use App\Models\Pallet;
use App\Models\PalletProduct;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Facades\DB;

class PalletQueryBuilderService
{
    /**
     * 팔레트 리스트
     */
    public function getPalletList(array $data): QueryBuilder|EloquentBuilder
    {
        // repair_grade_id가 null일 때만 서브쿼리 실행
        $gradeNameSubQuery = 'CASE
                WHEN pallets.repair_grade_id IS NOT NULL THEN
                    (SELECT name FROM repair_grades WHERE id = pallets.repair_grade_id)
                ELSE NULL
            END';
        $productCountSubQuery = 'SELECT COUNT(id) FROM pallet_products WHERE pallet_id = pallets.id';
        $productAmountSubQuery = 'SELECT SUM(amount) FROM pallet_products WHERE pallet_id = pallets.id';

        $builder = Pallet::select([
            'pallets.*',
            DB::raw("($gradeNameSubQuery) AS grade_name"),
            DB::raw("($productCountSubQuery) AS product_count"),
            DB::raw("($productAmountSubQuery) AS product_amount"),
        ])->leftJoin('locations', 'locations.id', '=', 'pallets.location_id');

        if (empty($data['status'])) {
            $builder->where('status', '!=', Pallet::STATUS_EXPORTED);
        } else {
            $builder->where('status', $data['status']);
        }

        if ($data['keyword']) {
            $builder->where('locations.name', 'like', '%'.$data['keyword'].'%');
        }

        if ($data['beginAt'] && $data['endAt']) {
            $builder->whereBetween('pallets.checked_at', [$data['beginAt'], $data['endAt']]);
        }

        if ($data['exported_at']) {
            $builder->where('pallets.exported_at', $data['exported_at']);
        }

        $builder->orderBy('pallets.checked_at', 'desc')
            ->orderBy('pallets.registered_at', 'desc');

        return $builder;
    }

    /**
     * 팔레트 상품 리스트
     */
    public function getPalletProductList(array $data): QueryBuilder|EloquentBuilder
    {
        $builder = PalletProduct::with('product')
            ->where('pallet_products.pallet_id', $data['pallet_id']);

        $checkedStatus = (int) $data['checked_status'] ?? null;
        if ($checkedStatus) {
            $builder->where('pallet_products.checked_status', $checkedStatus);
        }

        if ($data['keyword']) {
            if ($data['search_type'] === 'qaid') {
                $builder->leftJoin('products', 'products.id', '=', 'pallet_products.product_id')
                    ->where(function (EloquentBuilder $query) use ($data) {
                        $query->where('products.qaid', $data['keyword'])
                            ->orWhere('products.barcode', $data['keyword'])
                            ->orWhere('products.name', 'like', '%'.$data['keyword'].'%');
                    });
            } else {
                $builder->leftJoin('users', 'users.id', '=', 'pallet_products.registered_user_id')
                    ->where('users.name', 'like', '%'.$data['keyword'].'%');
            }
        }

        $builder->orderBy('pallet_products.registered_at', 'desc');

        return $builder;
    }
}
