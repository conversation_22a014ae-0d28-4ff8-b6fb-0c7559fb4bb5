<?php

namespace App\Services;

use App\Models\RepairParts;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Throwable;

class RepairPartsService
{
    public function __construct() {}

    public function getPartsList(array $data = []): Builder
    {
        $query = RepairParts::query();

        // 카테고리 필터링
        if (! empty($data['keyword'])) {
            $query->where('barcode', $data['keyword'])
                ->orWhere('name', 'like', '%'.$data['keyword'].'%');
        }

        // 정렬
        return $query->orderByDesc('repair_parts.acc_count')
            ->orderBy('repair_parts.name');
    }

    /**
     * @throws Throwable
     */
    public function storeParts(array $data): void
    {
        DB::beginTransaction();

        try {
            RepairParts::create([
                'category_id' => $data['category_id'],
                'barcode' => $data['barcode'] ?? null,
                'name' => $data['name'],
                'model_number' => $data['model_number'] ?? null,
                'price' => $data['price'] ?? 0,
                'stock' => $data['stock'] ?? 0,
                'reorder_stock' => $data['reorder_stock'] ?? 10,
                'is_purchasable' => 'Y',
                'memo' => $data['memo'] ?? null,
                'location_area' => $data['location_area'] ?? null,
                'location_zone' => $data['location_zone'] ?? null,
                'location_floor' => $data['location_floor'] ?? 1,
                'location_position' => $data['location_position'] ?? 1,
            ]);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }
    }

    /**
     * @throws Throwable
     */
    public function updateParts(array $data): void
    {
        DB::beginTransaction();
        try {
            RepairParts::where('id', $data['id'])->update([
                'category_id' => $data['category_id'] ?? null,
                'barcode' => $data['barcode'] ?? null,
                'name' => $data['name'],
                'model_number' => $data['model_number'] ?? null,
                'price' => $data['price'] ?? 0,
                'stock' => $data['stock'] ?? 0,
                'reorder_stock' => $data['reorder_stock'] ?? 10,
                'is_purchasable' => $data['is_purchasable'] ?? 'Y',
                'location_area' => $data['location_area'] ?? null,
                'location_zone' => $data['location_zone'] ?? null,
                'location_floor' => $data['location_floor'] ?? 1,
                'location_position' => $data['location_position'] ?? 1,
                'memo' => $data['memo'] ?? null,
            ]);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }
    }

    /**
     * @throws Throwable
     */
    public function destroyParts(int $id): void
    {
        DB::beginTransaction();
        try {
            RepairParts::destroy($id);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            throw $e;
        }
    }
}
