<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\NotificationRecipient;
use App\Models\NotificationGroup;
use App\Models\User;
use App\Events\NotificationSent;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Exceptions\ValidationException;
use App\Exceptions\ResourceNotFoundException;
use Throwable;

/**
 * 알림 전송 서비스
 *
 * 알림 생성, 수신자 목록 조회, 수신자 정보 저장을 담당합니다.
 */
class NotificationSendService
{
    /**
     * 알림 전송
     *
     * @param array $data 알림 데이터
     * @return array 전송 결과
     * @throws ValidationException 유효성 검증 실패 시
     * @throws ResourceNotFoundException|Throwable 리소스를 찾을 수 없을 때
     */
    public function sendNotification(array $data): array
    {
        // 입력 데이터 유효성 검증
        $this->validateNotificationData($data);

        return DB::transaction(function () use ($data) {
            // 1. 대상 타입별 수신자 목록 조회 (유효성 검증 포함)
            $recipients = $this->getRecipients($data['target_type'], $data['target_ids'] ?? []);

            // 2. 알림 생성 및 저장
            $notification = $this->createNotification($data, $recipients);

            // 3. 수신자 정보 저장
            $this->createRecipients($notification->id, $recipients);

            // 4. SSE 브로드캐스트를 위한 이벤트 발생
            event(new NotificationSent($notification, $recipients));

            Log::info('알림 전송 완료', [
                'notification_id' => $notification->id,
                'sender_id' => $data['sender_id'],
                'target_type' => $data['target_type'],
                'recipients_count' => count($recipients)
            ]);

            return [
                'notification_id' => $notification->id,
                'recipients_count' => count($recipients),
                'target_type' => $data['target_type'],
                'sent_at' => $notification->sent_at
            ];
        });
    }

    /**
     * 알림 데이터 유효성 검증
     *
     * @param array $data 검증할 데이터
     * @throws ValidationException 유효성 검증 실패 시
     */
    private function validateNotificationData(array $data): void
    {
        // 필수 필드 검증
        if (empty($data['title'])) {
            throw new ValidationException('알림 제목은 필수입니다.');
        }

        if (empty($data['content'])) {
            throw new ValidationException('알림 내용은 필수입니다.');
        }

        if (empty($data['sender_id'])) {
            throw new ValidationException('발신자 정보는 필수입니다.');
        }

        if (empty($data['target_type'])) {
            throw new ValidationException('대상 타입은 필수입니다.');
        }

        // 대상 타입 검증
        $allowedTargetTypes = ['all', 'group', 'individual'];
        if (!in_array($data['target_type'], $allowedTargetTypes)) {
            throw new ValidationException('유효하지 않은 대상 타입입니다.');
        }

        // 그룹 또는 개별 대상일 때 target_ids 검증
        if (in_array($data['target_type'], ['group', 'individual']) && empty($data['target_ids'])) {
            throw new ValidationException('그룹 또는 개별 대상 선택 시 대상 ID는 필수입니다.');
        }
    }

    /**
     * 알림 생성 및 저장
     *
     * @param array $data 알림 데이터
     * @param array $recipients 수신자 목록
     * @return Notification 생성된 알림
     */
    private function createNotification(array $data, array $recipients): Notification
    {
        // 타입별 target_id 설정
        $targetId = null;
        if ($data['target_type'] === 'group' && !empty($data['target_ids'])) {
            $targetId = $data['target_ids'][0]; // 그룹 ID 사용
        } elseif ($data['target_type'] === 'individual' && !empty($recipients)) {
            $targetId = $recipients[0]; // 첫 번째 개인 직원 ID 사용
        }

        return Notification::create([
            'title' => $data['title'],
            'content' => $data['content'],
            'priority' => $data['priority'] ?? 'normal',
            'action_url' => $data['action_url'] ?? null,
            'sender_id' => $data['sender_id'],
            'target_type' => $data['target_type'],
            'target_id' => $targetId,
            'status' => 'sent',
            'sent_at' => now()
        ]);
    }

    /**
     * 대상 타입별 수신자 목록 조회
     *
     * @param string $targetType 대상 타입 (all, group, individual)
     * @param array $targetIds 대상 ID 배열
     * @return array 수신자 ID 배열
     * @throws ResourceNotFoundException 리소스를 찾을 수 없을 때
     */
    private function getRecipients(string $targetType, array $targetIds): array
    {
        return match ($targetType) {
            'all' => $this->getAllActiveUsers(),
            'group' => $this->getGroupMembers($targetIds),
            'individual' => $this->getIndividualUsers($targetIds),
            default => [],
        };
    }

    /**
     * 모든 활성 사용자 조회
     *
     * @return array 활성 사용자 ID 배열
     */
    private function getAllActiveUsers(): array
    {
        return User::where('status', User::MEMBER_STATUS_ACTIVE)
            ->pluck('id')
            ->toArray();
    }

    /**
     * 그룹 멤버 조회
     *
     * @param array $groupIds 그룹 ID 배열
     * @return array 그룹 멤버 ID 배열
     * @throws ResourceNotFoundException 그룹을 찾을 수 없을 때
     */
    private function getGroupMembers(array $groupIds): array
    {
        $userIds = [];

        foreach ($groupIds as $groupId) {
            $group = NotificationGroup::where('id', $groupId)
                ->where('is_active', true)
                ->first();

            if (!$group) {
                throw new ResourceNotFoundException("그룹을 찾을 수 없습니다. (ID: {$groupId})");
            }

            // 그룹의 활성 멤버 조회
            $groupUserIds = $group->members()
                ->join('users', 'users.id', '=', 'notification_group_members.user_id')
                ->where('users.status', User::MEMBER_STATUS_ACTIVE)
                ->pluck('users.id')
                ->toArray();

            $userIds = array_merge($userIds, $groupUserIds);
        }

        return array_unique($userIds);
    }

    /**
     * 개별 사용자 조회
     *
     * @param array $userIds 사용자 ID 배열
     * @return array 유효한 사용자 ID 배열
     * @throws ResourceNotFoundException 사용자를 찾을 수 없을 때
     */
    private function getIndividualUsers(array $userIds): array
    {
        // 먼저 요청된 사용자들이 존재하는지 확인
        $existingUserIds = User::whereIn('id', $userIds)->pluck('id')->toArray();
        $missingUserIds = array_diff($userIds, $existingUserIds);

        if (!empty($missingUserIds)) {
            throw new ResourceNotFoundException(
                "사용자를 찾을 수 없습니다. (ID: " . implode(', ', $missingUserIds) . ")"
            );
        }

        // 활성 사용자만 필터링
        $validUserIds = User::whereIn('id', $userIds)
            ->where('status', User::MEMBER_STATUS_ACTIVE)
            ->pluck('id')
            ->toArray();

        return $validUserIds;
    }

    /**
     * 수신자 정보 저장
     *
     * @param string $notificationId 알림 ID
     * @param array $userIds 수신자 ID 배열
     */
    private function createRecipients(string $notificationId, array $userIds): void
    {
        if (empty($userIds)) {
            Log::warning('수신자가 없는 알림', ['notification_id' => $notificationId]);
            return;
        }

        $recipientData = [];
        $now = now();

        foreach ($userIds as $userId) {
            $recipientData[] = [
                'notification_id' => $notificationId,
                'user_id' => $userId,
                'created_at' => $now,
                'updated_at' => $now,
            ];
        }

        // 배치 삽입으로 성능 최적화
        NotificationRecipient::insert($recipientData);
    }

    /**
     * 알림 전송 통계 조회
     *
     * @param string $notificationId 알림 ID
     * @return array 전송 통계
     * @throws ResourceNotFoundException
     */
    public function getNotificationStats(string $notificationId): array
    {
        $notification = Notification::where('id', $notificationId)->first();

        if (!$notification) {
            throw new ResourceNotFoundException('알림을 찾을 수 없습니다.');
        }

        $totalRecipients = $notification->recipients()->count();
        $deliveredCount = $notification->recipients()->whereNotNull('delivered_at')->count();
        $readCount = $notification->recipients()->whereNotNull('read_at')->count();

        return [
            'notification_id' => $notificationId,
            'title' => $notification->title,
            'sent_at' => $notification->sent_at,
            'total_recipients' => $totalRecipients,
            'delivered_count' => $deliveredCount,
            'read_count' => $readCount,
            'delivery_rate' => $totalRecipients > 0 ? round(($deliveredCount / $totalRecipients) * 100, 2) : 0,
            'read_rate' => $totalRecipients > 0 ? round(($readCount / $totalRecipients) * 100, 2) : 0,
        ];
    }
}
