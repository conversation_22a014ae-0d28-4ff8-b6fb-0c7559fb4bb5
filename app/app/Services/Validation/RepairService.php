<?php

namespace App\Services\Validation;

use App\Models\Product;
use Exception;

class RepairService
{
    /**
     * 수리 가능 여부 확인
     *
     * @throws Exception
     */
    public function canBeRepaired(Product $product, string $qaid): void
    {
        $role = mb_strtolower(auth()->user()->role);

        // 잠금이 되어 있는지 확인
        if ($product->isLocked()) {
            throw new Exception("잠금된 상품입니다. ($product->memo) 관리자에게 문의하세요. (QAID: {$product->qaid})");
        }

        // 본사 직원일 경우
        if ($role !== 'guest') {
            if (in_array($product->status, [
                Product::STATUS_CARRIED_OUT,
                Product::STATUS_CARRIED_OUT_WAITING,
                Product::STATUS_CARRIED_OUT_REPAIRED,
            ])) {
                throw new Exception("[$qaid]는 외주 반출(외부수리의뢰) 상품으로 수리/점검이 불가능합니다.");
            }

            if ($product->checked_status === Product::CHECKED_STATUS_UNCHECKED) {
                throw new Exception("[$qaid]는 아직 검수되지 않았습니다. 상품을 검수해 주세요.");
            }

            if ($product->checked_status === Product::CHECKED_STATUS_UNDELIVERED) {
                throw new Exception("[$qaid]는 미입고 상품으로 수리/점검이 불가능합니다.");
            }

            if ($product->duplicated === Product::IS_DUPLICATED_Y) {
                throw new Exception('같은 QAID='.$qaid.'로 중복된 상품(보류처리)이 있습니다.');
            }

            if ($product->status > Product::STATUS_WAITING) {
                throw new Exception("[$qaid]는 수리/점검이 완료되었거나 삭제된 상품입니다.");
            }
        }

        // 협력 업체일 경우
        if ($role === 'guest') {
            if ($product->status === Product::STATUS_CARRIED_OUT_REPAIRED) {
                throw new Exception("이미 수리/점검이 완료된 상품입니다. 입력한 QAID: $qaid");
            }

            if (! in_array($product->status, [
                Product::STATUS_CARRIED_OUT,
                Product::STATUS_CARRIED_OUT_WAITING,
            ])) {
                throw new Exception("수리/점검이 불가능한 상품입니다. 입력한 QAID: $qaid");
            }
        }
    }
}
