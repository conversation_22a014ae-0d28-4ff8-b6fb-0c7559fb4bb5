<?php

namespace App\Services;

use App\Models\Product;
use App\Models\RepairCost;
use App\Models\RepairCostCategory;
use App\Models\RepairCostPolicy;
use App\Models\RepairCostRange;
use App\Models\RepairCostType;
use App\Models\RepairCostTypeProcessMapping;
use Exception;

/**
 * 수리비 계산 및 관리 서비스
 *
 * 제품 정보와 프로세스 코드를 기반으로 수리비를 계산하고 관리하는 서비스
 */
class RepairCostService
{
    /**
     * 모니터 크기 추출 서비스
     */
    protected MonitorSizeExtractionService $monitorSizeService;

    /**
     * 수리 프로세스 매핑 서비스
     */
    protected RepairCostTypeProcessMappingService $mappingService;

    protected TelegramService $telegramService;

    /**
     * 기본 수리비 (계산 실패 시 사용)
     */
    const DEFAULT_REPAIR_COST = 50000;

    /**
     * 생성자
     */
    public function __construct(
        TelegramService $telegramService,
        ?MonitorSizeExtractionService $monitorSizeService = null,
        ?RepairCostTypeProcessMappingService $mappingService = null,
    ) {
        $this->monitorSizeService = $monitorSizeService ?? new MonitorSizeExtractionService($telegramService);
        $this->mappingService = $mappingService ?? new RepairCostTypeProcessMappingService;
    }

    /**
     * 제품 정보와 프로세스 코드 ID를 기반으로 수리비 계산
     *
     * @param  Product  $product  제품 정보
     * @param  int  $processId  수리 프로세스 코드 ID
     * @return array 계산 결과 ['amount' => int, 'basis' => string, 'details' => array]
     */
    public function calculateRepairCost(Product $product, int $processId): array
    {
        try {
            // 1. 프로세스 ID로 수리 유형 조회
            $repairTypeId = $this->getRepairTypeIdByProcessId($processId);
            if (! $repairTypeId) {
                SimpleLogService::warning('repair', '프로세스 ID에 대한 수리 유형을 찾을 수 없음', [
                    'product_id' => $product->id,
                    'process_id' => $processId,
                ]);

                return $this->getDefaultResult($product, $processId, '매핑되지 않은 프로세스 ID');
            }

            // 만약 선택된 프로세스가 '소프트웨어' 유형이라면, 기본 수리비(invoice1)는 '검수' 비용으로 계산합니다.
            // OS 재설치 비용 자체는 invoice3에서 별도로 처리됩니다.
            $softwareRepairType = RepairCostType::byCode(RepairCostType::TYPE_SOFTWARE)->first();
            if ($softwareRepairType && $repairTypeId === $softwareRepairType->id) {
                SimpleLogService::info('repair', '소프트웨어 프로세스 감지. 수리_기타 비용으로 계산합니다.', [
                    'product_id' => $product->id,
                    'original_repair_type_id' => $repairTypeId,
                ]);
                $inspectionType = RepairCostType::byCode(RepairCostType::TYPE_OTHER)->firstOrFail();
                $repairTypeId = $inspectionType->id; // 실제 계산에 사용할 ID를 '검수' ID로 변경
            }

            // 2. 제품의 카테고리 찾기
            $category = $this->findCostCategory($product);
            if (! $category) {
                SimpleLogService::warning('repair', '수리비 카테고리를 찾을 수 없음', [
                    'product_id' => $product->id,
                    'cate4_id' => $product->cate4_id,
                    'cate5_id' => $product->cate5_id,
                ]);

                return $this->getDefaultResult($product, $processId, '카테고리 매핑 없음');
            }
            SimpleLogService::debug('repair', '수리비 카테고리 조회 결과', [
                'product_id' => $product->id,
                'category' => $category,
                'policy_name' => $category->policy->name ?? 'unknown',
            ]);

            // 3. 적절한 범위 찾기
            $range = $this->findCostRange($category, $product);
            if (! $range) {
                SimpleLogService::warning('repair', '적용 가능한 수리비 범위를 찾을 수 없음', [
                    'product_id' => $product->id,
                    'category_id' => $category->id,
                ]);

                return $this->getDefaultResult($product, $processId, '적용 가능한 범위 없음');
            }
            SimpleLogService::debug('repair', '적용 가능한 수리비 범위 조회 결과', [
                'product_id' => $product->id,
                'range' => $range,
            ]);

            // 4. 수리비 조회
            $amount = $this->getCostAmount($range, $repairTypeId);
            if ($amount === null) {
                SimpleLogService::warning('repair', '수리비를 찾을 수 없음', [
                    'range_id' => $range->id,
                    'repair_type_id' => $repairTypeId,
                ]);

                return $this->getDefaultResult($product, $processId, '수리비 미설정');
            }

            // 수리비 객체 조회
            $repairCost = RepairCost::findByRangeAndTypeId($range->id, $repairTypeId);
            SimpleLogService::debug('repair', '수리비 객체 조회 결과', [
                'product_id' => $product->id,
                'amount' => $amount,
                'repair_cost' => $repairCost,
                'basis' => $this->getBasisDescription($category, $range, $repairTypeId),
                'details' => [
                    'policy_name' => $category->policy->name ?? 'unknown',
                    'category_id' => $category->id,
                    'range_id' => $range->id,
                    'range_name' => $range->range_name,
                    'repair_type_id' => $repairTypeId,
                    'process_id' => $processId,
                    'cost_id' => $repairCost?->id,
                ],
            ]);

            return [
                'amount' => $amount,
                'basis' => $this->getBasisDescription($category, $range, $repairTypeId),
                'details' => [
                    'policy_name' => $category->policy->name ?? 'unknown',
                    'category_id' => $category->id,
                    'range_id' => $range->id,
                    'range_name' => $range->range_name,
                    'repair_type_id' => $repairTypeId,
                    'process_id' => $processId,
                    'cost_id' => $repairCost?->id,
                ],
            ];

        } catch (Exception $e) {
            SimpleLogService::error('repair', '수리비 계산 중 오류 발생', [
                'product_id' => $product->id,
                'process_id' => $processId,
                'error' => $e->getMessage(),
            ], $e);

            return $this->getDefaultResult($product, $processId, '계산 오류: '.$e->getMessage());
        }
    }

    /**
     * 제품에 적용할 가장 적절한 수리비 카테고리를 찾습니다.
     * 검색 순서:
     * 1. 가장 구체적인 조건(cate5)으로 모든 관련 정책을 확인합니다.
     * 2. 1단계에서 찾지 못한 경우, 덜 구체적인 조건(cate4)으로 모든 관련 정책을 확인합니다.
     * 3. 그래도 찾지 못하면 기본 정책으로 폴백합니다.
     */
    public function findCostCategory(Product $product): ?RepairCostCategory
    {
        $policyCodes = $this->getApplicablePolicyCodes($product);

        // 1. cate5를 사용하여 가장 구체적인 카테고리 먼저 검색
        if ($product->cate5_id) {
            foreach ($policyCodes as $policyCode) {
                $category = $this->findSpecificCategoryByPolicy($product, $policyCode);
                if ($category) {
                    return $category;
                }
            }
        }

        // 2. cate4를 사용하여 덜 구체적인 카테고리 검색
        foreach ($policyCodes as $policyCode) {
            $category = $this->findGeneralCategoryByPolicy($product, $policyCode);
            if ($category) {
                return $category;
            }
        }

        // 3. 최종 기본 정책으로 폴백
        SimpleLogService::warning('repair', '적용할 정책을 찾지 못해 최종 기본 정책으로 폴백', ['product_id' => $product->id]);

        return $this->getDefaultCategory(RepairCostPolicy::POLICY_DEFAULT);
    }

    /**
     * 제품 유형에 따라 적용 가능한 정책 코드 목록을 반환합니다.
     */
    private function getApplicablePolicyCodes(Product $product): array
    {
        if ($this->monitorSizeService->isMonitorProduct($product)) {
            $brand = $this->monitorSizeService->getMonitorModel($product);
            $policyCode = $this->getMonitorPolicyCodeByBrand($brand);

            // 일반 모니터 정책을 항상 폴백으로 포함
            return array_unique([$policyCode, RepairCostPolicy::POLICY_MONITOR_GENERAL]);
        }

        if ($this->isAppleProduct($product)) {
            return [
                RepairCostPolicy::POLICY_APPLE_MACBOOK,
                RepairCostPolicy::POLICY_APPLE_IMAC,
                RepairCostPolicy::POLICY_APPLE_ETC,
            ];
        }

        // 일반 제품 정책
        return [
            RepairCostPolicy::POLICY_GENERAL_PRICE,
            RepairCostPolicy::POLICY_GENERAL_COMMON,
            RepairCostPolicy::POLICY_DEFAULT, // 일반 제품의 최종 폴백
        ];
    }

    /**
     * cate4와 cate5를 모두 사용하여 특정 정책 내에서 가장 구체적인 카테고리를 찾습니다.
     */
    private function findSpecificCategoryByPolicy(Product $product, string $policyCode): ?RepairCostCategory
    {
        if (! $product->cate5_id) {
            return null;
        }

        return RepairCostCategory::active()
            ->whereHas('policy', fn ($q) => $q->where('code', $policyCode))
            ->byMapping($product->cate4_id, $product->cate5_id)
            ->with('policy')
            ->first();
    }

    /**
     * cate4만 사용하여 특정 정책 내에서 일반 카테고리를 찾습니다.
     */
    private function findGeneralCategoryByPolicy(Product $product, string $policyCode): ?RepairCostCategory
    {
        return RepairCostCategory::active()
            ->whereHas('policy', fn ($q) => $q->where('code', $policyCode))
            ->byMapping($product->cate4_id) // cate5는 null
            ->with('policy')
            ->first();
    }

    /**
     * 애플 제품 여부 확인
     */
    private function isAppleProduct(Product $product): bool
    {
        return $product->req->req_type === 2;
    }

    /**
     * 브랜드 정보를 바탕으로 모니터 정책 코드 결정
     */
    private function getMonitorPolicyCodeByBrand(?string $brand): string
    {
        return $brand === 'brand' ? RepairCostPolicy::POLICY_MONITOR_BRAND : RepairCostPolicy::POLICY_MONITOR_GENERAL;
    }

    /**
     * 카테고리와 제품 정보를 기반으로 수리비 범위 찾기
     */
    public function findCostRange(RepairCostCategory $category, Product $product): ?RepairCostRange
    {
        $pricingType = $category->policy->pricing_type ?? RepairCostPolicy::PRICING_TYPE_COMMON;

        return match ($pricingType) {
            RepairCostPolicy::PRICING_TYPE_SIZE => $this->findRangeBySizeType($category, $product),
            RepairCostPolicy::PRICING_TYPE_PRICE => $this->findRangeByPriceType($category, $product),
            default => $this->findRangeByGeneralType($category),
        };
    }

    /**
     * 크기 기준 정책으로 범위 찾기 (모니터 등)
     */
    private function findRangeBySizeType(RepairCostCategory $category, Product $product): ?RepairCostRange
    {
        $ranges = $category->ranges()
            ->active()
            ->whereIn('unit', [RepairCostRange::UNIT_INCH, RepairCostRange::UNIT_CM])
            ->orderBy('min_value')
            ->get();

        foreach ($ranges as $range) {
            if ($this->isProductInRange($range, $product)) {
                return $range;
            }
        }

        return null;
    }

    /**
     * 가격 기준 정책으로 범위 찾기 (일반 제품)
     */
    private function findRangeByPriceType(RepairCostCategory $category, Product $product): ?RepairCostRange
    {
        $ranges = $category->ranges()
            ->active()
            ->where('unit', RepairCostRange::UNIT_WON)
            ->get();

        foreach ($ranges as $range) {
            if ($this->isProductInRange($range, $product)) {
                return $range;
            }
        }

        return null;
    }

    /**
     * 일반 정책으로 범위 찾기 (애플 제품 등)
     */
    private function findRangeByGeneralType(RepairCostCategory $category): ?RepairCostRange
    {
        return $category->ranges()
            ->active()
            ->where('unit', RepairCostRange::UNIT_COMMON)
            ->first() ?? $category->ranges()->active()->first();
    }

    /**
     * 제품 크기 정보(크기, 단위)를 반환합니다. 단위를 변환하지 않습니다.
     */
    private function getProductSizeInfo(Product $product): ?array
    {
        // 1. monitor_size_lookup_id가 있는 경우 우선 사용
        if ($product->monitor_size_lookup_id) {
            if (! $product->relationLoaded('monitorSizeLookup')) {
                $product->load('monitorSizeLookup');
            }
            $monitorLookup = $product->monitorSizeLookup;
            if ($monitorLookup && $monitorLookup->size > 0) {
                return [
                    'size' => (float) $monitorLookup->size,
                    'unit' => strtolower($monitorLookup->unit) === 'cm' ? 'cm' : 'inch',
                ];
            }
        }
        // 2. 상품명으로 추출
        $sizeInfo = $this->monitorSizeService->extractSizeFromName($product);
        if ($sizeInfo && isset($sizeInfo['size'], $sizeInfo['unit'])) {
            return [
                'size' => (float) $sizeInfo['size'],
                'unit' => strtolower($sizeInfo['unit']) === 'cm' ? 'cm' : 'inch',
            ];
        }

        return null;
    }

    /**
     * 범위와 수리 유형 ID로 수리비 금액 조회
     */
    public function getCostAmount(RepairCostRange $range, int $repairTypeId): ?int
    {
        $repairCost = RepairCost::findByRangeAndTypeId($range->id, $repairTypeId);

        return $repairCost?->amount;
    }

    /**
     * 프로세스 ID로 수리 유형 ID 조회
     */
    public function getRepairTypeIdByProcessId(int $processId): ?int
    {
        $mapping = RepairCostTypeProcessMapping::where('repair_process_id', $processId)
            ->active()
            ->first();

        return $mapping?->repair_cost_type_id;
    }

    /**
     * 제품이 특정 범위에 포함되는지 확인
     */
    protected function isProductInRange(RepairCostRange $range, Product $product): bool
    {
        switch ($range->unit) {
            case RepairCostRange::UNIT_COMMON:
                return true;

            case RepairCostRange::UNIT_WON:
                $price = $product->amount ?? 0;
                $min = (float) $range->min_value;
                $max = (float) $range->max_value;

                if ($min == 0) {
                    return $price >= $min && $price < $max;
                }

                return $price >= $min && $price < $max;

            case RepairCostRange::UNIT_INCH:
            case RepairCostRange::UNIT_CM:
                $sizeInfo = $this->getProductSizeInfo($product);
                if (! $sizeInfo || $sizeInfo['unit'] !== $range->unit) {
                    return false;
                }
                $size = $sizeInfo['size'];
                $min = (float) $range->min_value;
                $max = (float) $range->max_value;

                if ($min == 0) {
                    return $size >= $min && $size <= $max;
                }

                return $size > $min && $size <= $max;

            default:
                return false;
        }
    }

    /**
     * OS 재설치비 계산
     */
    public function calculateOsInstallCost(Product $product): ?int
    {
        try {
            $osInstallType = RepairCostType::where('code', RepairCostType::TYPE_SOFTWARE)
                ->active()
                ->first();

            if (! $osInstallType) {
                return null;
            }

            // 가격별 OS 설치 정책을 먼저 시도
            $category = $this->findSpecificCategoryByPolicy($product, RepairCostPolicy::POLICY_OS_INSTALL_PRICE)
                     ?? $this->findGeneralCategoryByPolicy($product, RepairCostPolicy::POLICY_OS_INSTALL_COMMON);

            if (! $category) {
                SimpleLogService::warning('repair', 'OS 재설치비 카테고리를 찾을 수 없음', [
                    'product_id' => $product->id,
                    'qaid' => $product->qaid,
                ]);

                return null;
            }

            $range = $this->findCostRange($category, $product);
            if (! $range) {
                return null;
            }

            return $this->getCostAmount($range, $osInstallType->id);

        } catch (Exception $e) {
            SimpleLogService::error('repair', 'OS 재설치비 계산 중 오류 발생', [
                'product_id' => $product->id,
                'qaid' => $product->qaid,
                'error' => $e->getMessage(),
            ], $e);

            return null;
        }
    }

    /**
     * 기본 카테고리 조회
     */
    public function getDefaultCategory(string $policyCode): ?RepairCostCategory
    {
        $policy = RepairCostPolicy::active()
            ->byCode($policyCode)
            ->first();

        if (! $policy) {
            return null;
        }

        // 정책에 연결된 첫 번째 활성 카테고리를 반환
        return $policy->activeCategories()->first();
    }

    /**
     * 기본 결과 반환 (계산 실패 시)
     */
    protected function getDefaultResult(Product $product, int $processId, string $reason): array
    {
        try {
            $defaultResult = $this->calculateWithDefaultPolicy($product, $processId);
            if ($defaultResult) {
                $defaultResult['basis'] .= " (기본값 적용: {$reason})";
                $defaultResult['details']['failure_reason'] = $reason;

                return $defaultResult;
            }
        } catch (Exception $e) {
            SimpleLogService::error('repair', '기본 수리비 계산 중 오류 발생', [
                'error' => $e->getMessage(),
            ], $e);
        }

        return $this->getFallbackResult($product, $processId, $reason);
    }

    /**
     * 최종 폴백 결과 반환 (모든 기본 정책 조회 실패 시)
     */
    protected function getFallbackResult(Product $product, int $processId, string $reason): array
    {
        return [
            'amount' => self::DEFAULT_REPAIR_COST,
            'basis' => "최종 기본값 적용 ({$reason})",
            'details' => [
                'policy_name' => 'fallback',
                'category_id' => null,
                'range_id' => null,
                'range_name' => '최종 기본값',
                'repair_type_id' => null,
                'process_id' => $processId,
                'failure_reason' => $reason,
                'is_fallback' => true,
            ],
        ];
    }

    /**
     * 수리비 계산 근거 설명 생성
     */
    protected function getBasisDescription(RepairCostCategory $category, RepairCostRange $range, int $repairTypeId): string
    {
        $systemName = $category->policy->display_name ?? $category->policy->name ?? 'Unknown';
        $rangeName = $range->range_name;
        $repairType = RepairCostType::find($repairTypeId);
        $repairTypeName = $repairType ? $repairType->name : "ID: {$repairTypeId}";

        return "{$systemName} > {$rangeName} > {$repairTypeName}";
    }

    /**
     * 기본 수리비 정책으로 수리비 계산
     */
    public function calculateWithDefaultPolicy(Product $product, int $processId): ?array
    {
        $defaultPolicy = RepairCostPolicy::active()->byCode(RepairCostPolicy::POLICY_DEFAULT)->first();
        if (! $defaultPolicy) {
            return null;
        }

        $defaultCategory = $defaultPolicy->activeCategories()->first();
        if (! $defaultCategory) {
            return null;
        }

        $defaultRange = $defaultCategory->ranges()->active()->first();
        if (! $defaultRange) {
            return null;
        }

        $repairTypeId = $this->getRepairTypeIdByProcessId($processId);
        if (! $repairTypeId) {
            return null;
        }

        $amount = $this->getCostAmount($defaultRange, $repairTypeId);
        if ($amount === null) {
            return null;
        }

        $repairCost = RepairCost::findByRangeAndTypeId($defaultRange->id, $repairTypeId);

        return [
            'amount' => $amount,
            'basis' => $this->getBasisDescription($defaultCategory, $defaultRange, $repairTypeId),
            'details' => [
                'policy_name' => $defaultPolicy->name ?? 'default',
                'category_id' => $defaultCategory->id,
                'range_id' => $defaultRange->id,
                'range_name' => $defaultRange->range_name,
                'repair_type_id' => $repairTypeId,
                'process_id' => $processId,
                'cost_id' => $repairCost?->id,
                'is_default_policy' => true,
            ],
        ];
    }
}
