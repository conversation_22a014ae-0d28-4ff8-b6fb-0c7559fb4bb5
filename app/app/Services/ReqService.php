<?php

namespace App\Services;

use App\Models\ProductLog;
use App\Models\Req;
use App\Models\User;
use App\Models\WorkStatus;
use Exception;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class ReqService
{
    protected WorkStatusService $workStatusService;

    public function __construct(WorkStatusService $workStatusService)
    {
        $this->workStatusService = $workStatusService;
    }

    public function getReqList(array $data): QueryBuilder|EloquentBuilder|Req
    {
        $query = Req::query();

        if ($data['beginAt'] && $data['endAt']) {
            $query->whereBetween('req_at', [$data['beginAt'], $data['endAt']]);
        }

        if ($data['reqType']) {
            $query->where('req_type', $data['reqType']);
        } else {
            $query->whereNotIn('req_type', [Req::TYPE_GHOST, Req::TYPE_UNLINKED]);
        }

        $query->orderBy('req_at', 'DESC');

        return $query;
    }

    /**
     * 검수대기 요청서 리스트
     *
     * @param  array  $select  The columns to be selected (default: all columns)
     * @param  string  $orderByColumn  The column to order the results by (default: "id")
     * @param  string  $orderByDirection  The order direction (default: "asc")
     * @return Collection The collection of unchecked requests with the specified columns, ordered by the specified column and direction
     */
    public function getUncheckedList(array $select = ['*'], string $orderByColumn = 'id', string $orderByDirection = 'asc'): Collection
    {
        return Req::whereHas('reqCount', function ($query) {
            $query->where('unchecked', '>', 0);
        })->select($select)
            ->orderBy($orderByColumn, $orderByDirection)
            ->get();
    }

    /**
     * 입고 요청서의 날짜
     */
    public function getReqAt(int $id): ?string
    {
        return Req::where('id', $id)->select('req_at')->first()?->req_at;
    }

    /**
     * 쿠팡의 점검 요청 리스트 등록
     *
     * @throws Exception|Throwable
     */
    public function create(User $user, array $data): Req
    {
        try {
            DB::beginTransaction();

            $reqType = $data['req_type'] === 2 ? Req::TYPE_APPLE : Req::TYPE_COUPANG;

            $req = new Req;

            $req->req_at = $data['req_at'];
            $req->req_type = $reqType;
            $req->status = Req::STATUS_REGISTERED;
            $req->user_id = $user->id;
            // memo가 null이거나 빈 문자열일 때 null로 저장
            $req->memo = ! empty($data['memo']) ? $data['memo'] : null;
            $req->save();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_REQ_CREATE, // 입고목록 등록
            ]);

            $now = now();
            ProductLog::insert([
                'product_id' => null,
                'model_type' => 'App\Models\Req',
                'model_id' => $req->id,
                'work_status_id' => $statusIds[WorkStatus::LINK_REQ_CREATE],
                'user_id' => $user->id,
                'memo' => $req->memo, // 입고목록 등록
                'created_at' => $now,
                'updated_at' => $now,
            ]);

            DB::commit();

            SimpleLogService::info('req', '입고 등록 성공', ['req_id' => $req->id]);

            return $req;
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('req', '입고 등록 실패', [], $e);

            throw $e;
        }
    }

    /**
     * 쿠팡의 점검 요청 리스트 수정
     *
     * @throws Exception|Throwable
     */
    public function update(int $id, User $user, array $data): Req
    {
        try {
            DB::beginTransaction();

            $reqType = (int) $data['req_type'] === 2 ? Req::TYPE_APPLE : Req::TYPE_COUPANG;

            $req = Req::findOrFail($id);

            $req->req_at = $data['req_at'];
            $req->req_type = $reqType;
            $req->status = $data['status'];
            // memo가 null이거나 빈 문자열일 때 null로 저장
            $req->memo = ! empty($data['memo']) ? $data['memo'] : null;

            $req->save();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_REQ_UPDATE, // 입고목록 수정
            ]);

            $now = now();
            ProductLog::insert([
                'product_id' => null,
                'model_type' => 'App\Models\Req',
                'model_id' => $req->id,
                'work_status_id' => $statusIds[WorkStatus::LINK_REQ_UPDATE],
                'user_id' => $user->id,
                'memo' => $req->memo, // 입고목록 수정
                'created_at' => $now,
                'updated_at' => $now,
            ]);

            DB::commit();

            SimpleLogService::info('req', '입고 수정 성공', ['req_id' => $req->id]);

            return $req;
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('req', '입고 수정 실패', [], $e);

            throw $e;
        }
    }

    /**
     * @throws Throwable
     */
    public function destroy(int $id, User $user): void
    {
        $req = Req::find($id);

        try {
            DB::beginTransaction();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_REQ_DELETE, // 입고목록 삭제
            ]);

            $now = now();
            ProductLog::insert([
                'product_id' => null,
                'model_type' => 'App\Models\Req',
                'model_id' => $req->id,
                'work_status_id' => $statusIds[WorkStatus::LINK_REQ_DELETE],
                'user_id' => $user->id,
                'memo' => null, // 입고목록 삭제
                'created_at' => $now,
                'updated_at' => $now,
            ]);

            $deleteLog = $req->deleteLog();
            $deleteLog->create([
                'user_id' => $user->id,
                'content' => $req,
                'ip' => request()->getClientIp(),
            ]);

            $req->delete();

            SimpleLogService::info('req', "요청서($req->id) 삭제 성공");

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            SimpleLogService::error('req', "요청서($req->id) 삭제 실패", [], $e);

            throw $e;
        }
    }
}
