<?php

namespace App\Services;

use App\Exceptions\BusinessException;
use App\Exceptions\ValidationException;
use App\Repositories\Interfaces\NotificationTemplateRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Throwable;

/**
 * 템플릿 검색 서비스
 *
 * 알림 템플릿의 다양한 검색, 필터링, 정렬 및 페이지네이션 기능을 제공합니다.
 * 복잡한 검색 조건과 다중 정렬 옵션을 지원하며, 성능 최적화된 쿼리를 사용합니다.
 *
 * 주요 기능:
 * - 키워드 기반 템플릿 검색
 * - 우선순위, 생성자, 사용 횟수 등 다양한 필터링
 * - 생성일, 사용 횟수 기준 정렬
 * - 페이지네이션 지원
 * - 인기/최근 템플릿 조회
 * - 사용되지 않은 템플릿 조회
 * - 사용 횟수 범위별 조회
 *
 * <AUTHOR> Development Team
 *
 * @since 2.0.0
 */
class TemplateSearchService
{
    /**
     * 템플릿 리포지토리
     */
    protected NotificationTemplateRepositoryInterface $templateRepository;

    /**
     * 템플릿 유효성 검증 서비스
     */
    protected TemplateValidationService $validationService;

    /**
     * 템플릿 로깅 서비스
     */
    protected TemplateLoggingService $loggingService;

    /**
     * 생성자
     *
     * @param  NotificationTemplateRepositoryInterface  $templateRepository  템플릿 리포지토리
     * @param  TemplateValidationService  $validationService  유효성 검증 서비스
     * @param  TemplateLoggingService  $loggingService  로깅 서비스
     */
    public function __construct(
        NotificationTemplateRepositoryInterface $templateRepository,
        TemplateValidationService $validationService,
        TemplateLoggingService $loggingService
    ) {
        $this->templateRepository = $templateRepository;
        $this->validationService = $validationService;
        $this->loggingService = $loggingService;
    }

    /**
     * 템플릿 목록 조회 (페이지네이션)
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $page  페이지 번호
     * @param  array  $filters  필터 조건
     * @return LengthAwarePaginator 페이지네이션된 템플릿 목록
     *
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getTemplates(int $userId, int $page = 1, array $filters = []): LengthAwarePaginator
    {
        try {
            // 페이지네이션 설정 검증
            $page = max(1, (int) $page);
            $perPage = isset($filters['per_page']) ? max(1, min(100, (int) $filters['per_page'])) : 20;

            // 필터 조건 검증 및 정제
            $validatedFilters = $this->validationService->validateFilters($filters);
            $sanitizedFilters = $this->validationService->sanitizeFilters($validatedFilters);
            $sanitizedFilters['per_page'] = $perPage;

            $templates = $this->templateRepository->getPaginatedWithStats($page, $perPage, $sanitizedFilters);

            $this->loggingService->logInfo('템플릿 목록 조회 완료', [
                'user_id' => $userId,
                'page' => $page,
                'per_page' => $perPage,
                'total' => $templates->total(),
                'filters' => $sanitizedFilters,
            ]);

            return $templates;

        } catch (ValidationException $e) {
            throw $e;
        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 목록 조회 중 오류 발생', [
                'user_id' => $userId,
                'page' => $page,
                'filters' => $filters,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('템플릿 목록 조회 중 오류가 발생했습니다.', [
                'user_id' => $userId,
                'type' => 'list_failed',
            ], $e);
        }
    }

    /**
     * 모든 템플릿 조회 (페이지네이션 없음)
     *
     * @param  int  $userId  사용자 ID
     * @return Collection 모든 템플릿 목록
     *
     * @throws BusinessException
     */
    public function getAllTemplates(int $userId): Collection
    {
        try {
            $templates = $this->templateRepository->findAll();

            $this->loggingService->logInfo('전체 템플릿 조회 완료', [
                'user_id' => $userId,
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (Throwable $e) {
            $this->loggingService->logError('전체 템플릿 조회 중 오류 발생', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('전체 템플릿 조회 중 오류가 발생했습니다.', [
                'user_id' => $userId,
                'type' => 'list_all_failed',
            ], $e);
        }
    }

    /**
     * 템플릿 검색 (페이지네이션 없음)
     *
     * @param  int  $userId  사용자 ID
     * @param  string  $search  검색어
     * @param  array  $filters  필터 조건
     * @return Collection 검색된 템플릿 목록
     *
     * @throws BusinessException
     */
    public function searchTemplates(int $userId, string $search = '', array $filters = []): Collection
    {
        try {
            $templates = $this->templateRepository->search($search, $filters);

            $this->loggingService->logInfo('템플릿 검색 완료', [
                'user_id' => $userId,
                'search' => $search,
                'filters' => $filters,
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 검색 중 오류 발생', [
                'user_id' => $userId,
                'search' => $search,
                'filters' => $filters,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('템플릿 검색 중 오류가 발생했습니다.', [
                'user_id' => $userId,
                'type' => 'search_failed',
            ], $e);
        }
    }

    /**
     * 우선순위별 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @param  string  $priority  우선순위
     * @return Collection 우선순위별 템플릿 목록
     *
     * @throws BusinessException
     */
    public function getTemplatesByPriority(int $userId, string $priority): Collection
    {
        try {
            $templates = $this->templateRepository->findByPriority($priority);

            $this->loggingService->logInfo('우선순위별 템플릿 조회 완료', [
                'user_id' => $userId,
                'priority' => $priority,
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (Throwable $e) {
            $this->loggingService->logError('우선순위별 템플릿 조회 중 오류 발생', [
                'user_id' => $userId,
                'priority' => $priority,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('우선순위별 템플릿 조회 중 오류가 발생했습니다.', [
                'user_id' => $userId,
                'priority' => $priority,
                'type' => 'priority_search_failed',
            ], $e);
        }
    }

    /**
     * 사용 횟수 기준 정렬된 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @param  string  $direction  정렬 방향 ('asc' 또는 'desc')
     * @return Collection 사용 횟수 기준 정렬된 템플릿 목록
     *
     * @throws BusinessException
     */
    public function getTemplatesOrderedByUsage(int $userId, string $direction = 'desc'): Collection
    {
        try {
            $templates = $this->templateRepository->getOrderedByUsage($direction);

            $this->loggingService->logInfo('사용 횟수 기준 템플릿 조회 완료', [
                'user_id' => $userId,
                'direction' => $direction,
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (Throwable $e) {
            $this->loggingService->logError('사용 횟수 기준 템플릿 조회 중 오류 발생', [
                'user_id' => $userId,
                'direction' => $direction,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('사용 횟수 기준 템플릿 조회 중 오류가 발생했습니다.', [
                'user_id' => $userId,
                'type' => 'usage_order_failed',
            ], $e);
        }
    }

    /**
     * 생성일 기준 정렬된 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @param  string  $direction  정렬 방향 ('asc' 또는 'desc')
     * @return Collection 생성일 기준 정렬된 템플릿 목록
     *
     * @throws BusinessException
     */
    public function getTemplatesOrderedByCreated(int $userId, string $direction = 'desc'): Collection
    {
        try {
            $templates = $this->templateRepository->getOrderedByCreated($direction);

            $this->loggingService->logInfo('생성일 기준 템플릿 조회 완료', [
                'user_id' => $userId,
                'direction' => $direction,
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (Throwable $e) {
            $this->loggingService->logError('생성일 기준 템플릿 조회 중 오류 발생', [
                'user_id' => $userId,
                'direction' => $direction,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('생성일 기준 템플릿 조회 중 오류가 발생했습니다.', [
                'user_id' => $userId,
                'type' => 'created_order_failed',
            ], $e);
        }
    }

    /**
     * 인기 템플릿 조회 (사용 횟수 상위)
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $limit  조회할 개수
     * @return Collection 인기 템플릿 목록
     *
     * @throws BusinessException
     */
    public function getPopularTemplates(int $userId, int $limit = 10): Collection
    {
        try {
            $templates = $this->templateRepository->getPopularTemplates($limit);

            $this->loggingService->logInfo('인기 템플릿 조회 완료', [
                'user_id' => $userId,
                'limit' => $limit,
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (Throwable $e) {
            $this->loggingService->logError('인기 템플릿 조회 중 오류 발생', [
                'user_id' => $userId,
                'limit' => $limit,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('인기 템플릿 조회 중 오류가 발생했습니다.', [
                'user_id' => $userId,
                'type' => 'popular_failed',
            ], $e);
        }
    }

    /**
     * 최근 생성된 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $limit  조회할 개수
     * @return Collection 최근 생성된 템플릿 목록
     *
     * @throws BusinessException
     */
    public function getRecentTemplates(int $userId, int $limit = 10): Collection
    {
        try {
            $templates = $this->templateRepository->getRecentTemplates($limit);

            $this->loggingService->logInfo('최근 템플릿 조회 완료', [
                'user_id' => $userId,
                'limit' => $limit,
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (Throwable $e) {
            $this->loggingService->logError('최근 템플릿 조회 중 오류 발생', [
                'user_id' => $userId,
                'limit' => $limit,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('최근 템플릿 조회 중 오류가 발생했습니다.', [
                'user_id' => $userId,
                'type' => 'recent_failed',
            ], $e);
        }
    }

    /**
     * 고급 필터링을 통한 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @param  array  $filters  고급 필터 조건
     * @return Collection 필터링된 템플릿 목록
     *
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getTemplatesWithAdvancedFilters(int $userId, array $filters): Collection
    {
        try {
            // 기본 필터 검증
            $validatedFilters = $this->validationService->validateFilters($filters);

            $templates = $this->templateRepository->search('', $validatedFilters);

            $this->loggingService->logInfo('고급 필터링 템플릿 조회 완료', [
                'user_id' => $userId,
                'filters' => $validatedFilters,
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (ValidationException $e) {
            throw $e;
        } catch (Throwable $e) {
            $this->loggingService->logError('고급 필터링 템플릿 조회 중 오류 발생', [
                'user_id' => $userId,
                'filters' => $filters,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('고급 필터링 템플릿 조회 중 오류가 발생했습니다.', [
                'user_id' => $userId,
                'type' => 'advanced_filter_failed',
            ], $e);
        }
    }

    /**
     * 사용되지 않은 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @return Collection 사용되지 않은 템플릿 목록
     *
     * @throws BusinessException
     */
    public function getUnusedTemplates(int $userId): Collection
    {
        try {
            $templates = $this->templateRepository->getUnusedTemplates();

            $this->loggingService->logInfo('사용되지 않은 템플릿 조회 완료', [
                'user_id' => $userId,
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (Throwable $e) {
            $this->loggingService->logError('사용되지 않은 템플릿 조회 중 오류 발생', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('사용되지 않은 템플릿 조회 중 오류가 발생했습니다.', [
                'user_id' => $userId,
                'type' => 'unused_failed',
            ], $e);
        }
    }

    /**
     * 사용 횟수 범위별 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $minUsage  최소 사용 횟수
     * @param  int|null  $maxUsage  최대 사용 횟수
     * @return Collection 사용 횟수 범위별 템플릿 목록
     *
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getTemplatesByUsageRange(int $userId, int $minUsage = 0, ?int $maxUsage = null): Collection
    {
        try {
            // 입력 값 검증
            if ($minUsage < 0) {
                throw new ValidationException('최소 사용 횟수는 0 이상이어야 합니다.', [
                    'min_usage' => $minUsage,
                ]);
            }

            if ($maxUsage !== null && $maxUsage < 0) {
                throw new ValidationException('최대 사용 횟수는 0 이상이어야 합니다.', [
                    'max_usage' => $maxUsage,
                ]);
            }

            if ($maxUsage !== null && $minUsage > $maxUsage) {
                throw new ValidationException('최소 사용 횟수는 최대 사용 횟수보다 클 수 없습니다.', [
                    'min_usage' => $minUsage,
                    'max_usage' => $maxUsage,
                ]);
            }

            $templates = $this->templateRepository->getTemplatesByUsageRange($minUsage, $maxUsage);

            $this->loggingService->logInfo('사용 횟수 범위별 템플릿 조회 완료', [
                'user_id' => $userId,
                'min_usage' => $minUsage,
                'max_usage' => $maxUsage,
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (ValidationException $e) {
            throw $e;
        } catch (Throwable $e) {
            $this->loggingService->logError('사용 횟수 범위별 템플릿 조회 중 오류 발생', [
                'user_id' => $userId,
                'min_usage' => $minUsage,
                'max_usage' => $maxUsage,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('사용 횟수 범위별 템플릿 조회 중 오류가 발생했습니다.', [
                'user_id' => $userId,
                'type' => 'usage_range_failed',
            ], $e);
        }
    }

    // ========================================
    // 파사드 호환성 메서드들 (기존 TemplateService 인터페이스 유지)
    // ========================================

    /**
     * 템플릿 검색 (파사드 호환용)
     */
    public function search(int $userId, string $search = '', array $filters = []): Collection
    {
        return $this->searchTemplates($userId, $search, $filters);
    }

    /**
     * 우선순위별 템플릿 조회 (파사드 호환용)
     */
    public function getByPriority(int $userId, string $priority): Collection
    {
        return $this->getTemplatesByPriority($userId, $priority);
    }

    /**
     * 사용 횟수 기준 정렬된 템플릿 조회 (파사드 호환용)
     */
    public function getOrderedByUsage(int $userId, string $direction = 'desc'): Collection
    {
        return $this->getTemplatesOrderedByUsage($userId, $direction);
    }

    /**
     * 생성일 기준 정렬된 템플릿 조회 (파사드 호환용)
     */
    public function getOrderedByCreated(int $userId, string $direction = 'desc'): Collection
    {
        return $this->getTemplatesOrderedByCreated($userId, $direction);
    }

    /**
     * 인기 템플릿 조회 (파사드 호환용)
     */
    public function getPopular(int $userId, int $limit = 10): Collection
    {
        return $this->getPopularTemplates($userId, $limit);
    }

    /**
     * 최근 생성된 템플릿 조회 (파사드 호환용)
     */
    public function getRecent(int $userId, int $limit = 10): Collection
    {
        return $this->getRecentTemplates($userId, $limit);
    }

    /**
     * 고급 필터링을 통한 템플릿 조회 (파사드 호환용)
     */
    public function getWithAdvancedFilters(int $userId, array $filters): Collection
    {
        return $this->getTemplatesWithAdvancedFilters($userId, $filters);
    }

    /**
     * 사용되지 않은 템플릿 조회 (파사드 호환용)
     */
    public function getUnused(int $userId): Collection
    {
        return $this->getUnusedTemplates($userId);
    }

    /**
     * 사용 횟수 범위별 템플릿 조회 (파사드 호환용)
     */
    public function getByUsageRange(int $userId, int $minUsage = 0, ?int $maxUsage = null): Collection
    {
        return $this->getTemplatesByUsageRange($userId, $minUsage, $maxUsage);
    }
}
