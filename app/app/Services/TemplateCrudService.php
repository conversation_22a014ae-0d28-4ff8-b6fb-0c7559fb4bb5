<?php

namespace App\Services;

use App\Exceptions\BusinessException;
use App\Exceptions\TemplateException;
use App\Exceptions\ValidationException;
use App\Models\NotificationTemplate;
use App\Repositories\Interfaces\NotificationTemplateRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Throwable;

/**
 * 템플릿 CRUD 서비스
 *
 * 알림 템플릿의 기본적인 CRUD(Create, Read, Update, Delete) 작업을 담당합니다.
 * 각 작업에서 유효성 검증, 권한 확인, 로깅을 수행하여 데이터 무결성을 보장합니다.
 *
 * 주요 기능:
 * - 템플릿 생성 (유효성 검증 포함)
 * - 템플릿 조회 (ID 기반)
 * - 템플릿 수정 (권한 확인 포함)
 * - 템플릿 삭제 (권한 확인 포함)
 * - 전체 템플릿 목록 조회
 *
 * <AUTHOR> Development Team
 *
 * @since 2.0.0
 */
class TemplateCrudService
{
    /**
     * 생성자
     *
     * @param  NotificationTemplateRepositoryInterface  $templateRepository  템플릿 리포지토리
     * @param  TemplateValidationService  $validationService  유효성 검증 서비스
     * @param  TemplatePermissionService  $permissionService  권한 관리 서비스
     * @param  TemplateLoggingService  $loggingService  로깅 서비스
     */
    public function __construct(
        private NotificationTemplateRepositoryInterface $templateRepository,
        private TemplateValidationService $validationService,
        private TemplatePermissionService $permissionService,
        private TemplateLoggingService $loggingService
    ) {}

    /**
     * 템플릿 생성
     *
     * @param  int  $userId  생성자 ID
     * @param  array  $templateData  템플릿 데이터
     * @return NotificationTemplate 생성된 템플릿
     *
     * @throws ValidationException 유효성 검증 실패 시
     * @throws BusinessException 비즈니스 규칙 위반 시
     */
    public function create(int $userId, array $templateData): NotificationTemplate
    {
        try {
            // 생성자 ID 추가
            $templateData['created_by'] = $userId;

            // 유효성 검증 및 데이터 정제
            $validatedData = $this->validationService->validateForCreate($templateData);

            // 템플릿 생성
            $template = $this->templateRepository->create($validatedData);

            $this->loggingService->logTemplateAction('create', $template->id, $userId, [
                'template_name' => $template->name,
            ]);

            return $template;

        } catch (ValidationException|BusinessException $e) {
            throw $e;
        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 생성 중 오류 발생', [
                'user_id' => $userId,
                'template_data' => $templateData,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('템플릿 생성 중 오류가 발생했습니다.', [
                'user_id' => $userId,
                'type' => 'create_failed',
            ], $e);
        }
    }

    /**
     * 템플릿 ID로 조회
     *
     * @param  int  $templateId  템플릿 ID
     * @return NotificationTemplate 템플릿 정보
     *
     * @throws BusinessException 템플릿을 찾을 수 없는 경우
     */
    public function findById(int $templateId): NotificationTemplate
    {
        try {
            $template = $this->templateRepository->findById($templateId);

            if (! $template) {
                throw TemplateException::notFound($templateId);
            }

            return $template;
        } catch (BusinessException $e) {
            throw $e;
        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 조회 중 오류 발생', [
                'template_id' => $templateId,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('템플릿 조회 중 오류가 발생했습니다.', [
                'template_id' => $templateId,
                'type' => 'get_failed',
            ], $e);
        }
    }

    /**
     * 템플릿 수정
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $templateId  템플릿 ID
     * @param  array  $templateData  수정할 템플릿 데이터
     * @return NotificationTemplate 수정된 템플릿
     *
     * @throws ValidationException 유효성 검증 실패 시
     * @throws BusinessException 비즈니스 규칙 위반 시
     */
    public function update(int $userId, int $templateId, array $templateData): NotificationTemplate
    {
        try {
            // 템플릿 존재 여부 확인
            $existingTemplate = $this->findById($templateId);

            // 권한 검증 - 관리자이거나 템플릿 생성자만 수정 가능
            $this->permissionService->validatePermission($userId, $existingTemplate, 'update');

            // 유효성 검증 및 데이터 정제
            $validatedData = $this->validationService->validateForUpdate($templateData, $templateId);

            // 템플릿 수정
            $template = $this->templateRepository->update($templateId, $validatedData);

            $this->loggingService->logTemplateAction('update', $templateId, $userId, [
                'template_name' => $template->name,
                'updated_fields' => array_keys($templateData),
            ]);

            return $template;

        } catch (ValidationException|BusinessException $e) {
            throw $e;
        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 수정 중 오류 발생', [
                'template_id' => $templateId,
                'user_id' => $userId,
                'template_data' => $templateData,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('템플릿 수정 중 오류가 발생했습니다.', [
                'template_id' => $templateId,
                'user_id' => $userId,
                'type' => 'update_failed',
            ], $e);
        }
    }

    /**
     * 템플릿 삭제
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $templateId  템플릿 ID
     * @return bool 삭제 성공 여부
     *
     * @throws BusinessException 템플릿을 찾을 수 없거나 삭제 실패 시
     */
    public function delete(int $userId, int $templateId): bool
    {
        try {
            // 템플릿 존재 여부 확인
            $template = $this->findById($templateId);

            // 권한 검증 - 관리자이거나 템플릿 생성자만 삭제 가능
            $this->permissionService->validatePermission($userId, $template, 'delete');

            // 템플릿 삭제
            $result = $this->templateRepository->delete($templateId);

            if (! $result) {
                throw TemplateException::deleteFailed($templateId);
            }

            $this->loggingService->logTemplateAction('delete', $templateId, $userId, [
                'template_name' => $template->name,
            ]);

            return true;

        } catch (BusinessException $e) {
            throw $e;
        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 삭제 중 오류 발생', [
                'template_id' => $templateId,
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('템플릿 삭제 중 오류가 발생했습니다.', [
                'template_id' => $templateId,
                'user_id' => $userId,
                'type' => 'delete_failed',
            ], $e);
        }
    }

    /**
     * 모든 템플릿 조회 (페이지네이션 없음)
     *
     * @return Collection 모든 템플릿 목록
     *
     * @throws BusinessException
     */
    public function getAll(): Collection
    {
        try {
            $templates = $this->templateRepository->getAllWithRelations();

            $this->loggingService->logInfo('전체 템플릿 목록 조회 완료', [
                'total_count' => $templates->count(),
            ]);

            return $templates;

        } catch (Throwable $e) {
            $this->loggingService->logError('전체 템플릿 목록 조회 중 오류 발생', [
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('전체 템플릿 목록 조회 중 오류가 발생했습니다.', [
                'type' => 'get_all_failed',
            ], $e);
        }
    }
}
