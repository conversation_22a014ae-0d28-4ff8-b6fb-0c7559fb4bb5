<?php

namespace App\Services;

use App\Exceptions\BusinessException;
use App\Exceptions\TemplateException;
use App\Exceptions\ValidationException;
use App\Repositories\Interfaces\NotificationTemplateRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Throwable;

/**
 * 템플릿 통계 서비스
 *
 * 알림 템플릿의 사용 통계 수집, 분석 및 리포팅 기능을 제공합니다.
 * 템플릿 사용 패턴 분석을 통해 시스템 최적화와 사용자 경험 개선에 도움을 줍니다.
 *
 * 주요 기능:
 * - 템플릿 사용 횟수 증가 및 추적
 * - 전체 사용 통계 조회 (총 템플릿 수, 총 사용 횟수, 평균 사용 횟수)
 * - 우선순위별 템플릿 분포 조회
 * - 사용 빈도별 템플릿 분포 분석
 * - 인기 템플릿 및 최근 템플릿 조회
 * - 사용되지 않은 템플릿 식별
 * - 사용 트렌드 분석 및 통계 요약
 *
 * <AUTHOR> Development Team
 *
 * @since 2.0.0
 */
class TemplateStatisticsService
{
    /**
     * 템플릿 리포지토리
     */
    protected NotificationTemplateRepositoryInterface $templateRepository;

    /**
     * 템플릿 로깅 서비스
     */
    protected TemplateLoggingService $loggingService;

    /**
     * 생성자
     *
     * @param  NotificationTemplateRepositoryInterface  $templateRepository  템플릿 리포지토리
     * @param  TemplateLoggingService  $loggingService  로깅 서비스
     */
    public function __construct(
        NotificationTemplateRepositoryInterface $templateRepository,
        TemplateLoggingService $loggingService
    ) {
        $this->templateRepository = $templateRepository;
        $this->loggingService = $loggingService;
    }

    /**
     * 템플릿 사용 횟수 증가
     *
     * @param  int  $templateId  템플릿 ID
     * @return bool 증가 성공 여부
     *
     * @throws BusinessException 템플릿을 찾을 수 없거나 증가 실패 시
     */
    public function incrementUsage(int $templateId): bool
    {
        try {
            // 템플릿 존재 여부 확인
            $template = $this->templateRepository->findById($templateId);

            if (! $template) {
                throw new BusinessException('템플릿을 찾을 수 없습니다.', [
                    'template_id' => $templateId,
                    'type' => 'template_not_found',
                ]);
            }

            // 사용 횟수 증가
            $result = $this->templateRepository->incrementUsage($templateId);

            if (! $result) {
                throw TemplateException::incrementUsageFailed($templateId);
            }

            $this->loggingService->logInfo('템플릿 사용 횟수 증가 완료', [
                'template_id' => $templateId,
                'template_name' => $template->name,
                'previous_usage_count' => $template->usage_count,
                'new_usage_count' => $template->usage_count + 1,
            ]);

            return true;

        } catch (BusinessException $e) {
            throw $e;
        } catch (Throwable $e) {
            $this->loggingService->logError('사용 횟수 증가 중 오류 발생', [
                'template_id' => $templateId,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('사용 횟수 증가 중 오류가 발생했습니다.', [
                'template_id' => $templateId,
                'type' => 'increment_failed',
            ], $e);
        }
    }

    /**
     * 템플릿 사용 통계 조회
     *
     * @return array 사용 통계 정보
     *
     * @throws BusinessException
     */
    public function getUsageStatistics(): array
    {
        try {
            $statistics = $this->templateRepository->getUsageStatistics();

            $this->loggingService->logInfo('템플릿 사용 통계 조회 완료', [
                'total_templates' => $statistics['total_templates'],
                'total_usage' => $statistics['total_usage'],
                'average_usage' => $statistics['average_usage'] ?? 0,
            ]);

            return $statistics;

        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 사용 통계 조회 중 오류 발생', [
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('템플릿 사용 통계 조회 중 오류가 발생했습니다.', [
                'type' => 'statistics_failed',
            ], $e);
        }
    }

    /**
     * 우선순위별 템플릿 개수 조회
     *
     * @return array 우선순위별 개수
     *
     * @throws BusinessException
     */
    public function getCountByPriority(): array
    {
        try {
            $counts = $this->templateRepository->getCountByPriority();

            $this->loggingService->logInfo('우선순위별 템플릿 개수 조회 완료', [
                'counts' => $counts,
                'total_priorities' => count($counts),
            ]);

            return $counts;

        } catch (Throwable $e) {
            $this->loggingService->logError('우선순위별 템플릿 개수 조회 중 오류 발생', [
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('우선순위별 템플릿 개수 조회 중 오류가 발생했습니다.', [
                'type' => 'priority_count_failed',
            ], $e);
        }
    }

    /**
     * 사용 빈도별 템플릿 분포 조회
     *
     * @return array 사용 빈도별 분포
     *
     * @throws BusinessException
     */
    public function getUsageDistribution(): array
    {
        try {
            $distribution = $this->templateRepository->getUsageDistribution();

            $this->loggingService->logInfo('사용 빈도별 템플릿 분포 조회 완료', [
                'distribution' => $distribution,
                'distribution_ranges' => count($distribution),
            ]);

            return $distribution;

        } catch (Throwable $e) {
            $this->loggingService->logError('사용 빈도별 템플릿 분포 조회 중 오류 발생', [
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('사용 빈도별 템플릿 분포 조회 중 오류가 발생했습니다.', [
                'type' => 'distribution_failed',
            ], $e);
        }
    }

    /**
     * 사용되지 않은 템플릿 조회
     *
     * @return Collection 사용되지 않은 템플릿 목록
     *
     * @throws BusinessException
     */
    public function getUnusedTemplates(): Collection
    {
        try {
            $templates = $this->templateRepository->getUnusedTemplates();

            $this->loggingService->logInfo('사용되지 않은 템플릿 조회 완료', [
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (Throwable $e) {
            $this->loggingService->logError('사용되지 않은 템플릿 조회 중 오류 발생', [
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('사용되지 않은 템플릿 조회 중 오류가 발생했습니다.', [
                'type' => 'unused_failed',
            ], $e);
        }
    }

    /**
     * 사용 횟수 범위별 템플릿 조회
     *
     * @param  int  $minUsage  최소 사용 횟수
     * @param  int|null  $maxUsage  최대 사용 횟수
     * @return Collection 사용 횟수 범위별 템플릿 목록
     *
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getTemplatesByUsageRange(int $minUsage = 0, ?int $maxUsage = null): Collection
    {
        try {
            // 입력 값 검증
            if ($minUsage < 0) {
                throw new ValidationException('최소 사용 횟수는 0 이상이어야 합니다.', [
                    'min_usage' => $minUsage,
                ]);
            }

            if ($maxUsage !== null && $maxUsage < 0) {
                throw new ValidationException('최대 사용 횟수는 0 이상이어야 합니다.', [
                    'max_usage' => $maxUsage,
                ]);
            }

            if ($maxUsage !== null && $minUsage > $maxUsage) {
                throw new ValidationException('최소 사용 횟수는 최대 사용 횟수보다 클 수 없습니다.', [
                    'min_usage' => $minUsage,
                    'max_usage' => $maxUsage,
                ]);
            }

            $templates = $this->templateRepository->getTemplatesByUsageRange($minUsage, $maxUsage);

            $this->loggingService->logInfo('사용 횟수 범위별 템플릿 조회 완료', [
                'min_usage' => $minUsage,
                'max_usage' => $maxUsage,
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (ValidationException $e) {
            throw $e;
        } catch (Throwable $e) {
            $this->loggingService->logError('사용 횟수 범위별 템플릿 조회 중 오류 발생', [
                'min_usage' => $minUsage,
                'max_usage' => $maxUsage,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('사용 횟수 범위별 템플릿 조회 중 오류가 발생했습니다.', [
                'type' => 'usage_range_failed',
            ], $e);
        }
    }

    /**
     * 인기 템플릿 조회 (사용 횟수 상위)
     *
     * @param  int  $limit  조회할 개수
     * @return Collection 인기 템플릿 목록
     *
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getPopularTemplates(int $limit = 10): Collection
    {
        try {
            // 입력 값 검증
            if ($limit <= 0 || $limit > 100) {
                throw new ValidationException('조회 개수는 1~100 사이여야 합니다.', [
                    'limit' => $limit,
                ]);
            }

            $templates = $this->templateRepository->getPopularTemplates($limit);

            $this->loggingService->logInfo('인기 템플릿 조회 완료', [
                'limit' => $limit,
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (ValidationException $e) {
            throw $e;
        } catch (Throwable $e) {
            $this->loggingService->logError('인기 템플릿 조회 중 오류 발생', [
                'limit' => $limit,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('인기 템플릿 조회 중 오류가 발생했습니다.', [
                'type' => 'popular_failed',
            ], $e);
        }
    }

    /**
     * 최근 생성된 템플릿 조회
     *
     * @param  int  $limit  조회할 개수
     * @return Collection 최근 생성된 템플릿 목록
     *
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getRecentTemplates(int $limit = 10): Collection
    {
        try {
            // 입력 값 검증
            if ($limit <= 0 || $limit > 100) {
                throw new ValidationException('조회 개수는 1~100 사이여야 합니다.', [
                    'limit' => $limit,
                ]);
            }

            $templates = $this->templateRepository->getRecentTemplates($limit);

            $this->loggingService->logInfo('최근 템플릿 조회 완료', [
                'limit' => $limit,
                'result_count' => $templates->count(),
            ]);

            return $templates;

        } catch (ValidationException $e) {
            throw $e;
        } catch (Throwable $e) {
            $this->loggingService->logError('최근 템플릿 조회 중 오류 발생', [
                'limit' => $limit,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('최근 템플릿 조회 중 오류가 발생했습니다.', [
                'type' => 'recent_failed',
            ], $e);
        }
    }

    /**
     * 템플릿 사용 통계 요약 정보 조회
     *
     * @return array 통계 요약 정보
     *
     * @throws BusinessException
     */
    public function getStatisticsSummary(): array
    {
        try {
            // 기본 통계 정보
            $basicStats = $this->getUsageStatistics();

            // 우선순위별 개수
            $priorityCounts = $this->getCountByPriority();

            // 사용 분포
            $usageDistribution = $this->getUsageDistribution();

            // 사용되지 않은 템플릿 개수
            $unusedCount = $this->getUnusedTemplates()->count();

            $summary = [
                'basic_statistics' => $basicStats,
                'priority_distribution' => $priorityCounts,
                'usage_distribution' => $usageDistribution,
                'unused_templates_count' => $unusedCount,
                'generated_at' => now()->toISOString(),
            ];

            $this->loggingService->logInfo('템플릿 통계 요약 정보 조회 완료', [
                'total_templates' => $basicStats['total_templates'] ?? 0,
                'unused_count' => $unusedCount,
                'priority_types' => count($priorityCounts),
                'distribution_ranges' => count($usageDistribution),
            ]);

            return $summary;

        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 통계 요약 정보 조회 중 오류 발생', [
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('템플릿 통계 요약 정보 조회 중 오류가 발생했습니다.', [
                'type' => 'summary_failed',
            ], $e);
        }
    }

    /**
     * 템플릿 사용 트렌드 분석
     *
     * @param  int  $days  분석할 일수 (기본 30일)
     * @return array 사용 트렌드 정보
     *
     * @throws BusinessException
     * @throws ValidationException
     */
    public function getUsageTrends(int $days = 30): array
    {
        try {
            // 입력 값 검증
            if ($days <= 0 || $days > 365) {
                throw new ValidationException('분석 기간은 1~365일 사이여야 합니다.', [
                    'days' => $days,
                ]);
            }

            // 현재는 기본적인 통계만 제공하고, 향후 확장 가능
            $currentStats = $this->getUsageStatistics();
            $popularTemplates = $this->getPopularTemplates(5);
            $recentTemplates = $this->getRecentTemplates(5);

            $trends = [
                'analysis_period_days' => $days,
                'current_statistics' => $currentStats,
                'top_popular_templates' => $popularTemplates->map(function ($template) {
                    return [
                        'id' => $template->id,
                        'name' => $template->name,
                        'usage_count' => $template->usage_count,
                        'priority' => $template->priority,
                    ];
                })->toArray(),
                'recent_templates' => $recentTemplates->map(function ($template) {
                    return [
                        'id' => $template->id,
                        'name' => $template->name,
                        'created_at' => $template->created_at->toISOString(),
                        'usage_count' => $template->usage_count,
                    ];
                })->toArray(),
                'generated_at' => now()->toISOString(),
            ];

            $this->loggingService->logInfo('템플릿 사용 트렌드 분석 완료', [
                'analysis_days' => $days,
                'popular_count' => count($trends['top_popular_templates']),
                'recent_count' => count($trends['recent_templates']),
            ]);

            return $trends;

        } catch (ValidationException $e) {
            throw $e;
        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 사용 트렌드 분석 중 오류 발생', [
                'days' => $days,
                'error' => $e->getMessage(),
            ]);

            throw new BusinessException('템플릿 사용 트렌드 분석 중 오류가 발생했습니다.', [
                'type' => 'trends_failed',
            ], $e);
        }
    }
}
