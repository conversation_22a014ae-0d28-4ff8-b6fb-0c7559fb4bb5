<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Facades\DB;

class SchedulerService
{
    public function markDuplicateProducts(): void
    {
        DB::table('products')
            ->where('duplicated', Product::IS_DUPLICATED_N)
            ->whereIn('id', function ($query) {
                $query->select('id')
                    ->from('products as p')
                    ->where('req_id', '!=', 2)
                    ->whereIn('qaid', function ($subQuery) {
                        $subQuery->select('qaid')
                            ->from('products')
                            ->where('status', '!=', 90)
                            ->groupBy('qaid')
                            ->havingRaw('COUNT(*) > 1');
                    });
            })
            ->update(['duplicated' => 'Y']);
    }
}
