<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Facades\Hash;

class UserService
{
    /**
     * 검색 옵션을 이용한 사용자 검색
     * 만약 검색 로직이 복잡해 진다면 UserService 클래스로 이동해서 그 곳에서
     *
     * @return EloquentBuilder|QueryBuilder|User 필터가 적용된 쿼리 빌더 인스턴스를 반환
     */
    public function getList(array $data): EloquentBuilder|QueryBuilder|User
    {
        $query = User::withTrashed();

        if ($data['status'] > 0) {
            $query->where('status', $data['status']);
        }

        if ($data['role']) {
            $query->where('role', $data['role']);
        }

        $keyword = $data['keyword'];
        if (! empty($keyword)) {
            $query->where(function ($query) use ($keyword) {
                $query->where('username', 'like', "%{$keyword}%")
                    ->orWhere('name', 'like', "%{$keyword}%")
                    ->orWhere('email', 'like', "%{$keyword}%");
            });
        }

        return $query->orderBy('status')
            ->orderBy('updated_at', 'desc');
    }

    public function getUserById(int $id): User
    {
        return User::withTrashed()->find($id);
    }

    /**
     * 사용자 생성
     */
    public function storeUser(array $data): void
    {
        $user = new User;

        $user->company_id = 1;
        $user->role = $this->matchRole($data['role']);
        $user->username = $data['username'];
        $user->caps_id = $data['caps_id'] ?? null;
        $user->name = $data['name'];
        $user->email = $data['email'] ?? null;
        $user->cellphone = $data['cellphone'];
        $user->status = $data['status'];
        $user->part = $data['part'] ?? null;
        $user->position = $data['position'] ?? null;
        $user->menu = $data['menu'] ?? null;

        // 비밀번호 설정 (등록시에는 필수)
        $user->password = Hash::make($data['password']);

        $user->save();
    }

    /**
     * 사용자 정보 업데이트<br>
     * soft delete 된 회원 정보도 되돌릴 수 있음<br>
     * 추후 restore() 메서드를 활용 해서 관리자만 처리할 수 있도록 수정할 것
     */
    public function updateUser(int $id, array $data): void
    {
        $user = User::withTrashed()->findOrFail($id);

        // 삭제된 직원을 다시 활성화 시키면
        if ($user->status === User::MEMBER_STATUS_DELETED) {
            $user->status = User::MEMBER_STATUS_ACTIVE;
            $user->deleted_at = null;
        }

        $user->role = $this->matchRole($data['role']);
        $user->username = $data['username'];
        $user->caps_id = $data['caps_id'] ?? null;
        $user->name = $data['name'];
        $user->email = $data['email'];
        $user->cellphone = $data['cellphone'];
        $user->status = $data['status'];
        $user->part = $data['part'] ?? null;
        $user->position = $data['position'] ?? null;
        $user->menu = $data['menu'] ?? null;

        // 비밀번호가 제공된 경우에만 업데이트
        if (isset($data['password'])) {
            $user->password = Hash::make($data['password']);
        }

        $user->save();
    }

    /**
     * 사용자 삭제(soft deletion)
     */
    public function deleteUser(int $id): void
    {
        $user = User::findOrFail($id);
        $user->status = User::MEMBER_STATUS_DELETED;
        $user->save();

        // soft delete
        $user->delete();
    }

    /**
     * 사용자 복구(활성)
     */
    public function restoreUser(int $id): void
    {
        /**
         * @var User $user
         */
        $user = User::withTrashed()->findOrFail($id);
        $user->restore();

        // 상태를 활성화 상태로 변경 (필요한 경우)
        $user->status = User::MEMBER_STATUS_ACTIVE;
        $user->save();
    }

    private function matchRole($role): string
    {
        return match (strtolower($role)) {
            'employee' => User::ROLE_EMPLOYEE,
            'receiving-manager' => User::ROLE_RECEIVING_MANAGER,
            'pallet-manager' => User::ROLE_PALLET_MANAGER,
            'carryout-manager' => User::ROLE_CARRYOUT_MANAGER,
            'admin' => User::ROLE_ADMIN,
            'super-admin' => User::ROLE_SUPER_ADMIN,
            default => User::ROLE_GUEST,
        };
    }
}
