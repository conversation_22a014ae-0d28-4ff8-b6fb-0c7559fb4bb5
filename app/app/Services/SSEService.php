<?php

namespace App\Services;

use App\Models\NotificationRecipient;
use Carbon\Carbon;
use Illuminate\Support\Collection;

/**
 * SSE(Server-Sent Events) 서비스
 *
 * 실시간 알림 전달을 위한 SSE 관련 기능을 제공합니다.
 */
class SSEService
{
    /**
     * 사용자의 미읽음 알림 조회
     *
     * @param int $userId 사용자 ID
     * @return array 미읽음 알림 목록
     */
    public function getPendingNotifications(int $userId): array
    {
        return NotificationRecipient::where('user_id', $userId)
            ->whereNull('delivered_at')
            ->with(['notification' => function ($query) {
                $query->select('id', 'title', 'content', 'priority', 'action_url', 'sent_at');
            }])
            ->get()
            ->map(function ($recipient) {
                return [
                    'id' => $recipient->notification->id,
                    'title' => $recipient->notification->title,
                    'content' => $recipient->notification->content,
                    'priority' => $recipient->notification->priority ?? 'normal',
                    'action_url' => $recipient->notification->action_url,
                    'sent_at' => $recipient->notification->sent_at ?
                        Carbon::parse($recipient->notification->sent_at)->toISOString() : null,
                    'recipient_id' => $recipient->id
                ];
            })
            ->toArray();
    }

    /**
     * 알림 전달 완료 처리
     *
     * @param int $userId 사용자 ID
     * @param string $notificationId 알림 ID
     * @return bool 처리 성공 여부
     */
    public function markAsDelivered(int $userId, string $notificationId): bool
    {
        $updated = NotificationRecipient::where('user_id', $userId)
            ->where('notification_id', $notificationId)
            ->whereNull('delivered_at')
            ->update(['delivered_at' => now()]);

        return $updated > 0;
    }

    /**
     * 여러 알림을 한번에 전달 완료 처리
     *
     * @param int $userId 사용자 ID
     * @param array $notificationIds 알림 ID 배열
     * @return int 처리된 알림 수
     */
    public function markMultipleAsDelivered(int $userId, array $notificationIds): int
    {
        return NotificationRecipient::where('user_id', $userId)
            ->whereIn('notification_id', $notificationIds)
            ->whereNull('delivered_at')
            ->update(['delivered_at' => now()]);
    }

    /**
     * 알림 읽음 처리
     *
     * @param int $userId 사용자 ID
     * @param string $notificationId 알림 ID
     * @return bool 처리 성공 여부
     */
    public function markAsRead(int $userId, string $notificationId): bool
    {
        $updated = NotificationRecipient::where('user_id', $userId)
            ->where('notification_id', $notificationId)
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        return $updated > 0;
    }

    /**
     * 사용자의 미읽음 알림 개수 조회
     *
     * @param int $userId 사용자 ID
     * @return int 미읽음 알림 개수
     */
    public function getUnreadCount(int $userId): int
    {
        return NotificationRecipient::where('user_id', $userId)
            ->whereNull('delivered_at')
            ->count();
    }

    /**
     * 특정 사용자에게 새로운 알림이 있는지 확인
     *
     * @param int $userId 사용자 ID
     * @param string|null $lastCheckTime 마지막 확인 시간 (ISO 8601 형식)
     * @return bool 새로운 알림 존재 여부
     */
    public function hasNewNotifications(int $userId, ?string $lastCheckTime = null): bool
    {
        $query = NotificationRecipient::where('user_id', $userId)
            ->whereNull('delivered_at');

        if ($lastCheckTime) {
            $query->where('created_at', '>', $lastCheckTime);
        }

        return $query->exists();
    }

    /**
     * SSE 이벤트 데이터 포맷팅
     *
     * @param string $type 이벤트 타입
     * @param array $data 이벤트 데이터
     * @return string 포맷된 SSE 데이터
     */
    public function formatSSEData(string $type, array $data): string
    {
        $eventData = [
            'type' => $type,
            'timestamp' => now()->toISOString(),
            'data' => $data
        ];

        return "data: " . json_encode($eventData, JSON_UNESCAPED_UNICODE) . "\n\n";
    }

    /**
     * 하트비트 이벤트 생성
     *
     * @return string 하트비트 SSE 데이터
     */
    public function createHeartbeat(): string
    {
        return $this->formatSSEData('heartbeat', [
            'message' => 'connection alive'
        ]);
    }

    /**
     * 알림 이벤트 생성
     *
     * @param array $notifications 알림 데이터 배열
     * @return string 알림 SSE 데이터
     */
    public function createNotificationEvent(array $notifications): string
    {
        return $this->formatSSEData('notification', [
            'notifications' => $notifications,
            'count' => count($notifications)
        ]);
    }
}
