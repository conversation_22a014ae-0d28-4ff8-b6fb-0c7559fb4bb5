<?php

namespace App\Services;

use App\Exceptions\BusinessException;
use App\Exceptions\ValidationException;
use App\Models\NotificationTemplate;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * 알림 템플릿 관리 서비스 (파사드)
 *
 * 리팩토링된 템플릿 서비스들의 파사드 역할을 수행하며,
 * 기존 API 호환성을 유지하면서 각 기능을 적절한 서비스로 위임합니다.
 *
 * 이 클래스는 파사드 패턴을 사용하여 다음 서비스들을 조정합니다:
 * - TemplateCrudService: 템플릿 CRUD 작업
 * - TemplateSearchService: 템플릿 검색 및 필터링
 * - TemplateStatisticsService: 템플릿 사용 통계
 * - TemplatePermissionService: 템플릿 권한 관리
 *
 * <AUTHOR> Development Team
 *
 * @since 1.0.0
 *
 * @version 2.0.0 (리팩토링 버전)
 */
class TemplateService
{
    /**
     * 생성자
     *
     * @param  TemplateCrudService  $crudService  CRUD 서비스
     * @param  TemplateSearchService  $searchService  검색 서비스
     * @param  TemplateStatisticsService  $statisticsService  통계 서비스
     * @param  TemplatePermissionService  $permissionService  권한 서비스
     */
    public function __construct(
        private TemplateCrudService $crudService,
        private TemplateSearchService $searchService,
        private TemplateStatisticsService $statisticsService,
        private TemplatePermissionService $permissionService
    ) {}

    /**
     * 템플릿 생성
     *
     * @param  int  $userId  생성자 ID
     * @param  array  $templateData  템플릿 데이터
     * @return NotificationTemplate 생성된 템플릿
     *
     * @throws ValidationException 유효성 검증 실패 시
     * @throws BusinessException 비즈니스 규칙 위반 시
     */
    public function createTemplate(int $userId, array $templateData): NotificationTemplate
    {
        return $this->crudService->create($userId, $templateData);
    }

    /**
     * 템플릿 목록 조회 (페이지네이션)
     *
     * @param  int  $userId  사용자 ID
     * @param  array  $filters  필터 조건
     * @return LengthAwarePaginator 페이지네이션된 템플릿 목록
     *
     * @throws ValidationException 유효성 검증 실패 시
     * @throws BusinessException 비즈니스 규칙 위반 시
     */
    public function getTemplates(int $userId, array $filters = []): LengthAwarePaginator
    {
        $page = $filters['page'] ?? 1;

        return $this->searchService->getTemplates($userId, $page, $filters);
    }

    /**
     * 특정 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $templateId  템플릿 ID
     * @return NotificationTemplate 템플릿 정보
     *
     * @throws BusinessException 템플릿을 찾을 수 없거나 권한이 없는 경우
     */
    public function getTemplateById(int $userId, int $templateId): NotificationTemplate
    {
        return $this->crudService->findById($templateId);
    }

    /**
     * 템플릿 수정
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $templateId  템플릿 ID
     * @param  array  $templateData  수정할 템플릿 데이터
     * @return NotificationTemplate 수정된 템플릿
     *
     * @throws ValidationException 유효성 검증 실패 시
     * @throws BusinessException 비즈니스 규칙 위반 시
     */
    public function updateTemplate(int $userId, int $templateId, array $templateData): NotificationTemplate
    {
        return $this->crudService->update($userId, $templateId, $templateData);
    }

    /**
     * 템플릿 삭제
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $templateId  템플릿 ID
     * @return bool 삭제 성공 여부
     *
     * @throws BusinessException 템플릿을 찾을 수 없거나 삭제 실패 시
     */
    public function deleteTemplate(int $userId, int $templateId): bool
    {
        return $this->crudService->delete($userId, $templateId);
    }

    /**
     * 모든 템플릿 조회 (페이지네이션 없음)
     *
     * @param  int  $userId  사용자 ID
     * @return Collection 모든 템플릿 목록
     *
     * @throws BusinessException 조회 실패 시
     */
    public function getAllTemplates(int $userId): Collection
    {
        return $this->searchService->getAllTemplates($userId);
    }

    /**
     * 사용 횟수 증가
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $templateId  템플릿 ID
     * @return array 업데이트된 템플릿과 통계 정보
     *
     * @throws BusinessException 템플릿을 찾을 수 없거나 증가 실패 시
     */
    public function incrementUsage(int $userId, int $templateId): array
    {
        // 권한 확인
        if (! $this->permissionService->canUse($userId, $templateId)) {
            throw new BusinessException('템플릿 사용 권한이 없습니다.');
        }

        // 사용 횟수 증가
        $success = $this->statisticsService->incrementUsage($templateId);
        if (! $success) {
            throw new BusinessException('사용 횟수 증가에 실패했습니다.');
        }

        // 업데이트된 템플릿과 통계 조회
        $template = $this->crudService->findById($templateId);
        $statistics = $this->statisticsService->getUsageStatistics();

        return [
            'template' => $template,
            'statistics' => $statistics,
        ];
    }

    /**
     * 템플릿 검색
     *
     * @param  int  $userId  사용자 ID
     * @param  string  $search  검색어
     * @param  array  $filters  필터 조건
     * @return Collection 검색된 템플릿 목록
     *
     * @throws BusinessException 검색 실패 시
     */
    public function searchTemplates(int $userId, string $search, array $filters = []): Collection
    {
        return $this->searchService->search($userId, $search, $filters);
    }

    /**
     * 우선순위별 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @param  string  $priority  우선순위
     * @return Collection 템플릿 목록
     *
     * @throws BusinessException 조회 실패 시
     */
    public function getTemplatesByPriority(int $userId, string $priority): Collection
    {
        return $this->searchService->getByPriority($userId, $priority);
    }

    /**
     * 사용 횟수 기준 정렬된 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @param  string  $direction  정렬 방향 (asc, desc)
     * @return Collection 정렬된 템플릿 목록
     *
     * @throws BusinessException 조회 실패 시
     */
    public function getTemplatesOrderedByUsage(int $userId, string $direction = 'desc'): Collection
    {
        return $this->searchService->getOrderedByUsage($userId, $direction);
    }

    /**
     * 인기 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $limit  조회할 개수
     * @return Collection 인기 템플릿 목록
     *
     * @throws BusinessException 조회 실패 시
     */
    public function getPopularTemplates(int $userId, int $limit = 10): Collection
    {
        return $this->searchService->getPopular($userId, $limit);
    }

    /**
     * 최근 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $limit  조회할 개수
     * @return Collection 최근 템플릿 목록
     *
     * @throws BusinessException 조회 실패 시
     */
    public function getRecentTemplates(int $userId, int $limit = 10): Collection
    {
        return $this->searchService->getRecent($userId, $limit);
    }

    /**
     * 사용되지 않은 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @return Collection 미사용 템플릿 목록
     *
     * @throws BusinessException 조회 실패 시
     */
    public function getUnusedTemplates(int $userId): Collection
    {
        return $this->searchService->getUnused($userId);
    }

    /**
     * 사용 통계 조회
     *
     * @param  int  $userId  사용자 ID
     * @return array 사용 통계 정보
     *
     * @throws BusinessException 조회 실패 시
     */
    public function getUsageStatistics(int $userId): array
    {
        return $this->statisticsService->getUsageStatistics();
    }

    /**
     * 우선순위별 템플릿 개수 조회
     *
     * @param  int  $userId  사용자 ID
     * @return array 우선순위별 개수
     *
     * @throws BusinessException
     */
    public function getCountByPriority(int $userId): array
    {
        return $this->statisticsService->getCountByPriority();
    }

    /**
     * 사용 분포 조회
     *
     * @param  int  $userId  사용자 ID
     * @return array 사용 분포 정보
     *
     * @throws BusinessException
     */
    public function getUsageDistribution(int $userId): array
    {
        return $this->statisticsService->getUsageDistribution();
    }

    /**
     * 사용 횟수 범위별 템플릿 조회
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $minUsage  최소 사용 횟수
     * @param  int  $maxUsage  최대 사용 횟수
     * @return Collection 범위 내 템플릿 목록
     *
     * @throws ValidationException 범위 검증 실패 시
     * @throws BusinessException 조회 실패 시
     */
    public function getTemplatesByUsageRange(int $userId, int $minUsage, int $maxUsage): Collection
    {
        if ($minUsage > $maxUsage) {
            throw new ValidationException('최소 사용 횟수가 최대 사용 횟수보다 클 수 없습니다.');
        }

        return $this->searchService->getByUsageRange($userId, $minUsage, $maxUsage);
    }

    /**
     * 템플릿 권한 확인
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $templateId  템플릿 ID
     * @param  string  $action  액션 (read, update, delete)
     * @return bool 권한 여부
     */
    public function hasPermissionForTemplate(int $userId, int $templateId, string $action): bool
    {
        return match ($action) {
            'read' => $this->permissionService->canRead($userId, $templateId),
            'update' => $this->permissionService->canUpdate($userId, $templateId),
            'delete' => $this->permissionService->canDelete($userId, $templateId),
            default => false,
        };
    }
}
