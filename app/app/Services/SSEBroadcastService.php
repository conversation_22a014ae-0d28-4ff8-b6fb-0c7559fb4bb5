<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\NotificationRecipient;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * SSE 브로드캐스트 서비스
 *
 * 실시간 알림을 SSE 연결된 클라이언트들에게 전달하는 서비스입니다.
 * Redis를 사용하여 연결된 클라이언트 정보를 관리합니다.
 */
class SSEBroadcastService
{
    private const SSE_CONNECTIONS_KEY = 'sse_connections';
    private const SSE_PENDING_NOTIFICATIONS_KEY = 'sse_pending_notifications';
    private const CONNECTION_TTL = 3600; // 1시간

    /**
     * 알림을 SSE 연결된 클라이언트들에게 브로드캐스트
     *
     * @param Notification $notification
     * @param array $recipientIds
     * @return void
     * @throws Throwable
     */
    public function broadcastNotification(Notification $notification, array $recipientIds): void
    {
        try {
            // 알림 데이터 준비
            $notificationData = $this->prepareNotificationData($notification);

            foreach ($recipientIds as $userId) {
                // 사용자가 현재 SSE에 연결되어 있는지 확인
                if ($this->isUserConnected($userId)) {
                    // 연결된 사용자에게 즉시 전송
                    $this->sendToConnectedUser($userId, $notificationData);
                } else {
                    // 연결되지 않은 사용자를 위해 대기열에 저장
                    $this->queueNotificationForUser($userId, $notificationData);
                }
            }

        } catch (Throwable $e) {
            Log::error('SSE 브로드캐스트 중 오류 발생', [
                'notification_id' => $notification->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 사용자 SSE 연결 등록
     *
     * @param int $userId
     * @param string $connectionId
     * @return void
     */
    public function registerConnection(int $userId, string $connectionId): void
    {
        $connections = Cache::get(self::SSE_CONNECTIONS_KEY, []);
        $connections[$userId] = [
            'connection_id' => $connectionId,
            'connected_at' => now()->toISOString(),
            'last_heartbeat' => now()->toISOString()
        ];

        Cache::put(self::SSE_CONNECTIONS_KEY, $connections, self::CONNECTION_TTL);

        Log::info('SSE 연결 등록', [
            'user_id' => $userId,
            'connection_id' => $connectionId
        ]);
    }

    /**
     * 사용자 SSE 연결 해제
     *
     * @param int $userId
     * @return void
     */
    public function unregisterConnection(int $userId): void
    {
        $connections = Cache::get(self::SSE_CONNECTIONS_KEY, []);
        unset($connections[$userId]);
        Cache::put(self::SSE_CONNECTIONS_KEY, $connections, self::CONNECTION_TTL);

        Log::info('SSE 연결 해제', ['user_id' => $userId]);
    }

    /**
     * 사용자 하트비트 업데이트
     *
     * @param int $userId
     * @return void
     */
    public function updateHeartbeat(int $userId): void
    {
        $connections = Cache::get(self::SSE_CONNECTIONS_KEY, []);

        if (isset($connections[$userId])) {
            $connections[$userId]['last_heartbeat'] = now()->toISOString();
            Cache::put(self::SSE_CONNECTIONS_KEY, $connections, self::CONNECTION_TTL);
        }
    }

    /**
     * 사용자가 현재 연결되어 있는지 확인
     *
     * @param int $userId
     * @return bool
     */
    public function isUserConnected(int $userId): bool
    {
        $connections = Cache::get(self::SSE_CONNECTIONS_KEY, []);

        if (!isset($connections[$userId])) {
            return false;
        }

        // 마지막 하트비트가 5분 이상 지났으면 연결 해제된 것으로 간주
        $lastHeartbeat = $connections[$userId]['last_heartbeat'];
        $heartbeatTime = \Carbon\Carbon::parse($lastHeartbeat);

        if ($heartbeatTime->diffInMinutes(now()) > 5) {
            $this->unregisterConnection($userId);
            return false;
        }

        return true;
    }

    /**
     * 연결된 사용자에게 알림 전송
     *
     * @param int $userId
     * @param array $notificationData
     * @return void
     */
    private function sendToConnectedUser(int $userId, array $notificationData): void
    {
        // 실제 SSE 전송은 SSEService에서 처리
        // 여기서는 Redis Pub/Sub 또는 다른 메커니즘을 사용할 수 있습니다.

        // Redis Pub/Sub을 사용한 예시
        $channel = "sse_user_{$userId}";
        $message = json_encode([
            'type' => 'notification',
            'data' => $notificationData
        ]);

        try {
            // Redis publish를 통해 SSE 연결에 알림 전송
            \Illuminate\Support\Facades\Redis::publish($channel, $message);

            // 전송 완료 표시
            $this->markNotificationAsDelivered($userId, $notificationData['id']);

            Log::info('SSE 알림 전송 완료', [
                'user_id' => $userId,
                'notification_id' => $notificationData['id']
            ]);

        } catch (Throwable $e) {
            Log::error('SSE 알림 전송 실패', [
                'user_id' => $userId,
                'notification_id' => $notificationData['id'],
                'error' => $e->getMessage()
            ]);

            // 전송 실패 시 대기열에 추가
            $this->queueNotificationForUser($userId, $notificationData);
        }
    }

    /**
     * 연결되지 않은 사용자를 위해 알림을 대기열에 저장
     *
     * @param int $userId
     * @param array $notificationData
     * @return void
     */
    private function queueNotificationForUser(int $userId, array $notificationData): void
    {
        $queueKey = self::SSE_PENDING_NOTIFICATIONS_KEY . "_{$userId}";
        $pendingNotifications = Cache::get($queueKey, []);

        $pendingNotifications[] = $notificationData;

        // 최대 50개까지만 저장 (메모리 절약)
        if (count($pendingNotifications) > 50) {
            $pendingNotifications = array_slice($pendingNotifications, -50);
        }

        Cache::put($queueKey, $pendingNotifications, self::CONNECTION_TTL);

        Log::info('SSE 알림 대기열 저장', [
            'user_id' => $userId,
            'notification_id' => $notificationData['id']
        ]);
    }

    /**
     * 사용자의 대기 중인 알림 조회
     *
     * @param int $userId
     * @return array
     */
    public function getPendingNotifications(int $userId): array
    {
        $queueKey = self::SSE_PENDING_NOTIFICATIONS_KEY . "_{$userId}";
        $notifications = Cache::get($queueKey, []);

        // 조회 후 대기열 비우기
        Cache::forget($queueKey);

        return $notifications;
    }

    /**
     * 알림 데이터 준비
     *
     * @param Notification $notification
     * @return array
     */
    private function prepareNotificationData(Notification $notification): array
    {
        return [
            'id' => $notification->id,
            'title' => $notification->title,
            'content' => $notification->content,
            'priority' => $notification->priority,
            'action_url' => $notification->action_url,
            'sent_at' => $notification->sent_at->toISOString(),
            'sender' => [
                'id' => $notification->sender->id,
                'name' => $notification->sender->name
            ]
        ];
    }

    /**
     * 알림 전송 완료 표시
     *
     * @param int $userId
     * @param string $notificationId
     * @return void
     */
    private function markNotificationAsDelivered(int $userId, string $notificationId): void
    {
        try {
            NotificationRecipient::where('notification_id', $notificationId)
                ->where('user_id', $userId)
                ->update(['delivered_at' => now()]);

        } catch (Throwable $e) {
            Log::error('알림 전송 완료 표시 실패', [
                'user_id' => $userId,
                'notification_id' => $notificationId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 연결된 사용자 목록 조회
     *
     * @return array
     */
    public function getConnectedUsers(): array
    {
        $connections = Cache::get(self::SSE_CONNECTIONS_KEY, []);
        $connectedUsers = [];

        foreach ($connections as $userId => $connection) {
            if ($this->isUserConnected($userId)) {
                $connectedUsers[] = [
                    'user_id' => $userId,
                    'connected_at' => $connection['connected_at'],
                    'last_heartbeat' => $connection['last_heartbeat']
                ];
            }
        }

        return $connectedUsers;
    }
}
