<?php

namespace App\Services;

use App\Exceptions\ValidationException;
use App\Models\NotificationTemplate;

/**
 * 템플릿 유효성 검증 서비스
 *
 * 알림 템플릿 데이터와 검색 필터의 유효성 검증 및 데이터 정제를 담당합니다.
 * 보안과 데이터 무결성을 위해 모든 입력 데이터를 철저히 검증하고 정제합니다.
 *
 * 주요 기능:
 * - 템플릿 생성/수정 데이터 유효성 검증
 * - 검색 필터 조건 유효성 검증
 * - 입력 데이터 정제 및 보안 처리 (XSS 방지, HTML 이스케이프)
 * - 날짜 범위, 숫자 범위 검증
 * - 정렬 옵션 및 페이지네이션 파라미터 검증
 *
 * 검증 규칙:
 * - 템플릿명: 필수, 1-100자, 중복 불가
 * - 템플릿 내용: 필수, 최대 1000자
 * - 우선순위: 정의된 값 중 선택
 * - 검색어: 최대 100자
 * - 사용 횟수 범위: 0 이상의 정수
 *
 * <AUTHOR> Development Team
 *
 * @since 2.0.0
 */
class TemplateValidationService
{
    /**
     * 템플릿 생성을 위한 데이터 유효성 검증
     *
     * @param  array  $data  검증할 템플릿 데이터
     * @return array 검증된 데이터
     *
     * @throws ValidationException 유효성 검증 실패 시
     */
    public function validateForCreate(array $data): array
    {
        $errors = NotificationTemplate::validateForCreate($data);

        if (! empty($errors)) {
            throw new ValidationException('템플릿 생성 데이터 유효성 검증에 실패했습니다.', [
                'validation_errors' => $errors,
                'operation' => 'create',
                'provided_data' => $data,
            ]);
        }

        return $this->sanitizeTemplateData($data);
    }

    /**
     * 템플릿 수정을 위한 데이터 유효성 검증
     *
     * @param  array  $data  검증할 템플릿 데이터
     * @param  int  $templateId  수정할 템플릿 ID
     * @return array 검증된 데이터
     *
     * @throws ValidationException 유효성 검증 실패 시
     */
    public function validateForUpdate(array $data, int $templateId): array
    {
        $errors = NotificationTemplate::validateForUpdate($data, $templateId);

        if (! empty($errors)) {
            throw new ValidationException('템플릿 수정 데이터 유효성 검증에 실패했습니다.', [
                'validation_errors' => $errors,
                'operation' => 'update',
                'template_id' => $templateId,
                'provided_data' => $data,
            ]);
        }

        return $this->sanitizeTemplateData($data);
    }

    /**
     * 필터 조건 유효성 검증 및 정제
     *
     * @param  array  $filters  원본 필터 조건
     * @return array 검증된 필터 조건
     *
     * @throws ValidationException 필터 조건이 유효하지 않은 경우
     */
    public function validateFilters(array $filters): array
    {
        $validatedFilters = [];
        $errors = [];

        // 검색어 검증
        if (isset($filters['search'])) {
            $searchResult = $this->validateSearchTerm($filters['search']);
            if ($searchResult['error']) {
                $errors[] = $searchResult['error'];
            } elseif ($searchResult['value'] !== null) {
                $validatedFilters['search'] = $searchResult['value'];
            }
        }

        // 우선순위 필터 검증
        if (isset($filters['priority'])) {
            $priorityResult = $this->validatePriority($filters['priority']);
            if ($priorityResult['error']) {
                $errors[] = $priorityResult['error'];
            } else {
                $validatedFilters['priority'] = $priorityResult['value'];
            }
        }

        // 생성자 필터 검증
        if (isset($filters['created_by'])) {
            $createdByResult = $this->validateCreatedBy($filters['created_by']);
            if ($createdByResult['error']) {
                $errors[] = $createdByResult['error'];
            } else {
                $validatedFilters['created_by'] = $createdByResult['value'];
            }
        }

        // 사용 횟수 범위 필터 검증
        $usageRangeResult = $this->validateUsageRange($filters);
        if ($usageRangeResult['errors']) {
            $errors = array_merge($errors, $usageRangeResult['errors']);
        } else {
            if (isset($usageRangeResult['min_usage'])) {
                $validatedFilters['min_usage'] = $usageRangeResult['min_usage'];
            }
            if (isset($usageRangeResult['max_usage'])) {
                $validatedFilters['max_usage'] = $usageRangeResult['max_usage'];
            }
        }

        // 생성일 범위 필터 검증
        $dateRangeResult = $this->validateDateRange($filters);
        if ($dateRangeResult['errors']) {
            $errors = array_merge($errors, $dateRangeResult['errors']);
        } else {
            if (isset($dateRangeResult['created_from'])) {
                $validatedFilters['created_from'] = $dateRangeResult['created_from'];
            }
            if (isset($dateRangeResult['created_to'])) {
                $validatedFilters['created_to'] = $dateRangeResult['created_to'];
            }
        }

        // 정렬 옵션 검증
        $sortResult = $this->validateSortOptions($filters);
        if ($sortResult['errors']) {
            $errors = array_merge($errors, $sortResult['errors']);
        } else {
            if (isset($sortResult['sortBy'])) {
                $validatedFilters['sortBy'] = $sortResult['sortBy'];
            }
            if (isset($sortResult['sortDirection'])) {
                $validatedFilters['sortDirection'] = $sortResult['sortDirection'];
            }
        }

        // 페이지 크기 검증
        if (isset($filters['per_page'])) {
            $perPageResult = $this->validatePerPage($filters['per_page']);
            if ($perPageResult['error']) {
                $errors[] = $perPageResult['error'];
            }
            // per_page는 별도로 처리되므로 validatedFilters에 추가하지 않음
        }

        if (! empty($errors)) {
            throw new ValidationException('필터 조건 검증에 실패했습니다.', [
                'validation_errors' => $errors,
                'provided_filters' => $filters,
            ]);
        }

        return $validatedFilters;
    }

    /**
     * 필터 조건 정제 (검증 후 추가 정제 작업)
     *
     * @param  array  $filters  검증된 필터 조건
     * @return array 정제된 필터 조건
     */
    public function sanitizeFilters(array $filters): array
    {
        $sanitized = [];

        foreach ($filters as $key => $value) {
            switch ($key) {
                case 'search':
                    // 검색어 정제 (앞뒤 공백 제거, 특수문자 이스케이프)
                    $sanitized[$key] = trim(htmlspecialchars($value, ENT_QUOTES, 'UTF-8'));
                    break;
                case 'priority':
                case 'sortBy':
                case 'sortDirection':
                    // 문자열 필터는 그대로 유지
                    $sanitized[$key] = $value;
                    break;
                case 'created_by':
                case 'min_usage':
                case 'max_usage':
                    // 숫자 필터는 정수로 변환
                    $sanitized[$key] = (int) $value;
                    break;
                case 'created_from':
                case 'created_to':
                    // 날짜 필터는 그대로 유지 (이미 검증됨)
                    $sanitized[$key] = $value;
                    break;
                default:
                    // 알 수 없는 필터는 제외
                    break;
            }
        }

        return $sanitized;
    }

    /**
     * 템플릿 데이터 정제
     *
     * @param  array  $data  원본 템플릿 데이터
     * @return array 정제된 템플릿 데이터
     */
    protected function sanitizeTemplateData(array $data): array
    {
        $sanitized = [];

        // 허용된 필드만 처리
        $allowedFields = ['name', 'title', 'content', 'priority', 'created_by'];

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                switch ($field) {
                    case 'name':
                    case 'title':
                    case 'content':
                        // 문자열 필드는 앞뒤 공백 제거 및 HTML 이스케이프
                        $sanitized[$field] = trim(htmlspecialchars($data[$field], ENT_QUOTES, 'UTF-8'));
                        break;
                    case 'priority':
                        // 우선순위는 그대로 유지 (이미 검증됨)
                        $sanitized[$field] = $data[$field];
                        break;
                    case 'created_by':
                        // 생성자 ID는 정수로 변환
                        $sanitized[$field] = (int) $data[$field];
                        break;
                }
            }
        }

        return $sanitized;
    }

    /**
     * 검색어 유효성 검증
     *
     * @param  mixed  $search  검색어
     * @return array 검증 결과 ['value' => mixed, 'error' => string|null]
     */
    protected function validateSearchTerm(mixed $search): array
    {
        // 검색어가 아예 없거나(null) 비어있으면 에러 없이 검색 미적용 처리
        if ($search === null) {
            return ['value' => null, 'error' => null];
        }

        // 문자열이면 트림, 스칼라(숫자/불린)면 문자열로 캐스팅 후 트림
        if (is_string($search)) {
            $search = trim($search);
        } elseif (is_scalar($search)) {
            $search = trim((string) $search);
        } else {
            // 배열/객체 등은 에러로 막지 않고 검색 미적용 처리
            return ['value' => null, 'error' => null];
        }

        if ($search === '') {
            return ['value' => null, 'error' => null];
        }

        if (strlen($search) > 100) {
            return ['value' => null, 'error' => '검색어는 100자를 초과할 수 없습니다.'];
        }

        return ['value' => $search, 'error' => null];
    }

    /**
     * 우선순위 유효성 검증
     *
     * @param  mixed  $priority  우선순위
     * @return array 검증 결과 ['value' => mixed, 'error' => string|null]
     */
    protected function validatePriority($priority): array
    {
        if (! array_key_exists($priority, NotificationTemplate::PRIORITIES)) {
            return ['value' => null, 'error' => '유효하지 않은 우선순위입니다.'];
        }

        return ['value' => $priority, 'error' => null];
    }

    /**
     * 생성자 ID 유효성 검증
     *
     * @param  mixed  $createdBy  생성자 ID
     * @return array 검증 결과 ['value' => mixed, 'error' => string|null]
     */
    protected function validateCreatedBy($createdBy): array
    {
        if (! is_numeric($createdBy) || $createdBy <= 0) {
            return ['value' => null, 'error' => '유효하지 않은 생성자 ID입니다.'];
        }

        return ['value' => (int) $createdBy, 'error' => null];
    }

    /**
     * 사용 횟수 범위 유효성 검증
     *
     * @param  array  $filters  필터 조건
     * @return array 검증 결과 ['min_usage' => int|null, 'max_usage' => int|null, 'errors' => array]
     */
    protected function validateUsageRange(array $filters): array
    {
        $result = ['min_usage' => null, 'max_usage' => null, 'errors' => []];

        // 최소 사용 횟수 검증
        if (isset($filters['min_usage'])) {
            if (! is_numeric($filters['min_usage']) || $filters['min_usage'] < 0) {
                $result['errors'][] = '최소 사용 횟수는 0 이상이어야 합니다.';
            } else {
                $result['min_usage'] = (int) $filters['min_usage'];
            }
        }

        // 최대 사용 횟수 검증
        if (isset($filters['max_usage'])) {
            if (! is_numeric($filters['max_usage']) || $filters['max_usage'] < 0) {
                $result['errors'][] = '최대 사용 횟수는 0 이상이어야 합니다.';
            } else {
                $result['max_usage'] = (int) $filters['max_usage'];
            }
        }

        // 사용 횟수 범위 논리 검증
        if ($result['min_usage'] !== null && $result['max_usage'] !== null) {
            if ($result['min_usage'] > $result['max_usage']) {
                $result['errors'][] = '최소 사용 횟수는 최대 사용 횟수보다 클 수 없습니다.';
            }
        }

        return $result;
    }

    /**
     * 생성일 범위 유효성 검증
     *
     * @param  array  $filters  필터 조건
     * @return array 검증 결과 ['created_from' => string|null, 'created_to' => string|null, 'errors' => array]
     */
    protected function validateDateRange(array $filters): array
    {
        $result = ['created_from' => null, 'created_to' => null, 'errors' => []];

        // 시작 날짜 검증
        if (isset($filters['created_from'])) {
            $timestamp = strtotime($filters['created_from']);
            if ($timestamp === false) {
                $result['errors'][] = '유효하지 않은 시작 날짜 형식입니다.';
            } else {
                $result['created_from'] = date('Y-m-d H:i:s', $timestamp);
            }
        }

        // 종료 날짜 검증
        if (isset($filters['created_to'])) {
            $timestamp = strtotime($filters['created_to']);
            if ($timestamp === false) {
                $result['errors'][] = '유효하지 않은 종료 날짜 형식입니다.';
            } else {
                $result['created_to'] = date('Y-m-d H:i:s', $timestamp);
            }
        }

        return $result;
    }

    /**
     * 정렬 옵션 유효성 검증
     *
     * @param  array  $filters  필터 조건
     * @return array 검증 결과 ['sortBy' => string|null, 'sortDirection' => string|null, 'errors' => array]
     */
    protected function validateSortOptions(array $filters): array
    {
        $result = ['sortBy' => null, 'sortDirection' => null, 'errors' => []];

        // 정렬 필드 검증
        if (isset($filters['sortBy'])) {
            $validSortFields = ['usage_count', 'created_at', 'name', 'priority', 'title'];
            if (! in_array($filters['sortBy'], $validSortFields)) {
                $result['errors'][] = '유효하지 않은 정렬 필드입니다.';
            } else {
                $result['sortBy'] = $filters['sortBy'];
            }
        }

        // 정렬 방향 검증
        if (isset($filters['sortDirection'])) {
            $direction = strtolower($filters['sortDirection']);
            if (! in_array($direction, ['asc', 'desc'])) {
                $result['errors'][] = '정렬 방향은 asc 또는 desc여야 합니다.';
            } else {
                $result['sortDirection'] = $direction;
            }
        }

        return $result;
    }

    /**
     * 페이지 크기 유효성 검증
     *
     * @param  mixed  $perPage  페이지 크기
     * @return array 검증 결과 ['value' => int|null, 'error' => string|null]
     */
    protected function validatePerPage($perPage): array
    {
        $perPage = (int) $perPage;
        if ($perPage < 1 || $perPage > 100) {
            return ['value' => null, 'error' => '페이지 크기는 1~100 사이여야 합니다.'];
        }

        return ['value' => $perPage, 'error' => null];
    }
}
