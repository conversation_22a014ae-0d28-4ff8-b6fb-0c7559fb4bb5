<?php

namespace App\Services;

use App\Models\Process;
use App\Models\RepairGrade;
use App\Models\RepairProcess;
use App\Models\RepairSymptom;
use Illuminate\Database\Eloquent\Model;

class ProcessService
{
    public function fetchTypes(): array
    {
        $items = [];

        // check 유형 가져오기 (RepairSymptom)
        $checkItems = RepairSymptom::select(['code', 'name'])
            ->get()
            ->map(function ($item) {
                return [
                    'code' => $item->code,
                    'name' => $item->name,
                ];
            })
            ->toArray();

        if (! empty($checkItems)) {
            $items['check'] = $checkItems;
        }

        // repair 유형 가져오기 (RepairProcess)
        $repairItems = RepairProcess::select(['code', 'name'])
            ->get()
            ->map(function ($item) {
                return [
                    'code' => $item->code,
                    'name' => $item->name,
                ];
            })
            ->toArray();

        if (! empty($repairItems)) {
            $items['repair'] = $repairItems;
        }

        // grade 유형 가져오기 (RepairGrade)
        $gradeItems = RepairGrade::select(['code', 'name'])
            ->orderBy('order_no')
            ->get()
            ->map(function ($item) {
                return [
                    'code' => $item->code,
                    'name' => $item->name,
                ];
            })
            ->toArray();

        if (! empty($gradeItems)) {
            $items['grade'] = $gradeItems;
        }

        return $items;
    }

    public function getProcessByTypeAndCode(string $type, string $code): ?Model
    {
        return Process::where('type', $type)
            ->where('code', $code)
            ->first();
    }
}
