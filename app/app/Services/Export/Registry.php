<?php

namespace App\Services\Export;

use App\Exports\BaseExport;
use App\Exports\CarryoutExport;
use App\Exports\MonthlyClosingExport;
use App\Exports\PalletExport;
use App\Exports\ProductsExport;
use App\Exports\QaidRePrintExport;
use Illuminate\Http\Request;
use InvalidArgumentException;

/**
 * Export 클래스들을 자동으로 등록하고 관리하는 Registry
 */
class Registry
{
    /**
     * 클래스가 처음 로드될 때 기본적으로 사용할 Export 타입과 클래스를 미리 등록<br>
     * - getRegisteredTypes() 등에서 이 배열을 사용<br>
     * - 이후에 register 메서드를 통해 추가 등록 가능<br>
     * - 배열을 비워 두는 걸 추천한다고 함
     */
    private static array $exports = [];

    private static bool $initialized = false;

    /**
     * Export 클래스들 자동 초기화<br>
     * 실제로 Export 클래스를 사용할 때(즉, create 등 메서드 호출 시) 한 번 더 등록 과정을 거칩니다.
     */
    private static function initialize(): void
    {
        if (self::$initialized) {
            return;
        }

        // Export 클래스들 자동 등록
        self::register('products', ProductsExport::class);
        self::register('pallets', PalletExport::class);
        self::register('carryouts', CarryoutExport::class);
        self::register('qaids', QaidRePrintExport::class);
        self::register('monthlyClosing', MonthlyClosingExport::class);

        self::$initialized = true;
    }

    /**
     * Export 클래스 자동 등록<br>
     * - 클래스가 BaseExport를 상속받았는지 검증(안전성)
     * - 혹시라도 동적으로 추가 등록이 필요한 경우를 대비
     */
    public static function register(string $type, string $exportClass): void
    {
        if (! is_subclass_of($exportClass, BaseExport::class)) {
            throw new InvalidArgumentException("{$exportClass}는 BaseExport를 상속받아야 합니다.");
        }

        self::$exports[$type] = $exportClass;
    }

    /**
     * 등록된 Export 타입 목록 반환
     */
    public static function getRegisteredTypes(): array
    {
        self::initialize();

        return array_keys(self::$exports);
    }

    /**
     * Export 객체 생성
     */
    public static function create(string $type, Request $request): BaseExport
    {
        self::initialize();

        if (! isset(self::$exports[$type])) {
            throw new InvalidArgumentException("지원하지 않는 Export 타입: {$type}");
        }

        $exportClass = self::$exports[$type];

        // fromRequest를 통해 인스턴스 생성
        return $exportClass::fromRequest($request);
    }
}
