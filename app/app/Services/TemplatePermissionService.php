<?php

namespace App\Services;

use App\Exceptions\BusinessException;
use App\Models\NotificationTemplate;
use App\Models\User;
use Throwable;

/**
 * 템플릿 권한 관리 서비스
 *
 * 알림 템플릿에 대한 사용자 권한 검증 및 접근 제어를 담당합니다.
 * 역할 기반 접근 제어(RBAC)를 통해 보안을 강화하고 데이터 무결성을 보장합니다.
 *
 * 권한 정책:
 * - 읽기: 모든 인증된 사용자
 * - 생성: 모든 인증된 사용자
 * - 사용: 모든 인증된 사용자 (사용 횟수 증가)
 * - 수정: 관리자 또는 템플릿 생성자
 * - 삭제: 관리자 또는 템플릿 생성자
 *
 * 사용자 역할:
 * - Super Admin: 모든 권한
 * - Admin: 모든 권한
 * - 일반 사용자: 자신이 생성한 템플릿에 대한 수정/삭제 권한
 *
 * <AUTHOR> Development Team
 *
 * @since 2.0.0
 */
class TemplatePermissionService
{
    /**
     * 템플릿 로깅 서비스
     */
    protected TemplateLoggingService $loggingService;

    /**
     * 생성자
     *
     * @param  TemplateLoggingService  $loggingService  로깅 서비스
     */
    public function __construct(TemplateLoggingService $loggingService)
    {
        $this->loggingService = $loggingService;
    }

    /**
     * 템플릿 생성 권한 확인
     *
     * @param  int  $userId  사용자 ID
     * @return bool 생성 권한 여부
     */
    public function canCreate(int $userId): bool
    {
        try {
            $user = User::find($userId);

            if (! $user) {
                $this->loggingService->logError('사용자를 찾을 수 없음', [
                    'user_id' => $userId,
                    'action' => 'create_permission_check',
                ]);

                return false;
            }

            // 모든 인증된 사용자는 템플릿을 생성할 수 있음
            return true;

        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 생성 권한 확인 중 오류 발생', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 템플릿 읽기 권한 확인
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $templateId  템플릿 ID
     * @return bool 읽기 권한 여부
     */
    public function canRead(int $userId, int $templateId): bool
    {
        try {
            $user = User::find($userId);

            if (! $user) {
                $this->loggingService->logError('사용자를 찾을 수 없음', [
                    'user_id' => $userId,
                    'template_id' => $templateId,
                    'action' => 'read_permission_check',
                ]);

                return false;
            }

            // 모든 인증된 사용자는 템플릿을 읽을 수 있음
            return true;

        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 읽기 권한 확인 중 오류 발생', [
                'user_id' => $userId,
                'template_id' => $templateId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 템플릿 사용 권한 확인
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $templateId  템플릿 ID
     * @return bool 사용 권한 여부
     */
    public function canUse(int $userId, int $templateId): bool
    {
        try {
            $user = User::find($userId);
            $template = NotificationTemplate::find($templateId);

            if (! $user || ! $template) {
                $this->loggingService->logError('사용 권한 확인 실패 - 사용자 또는 템플릿을 찾을 수 없음', [
                    'user_id' => $userId,
                    'template_id' => $templateId,
                    'user_found' => $user !== null,
                    'template_found' => $template !== null,
                    'action' => 'use_permission_check',
                ]);

                return false;
            }

            // 템플릿 사용은 인증된 사용자에게 허용 (추후 상태/소유자 정책이 필요하면 여기에서 확장)
            return true;

        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 사용 권한 확인 중 오류 발생', [
                'user_id' => $userId,
                'template_id' => $templateId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 템플릿 수정 권한 확인
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $templateId  템플릿 ID
     * @return bool 수정 권한 여부
     */
    public function canUpdate(int $userId, int $templateId): bool
    {
        try {
            $user = User::find($userId);
            $template = NotificationTemplate::find($templateId);

            if (! $user || ! $template) {
                $this->loggingService->logError('사용자 또는 템플릿을 찾을 수 없음', [
                    'user_id' => $userId,
                    'template_id' => $templateId,
                    'user_found' => $user !== null,
                    'template_found' => $template !== null,
                    'action' => 'update_permission_check',
                ]);

                return false;
            }

            // 관리자이거나 템플릿 생성자인 경우 수정 가능
            return $this->isAdmin($user) || $template->created_by === $userId;

        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 수정 권한 확인 중 오류 발생', [
                'user_id' => $userId,
                'template_id' => $templateId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 템플릿 삭제 권한 확인
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $templateId  템플릿 ID
     * @return bool 삭제 권한 여부
     */
    public function canDelete(int $userId, int $templateId): bool
    {
        try {
            $user = User::find($userId);
            $template = NotificationTemplate::find($templateId);

            if (! $user || ! $template) {
                $this->loggingService->logError('사용자 또는 템플릿을 찾을 수 없음', [
                    'user_id' => $userId,
                    'template_id' => $templateId,
                    'user_found' => $user !== null,
                    'template_found' => $template !== null,
                    'action' => 'delete_permission_check',
                ]);

                return false;
            }

            // 관리자이거나 템플릿 생성자인 경우 삭제 가능
            return $this->isAdmin($user) || $template->created_by === $userId;

        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 삭제 권한 확인 중 오류 발생', [
                'user_id' => $userId,
                'template_id' => $templateId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 템플릿에 대한 권한 검증 (예외 발생)
     *
     * @param  int  $userId  사용자 ID
     * @param  NotificationTemplate  $template  템플릿 객체
     * @param  string  $action  수행하려는 작업 ('create', 'read', 'update', 'delete')
     *
     * @throws BusinessException 권한이 없는 경우
     */
    public function validatePermission(int $userId, NotificationTemplate $template, string $action): void
    {
        // 사용자 정보 조회
        $user = User::find($userId);

        if (! $user) {
            throw new BusinessException('사용자를 찾을 수 없습니다.', [
                'user_id' => $userId,
                'type' => 'user_not_found',
            ]);
        }

        // 읽기 권한은 모든 사용자에게 허용
        if ($action === 'read') {
            return;
        }

        // 관리자 권한 확인 (Super-Admin 또는 Admin)
        if ($this->isAdmin($user)) {
            $this->loggingService->logInfo('관리자 권한으로 템플릿 접근 허용', [
                'user_id' => $userId,
                'user_role' => $user->role,
                'template_id' => $template->id,
                'action' => $action,
            ]);

            return; // 관리자는 모든 템플릿에 대한 권한 보유
        }

        // 템플릿 생성자 확인
        if ($template->created_by === $userId) {
            $this->loggingService->logInfo('템플릿 생성자 권한으로 접근 허용', [
                'user_id' => $userId,
                'template_id' => $template->id,
                'action' => $action,
            ]);

            return; // 생성자는 자신의 템플릿에 대한 권한 보유
        }

        // 권한이 없는 경우
        $this->loggingService->logError('템플릿 접근 권한 없음', [
            'user_id' => $userId,
            'template_id' => $template->id,
            'template_creator' => $template->created_by,
            'user_role' => $user->role,
            'action' => $action,
        ]);

        throw new BusinessException("템플릿을 {$action}할 권한이 없습니다.", [
            'user_id' => $userId,
            'template_id' => $template->id,
            'template_creator' => $template->created_by,
            'user_role' => $user->role,
            'action' => $action,
            'type' => 'permission_denied',
        ]);
    }

    /**
     * 사용자가 관리자 권한을 가지고 있는지 확인
     *
     * @param  User  $user  사용자 객체
     * @return bool 관리자 권한 여부
     */
    public function isAdmin(User $user): bool
    {
        return in_array($user->role, [
            User::ROLE_SUPER_ADMIN,
            User::ROLE_ADMIN,
        ]);
    }

    /**
     * 사용자의 템플릿 접근 권한 확인 (기존 호환성 메서드)
     *
     * @param  int  $userId  사용자 ID
     * @param  int  $templateId  템플릿 ID
     * @param  string  $action  수행하려는 작업
     * @return bool 권한 여부
     */
    public function hasPermissionForTemplate(int $userId, int $templateId, string $action = 'read'): bool
    {
        try {
            $template = NotificationTemplate::find($templateId);

            if (! $template) {
                $this->loggingService->logError('템플릿을 찾을 수 없음', [
                    'user_id' => $userId,
                    'template_id' => $templateId,
                    'action' => $action,
                ]);

                return false;
            }

            // 권한 확인 메서드 호출
            switch ($action) {
                case 'create':
                    return $this->canCreate($userId);
                case 'read':
                    return $this->canRead($userId, $templateId);
                case 'update':
                    return $this->canUpdate($userId, $templateId);
                case 'delete':
                    return $this->canDelete($userId, $templateId);
                default:
                    $this->loggingService->logError('알 수 없는 권한 작업', [
                        'user_id' => $userId,
                        'template_id' => $templateId,
                        'action' => $action,
                    ]);

                    return false;
            }

        } catch (Throwable $e) {
            $this->loggingService->logError('템플릿 권한 확인 중 오류 발생', [
                'user_id' => $userId,
                'template_id' => $templateId,
                'action' => $action,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
