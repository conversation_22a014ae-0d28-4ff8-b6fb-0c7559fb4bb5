<?php

namespace App\Services;

use App\Helpers\CacheHelper;
use App\Http\Resources\Cate4Resource;
use App\Models\Cate4;
use App\Models\Cate5;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class CategoryService
{
    private const CACHE_KEY_ALL_CATEGORY = 'categories.all';

    private const CACHE_KEY_CATE4_PREFIX = 'category.cate4.';

    private const CACHE_KEY_CATE5_PREFIX = 'category.cate5.';

    private const CACHE_TTL_CATEGORY = 86400; // 24시간(초)

    private const CACHE_TAG_CATEGORY = 'categories';

    /**
     * 모든 카테고리를 캐시와 함께 조회
     *
     * @param  bool  $useCache  캐시 사용 여부 (기본값: true)
     *
     * @throws Exception 카테고리 조회 실패시
     */
    public function getAllCategories(bool $useCache = true): Collection|AnonymousResourceCollection
    {
        try {
            $builder = Cate4::select(['id', 'name'])
                ->with('cate5:id,cate4_id,name')
                ->orderBy('name');

            if (! $useCache) {
                return Cate4Resource::collection($builder->get());
            }

            return CacheHelper::rememberSafe(
                self::CACHE_KEY_ALL_CATEGORY,
                self::CACHE_TTL_CATEGORY,
                fn () => Cate4Resource::collection($builder->get()),
                [self::CACHE_TAG_CATEGORY]
            );
        } catch (Exception $e) {
            SimpleLogService::error('daily', '카테고리 조회에 실패했습니다.', ['error' => $e->getMessage()]);
            throw new Exception('카테고리 조회에 실패했습니다: '.$e->getMessage());
        }
    }

    /**
     * Cate4 카테고리 조회
     *
     * @param  int  $id  카테고리 ID
     * @param  bool  $useCache  캐시 사용 여부 (기본값: true)
     * @param  bool  $withRelations  관계 데이터 포함 여부 (기본값: false)
     *
     * @throws Exception 카테고리 조회 실패시
     */
    public function getCate4(int $id, bool $useCache = true, bool $withRelations = false): ?Cate4
    {
        try {
            if (! $useCache) {
                return $withRelations
                    ? Cate4::with('cate5:id,cate4_id,name')->find($id)
                    : Cate4::find($id);
            }

            return CacheHelper::rememberSafe(
                self::CACHE_KEY_CATE4_PREFIX.$id.($withRelations ? '.with_relations' : ''),
                self::CACHE_TTL_CATEGORY,
                fn () => $withRelations
                    ? Cate4::with('cate5:id,cate4_id,name')->find($id)
                    : Cate4::find($id),
                [self::CACHE_TAG_CATEGORY]
            );
        } catch (Exception $e) {
            SimpleLogService::error('daily', 'Failed to get Cate4', [
                'id' => $id,
                'error' => $e->getMessage(),
            ]);
            throw new Exception("Cate4 조회에 실패했습니다 (ID: {$id}): ".$e->getMessage());
        }
    }

    /**
     * Cate5 카테고리 조회
     *
     * @param  int  $id  카테고리 ID
     * @param  bool  $useCache  캐시 사용 여부 (기본값: true)
     *
     * @throws Exception 카테고리 조회 실패시
     */
    public function getCate5(int $id, bool $useCache = true): ?Cate5
    {
        try {
            if (! $useCache) {
                return Cate5::find($id);
            }

            return CacheHelper::rememberSafe(
                self::CACHE_KEY_CATE5_PREFIX.$id,
                self::CACHE_TTL_CATEGORY,
                fn () => Cate5::find($id),
                [self::CACHE_TAG_CATEGORY]
            );
        } catch (Exception $e) {
            SimpleLogService::error('daily', 'Cate5 조회에 실패했습니다.', ['id' => $id, 'error' => $e->getMessage()]);
            throw new Exception("Cate5 조회에 실패했습니다 (ID: {$id}): ".$e->getMessage());
        }
    }

    /**
     * 특정 Cate4와 연관된 모든 Cate5 조회
     *
     * @param  int  $cate4Id  Cate4 ID
     * @param  bool  $useCache  캐시 사용 여부 (기본값: true)
     * @return Collection<Cate5>
     *
     * @throws Exception 조회 실패시
     */
    public function getCate5ByCate4Id(int $cate4Id, bool $useCache = true): Collection
    {
        try {
            $cacheKey = "category.cate5.by_cate4.{$cate4Id}";

            if (! $useCache) {
                return Cate5::select(['id', 'cate4_id', 'name'])->where('cate4_id', $cate4Id)->orderBy('name')->get();
            }

            return CacheHelper::rememberSafe(
                $cacheKey,
                self::CACHE_TTL_CATEGORY,
                fn () => Cate5::select(['id', 'cate4_id', 'name'])->where('cate4_id', $cate4Id)->orderBy('name')->get(),
                [self::CACHE_TAG_CATEGORY]
            );
        } catch (Exception $e) {
            SimpleLogService::error('daily', 'Cate4에 속한 Cate5 조회에 실패했습니다', ['cate4_id' => $cate4Id, 'error' => $e->getMessage()]);
            throw new Exception("Cate4에 속한 Cate5 조회에 실패했습니다 (Cate4 ID: {$cate4Id}): ".$e->getMessage());
        }
    }

    /**
     * 카테고리 존재 여부 확인
     *
     * @param  string  $type  카테고리 타입 ('cate4' 또는 'cate5')
     * @param  int  $id  카테고리 ID
     */
    public function categoryExists(string $type, int $id): bool
    {
        try {
            return match ($type) {
                'cate4' => $this->getCate4($id) !== null,
                'cate5' => $this->getCate5($id) !== null,
                default => false
            };
        } catch (Exception $e) {
            SimpleLogService::warning('daily', 'Failed to check category existence', [
                'type' => $type,
                'id' => $id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 카테고리 캐시 무효화
     *
     * @return bool 성공 여부
     */
    public function flushCategoryCache(): bool
    {
        try {
            CacheHelper::flushTags([self::CACHE_TAG_CATEGORY]);
            SimpleLogService::info('daily', '카테고리 캐시가 초기화되었습니다.');

            return true;
        } catch (Exception $e) {
            SimpleLogService::error('daily', '카테고리 캐시 초기화 실패', ['error' => $e->getMessage()]);

            return false;
        }
    }

    /**
     * 특정 카테고리의 캐시만 무효화
     *
     * @param  string  $type  카테고리 타입 ('cate4' 또는 'cate5')
     * @param  int  $id  카테고리 ID
     * @return bool 성공 여부
     */
    public function flushSpecificCategoryCache(string $type, int $id): bool
    {
        try {
            $keys = match ($type) {
                'cate4' => [
                    self::CACHE_KEY_CATE4_PREFIX.$id,
                    self::CACHE_KEY_CATE4_PREFIX.$id.'.with_relations',
                ],
                'cate5' => [
                    self::CACHE_KEY_CATE5_PREFIX.$id,
                ],
                default => []
            };

            foreach ($keys as $key) {
                CacheHelper::forgetCache($key, [self::CACHE_TAG_CATEGORY]);
            }

            // 전체 카테고리 캐시도 무효화
            CacheHelper::forgetCache(self::CACHE_KEY_ALL_CATEGORY, [self::CACHE_TAG_CATEGORY]);

            SimpleLogService::info('daily', 'Specific category cache flushed', ['type' => $type, 'id' => $id]);

            return true;
        } catch (Exception $e) {
            SimpleLogService::error('daily', 'Failed to flush specific category cache', [
                'type' => $type,
                'id' => $id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 캐시 상태 확인
     *
     * @return array 캐시 상태 정보
     */
    public function getCacheStatus(): array
    {
        return [
            'all_categories_cached' => CacheHelper::has(self::CACHE_KEY_ALL_CATEGORY),
            'cache_ttl' => self::CACHE_TTL_CATEGORY,
            'cache_tag' => self::CACHE_TAG_CATEGORY,
            'timestamp' => now()->toISOString(),
        ];
    }
}
