<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductLog;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;

class ProductLogService
{
    private array $logs = [];

    private Carbon $now;

    private int $userId;

    public function __construct()
    {
        $this->now = now();
    }

    public function getQaidHistory(int $id): Collection
    {
        return ProductLog::with('user:id,name')
            ->where('model_type', 'App\Models\QaidReprint')
            ->where('model_id', $id)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 로그 추가
     *
     * @param  Product|null  $product  관련 제품 또는 해당되지 않은 경우 null
     * @param  string  $modelType  연결 되어야 할 모델
     * @param  int  $modelId  연결 되는 모델의 ID
     * @param  int  $statusId  이 로그 항목과 관련된 상태 ID
     * @param  string|null  $memo  (옵션)메모
     */
    public function addLog(?Product $product, string $modelType, int $modelId, int $statusId, int $userId, ?string $memo = null): void
    {
        $this->logs[] = [
            'product_id' => $product?->id,
            'model_type' => $modelType,
            'model_id' => $modelId,
            'work_status_id' => $statusId,
            'user_id' => $userId,
            'memo' => $memo,
            'created_at' => $this->now,
            'updated_at' => $this->now,
        ];
    }

    public function save(): void
    {
        if (empty($this->logs)) {
            return;
        }

        // 너무 많은 양을 한 번에 삽입하면 DB 부하가 커질 수 있으므로 500개 단위로 청크 삽입
        foreach (array_chunk($this->logs, 500) as $logsChunk) {
            ProductLog::insert($logsChunk);
        }
    }
}
