<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

/**
 * 템플릿 관련 로깅 유틸리티 서비스
 *
 * 템플릿 서비스들에서 사용하는 구조화된 로깅 기능을 제공합니다.
 * 일관된 로그 형식과 컨텍스트 정보를 통해 디버깅과 모니터링을 지원합니다.
 *
 * 주요 기능:
 * - 구조화된 로그 메시지 생성
 * - 컨텍스트 정보 자동 추가 (타임스탬프, 요청 ID, IP 주소 등)
 * - 민감한 정보 자동 마스킹 (비밀번호, 토큰 등)
 * - 템플릿 액션 로깅 (생성, 수정, 삭제, 사용)
 * - 성능 메트릭 로깅
 * - 비즈니스 이벤트 로깅
 * - 예외 및 검증 실패 로깅
 *
 * 로그 레벨:
 * - INFO: 일반적인 작업 완료
 * - WARNING: 주의가 필요한 상황
 * - ERROR: 오류 발생
 * - DEBUG: 디버깅 정보
 *
 * <AUTHOR> Development Team
 *
 * @since 2.0.0
 */
class TemplateLoggingService
{
    /**
     * 로그 채널명
     */
    private const LOG_CHANNEL = 'single';

    /**
     * 서비스 식별자
     */
    private const SERVICE_PREFIX = '[TemplateService]';

    /**
     * 정보 로그 기록
     *
     * @param  string  $message  로그 메시지
     * @param  array  $context  로그 컨텍스트
     */
    public function logInfo(string $message, array $context = []): void
    {
        $formattedContext = $this->formatContext($context);

        Log::channel(self::LOG_CHANNEL)->info(
            self::SERVICE_PREFIX." {$message}",
            $formattedContext
        );
    }

    /**
     * 에러 로그 기록
     *
     * @param  string  $message  로그 메시지
     * @param  array  $context  로그 컨텍스트
     */
    public function logError(string $message, array $context = []): void
    {
        $formattedContext = $this->formatContext($context);

        Log::channel(self::LOG_CHANNEL)->error(
            self::SERVICE_PREFIX." {$message}",
            $formattedContext
        );
    }

    /**
     * 경고 로그 기록
     *
     * @param  string  $message  로그 메시지
     * @param  array  $context  로그 컨텍스트
     */
    public function logWarning(string $message, array $context = []): void
    {
        $formattedContext = $this->formatContext($context);

        Log::channel(self::LOG_CHANNEL)->warning(
            self::SERVICE_PREFIX." {$message}",
            $formattedContext
        );
    }

    /**
     * 디버그 로그 기록
     *
     * @param  string  $message  로그 메시지
     * @param  array  $context  로그 컨텍스트
     */
    public function logDebug(string $message, array $context = []): void
    {
        $formattedContext = $this->formatContext($context);

        Log::channel(self::LOG_CHANNEL)->debug(
            self::SERVICE_PREFIX." {$message}",
            $formattedContext
        );
    }

    /**
     * 템플릿 액션 로그 기록
     *
     * @param  string  $action  수행된 액션 (create, update, delete, read 등)
     * @param  int  $templateId  템플릿 ID
     * @param  int  $userId  사용자 ID
     * @param  array  $additionalContext  추가 컨텍스트 정보
     */
    public function logTemplateAction(string $action, int $templateId, int $userId, array $additionalContext = []): void
    {
        $context = array_merge([
            'action' => $action,
            'template_id' => $templateId,
            'user_id' => $userId,
            'timestamp' => now()->toISOString(),
        ], $additionalContext);

        $this->logInfo("템플릿 액션 수행: {$action}", $context);
    }

    /**
     * 성능 메트릭 로그 기록
     *
     * @param  string  $operation  수행된 작업명
     * @param  float  $executionTime  실행 시간 (초)
     * @param  array  $additionalContext  추가 컨텍스트 정보
     */
    public function logPerformance(string $operation, float $executionTime, array $additionalContext = []): void
    {
        $context = array_merge([
            'operation' => $operation,
            'execution_time_seconds' => $executionTime,
            'execution_time_ms' => round($executionTime * 1000, 2),
            'timestamp' => now()->toISOString(),
        ], $additionalContext);

        $this->logInfo("성능 메트릭: {$operation}", $context);
    }

    /**
     * 비즈니스 이벤트 로그 기록
     *
     * @param  string  $event  이벤트명
     * @param  array  $context  이벤트 컨텍스트
     */
    public function logBusinessEvent(string $event, array $context = []): void
    {
        $formattedContext = array_merge([
            'event' => $event,
            'timestamp' => now()->toISOString(),
        ], $context);

        $this->logInfo("비즈니스 이벤트: {$event}", $formattedContext);
    }

    /**
     * 로그 컨텍스트 포맷팅
     *
     * @param  array  $context  원본 컨텍스트
     * @return array 포맷팅된 컨텍스트
     */
    private function formatContext(array $context): array
    {
        // 타임스탬프 추가 (없는 경우에만)
        if (! isset($context['timestamp'])) {
            $context['timestamp'] = now()->toISOString();
        }

        // 요청 ID 추가 (있는 경우)
        if (request()->hasHeader('X-Request-ID')) {
            $context['request_id'] = request()->header('X-Request-ID');
        }

        // 사용자 에이전트 추가 (웹 요청인 경우)
        if (request()->hasHeader('User-Agent')) {
            $context['user_agent'] = request()->header('User-Agent');
        }

        // IP 주소 추가
        $context['ip_address'] = request()->ip();

        // 민감한 정보 마스킹
        $context = $this->maskSensitiveData($context);

        return $context;
    }

    /**
     * 민감한 정보 마스킹
     *
     * @param  array  $context  컨텍스트 배열
     * @return array 마스킹된 컨텍스트 배열
     */
    private function maskSensitiveData(array $context): array
    {
        $sensitiveKeys = [
            'password',
            'token',
            'secret',
            'api_key',
            'private_key',
            'credit_card',
            'ssn',
        ];

        foreach ($sensitiveKeys as $key) {
            if (isset($context[$key])) {
                $context[$key] = '***MASKED***';
            }
        }

        // 중첩된 배열에서도 마스킹 적용
        foreach ($context as $key => $value) {
            if (is_array($value)) {
                $context[$key] = $this->maskSensitiveData($value);
            }
        }

        return $context;
    }

    /**
     * 구조화된 에러 로그 기록
     *
     * @param  string  $message  에러 메시지
     * @param  \Throwable  $exception  예외 객체
     * @param  array  $additionalContext  추가 컨텍스트
     */
    public function logException(string $message, \Throwable $exception, array $additionalContext = []): void
    {
        $context = array_merge([
            'exception_class' => get_class($exception),
            'exception_message' => $exception->getMessage(),
            'exception_code' => $exception->getCode(),
            'exception_file' => $exception->getFile(),
            'exception_line' => $exception->getLine(),
            'stack_trace' => $exception->getTraceAsString(),
        ], $additionalContext);

        $this->logError($message, $context);
    }

    /**
     * 검증 실패 로그 기록
     *
     * @param  string  $validationType  검증 타입
     * @param  array  $validationErrors  검증 에러 목록
     * @param  array  $inputData  입력 데이터
     */
    public function logValidationFailure(string $validationType, array $validationErrors, array $inputData = []): void
    {
        $context = [
            'validation_type' => $validationType,
            'validation_errors' => $validationErrors,
            'input_data_keys' => array_keys($inputData), // 실제 데이터는 로그하지 않고 키만 로그
            'error_count' => count($validationErrors),
        ];

        $this->logWarning("검증 실패: {$validationType}", $context);
    }
}
