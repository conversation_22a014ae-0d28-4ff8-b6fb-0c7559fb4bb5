<?php

namespace App\Services\Warehouse;

use App\Helpers\HelperLibrary;
use App\Models\WarehousePallet;
use Illuminate\Support\Collection;

class PalletService
{
    public function __construct() {}

    public function isPalletNumber(string $code): bool
    {
        return WarehousePallet::where('pallet_number', $code)->exists();
    }

    public function generatePalletNumber(): string
    {
        $characters = 'ABCEFGHIKLMNPQRSTUVWXYZ';
        $characters2 = 'ABDEFGHJKLMNOQRSTUWXYZ23456789';
        $numbers = '0123456789';

        // 코드 생성과 검증이 실패할 때까지 계속
        do {
            $level = HelperLibrary::generateRandomString($characters)
                .HelperLibrary::generateRandomString($characters2, 2)
                .HelperLibrary::generateRandomString($characters);
            $column = HelperLibrary::generateRandomString($numbers, 4);

            $newCode = $level.'-'.$column;
            $isCode = $this->isPalletNumber($newCode);
        } while ($isCode);

        return $newCode;
    }

    /**
     * 사용할 수 있는 팔레트 리스트(id, pallet_number, item_count, recent_items(최근 10개 아이템 정보))
     */
    public function getLoadingPallets(): Collection
    {
        return WarehousePallet::where('type', WarehousePallet::TYPE_WAREHOUSING)
            ->whereIn('status', [
                WarehousePallet::STATUS_AVAILABLE,
                WarehousePallet::STATUS_IN_USE,
            ])
            ->where('created_by', auth()->id())
            ->orderBy('updated_at', 'desc')
            ->get()
            ->map(fn ($pallet) => [
                'id' => $pallet->id,
                'pallet_number' => $pallet->pallet_number,
                'current_capacity' => $pallet->current_capacity,
                'max_capacity' => $pallet->max_capacity,
                'items_count' => $pallet->palletItems()->count(), // 팔레트 아이템 총 개수
                'recent_items' => $pallet->palletItems()
                    ->with('product:id,name') // Product 모델의 id와 name만 가져옴
                    ->orderBy('created_at', 'desc')
                    ->take(10)
                    ->get()
                    ->map(fn ($item) => [
                        'id' => $item->id,
                        'product_name' => $item->product->name, // 제품명만 가져옴
                        'quantity' => $item->quantity,
                    ]),
            ]);
    }
}
