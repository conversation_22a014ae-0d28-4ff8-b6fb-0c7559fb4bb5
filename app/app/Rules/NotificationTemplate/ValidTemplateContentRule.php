<?php

namespace App\Rules\NotificationTemplate;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * 알림 템플릿 내용 유효성 검증 규칙
 *
 * 템플릿 내용에 허용되지 않는 HTML 태그나 스크립트가 포함되어 있는지 검증합니다.
 */
class ValidTemplateContentRule implements ValidationRule
{
    /**
     * 허용되지 않는 HTML 태그 목록
     */
    private array $forbiddenTags = [
        'script',
        'iframe',
        'object',
        'embed',
        'form',
        'input',
        'button',
        'select',
        'textarea',
        'link',
        'meta',
        'style',
    ];

    /**
     * 유효성 검사 규칙을 실행합니다.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! is_string($value)) {
            return;
        }

        // JavaScript 이벤트 핸들러 검사 (금지된 태그보다 먼저)
        $jsEvents = [
            'onclick', 'onload', 'onmouseover', 'onmouseout', 'onfocus', 'onblur',
            'onchange', 'onsubmit', 'onkeydown', 'onkeyup', 'onkeypress',
        ];

        foreach ($jsEvents as $event) {
            if (preg_match('/'.preg_quote($event, '/').'\s*=/i', $value)) {
                $fail("템플릿 내용에 JavaScript 이벤트 핸들러({$event})가 포함되어 있습니다.");

                return;
            }
        }

        // javascript: 프로토콜 검사 (공백, 탭, 개행 등 모든 공백 문자 포함)
        if (preg_match('/java[\s]*script[\s]*:/i', $value)) {
            $fail('템플릿 내용에 JavaScript 코드가 포함되어 있습니다.');

            return;
        }

        // 금지된 HTML 태그 검사 (탭, 개행 등 모든 공백 문자 포함)
        foreach ($this->forbiddenTags as $tag) {
            if (preg_match('/<\s*'.preg_quote($tag, '/').'(\s|>)/i', $value)) {
                $fail("템플릿 내용에 허용되지 않는 HTML 태그({$tag})가 포함되어 있습니다.");

                return;
            }
        }

        // 과도한 HTML 엔티티 검사 (잠재적 XSS 공격 방지)
        $entityCount = preg_match_all('/&#?\w+;/', $value);
        if ($entityCount > 50) {
            $fail('템플릿 내용에 과도한 HTML 엔티티가 포함되어 있습니다.');

            return;
        }
    }
}
