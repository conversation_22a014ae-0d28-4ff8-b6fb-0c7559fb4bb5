<?php

namespace App\Rules\NotificationTemplate;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * 알림 템플릿명 유효성 검증 규칙
 *
 * 템플릿명이 시스템 예약어나 부적절한 문자를 포함하고 있는지 검증합니다.
 */
class ValidTemplateNameRule implements ValidationRule
{
    /**
     * 시스템 예약어 목록
     */
    private array $reservedWords = [
        'system',
        'admin',
        'root',
        'default',
        'template',
        'notification',
        'alert',
        'warning',
        'error',
        'success',
        'info',
        'debug',
        'test',
        'sample',
        'example',
        'demo',
    ];

    /**
     * 유효성 검사 규칙을 실행합니다.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! is_string($value)) {
            return;
        }

        // 1. 빈 문자열이거나 공백만으로 구성된 경우 (최우선 검사)
        if (trim($value) === '') {
            $fail('템플릿명은 공백만으로 구성될 수 없습니다.');

            return;
        }

        $trimmedValue = trim($value);

        // 2. 숫자로만 구성된 이름 방지
        if (preg_match('/^\d+$/', $trimmedValue)) {
            $fail('템플릿명은 숫자로만 구성될 수 없습니다.');

            return;
        }

        // 3. 시작/끝 공백 검사 (내용이 있는 경우에만)
        if ($value !== $trimmedValue) {
            $fail('템플릿명의 앞뒤에 공백은 사용할 수 없습니다.');

            return;
        }

        // 4. 연속된 공백 검사
        if (preg_match('/\s{2,}/', $value)) {
            $fail('템플릿명에 연속된 공백은 사용할 수 없습니다.');

            return;
        }

        $name = strtolower($trimmedValue);

        // 5. 시스템 예약어 검사
        if (in_array($name, $this->reservedWords)) {
            $fail('시스템 예약어는 템플릿명으로 사용할 수 없습니다.');

            return;
        }

        // 6. 특수문자 조합 검사 (연속된 특수문자 방지)
        if (preg_match('/[-_]{2,}/', $value)) {
            $fail('템플릿명에 연속된 특수문자는 사용할 수 없습니다.');

            return;
        }
    }
}
