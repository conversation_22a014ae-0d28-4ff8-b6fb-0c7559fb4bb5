<?php

namespace App\Exports;

use App\Helpers\XLSXWriter\XLSXWriter;
use App\Models\Pallet;
use App\Models\PalletProduct;
use App\Models\RepairCostType;
use App\Models\RepairGrade;
use App\Models\RepairProduct;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class MonthlyClosingExport extends BaseExport
{
    // 식별자로, 이 Export 클래스가 어떤 타입(종류)인지 구분할 때 사용
    public static function type(): string
    {
        return 'monthlyClosing';
    }

    // request에서 필요한 데이터 추출
    public static function fromRequest(Request $request): self
    {
        $origin = $request->headers->get('origin');

        // 기본 메타데이터 생성
        $meta = [
            'author' => auth()->user()->name ?? '',
            'company' => '코너스톤 프로젝트',
            'origin' => $origin,
        ];

        // 동적 제목 설정
        $year = Carbon::now()->year;
        $month = Carbon::now()->month;

        $palletCode = $request->input('palletCode');
        $beginAt = $request->input('beginAt');
        $endAt = $request->input('endAt');

        $title = self::getDynamicTitle($year, $month);
        $meta['title'] = $title;

        // 실제 쿼리 파라미터
        $data = [
            'year' => $year,
            'month' => $month,
            'palletCode' => $palletCode,
            'palletIds' => $request->input('palletIds'),
            'status' => $request->input('status'),
            'beginAt' => $beginAt,
            'endAt' => $endAt,
        ];

        return new self($meta, $data);
    }

    /**
     * Export 제목 반환<br>
     * 기본 제목이고, 아래 getDynamicTitle() 메서드에서 동적으로 변경됨
     */
    public static function getExportTitle(): string
    {
        return '월간 마감 보고서';
    }

    /**
     * 동적 제목 생성
     */
    private static function getDynamicTitle(int $year, int $month): string
    {
        $monthName = Carbon::createFromDate($year, $month, 1)->format('Y년 m월');

        return "CE Repair Monthly Tracker_{$monthName}";
    }

    /**
     * 헤더 정보 반환 (CSV 파일의 헤더를 참고하여 작성)
     */
    protected function getHeader(): array
    {
        return [
            'A' => ['Supplier' => 'string'], // 20
            'B' => ['Month' => 'string'], // 10
            'C' => ['Week' => 'string'], // 8
            'D' => ['Receiving Date' => 'string'], // 15
            'E' => ['Repair Date' => 'string'], // 15
            'F' => ['QAID' => 'string'], // 15
            'G' => ['wmsSkuid' => 'string'], // 15
            'H' => ['extid' => 'string'], // 15
            'I' => ['bar_cd' => 'string'], // 20
            'J' => ['Product_Classification' => 'string'], // 25
            'K' => ['Product_Tier 1' => 'string'], // 20
            'L' => ['Product_Tier 2' => 'string'], // 20
            'M' => ['Product_Description' => 'string'], // 50
            'N' => ['Product_Value' => '#,##0'], // 15
            'O' => ['Product_Quantity' => 'integer'], // 12
            'P' => ['Product_Manufacturer' => 'string'], // 20
            'Q' => ['Repair_Description' => 'string'], // 30
            'R' => ['Repair_Type' => 'string'], // 20
            'S' => ['Repair_Result' => 'string'], // 15
            'T' => ['Repair_Replacement' => 'string'], // 15
            'U' => ['Repair_Cleaning' => 'string'], // 15
            'V' => ['Repair_S/W' => 'string'], // 15
            'W' => ['Repair_Others' => 'string'], // 15
            'X' => ['Inspection' => 'string'], // 15
            'Y' => ['Replacement Part' => 'string'], // 20
            'Z' => ['O/S Cost' => '#,##0'], // 15
            'AA' => ['Replacement Part_Cost' => '#,##0'], // 20
            'AB' => ['Release_Status' => 'string'], // 15
            'AC' => ['Release_Date' => 'string'], // 15
            'AD' => ['PLT_Number' => 'string'], // 15
            'AE' => ['Repacking_Components' => 'string'], // 25
        ];
    }

    /**
     * 헤더 옵션 반환
     */
    protected function getHeaderOption(): array
    {
        $fontSize = 9;
        $height = 20;

        return [
            'widths' => [20, 10, 8, 15, 15, 15, 15, 15, 20, 25, 20, 20, 50, 15, 12, 20, 30, 20, 15, 15, 15, 15, 15, 15, 20, 15, 20, 15, 15, 15, 25],

            // 상단 첫 번째 행 고정
            'freeze_rows' => 1,
            'freeze_columns' => 3,

            // 전체적으로 하나의 스타일을 적용하려면 이것만 있어도 됨 - height는 안 됨.
            'styles' => [
                'font' => '맑은 고딕',
                'font-size' => $fontSize,
                'font-style' => 'bold',
                'halign' => 'center',
                'fill' => '#A4A4A4',
                'height' => $height,
            ],
        ];
    }

    /**
     * 데이터 조회
     */
    protected function getData(): Collection
    {
        return $this->getMonthlyClosingData($this->data);
    }

    /**
     * 행 데이터 작성
     */
    protected function writeRows(XLSXWriter $writer, Collection $rows): void
    {
        foreach ($rows as $index => $palletProduct) {
            $product = $palletProduct->product;
            $barcode = $product->productBarcode;
            $repairProduct = $palletProduct->repairProduct ?? null;

            // 수리 일자
            if (! $repairProduct) {
                $repairDate = substr($palletProduct->registered_at, 0, 10);
            } else {
                $repairDate = $repairProduct?->completed_at ?? null;
                if ($repairDate) {
                    $repairDate = substr($repairDate, 0, 10);
                }
            }

            // 입고 일자
            $receivingDate = $product->req->req_at ?? null;

            // 증상 내용
            $repairDescription = $palletProduct?->repairSymptom?->name ?? '';

            // 처리 내용
            $repairType = $palletProduct?->repairProcess?->name ?? '';

            // 주차 계산 (출고일 기준) 및 출고 일자
            $exportedDate = $palletProduct?->pallet?->exported_at ?? null;
            $weekInfo = ['week' => '', 'month' => ''];
            if ($exportedDate) {
                $year = Carbon::parse($exportedDate)->year;
                $month = Carbon::parse($exportedDate)->month;

                $exportedDate = substr($exportedDate, 0, 10);
                $weekInfo = $this->calculateWeekAndMonth($exportedDate, $year, $month);
            }

            // 수리 결과 및 타입 분석
            $repairResult = $this->getRepairResult($palletProduct);

            // 수리 세부 항목들
            $repairCost = $this->getRepairCost($repairProduct);

            // 팔레트 번호
            $palletNumberArr = explode('-', $palletProduct->pallet->location->code);

            $item = [
                'A' => '코너스톤 프로젝트', // Supplier
                'B' => $weekInfo['month'], // Month
                'C' => $weekInfo['week'], // Week
                'D' => $receivingDate ? substr($receivingDate, 0, 10) : '', // Receiving Date
                'E' => $repairDate, // Repair Date
                'F' => $product->qaid, // QAID
                'G' => $barcode->wms_sku_id, // wmsSkuid (Product ID)
                'H' => $barcode->external_wms_sku_id ?? '', // extid
                'I' => $product->barcode ?? '', // bar_cd
                'J' => $product->rg === 'Y' ? '2P(RG)' : '1P', // Product_Classification
                'K' => $product->cate4->name ?? '', // Product_Tier 1 (카테고리4차)
                'L' => $product->cate5->name ?? '', // Product_Tier 2 (카테고리5차)
                'M' => $product->name ?? '', // Product_Description
                'N' => $product->amount, // Product_Value
                'O' => 1, // Product_Quantity (기본값 1)
                'P' => $product->vendor->name ?? '', // Product_Manufacturer (Vendor)
                'Q' => $repairDescription, // Repair_Description - null-safe 연산자 추가
                'R' => $repairType, // Repair_Type - null-safe 연산자 추가
                'S' => $repairResult, // Repair_Result
                'T' => $repairCost['parts'] ?? '', // Repair_Replacement
                'U' => $repairCost['cleaning'] ?? '', // Repair_Cleaning
                'V' => $repairCost['software'] ?? '', // Repair_S/W
                'W' => $repairCost['others'] ?? '', // Repair_Others
                'X' => $repairCost['inspection'] ?? '', // Inspection
                'Y' => $repairCost['replacement_part'] ?? '', // Replacement Part
                'Z' => $repairCost['os_cost'] ?? 0, // O/S Cost
                'AA' => $repairCost['replacement_cost'] ?? 0, // Replacement Part_Cost
                'AB' => Pallet::$STATUS_NAME[$palletProduct?->pallet?->status] ?? '', // Release_Status (출고상태) - null-safe 연산자 추가
                'AC' => $exportedDate, // Release_Date (출고일)
                'AD' => $palletNumberArr[3].'-'.$palletNumberArr[4], // PLT_Number
                'AE' => '', // Repacking_Components
            ];

            $writer->writeSheetRow($this->sheetName, $item, $this->defaultCellStyle);
        }
    }

    /**
     * 월간 마감 데이터 조회
     */
    private function getMonthlyClosingData(array $data): Collection
    {
        $builder = PalletProduct::with([
            'product' => function ($q) {
                $q->without([
                    'lot',
                    'link',
                    'user',
                    'checkedUser',
                    'palletProducts',
                    'carryoutProducts',
                    'repairProduct',
                ])->with('productBarcode'); // 필요한 건 다시 명시
            },

            // 또는 방법 2) 모두 끄고 필요한 것만 켠다
            // 'product' => function ($q) {
            //     $q->withoutEagerLoads()      // Product::$with 전부 비활성화
            //       ->with('productBarcode');  // 필요한 관계만 지정
            // },

            'repairProduct',
        ])
            ->leftJoin('pallets', 'pallets.id', '=', 'pallet_products.pallet_id')
            ->whereIn('pallets.id', $data['palletIds']);

        if (! empty($data['status'])) {
            $builder->where('pallets.status', $data['status']);
        }

        if ($data['beginAt'] && $data['endAt']) {
            $builder->whereBetween('pallets.checked_at', [$data['beginAt'], $data['endAt']]);
        }

        $builder->select('pallet_products.*')
            ->orderBy('pallets.checked_at', 'desc')
            ->orderBy('pallets.registered_at', 'desc')
            ->orderBy('pallet_products.registered_at', 'desc');

        return $builder->get();

    }

    /**
     * 주차 및 월 계산: 해당 월의 몇 번째 주인지 계산하고, 귀속 월도 반환
     * 규칙:
     * - 주는 월요일~일요일.
     * - 한 달에 온전히 포함되지 않으면, 주 시작(월)이 속한 달이면 이전 달, 주 끝(일)이 속한 달이면 다음 달로 귀속.
     */
    private function calculateWeekAndMonth(?string $date, int $year, int $month): array
    {
        if (! $date) {
            return ['week' => '', 'month' => $month];
        }

        $d = Carbon::parse($date);

        // 해당 날짜의 주 경계
        $weekStart = $d->copy()->startOfWeek(CarbonInterface::MONDAY);
        $weekEnd = $d->copy()->endOfWeek(CarbonInterface::SUNDAY);

        // 귀속 월 결정
        if ($weekStart->month !== $month) {
            $ownerMonth = $weekStart->month; // 이전 달
        } elseif ($weekEnd->month !== $month) {
            $ownerMonth = $weekEnd->month;   // 다음 달
        } else {
            $ownerMonth = $month;            // 해당 달
        }

        // 귀속 월의 첫날과, 그 달의 "첫 번째 주 시작 기준점" 계산
        $firstDayOfOwner = Carbon::createFromDate($year, $ownerMonth, 1);
        $anchor = $firstDayOfOwner->copy()->startOfWeek(CarbonInterface::MONDAY);
        if ($anchor->month !== $ownerMonth) {
            // startOfWeek가 이전 달로 넘어가면, 달의 첫날을 기준으로 주차를 계산
            $anchor = $firstDayOfOwner;
        }

        $week = intdiv($anchor->diffInDays($d), 7) + 1;

        return [
            'week' => 'W'.$week,
            'month' => (int) $ownerMonth,
        ];
    }

    /**
     * 수리 결과 반환
     */
    private function getRepairResult(PalletProduct $palletProduct): string
    {
        // 수리 등급에 따른 타입 분류
        $gradeCode = $palletProduct->repairGrade?->code;

        return match ($gradeCode) {
            RepairGrade::GRADE_BEST, RepairGrade::GRADE_GOOD, RepairGrade::GRADE_NORMAL => '점검완료(중)',
            RepairGrade::GRADE_REFURB => '리퍼완료',
            default => 'XL',
        };
    }

    /**
     * 수리 세부 항목들 반환
     */
    private function getRepairCost(?RepairProduct $repairProduct): array
    {
        // 기본값 설정
        $result = [
            'parts' => 0,
            'cleaning' => 0,
            'software' => 0,
            'others' => 0,
            'inspection' => 0,
            'os_cost' => 0,
            'replacement_part' => '',
            'replacement_cost' => 0,
        ];

        if (! $repairProduct) {
            return $result;
        }

        // 수리 부품 정보와 OS 비용 설정
        $result['replacement_part'] = $repairProduct->repairProductParts->pluck('repairPart.name')->filter()->implode(', ');
        $result['os_cost'] = $repairProduct->invoice3 ?? 0;

        // 수리비 정보가 있는 경우 해당 유형에 invoice1 값을 매핑
        if ($repairProduct->repairCost?->repairCostType) {
            $repairTypeCode = $repairProduct->repairCost->repairCostType->code;
            $invoice1Amount = $repairProduct->invoice1 ?? 0;

            // 수리 유형 코드에 따라 해당하는 키에 invoice1 값을 설정
            $key = match ($repairTypeCode) {
                RepairCostType::TYPE_PARTS => 'parts',
                RepairCostType::TYPE_CLEANING => 'cleaning',
                RepairCostType::TYPE_SOFTWARE => 'software',
                RepairCostType::TYPE_OTHER => 'others',
                RepairCostType::TYPE_INSPECTION => 'inspection',
                default => null,
            };

            if ($key) {
                $result[$key] = $invoice1Amount;
            }
        }

        return $result;
    }

    /**
     * 재포장 구성품 반환
     */
    private function getRepackingComponents(RepairProduct $repairProduct): string
    {
        return '';
    }
}
