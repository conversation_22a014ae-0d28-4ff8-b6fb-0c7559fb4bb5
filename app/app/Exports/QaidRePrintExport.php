<?php

namespace App\Exports;

use App\Helpers\XLSXWriter\XLSXWriter;
use App\Models\QaidReprint;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class QaidRePrintExport extends BaseExport
{
    // 식별자로, 이 Export 클래스가 어떤 타입(종류)인지 구분할 때 사용
    public static function type(): string
    {
        return 'qaids';
    }

    // request에서 필요한 데이터 추출
    public static function fromRequest(Request $request): self
    {
        $user = Auth::user();
        $origin = $request->headers->get('origin');

        // 기본 메타데이터 생성
        $meta = [
            'title' => 'QAID리스트',
            'author' => $user->name,
            'company' => 'Cornerstone Project',
            'origin' => $origin,
        ];

        // 실제 쿼리 파라미터
        $data = [
            'beginAt' => $request->input('beginAt'),
            'endAt' => $request->input('endAt'),
            'keyword' => $request->input('keyword'),
        ];

        return new self($meta, $data);
    }

    /**
     * Export 제목 반환<br>
     * 기본 제목이고, 아래 getDynamicTitle() 메서드에서 동적으로 변경됨
     */
    public static function getExportTitle(): string
    {
        return 'QAID 재발행 목록';
    }

    protected function getHeader(): array
    {
        return [
            'A' => ['번호' => 'string'],
            'B' => ['QAID' => 'string'],
            'C' => ['재발행 횟수' => 'integer'],
            'D' => ['제품명' => 'string'],
            'E' => ['작업자' => 'string'],
            'F' => ['재발행 시간' => 'string'],
            'G' => ['설명' => 'string'],
        ];
    }

    protected function getHeaderOption(): array
    {
        $fontSize = 13;
        $height = 30;
        $fill = '#E6E6FA';

        return [
            'widths' => [8, 15, 15, 50, 15, 30, 50],
            'freeze_rows' => 1,
            'freeze_columns' => 1,
            'styles' => [
                'font' => '맑은 고딕',
                'font-size' => $fontSize,
                'font-style' => 'bold',
                'halign' => 'center',
                'fill' => $fill,
                'height' => $height,
            ],
        ];
    }

    protected function getData(): Collection
    {
        $query = QaidReprint::query();

        if (! empty($this->data['beginAt'])) {
            $query->whereDate('created_at', '>=', $this->data['beginAt']);
        }

        if (! empty($this->data['endAt'])) {
            $query->whereDate('created_at', '<=', $this->data['endAt']);
        }

        if (! empty($this->data['keyword'])) {
            $query->where(function ($q) {
                $q->where('qaid', 'like', '%'.$this->data['keyword'].'%')
                    ->orWhere('product_name', 'like', '%'.$this->data['keyword'].'%')
                    ->orWhere('user_name', 'like', '%'.$this->data['keyword'].'%');
            });
        }

        return $query->with([
            'logs',
            'latestProduct',
            'user:id,name',
        ])->orderBy('created_at', 'desc')->get();
    }

    protected function writeRows(XLSXWriter $writer, Collection $rows): void
    {
        $no = 1;

        foreach ($rows as $row) {
            $isFirstLogRow = true; // 각 QaidReprint 항목의 첫 번째 로그 행인지 확인하기 위한 플래그

            foreach ($row->logs as $logIndex => $log) {
                $logItem = [];

                if ($isFirstLogRow) {
                    // 첫 번째 로그 행에만 A, B, C 컬럼 값 채우기
                    $logItem = [
                        'A' => "{$no}",
                        'B' => $row->qaid,
                        'C' => $row->print_count,
                        'D' => $row->latestProduct->name ?? '',
                        'E' => $row->user->name,
                        'F' => $log->created_at->format('Y-m-d H:i:s'),
                        'G' => $log->memo,
                    ];
                    $isFirstLogRow = false; // 플래그 변경
                } else {
                    // 이후 로그 행에는 A, B, C에 빈 값 설정
                    $logItem = [
                        'A' => "{$no}-{$logIndex}",
                        'B' => '',
                        'C' => '',
                        'D' => $row->latestProduct->name ?? '',
                        'E' => $row->user->name,
                        'F' => $log->created_at->format('Y-m-d H:i:s'),
                        'G' => $log->memo,
                    ];
                }

                $writer->writeSheetRow($this->sheetName, $logItem, $this->defaultCellStyle);
            }

            $no++; // 다음 QaidReprint 항목으로 이동
        }

    }
}
