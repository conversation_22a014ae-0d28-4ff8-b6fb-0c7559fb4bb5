<?php

namespace App\Exports;

use App\Helpers\XLSXWriter\XLSXWriter;
use App\Models\Pallet;
use App\Models\PalletProduct;
use App\Models\ProductBarcode;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class PalletExport extends BaseExport
{
    // 식별자로, 이 Export 클래스가 어떤 타입(종류)인지 구분할 때 사용
    public static function type(): string
    {
        return 'pallets';
    }

    // request에서 필요한 데이터 추출
    public static function fromRequest(Request $request): self
    {
        $user = Auth::user();
        $origin = $request->headers->get('origin');

        // 기본 메타데이터 생성
        $meta = [
            'author' => $user->name,
            'company' => 'Cornerstone Project',
            'origin' => $origin,
        ];

        // 동적 제목 설정
        $palletCode = $request->input('palletCode');
        $beginAt = $request->input('beginAt');
        $endAt = $request->input('endAt');

        if ($palletCode) {
            $title = "RP팔레트[ {$palletCode} ]상품 출고리스트";
        } elseif ($beginAt && $endAt) {
            $title = "RP팔레트[ {$beginAt}_{$endAt} ]상품 출고리스트";
        } else {
            $title = '팔레트 상품 출고리스트';
        }
        $meta['title'] = $title;

        // 실제 쿼리 파라미터
        $data = [
            'palletCode' => $palletCode,
            'palletIds' => $request->input('palletIds'),
            'status' => $request->input('status'),
            'beginAt' => $beginAt,
            'endAt' => $endAt,
        ];

        return new self($meta, $data);
    }

    /**
     * Export 제목 반환<br>
     * 기본 제목이고, 아래 getDynamicTitle() 메서드에서 동적으로 변경됨
     */
    public static function getExportTitle(): string
    {
        return '팔레트 상품 출고리스트';
    }

    /**
     * 헤더 정보 반환
     */
    protected function getHeader(): array
    {
        return [
            'A' => ['입고일자' => 'string'],
            'B' => ['수리일자' => 'string'],
            'C' => ['입고처' => 'string'],
            'D' => ['QAID' => 'string'],
            'E' => ['wmsSkuid' => 'string'],
            'F' => ['extid' => 'string'],
            'G' => ['bar_cd' => 'string'],
            'H' => ['4차' => 'string'],
            'I' => ['5차' => 'string'],
            'J' => ['제품명' => 'string'],
            'K' => ['수량' => '#,##0'],
            'L' => ['금액' => '#,##0'],
            'M' => ['VendorName' => 'string'],
            'N' => ['증상내용' => 'string'],
            'O' => ['수리상태' => 'string'],
            'P' => ['담당자' => 'string'],
            'Q' => ['처리내용' => 'string'],
            'R' => ['수리비용' => 'string'],
            'S' => ['별도수리비' => 'string'],
            'T' => ['추가비용' => 'string'],
            'U' => ['출고상태' => 'string'],
            'V' => ['출고일자' => 'string'],
            'W' => ['PLT번호' => 'string'],
            'X' => ['비고' => 'string'], // 10
        ];
    }

    /**
     * 헤더 옵션 반환
     */
    protected function getHeaderOption(): array
    {
        $fontSize = 13;
        $height = 30;

        return [
            'widths' => [12, 12, 17, 12, 12, 12, 17, 20, 20, 50, 8, 12, 25, 30, 12, 17, 17, 12, 12, 12, 20, 12, 17, 10],

            // 상단 첫 번째 행 고정
            'freeze_rows' => 1,
            'freeze_columns' => 2,

            // 전체적으로 하나의 스타일을 적용하려면 이것만 있어도 됨 - height는 안 됨.
            'styles' => [
                'font' => '맑은 고딕',
                'font-size' => $fontSize,
                'font-style' => 'bold',
                'halign' => 'center',
                'fill' => '#A4A4A4',
                'height' => $height,
            ],
        ];
    }

    /**
     * 데이터 조회
     */
    protected function getData(): Collection
    {
        return $this->getProducts($this->data);
    }

    /**
     * 행 데이터 작성
     */
    protected function writeRows(XLSXWriter $writer, Collection $rows): void
    {
        $productBarcodes = ProductBarcode::whereIn('barcode', collect($rows)->pluck('product.barcode')->unique()->filter())->get()
            ->keyBy('barcode');

        foreach ($rows as $row) {
            $row = (object) $row; // 배열을 객체로 변환
            $product = $row->product;
            $barcode = $productBarcodes->get($product->barcode);
            $pallet = $row->pallet;

            $invoice1 = $row->invoice1 === 0 ? ' 0' : number_format($row->invoice1);
            $invoice2 = $row->invoice2 === 0 ? ' 0' : number_format($row->invoice2);
            $invoice3 = $row->invoice3 === 0 ? ' 0' : number_format($row->invoice3);

            $palletCodeArr = explode('-', $pallet->location->code);
            $rg = $product->rg === 'Y' ? 'RG' : '일반';

            $memo = null;
            if (! empty($row->memo)) {
                $memo = "\n".rtrim($row->memo);
            }

            $item = [
                'A' => $product->req->req_at,
                'B' => substr($row->registered_at, 0, 10) ?? '',
                'C' => '코너스톤프로젝트',
                'D' => $product->qaid,
                'E' => $barcode->wms_sku_id ?? '',
                'F' => $barcode->external_wms_sku_id ?? '',
                'G' => $product->barcode,
                'H' => $product->cate4->name ?? '',
                'I' => $product->cate5->name ?? '',
                'J' => $product->name,
                'K' => $product->quantity,
                'L' => $product->amount,
                'M' => $product->vendor->name,
                'N' => $row->repairSymptom->name ?? '',
                'O' => $row->repairGrade->name ?? '',
                'P' => '코너스톤001',
                'Q' => $row->repairProcess->name.$memo,
                'R' => $invoice1,
                'S' => $invoice2,
                'T' => $invoice3,
                'U' => Pallet::$STATUS_NAME[$pallet->status],
                'V' => substr($pallet->exported_at, 0, 10) ?? '',
                'W' => $palletCodeArr[3].'-'.$palletCodeArr[4],
                'X' => $rg,
            ];

            $writer->writeSheetRow($this->sheetName, $item, $this->defaultCellStyle);
        }
    }

    /**
     * 상품 리스트
     */
    private function getProducts(array $data): Collection
    {
        $builder = PalletProduct::with(['product'])
            ->leftJoin('pallets', 'pallets.id', '=', 'pallet_products.pallet_id')
            ->whereIn('pallets.id', $data['palletIds']);

        if (! empty($data['status'])) {
            $builder->where('pallets.status', $data['status']);
        }

        if ($data['beginAt'] && $data['endAt']) {
            $builder->whereBetween('pallets.checked_at', [$data['beginAt'], $data['endAt']]);
        }

        $builder->select('pallet_products.*')
            ->orderBy('pallets.checked_at', 'desc')
            ->orderBy('pallets.registered_at', 'desc')
            ->orderBy('pallet_products.registered_at', 'desc');

        return $builder->get();
    }
}
