<?php

namespace App\Exports;

use App\Helpers\HelperLibrary;
use App\Helpers\MailHelper;
use App\Helpers\XLSXWriter\XLSXWriter;
use App\Mails\ReqReportMail;
use App\Models\Req;
use App\Services\SimpleLogService;
use App\Services\TelegramService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;

class DuplicateQaidExport
{
    private array $defaultCellStyle = [
        'border' => 'left,right,top,bottom',
        'height' => 20,
    ];

    private string $title;

    private string $company;

    private string $redisKey;

    private string $sheetName = 'Sheet1';

    public function __construct(Req $req, string $redisKey)
    {
        $this->title = "[$req->req_at]입고_중복_QAID_리스트_".now()->format('Ymd_Hi');
        $this->company = '코너스톤 프로젝트';
        $this->redisKey = $redisKey;
    }

    private function setHeader(): array
    {
        return [
            'A' => ['번호' => 'integer'], // 8
            'B' => ['입고번호' => 'string'], // 10
            'C' => ['입고일자' => 'string'], // 12
            'D' => ['로트번호' => 'string'], // 20
            'E' => ['카테고리4차' => 'string'], // 20
            'F' => ['카테고리5차' => 'string'], // 20
            'G' => ['QAID' => 'string'], // 15
            'H' => ['바코드' => 'string'], // 20
            'I' => ['상품명' => 'string'], // 50
            'J' => ['중복여부' => 'string'], // 10
            'K' => ['단가' => '#,##0'], // 10
            'L' => ['처리상태' => 'string'], // 17
            'M' => ['수리상태' => 'string'], // 12
            'N' => ['등록일시' => 'string'], // 17
            'O' => ['등록자' => 'string'], // 10
            'P' => ['검수상태' => 'string'], // 10
            'Q' => ['검수일자' => 'string'], // 17
            'R' => ['검수자' => 'string'], // 10
            'S' => ['출고일자' => 'string'], // 12
            'T' => ['비고' => 'string'], // 10
        ];
    }

    /**
     * 문서의 헤더 옵션을 설정
     * - 문서 헤더에 대한 다양한 옵션을 설정합니다.
     *
     * @param  string  $fill  The fill color for the header. Default is '#A4A4A4'.
     * @return array Returns an array containing the header options.
     */
    private function setHeaderOption(string $fill = '#A4A4A4'): array
    {
        $fontSize = 13;
        $height = 30;

        return [
            'widths' => [8, 10, 12, 20, 20, 20, 15, 20, 50, 10, 10, 17, 12, 17, 10, 10, 17, 10, 12, 10],

            // 상단 첫 번째 행 고정
            'freeze_rows' => 1,
            'freeze_columns' => 3,

            // 전체적으로 하나의 스타일을 적용하려면 이것만 있어도 됨 - height는 안 됨.
            'styles' => [
                'font' => '맑은 고딕',
                'font-size' => $fontSize,
                'font-style' => 'bold',
                'halign' => 'center',
                'fill' => $fill,
                'height' => $height,
            ],
        ];
    }

    public function export(): void
    {
        set_time_limit(0);
        ini_set('memory_limit', '1G');

        $writer = new XLSXWriter;
        $writer->setAuthor('System');
        $writer->setCompany($this->company);

        $writer->writeSheetHeader($this->sheetName, $this->setHeader(), $this->setHeaderOption());

        // Redis에서 리스트 타입의 전체 데이터 가져오기
        $redis = Redis::connection(0);
        $allDuplicates = $redis->command('lrange', [
            $this->redisKey, 0, -1,
        ]);

        $decodedDuplicates = [];
        if (! empty($allDuplicates)) {
            // 각 항목이 JSON 문자열이라면 디코딩이 필요할 수 있습니다
            foreach ($allDuplicates as $item) {
                $decodedDuplicates[] = json_decode($item, true);
            }
        }

        $no = 1;
        foreach ($decodedDuplicates as $row) {
            $createdAt = Carbon::parse($row['created_at'])
                ->format('Y-m-d H:i:s');

            $item = [
                'A' => $no++,
                'B' => $row['req_id'],
                'C' => $row['req_at'],
                'D' => $row['lot_full_name'],
                'E' => $row['cate4'],
                'F' => $row['cate5'],
                'G' => $row['qaid'],
                'H' => $row['barcode'],
                'I' => $row['description'],
                'J' => $row['is_duplicated'],
                'K' => $row['amount'],
                'L' => $row['checked_status'],
                'M' => '', // 수리상태
                'N' => $createdAt,
                'O' => $row['user_name'],
                'P' => $row['status'],
                'Q' => '', // Carbon 개체가 아님
                'R' => '',
                'S' => '', // 출고일자
                'T' => $row['is_rg'], // RG 상품
            ];

            $writer->writeSheetRow($this->sheetName, $item, $this->defaultCellStyle);
        }

        $dir = 'temp';
        $filename = $this->title.'.xlsx';
        $filePath = $dir.'/'.$filename;

        $absolutePath = HelperLibrary::getAbsoluteFilePath($dir, $filename);
        $writer->writeToFile($absolutePath);

        // telegram 전송::storage 안의 상대경로
        $telegram = new TelegramService;
        $telegram->sendDocumentToMultipleTeams($filePath, $filename, '입고 등록시 중복 QAID 목록', ['admin', 'req']);

        // 메일 발송
        $this->sendEmailReport($filePath, $filename);

        // 완료 후 삭제
        Storage::delete($filePath);
    }

    /**
     * 메일로 중복 QAID 리포트 발송
     */
    private function sendEmailReport(string $filePath, string $filename): void
    {
        // 수신자 목록 파싱 (설정 파일에서 가져오기 및 검증 포함)
        $recipientList = MailHelper::parseRecipients();

        if (empty($recipientList)) {
            return; // 수신자가 없으면 메일 발송하지 않음
        }

        // 마크다운 형식으로 내용 작성 (Laravel Mail 컴포넌트 호환)
        $mailContent = "입고 등록 시 발견된 **중복 QAID 목록**을 첨부파일로 보내드립니다.\n\n".
                      "## 파일 정보\n\n".
                      "- **파일명**: `{$filename}`\n".
                      '- **생성일시**: '.now()->format('Y-m-d H:i:s')."\n\n".
                      "## 안내사항\n\n".
                      "- 첨부된 엑셀 파일에서 중복된 QAID 목록을 확인하실 수 있습니다.\n".
                      "- 필요한 조치를 취해주시기 바랍니다.\n\n";
        $mailData = [
            'title' => $this->title,
            'content' => $mailContent,
        ];

        try {
            // 각 수신자에게 메일 발송
            foreach ($recipientList as $recipient) {
                Mail::to($recipient['email'], $recipient['name'])->send(new ReqReportMail(
                    $filePath,
                    $filename,
                    $mailData
                ));
            }
        } catch (Exception $e) {
            SimpleLogService::error('req', '중복 QAID 메일 발송 실패', [], $e);
        }
    }
}
