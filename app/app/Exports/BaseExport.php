<?php

namespace App\Exports;

use App\Helpers\HelperLibrary;
use App\Helpers\XLSXWriter\XLSXWriter;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Response as ResponseFacade;
use Illuminate\Support\Facades\Storage;

/**
 * 엑셀 Export의 공통 로직만 담는 간단한 추상 클래스<br>
 * 추상 클래스는 기능의 확장을 위한 템플릿 역할을 합니다.
 * 엑셀 다운로드 로직은 하위 클래스에서 구현합니다.
 */
abstract class BaseExport
{
    protected array $defaultCellStyle = [
        'border' => 'left,right,top,bottom',
        'height' => 20,
    ];

    protected string $title;

    protected string $author;

    protected string $company;

    protected array $data;

    protected string $sheetName = 'Sheet1';

    protected string $origin;

    abstract public static function type(): string;

    abstract public static function fromRequest(Request $request): self;

    public function __construct(array $metaData, array $queryData = [])
    {
        $now = Carbon::now();

        $this->title = $metaData['title'].'_'.$now->format('Ymd_Hi');
        $this->author = $metaData['author'] ?? '';
        $this->company = $metaData['company'] ?? 'Cornerstone Project';
        $this->origin = $metaData['origin'] ?? '';
        $this->data = $queryData;
    }

    /**
     * Export 제목 반환 (하위 클래스에서 구현)
     */
    abstract public static function getExportTitle(): string;

    /**
     * 헤더 정보 반환 (하위 클래스에서 구현)
     */
    abstract protected function getHeader(): array;

    /**
     * 헤더 옵션 반환 (하위 클래스에서 구현)
     */
    abstract protected function getHeaderOption(): array;

    /**
     * 데이터 조회 (하위 클래스에서 구현)
     */
    abstract protected function getData(): Collection;

    /**
     * 행 데이터 작성 (하위 클래스에서 구현)
     */
    abstract protected function writeRows(XLSXWriter $writer, Collection $rows): void;

    /**
     * 엑셀 파일 다운로드 실행
     */
    public function export()
    {
        $this->setExportLimits();

        $filename = $this->title.'.xlsx';
        $writer = $this->createWriter();

        $this->writeSheetHeader($writer);
        $this->writeSheetData($writer);

        return $this->downloadFile($writer, $filename);
    }

    /**
     * Export 제한 설정
     */
    protected function setExportLimits(): void
    {
        set_time_limit(0);
        ini_set('memory_limit', '1G');
    }

    /**
     * XLSXWriter 인스턴스 생성 및 기본 설정
     */
    protected function createWriter(): XLSXWriter
    {
        $writer = new XLSXWriter;

        if ($this->author) {
            $writer->setAuthor($this->author);
        }

        $writer->setCompany($this->company);

        return $writer;
    }

    /**
     * 시트 헤더 작성
     */
    protected function writeSheetHeader(XLSXWriter $writer): void
    {
        $writer->writeSheetHeader($this->sheetName, $this->getHeader(), $this->getHeaderOption());
    }

    /**
     * 시트 데이터 작성
     */
    protected function writeSheetData(XLSXWriter $writer): void
    {
        $rows = $this->getData();

        if ($rows->isEmpty()) {
            $this->writeEmptyMessage($writer);
        } else {
            $this->writeRows($writer, $rows);
        }
    }

    /**
     * 빈 데이터 메시지 작성
     */
    protected function writeEmptyMessage(XLSXWriter $writer): void
    {
        $row['A'] = '조회된 내역이 없습니다.';
        $writer->writeSheetRow($this->sheetName, $row);
    }

    /**
     * 파일 다운로드 처리
     */
    protected function downloadFile(XLSXWriter $writer, string $filename)
    {
        // 1. Storage를 기준으로 상대 경로를 먼저 정의합니다.
        $tempRelativePath = 'temp/'.$filename;

        // 2. Storage::path()를 사용해 XLSXWriter가 사용할 절대 경로를 얻습니다.
        $absolutePathForWriter = Storage::path($tempRelativePath);

        // 3. XLSXWriter에 Storage 기준 절대 경로를 전달하여 파일을 씁니다.
        $writer->writeToFile($absolutePathForWriter);

        // 4. 이제 Storage는 방금 생성된 파일을 정확히 찾을 수 있습니다.
        $fileContent = Storage::get($tempRelativePath);
        $size = Storage::size($tempRelativePath);

        // 5. 임시 파일을 삭제합니다.
        Storage::delete($tempRelativePath);

        // 6. 다운로드 헤더 설정(CORS 관련 헤더는 제외)
        // RFC 5987 표준에 따라 non-ASCII 파일명 처리
        $sanitizedFilename = XLSXWriter::sanitize_filename($filename);
        $asciiFilename = 'export.xlsx'; // 구형 브라우저를 위한 ASCII 전용 fallback 파일명
        $encodedFilename = rawurlencode($sanitizedFilename);
        // 표준에 맞는 Content-Disposition 헤더 문자열 생성
        $dispositionHeader = sprintf(
            'attachment; filename="%s"; filename*=UTF-8\'\'%s',
            $asciiFilename,
            $encodedFilename
        );
        $header = [
            'Content-Disposition' => $dispositionHeader,
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Transfer-Encoding' => 'binary',
            'Content-Length' => $size,
            'Cache-Control' => 'must-revalidate',
            'Expires' => '0',
            'Pragma' => 'public',
        ];

        // 7. Response 객체를 생성하여 반환(직접 출력하지 않음)
        return ResponseFacade::make($fileContent, 200, $header);

        // $absolutePath = HelperLibrary::getAbsoluteFilePath('temp', $filename);
        // $writer->writeToFile($absolutePath);
        // $size = Storage::size('temp/' . $filename);
        // Storage::delete('temp/' . $filename);
        //
        // // 다운로드 헤더 설정
        // $formatFilename = rawurlencode(XLSXWriter::sanitize_filename($filename));
        // HelperLibrary::excelDownloadHeaders($formatFilename, $size, $this->origin);
        //
        // // 실제 파일 출력
        // $writer->writeToStdOut();
    }
}
