<?php

namespace App\Listeners;

use App\Events\NotificationSent;
use App\Services\SSEBroadcastService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * SSE 브로드캐스트 리스너
 * 
 * 알림 전송 이벤트를 받아서 SSE를 통해 
 * 해당 수신자들에게 실시간으로 알림을 전달합니다.
 */
class BroadcastNotificationToSSE implements ShouldQueue
{
    use InteractsWithQueue;

    public function __construct(
        private SSEBroadcastService $sseBroadcastService
    ) {}

    /**
     * 이벤트 처리
     *
     * @param NotificationSent $event
     * @return void
     */
    public function handle(NotificationSent $event): void
    {
        try {
            // SSE를 통해 수신자들에게 알림 브로드캐스트
            $this->sseBroadcastService->broadcastNotification(
                $event->notification,
                $event->recipientIds
            );

            Log::info('SSE 알림 브로드캐스트 완료', [
                'notification_id' => $event->notification->id,
                'recipients_count' => count($event->recipientIds)
            ]);

        } catch (Throwable $e) {
            Log::error('SSE 알림 브로드캐스트 실패', [
                'notification_id' => $event->notification->id,
                'error' => $e->getMessage(),
                'recipients_count' => count($event->recipientIds)
            ]);

            // 큐 재시도를 위해 예외를 다시 던짐
            throw $e;
        }
    }

    /**
     * 실패한 작업 처리
     *
     * @param NotificationSent $event
     * @param Throwable $exception
     * @return void
     */
    public function failed(NotificationSent $event, Throwable $exception): void
    {
        Log::error('SSE 알림 브로드캐스트 최종 실패', [
            'notification_id' => $event->notification->id,
            'recipients_count' => count($event->recipientIds),
            'error' => $exception->getMessage()
        ]);
    }
}