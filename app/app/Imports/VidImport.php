<?php

namespace App\Imports;

use App\Models\ProductLink;
use App\Services\SimpleLogService;
use Exception;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithCalculatedFormulas;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Events\AfterImport;
use Maatwebsite\Excel\Events\ImportFailed;

/**
 * Vid.xlsx 파일 임포트 클래스
 * external_wms_sku_id, vendor_item_id, product_id, item_id 정보를 ProductLink 테이블에 저장
 */
class VidImport implements ToCollection, WithCalculatedFormulas, WithEvents, WithStartRow
{
    use Importable;

    protected int $startRow;

    protected float $startTime;

    public int $totalProcessed = 0;

    public int $totalCreated = 0;

    public int $totalSkipped = 0;

    public function __construct(int $startRow = 2)
    {
        $this->startRow = $startRow;
        $this->startTime = microtime(true);
    }

    public function startRow(): int
    {
        return $this->startRow;
    }

    public function registerEvents(): array
    {
        return [
            ImportFailed::class => function (ImportFailed $event) {
                $this->importFailedHandler($event);
            },
            AfterImport::class => function (AfterImport $event) {
                $this->afterImportHandler($event);
            },
        ];
    }

    protected function importFailedHandler(ImportFailed $event): void
    {
        SimpleLogService::error('vid', 'Vid.xlsx 임포트 오류 발생', [], $event->e);
    }

    protected function afterImportHandler(AfterImport $event): void
    {
        $endTime = microtime(true);
        $processingTime = $endTime - $this->startTime;

        $message = "Vid.xlsx 임포트 완료\n";
        $message .= "처리된 행: {$this->totalProcessed}개\n";
        $message .= "새로 생성: {$this->totalCreated}개\n";
        $message .= "건너뛴 행: {$this->totalSkipped}개\n";
        $message .= '처리 시간: '.round($processingTime, 2).'초';

        SimpleLogService::info('vid', $message, [
            'total_processed' => $this->totalProcessed,
            'total_created' => $this->totalCreated,
            'total_skipped' => $this->totalSkipped,
            'processing_time' => round($processingTime, 2),
        ]);
    }

    public function collection(Collection $collection): void
    {
        foreach ($collection as $row) {
            $this->totalProcessed++;

            try {
                $item = $this->prepareRow($row->toArray());
                $this->processRow($item);
            } catch (Exception $e) {
                $this->totalSkipped++;
                SimpleLogService::warning('vid', '행 처리 실패', [
                    'row_number' => $this->totalProcessed + $this->startRow - 1,
                    'row_data' => $row->toArray(),
                    'error' => $e->getMessage(),
                ], $e);
            }
        }
    }

    /**
     * 행 데이터 준비
     *
     * @throws Exception
     */
    protected function prepareRow(array $row): array
    {
        // Vid.xlsx의 컬럼 순서에 맞게 매핑
        // 실제 컬럼 순서에 따라 인덱스 조정 필요
        $item = [
            'external_wms_sku_id' => $this->removeCarriageReturn($row[0] ?? null),
            'vendor_item_id' => $this->removeCarriageReturn($row[1] ?? null),
            'product_id' => $this->removeCarriageReturn($row[2] ?? null),
            'item_id' => $this->removeCarriageReturn($row[3] ?? null),
        ];

        // 필수 필드 검증
        $requiredFields = ['external_wms_sku_id', 'vendor_item_id'];
        $missingFields = [];

        foreach ($requiredFields as $field) {
            if (empty($item[$field])) {
                $missingFields[] = $field;
            }
        }

        if (! empty($missingFields)) {
            throw new Exception('필수 필드 누락: '.implode(', ', $missingFields));
        }

        // 모든 필드가 비어있는 경우 빈 행으로 처리
        $allEmpty = true;
        foreach ($item as $value) {
            if (! empty($value)) {
                $allEmpty = false;
                break;
            }
        }

        if ($allEmpty) {
            throw new Exception('완전히 빈 행 감지됨');
        }

        return $item;
    }

    /**
     * 행 처리
     */
    protected function processRow(array $item): void
    {
        // [external_wms_sku_id, vendor_item_id] 조합으로 기존 레코드 확인
        $existingLink = ProductLink::where([
            'external_wms_sku_id' => $item['external_wms_sku_id'],
            'vendor_item_id' => $item['vendor_item_id'],
        ])->first();

        if ($existingLink) {
            // 기존 레코드가 있으면 건너뛰기
            $this->totalSkipped++;
            SimpleLogService::info('vid', '기존 ProductLink 건너뛰기', [
                'external_wms_sku_id' => $item['external_wms_sku_id'],
                'vendor_item_id' => $item['vendor_item_id'],
                'existing_id' => $existingLink->id,
            ]);

            return;
        }

        // 새로운 레코드 생성
        $additionalData = [];
        if (! empty($item['product_id']) || ! empty($item['item_id'])) {
            $additionalData['product_id'] = $item['product_id'] ?? null;
            $additionalData['item_id'] = $item['item_id'] ?? null;
        }

        ProductLink::create(array_merge([
            'external_wms_sku_id' => $item['external_wms_sku_id'],
            'vendor_item_id' => $item['vendor_item_id'],
        ], $additionalData));

        $this->totalCreated++;

        SimpleLogService::info('vid', '새로운 ProductLink 생성', [
            'external_wms_sku_id' => $item['external_wms_sku_id'],
            'vendor_item_id' => $item['vendor_item_id'],
            'product_id' => $item['product_id'],
            'item_id' => $item['item_id'],
        ]);
    }

    /**
     * 캐리지 리턴 처리
     */
    private function removeCarriageReturn(?string $string = null): ?string
    {
        if (! $string) {
            return null;
        }

        return trim(preg_replace('/\r\n|\r|\n/', ' ', $string));
    }
}
