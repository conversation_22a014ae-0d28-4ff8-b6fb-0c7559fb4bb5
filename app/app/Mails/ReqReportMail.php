<?php

namespace App\Mails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class ReqReportMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * 새로운 메시지 인스턴스를 생성
     *
     * @param  string  $filePath  base64로 인코딩된 첨부파일 데이터
     * @param  string  $filename  첨부파일명
     * @param  array  $mailData  메일 내용
     */
    public function __construct(
        public string $filePath,
        public string $filename,
        public array $mailData
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->mailData['title'],
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'emails.req-report',
            with: [
                'title' => $this->mailData['title'],
                'content' => $this->mailData['content'],
            ],
        );
    }

    public function attachments(): array
    {
        $mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

        // Storage 경로인지 확인하여 적절한 방법으로 첨부
        if (Storage::exists($this->filePath)) {
            return [
                Attachment::fromStorage($this->filePath)
                    ->as($this->filename)
                    ->withMime($mimeType),
            ];
        }

        // 절대 경로인 경우 fromPath 사용
        if (file_exists($this->filePath)) {
            return [
                Attachment::fromPath($this->filePath)
                    ->as($this->filename)
                    ->withMime($mimeType),
            ];
        }

        return [];
    }
}
