<?php

namespace App\Traits\Product;

use App\Exceptions\CarryoutException;
use App\Exceptions\ProductException;
use App\Models\Product;

trait SearchTrait
{
    /**
     * 해당 id를 가진 제품이 존재하는지 확인합니다.(중복 방지)
     *
     * @return bool Returns true if a product with the given qaid exists, false otherwise.
     *
     * @throws ProductException
     */
    public function existsProductById(int $id): bool
    {
        $isExists = Product::where('id', $id)->exists();

        if (! $isExists) {
            throw ProductException::notFound($id);
        }

        return $isExists;
    }

    /**
     * 해당 qaid를 가진 제품이 존재하는지 확인합니다.(중복 방지)
     *
     * @param  string  $qaid  The qaid to check.
     * @return bool Returns true if a product with the given qaid exists, false otherwise.
     *
     * @throws ProductException
     */
    public function existsProductByQaid(string $qaid): bool
    {
        $qaid = mb_strtoupper(trim($qaid));
        $isExists = Product::where('qaid', $qaid)->exists();

        if (! $isExists) {
            throw ProductException::notFound($qaid);
        }

        return $isExists;
    }

    /**
     * 상품의 id로 상품을 검색합니다.
     *
     * @param  int  $id  상품 ID
     * @return Product 조회된 상품
     *
     * @throws ProductException
     */
    public function findProductById(int $id): Product
    {
        $product = Product::find($id);

        if ($product === null) {
            throw ProductException::notFound($id);
        }

        return $product;
    }

    /**
     * QAID로 상품을 조회합니다.
     *
     * @param  string  $qaid  상품 QAID
     * @return Product 조회된 상품
     *
     * @throws ProductException
     */
    public function findProductByQaid(string $qaid): Product
    {
        $qaid = mb_strtoupper(trim($qaid));
        $product = Product::where('qaid', $qaid)
            ->orderByDesc('req_id')
            ->first();

        if ($product === null) {
            throw ProductException::notFound($qaid);
        }

        return $product;
    }

    /**
     * 반출 가능한 상품을 조회합니다.
     *
     * @param  string  $qaid  상품 QAID
     * @return Product 조회된 상품
     *
     * @throws CarryoutException
     * @throws ProductException
     *
     * @todo: 원칙적으로 중복되는 상품이 없어야 하지만 예전 시스템에서는 중복 처리 문제로 중복된 QAID가 발생 했었음. 따라서 중복된 QAID가 있다면 가장 최근에 입고된 상품을 조회하도록 수정
     */
    public function findExportableProducts(string $qaid): Product
    {
        $qaid = mb_strtoupper(trim($qaid));
        $product = $this->findProductByQaid($qaid);

        if ($product->checked_status === Product::CHECKED_STATUS_UNDELIVERED) {
            throw ProductException::undelivered($qaid);
        }

        if ($product->status !== Product::STATUS_REGISTERED) {
            $statusName = Product::$STATUS_NAME[$product->status] ?? '알 수 없음';
            throw CarryoutException::isNotExportable($qaid, $statusName);
        }

        return $product;
    }

    /**
     * 반입 가능한 상품을 조회 합니다.
     *
     * @param  string  $qaid  상품 QAID
     * @return Product 조회된 상품
     *
     * @throws CarryoutException 상품이 존재하지 않거나 상태가 맞지 않을 때
     * @throws ProductException
     */
    public function findImportableProduct(string $qaid): Product
    {
        $qaid = mb_strtoupper(trim($qaid));
        $product = $this->findProductByQaid($qaid);

        if ($product->checked_status === Product::CHECKED_STATUS_UNDELIVERED) {
            throw ProductException::undelivered($qaid);
        }

        if (! in_array($product->status, [
            Product::STATUS_CARRIED_OUT,
            Product::STATUS_CARRIED_OUT_WAITING,
            Product::STATUS_CARRIED_OUT_REPAIRED,
        ])) {
            $statusName = Product::$STATUS_NAME[$product->status] ?? '알 수 없음';
            throw CarryoutException::isNotImportable($qaid, $statusName);
        }

        return $product;
    }
}
