<?php

namespace App\Traits\Repair;

use App\Models\Product;
use App\Models\RepairFee;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;

trait AppleTrait
{
    /**
     * 애플 제품인지 확인(애플 제품의 경우 req_type이 2로 설정되어 있음)<br>
     * repair_ree_category_id: 맥북(3), 아이맥(5), etc
     */
    public function isAppleProduct(Product $product): bool
    {
        return $product->req->req_type === 2;
    }

    /**
     * Apple 제품 모델 판별 헬퍼 메서드
     *
     * @return array ['model' => string, 'feeType' => string]
     */
    public function getAppleProductModel(Product $product): array
    {
        if ($product->cate4 === 91 && $product->cate5 === 88) {
            return ['model' => '맥북', 'feeType' => 'none'];
        } elseif ($product->cate4 === 91 && $product->cate5 === 89) {
            return ['model' => '아이맥', 'feeType' => 'none'];
        }

        return ['model' => 'etc', 'feeType' => 'price'];
    }

    /**
     * 애플 제품의 기본 수리비 범위 ID 가져오기
     */
    public function getDefaultAppleFeeRangeId(Product $product): int
    {
        $amount = $product->amount;

        if ($amount <= 50000) {
            return RepairFee::DEFAULT_FEE_RANGE_IDS['APPLE_CHECK_1'];
        } elseif ($amount <= 100000) {
            return RepairFee::DEFAULT_FEE_RANGE_IDS['APPLE_CHECK_2'];
        } elseif ($amount <= 200000) {
            return RepairFee::DEFAULT_FEE_RANGE_IDS['APPLE_CHECK_3'];
        } else {
            return RepairFee::DEFAULT_FEE_RANGE_IDS['APPLE_CHECK_4'];
        }
    }

    /**
     * 애플 제품 기본 가격 정보 가져오기 (General과 동일한 형식으로 변경)
     */
    public function getDefaultAppleFeeList(Product $product, string $repairType = 'CHECK'): array
    {
        // 애플 제품은 점검만 있으므로 repairType은 항상 CHECK
        $defaultRangeId = $this->getDefaultAppleFeeRangeId($product);
        $defaultInfo = RepairFee::getDefaultFeeRangeInfo($defaultRangeId);

        // 애플 제품 기본값 ID들
        $appleDefaultIds = [
            RepairFee::DEFAULT_FEE_RANGE_IDS['APPLE_CHECK_1'],
            RepairFee::DEFAULT_FEE_RANGE_IDS['APPLE_CHECK_2'],
            RepairFee::DEFAULT_FEE_RANGE_IDS['APPLE_CHECK_3'],
            RepairFee::DEFAULT_FEE_RANGE_IDS['APPLE_CHECK_4'],
        ];

        // DEFAULT_FEE_RANGES에서 애플 제품 기본값들을 동적으로 생성
        $feeRangeList = [];
        foreach ($appleDefaultIds as $id) {
            $feeInfo = RepairFee::getDefaultFeeRangeInfo($id);
            if ($feeInfo) {
                $unitName = RepairFee::$FEE_UNIT_NAME[$feeInfo['fee_unit']] ?? '원';
                $minValue = number_format($feeInfo['min_value']);
                $maxValue = $feeInfo['max_value'] >= 100000000 ? '∞' : number_format($feeInfo['max_value']);

                $feeRangeList[] = [
                    'id' => $id,
                    'min_value' => $feeInfo['min_value'],
                    'max_value' => $feeInfo['max_value'],
                    'amount' => $feeInfo['amount'],
                    'formatted_amount' => number_format($feeInfo['amount']).'원',
                    'range' => " {$minValue}{$unitName} 초과 ~ {$maxValue}{$unitName} 이하",
                ];
            }
        }

        return [
            'feeRangeList' => $feeRangeList,
            'invoiceAmount' => $defaultInfo['amount'],
            'minValue' => $defaultInfo['min_value'],
            'maxValue' => $defaultInfo['max_value'],
            'defaultRangeId' => $defaultRangeId,
        ];
    }

    /**
     * 애플제품 수리 요금 쿼리 빌더<br>
     * 애플 제품은 점검만 있음: repair_type = CHECK
     */
    public function appleFeeListQuery($product, $model, $feeType): Builder
    {
        return DB::table('repair_categories')
            ->join('repair_fee_ranges', 'repair_categories.id', '=', 'repair_fee_ranges.repair_category_id')
            ->join('repair_fees', 'repair_fee_ranges.id', '=', 'repair_fees.repair_fee_range_id')
            ->where('repair_categories.cate4_id', $product->cate4_id)
            ->where('repair_categories.cate5_id', $product->cate5_id)
            ->where('repair_fee_ranges.type', 'apple')
            ->where('repair_fee_ranges.model', $model)
            ->where('repair_fee_ranges.fee_type', RepairFee::FEE_TYPE[$feeType])
            ->where('repair_fees.repair_type', RepairFee::REPAIR_TYPE['CHECK'])
            ->select(
                'repair_fee_ranges.id',
                'repair_fee_ranges.fee_type',
                'repair_fee_ranges.fee_unit',
                'repair_fee_ranges.min_value',
                'repair_fee_ranges.max_value',
                'repair_fees.amount'
            );
    }
}
