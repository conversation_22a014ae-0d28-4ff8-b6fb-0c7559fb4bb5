<?php

namespace App\Traits\Repair;

use App\Models\RepairProcess;
use Exception;

trait ProcessTrait
{
    use SymptomTrait;

    public function findProcess(int $id, array $select = ['*']): RepairProcess
    {
        return RepairProcess::select($select)->find($id);
    }

    public function findProcessIdByCode(string $code): int
    {
        return RepairProcess::where('code', $code)->first()->id;
    }

    public function findProcessNameByCode(string $code): int
    {
        return RepairProcess::where('code', $code)->first()->name;
    }

    /**
     * 증상에서 연결된 프로세스를 가져옵니다.
     *
     * @param  int  $symptomId  증상 ID
     * @param  int|null  $gradeId  등급 ID (선택사항, null이면 모든 프로세스 반환)
     * @return array 프로세스 배열 (프로세스 ID를 키로 하는 객체)
     */
    public function getProcesses(int $symptomId, ?int $gradeId = null): array
    {
        try {
            $symptom = $this->findSymptom($symptomId);

            // 등급 ID가 제공된 경우, 해당 등급과 증상이 연결되어 있는지 먼저 확인
            if ($gradeId !== null) {
                $grade = \App\Models\RepairGrade::find($gradeId);
                if (! $grade || ! $grade->repairSymptoms->contains('id', $symptomId)) {
                    return []; // 등급과 증상이 연결되지 않았으면 빈 배열 반환
                }
            }

            // default_repair_process_id가 설정되어 있는지 확인
            if ($symptom->default_repair_process_id) {
                // defaultRepairProcess 관계를 사용하여 기본 process를 가져옴
                $defaultProcess = $symptom->defaultRepairProcess;

                // 기본 process가 존재하지 않으면 빈 배열 반환
                if (! $defaultProcess) {
                    return [];
                }

                // 등급 ID가 제공된 경우, 해당 등급과 연결된 프로세스인지 확인
                if ($gradeId !== null) {
                    $grade = \App\Models\RepairGrade::find($gradeId);
                    if ($grade && ! $grade->repairProcesses->contains('id', $defaultProcess->id)) {
                        return []; // 등급과 연결되지 않은 프로세스는 제외
                    }
                }

                // 프로세스 ID를 키로 하는 객체 형태로 반환
                $result[] = $defaultProcess->toArray();

                return $result;
            }

            // default_repair_process_id가 null이면,
            // repairProcesses 관계를 통해 관련된 모든 process를 가져옴
            $processes = $symptom->repairProcesses;

            // 등급 ID가 제공된 경우, 해당 등급과 연결된 프로세스만 필터링
            if ($gradeId !== null) {
                $grade = \App\Models\RepairGrade::find($gradeId);
                if ($grade) {
                    $processes = $processes->filter(function ($process) use ($grade) {
                        return $grade->repairProcesses->contains('id', $process->id);
                    });
                }
            }

            // 프로세스 ID를 키로 하는 객체 형태로 변환
            $result = [];
            foreach ($processes as $process) {
                $result[] = $process->toArray();
            }

            return $result;
        } catch (Exception $e) {
            return [];
        }
    }
}
