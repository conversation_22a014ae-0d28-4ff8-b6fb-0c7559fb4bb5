<?php

namespace App\Traits\Repair;

use App\Models\RepairGrade;
use App\Models\Req;

trait GradeTrait
{
    use ProcessTrait;

    /**
     * 모든 수리 등급을 조회합니다.
     *
     * @param  int  $reqType  요청 타입 (Req::TYPE_COUPANG 또는 Req::TYPE_APPLE)
     * @return array 수리 등급 배열
     */
    public function getGradesByReqType(int $reqType): array
    {
        // 애플 요청인 경우 ST_XL 코드만 필터링
        if ($reqType === Req::TYPE_APPLE) {
            return RepairGrade::where('code', 'ST_XL')->get()->toArray();
        }

        // 일반 쿠팡 요청인 경우 모든 등급 조회
        return RepairGrade::orderBy('order_no')->get()->toArray();
    }

    public function getGrades(int $processId): array
    {
        $process = $this->findProcess($processId);

        return $process->repairGrades->toArray();
    }

    public function getGradeCodeById(int $gradeId): RepairGrade
    {
        return RepairGrade::where('id', $gradeId)
            ->select('code')
            ->first();
    }

    public function findGradeIdByCode(string $code): int
    {
        return RepairGrade::where('code', $code)->first()->id;
    }

    public function findGradeByCode(string $code): RepairGrade
    {
        return RepairGrade::where('code', $code)->first();
    }

    /**
     * 등급에서 연결된 증상과 프로세스를 가져옵니다.
     *
     * @param  int  $gradeId  등급 ID
     * @param  int  $reqType  요청 타입 (Req::TYPE_COUPANG 또는 Req::TYPE_APPLE)
     * @return array 증상과 프로세스 정보
     */
    public function getSymptomsAndProcessesByGrade(int $gradeId, int $reqType): array
    {
        $grade = RepairGrade::findOrFail($gradeId);

        // 요청 타입에 따른 증상 타입 매핑
        $symptomType = $reqType === Req::TYPE_APPLE ? 'apple' : 'general';

        // 등급과 직접 연결된 증상들을 가져옴 (reqType에 따라 필터링)
        $symptoms = $grade->repairSymptoms()->where('type', $symptomType)->get();

        // 등급과 직접 연결된 프로세스들을 가져옴
        $gradeProcesses = $grade->repairProcesses;

        // 각 증상과 연결된 프로세스들을 수집 (reqType에 따라 필터링)
        $processSymptomMap = [];
        $validProcessIds = [];

        foreach ($symptoms as $symptom) {
            // 증상과 연결된 프로세스들을 가져옴
            $symptomProcesses = $symptom->repairProcesses;

            // 증상과 연결된 프로세스 중에서 등급과도 연결된 프로세스만 필터링
            $validProcesses = $symptomProcesses->filter(function ($process) use ($gradeProcesses) {
                return $gradeProcesses->contains('id', $process->id);
            });

            // 유효한 프로세스 ID들을 수집
            foreach ($validProcesses as $process) {
                $validProcessIds[] = $process->id;
                if (! isset($processSymptomMap[$process->id])) {
                    $processSymptomMap[$process->id] = [];
                }
                $processSymptomMap[$process->id][] = $symptom->id;
            }
        }

        // 중복 제거된 유효한 프로세스들만 가져옴
        $validProcessIds = array_unique($validProcessIds);
        $processes = $gradeProcesses->whereIn('id', $validProcessIds);

        // 프로세스 ID를 키로 하는 객체 형태로 변환
        $processesArray = [];
        foreach ($processes as $process) {
            $processesArray[] = $process->toArray();
        }

        return [
            'symptoms' => $symptoms->toArray(),
            'processes' => $processesArray,
            'process_symptom_map' => $processSymptomMap,
        ];
    }
}
