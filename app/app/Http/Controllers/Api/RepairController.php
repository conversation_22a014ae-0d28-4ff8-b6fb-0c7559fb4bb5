<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreRepairRequest;
use App\Models\Product;
use App\Models\RepairParts;
use App\Models\RepairPartsCategory;
use App\Services\ProductService;
use App\Services\RepairService;
use App\Services\Validation\RepairService as ValidationService;
use App\Traits\Product\SearchTrait as ProductSearchTrait;
use App\Traits\Repair\GradeTrait;
use App\Traits\Repair\ProcessTrait;
use App\Traits\Repair\SymptomTrait;
use Cache;
use Exception;
use Illuminate\Http\JsonResponse;
use Throwable;

class RepairController extends Controller
{
    use GradeTrait, ProcessTrait, ProductSearchTrait, SymptomTrait;

    private ProductService $productService;

    private RepairService $repairService;

    private ValidationService $validationService;

    public function __construct(
        ProductService $productService,
        RepairService $repairService,
        ValidationService $validationService,
    ) {
        $this->productService = $productService;
        $this->repairService = $repairService;
        $this->validationService = $validationService;
    }

    /**
     * 수리/점검 가능한 상품인지 확인
     */
    public function checkProduct(string $qaid): JsonResponse
    {
        try {
            $qaid = mb_strtoupper(trim($qaid));
            $this->existsProductByQaid($qaid);

            $product = $this->productService->findByQaid($qaid);
            $this->validationService->canBeRepaired($product, $qaid);

            $grades = $this->getGradesByReqType($product->req->req_type);

            return $this->successResponse([
                'product' => $product,
                'grades' => $grades,
            ]);
        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
                'log_message' => $e->getMessage(),
            ], $e, 'repair', 400);
        }
    }

    public function getSymptom(int $reqType): JsonResponse
    {
        return $this->successResponse([
            'symptoms' => $this->getSymptoms($reqType),
        ]);
    }

    public function getProcess(int $symptomId, ?int $gradeId = null): JsonResponse
    {
        return $this->successResponse([
            'processes' => $this->getProcesses($symptomId, $gradeId),
        ]);
    }

    public function getGrade(int $processId): JsonResponse
    {
        return $this->successResponse([
            'grades' => $this->getGrades($processId),
        ]);
    }

    /**
     * 등급에서 연결된 증상과 프로세스를 가져옵니다.
     *
     * @param  int  $gradeId  등급 ID
     * @param  int  $req_type  요청 타입
     */
    public function getSymptomsAndProcesses(int $gradeId, int $req_type): JsonResponse
    {
        try {
            $data = $this->getSymptomsAndProcessesByGrade($gradeId, $req_type);

            return $this->successResponse($data);
        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => '증상 및 프로세스 조회 실패',
                'log_message' => $e->getMessage(),
            ], $e, 'repair', 400);
        }
    }

    public function getPartsCategories(): JsonResponse
    {
        // 캐시 키 정의
        $cacheKey = 'repair_parts_categories_parents_null';

        // 캐시에서 카테고리 데이터 조회 (24시간 유지)
        $categories = Cache::remember($cacheKey, 86400, function () {
            return RepairPartsCategory::whereNull('parent_id')
                ->select(['id', 'name', 'parent_id', 'level', 'order_no'])
                ->orderBy('order_no')
                ->get();
        });

        return $this->successResponse([
            'categories' => $categories,
        ]);
    }

    // 구성품 카테고리 모두 나와야 하고 구성품 리스트도 따로 나와야 함. 단 구성품의 카테고리가 선택되면 그에 맞는 구성품만 리스트에 보여줘야 함
    public function getParts(?int $categoryId = null): JsonResponse
    {
        // 선택된 카테고리의 구성품 조회
        if ($categoryId) {
            $selectedCategory = RepairPartsCategory::find($categoryId);
            $parts = $selectedCategory->repairParts()->get();
        } else {
            $parts = RepairParts::all();
        }

        return $this->successResponse([
            'parts' => $parts,
        ]);
    }

    /**
     * 수리/점검 내용 기록
     */
    public function store(StoreRepairRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();

            $product = Product::find($data['product_id']);

            if ($data['status'] === 'complete') {
                $this->repairService->complete($data, $product);
            } elseif ($data['status'] === 'waiting') {
                $this->repairService->waiting($data, $product);
            } else {
                throw new Exception('지원하지 않는 수리 상태입니다.');
            }

            return $this->successResponse([
                'success' => true,
            ]);
        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => '수리/점검 저장 실패',
                'log_message' => '수리/점검 저장 실패(RepairController::store)',
            ], $e, 'repair', 400);
        }
    }
}
