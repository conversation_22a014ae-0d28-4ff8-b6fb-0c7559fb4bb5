<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\NotificationHistoryRequest;
use App\Models\Notification;
use Illuminate\Http\JsonResponse;

class NotificationHistoryController extends Controller
{
    /**
     * 알림 히스토리 목록 조회 (필터링, 검색, 페이지네이션 포함)
     *
     * @param NotificationHistoryRequest $request
     * @return JsonResponse
     */
    public function index(NotificationHistoryRequest $request): JsonResponse
    {
        $query = Notification::with('sender:id,name')
            ->withCount('recipients');

        // 검색 처리 (제목 또는 내용에서 검색)
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('content', 'LIKE', "%{$searchTerm}%");
            });
        }

        // 우선순위 필터
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // 상태 필터
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        } else {
            // status가 지정되지 않은 경우 기본적으로 sent 상태만 조회
            $query->where('status', 'sent');
        }

        // 대상 유형 필터
        if ($request->filled('target_type') && $request->target_type !== 'all') {
            $query->where('target_type', $request->target_type);
        }

        // 기간 검색 처리
        if ($request->filled('date_from')) {
            $query->where('sent_at', '>=', $request->date_from . ' 00:00:00');
        }

        if ($request->filled('date_to')) {
            $query->where('sent_at', '<=', $request->date_to . ' 23:59:59');
        }

        // 페이지 크기 설정 (기본값: 20, 최대: 100)
        $pageSize = $request->filled('pageSize') ? min((int)$request->pageSize, 100) : 20;

        // 전송시간 역순으로 정렬하여 페이지네이션
        $notifications = $query->orderBy('sent_at', 'desc')
            ->paginate($pageSize)
            ->withQueryString();

        return $this->successResponse([
            'items' => $notifications->items(),
            'pagination' => PaginationHelper::optimize($notifications),
        ], '알림 히스토리를 성공적으로 조회했습니다.');
    }

    /**
     * 특정 알림 상세 조회
     *
     * @param Notification $notification
     * @return JsonResponse
     */
    public function show(Notification $notification): JsonResponse
    {
        // 발송자 정보와 수신자 통계 로드
        $notification->load([
            'sender:id,name',
        ]);

        // target_type에 따라 대상 정보 로드
        $targetInfo = null;
        if ($notification->target_type === 'group' && $notification->target_id) {
            $targetInfo = \App\Models\NotificationGroup::select('id', 'name', 'description', 'is_active')
                ->find($notification->target_id);
        } elseif ($notification->target_type === 'individual' && $notification->target_id) {
            $targetInfo = \App\Models\User::select('id', 'name', 'email')
                ->find($notification->target_id);
        }

        // 수신자 통계 계산
        $recipientStats = $notification->recipients()
            ->selectRaw('
            COUNT(*) as total_count,
            SUM(CASE WHEN read_at IS NOT NULL THEN 1 ELSE 0 END) as read_count,
            SUM(CASE WHEN delivered_at IS NOT NULL THEN 1 ELSE 0 END) as delivered_count
        ')
            ->first();

        // 알림 데이터에 통계 및 대상 정보 추가
        $notificationData = $notification->toArray();
        $notificationData['recipient_stats'] = [
            'total_count' => $recipientStats->total_count ?? 0,
            'read_count' => $recipientStats->read_count ?? 0,
            'delivered_count' => $recipientStats->delivered_count ?? 0,
            'unread_count' => ($recipientStats->total_count ?? 0) - ($recipientStats->read_count ?? 0),
        ];

        // 대상 정보 추가
        if ($targetInfo) {
            if ($notification->target_type === 'group') {
                $notificationData['target_group'] = $targetInfo;
            } elseif ($notification->target_type === 'individual') {
                $notificationData['target_user'] = $targetInfo;
            }
        }

        return $this->successResponse([
            'data' => $notificationData,
        ], '알림 상세 정보를 성공적으로 조회했습니다.');
    }

    /**
     * 특정 알림의 수신자 목록 조회
     *
     * @param Notification $notification
     * @return JsonResponse
     */
    public function recipients(Notification $notification): JsonResponse
    {
        $recipients = $notification->recipients()
                                  ->with('user:id,name,email')
                                  ->select([
                                      'id',
                                      'notification_id',
                                      'user_id',
                                      'delivered_at',
                                      'read_at',
                                      'created_at'
                                  ])
                                  ->orderBy('created_at', 'desc')
                                  ->get();

        // 수신자 데이터 변환
        $recipientData = $recipients->map(function ($recipient) {
            return [
                'id' => $recipient->id,
                'user' => $recipient->user,
                'delivered_at' => $recipient->delivered_at,
                'read_at' => $recipient->read_at,
                'is_delivered' => !is_null($recipient->delivered_at),
                'is_read' => !is_null($recipient->read_at),
                'created_at' => $recipient->created_at,
            ];
        });

        return $this->successResponse([
            'data' => $recipientData,
        ], '수신자 목록을 성공적으로 조회했습니다.');
    }
}
