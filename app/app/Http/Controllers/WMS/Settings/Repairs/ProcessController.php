<?php

namespace App\Http\Controllers\WMS\Settings\Repairs;

use App\Http\Controllers\Controller;
use App\Models\RepairProcess;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ProcessController extends Controller
{
    public function index(): JsonResponse
    {
        return $this->successResponse([
            'items' => RepairProcess::all(),
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:repair_processes,code',
        ]);

        $process = RepairProcess::create($validated);

        return $this->successResponse([
            'item' => $process->load('repairGrades'),
        ], 201);
    }

    public function show(RepairProcess $process): JsonResponse
    {
        return $this->successResponse([
            'item' => $process->load('repairGrades'),
        ]);
    }

    public function update(Request $request, RepairProcess $process): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => ['required', 'string', 'max:50', Rule::unique('repair_processes')->ignore($process->id)],
        ]);

        $process->update($validated);

        return $this->successResponse([
            'item' => $process->load('repairGrades'),
        ]);
    }

    public function destroy(RepairProcess $process): JsonResponse
    {
        // 이 처리 내용을 참조하는 repair_products가 있는지 확인
        if ($process->repairProducts()->exists()) {
            return $this->errorResponse([
                'message' => '이 처리 내용을 참조하고 있는 [수리/점검된 상품]이 있어 삭제할 수 없습니다.',
            ], null, 'setting', 409);
        }

        // 관계 테이블의 연결 해제 (필요한 경우)
        $process->repairSymptoms()->detach();
        $process->repairGrades()->detach();

        $process->delete();

        return $this->successResponse([], 204);
    }

    /**
     * 특정 처리 내용에 연결된 등급 목록을 가져옵니다.
     */
    public function getGrades(RepairProcess $process): JsonResponse
    {
        return $this->successResponse([
            'item' => $process->load('repairGrades'),
        ]);
    }

    /**
     * 처리 내용과 등급의 관계를 동기화합니다.
     */
    public function syncGrades(Request $request, RepairProcess $process): JsonResponse
    {
        $validated = $request->validate([
            'grade_ids' => 'nullable|array',
            'grade_ids.*' => 'integer|exists:repair_grades,id',
        ]);

        $process->repairGrades()->sync($validated['grade_ids'] ?? []);

        return $this->successResponse([
            'item' => $process->load('repairGrades'),
        ]);
    }
}
