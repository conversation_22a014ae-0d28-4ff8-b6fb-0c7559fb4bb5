<?php

namespace App\Http\Controllers\WMS\Settings\Repairs;

use App\Http\Controllers\Controller;
use App\Models\RepairGrade;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class GradeController extends Controller
{
    public function index(): JsonResponse
    {
        return $this->successResponse([
            'items' => RepairGrade::orderBy('order_no')->get(),
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'order_no' => 'required|integer|min:0',
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:repair_grades,code',
        ]);

        $grade = RepairGrade::create($validated);

        return $this->successResponse([
            'item' => $grade,
        ], 201);
    }

    public function show(RepairGrade $grade): JsonResponse
    {
        return $this->successResponse([
            'item' => $grade->load('repairSymptoms'),
        ]);
    }

    public function update(Request $request, RepairGrade $grade): JsonResponse
    {
        $validated = $request->validate([
            'order_no' => 'required|integer|min:0',
            'name' => 'required|string|max:255',
            'code' => ['required', 'string', 'max:50', Rule::unique('repair_grades')->ignore($grade->id)],
        ]);

        $grade->update($validated);

        return $this->successResponse([
            'item' => $grade,
        ]);
    }

    public function destroy(RepairGrade $grade): JsonResponse
    {
        // 관계가 설정된 처리 내용이 있으면 삭제 불가
        if ($grade->repairProcesses()->exists()) {
            return $this->errorResponse([
                'message' => '이 등급을 사용하는 [처리 내용]이 있어 삭제할 수 없습니다.',
            ], null, 'setting', 409);
        }

        // 관계가 설정된 증상이 있으면 삭제 불가
        if ($grade->repairSymptoms()->exists()) {
            return $this->errorResponse([
                'message' => '이 등급을 사용하는 [증상]이 있어 삭제할 수 없습니다.',
            ], null, 'setting', 409);
        }

        $grade->delete();

        return $this->successResponse([], 204);
    }

    /**
     * 특정 등급에 연결된 증상 목록을 가져옵니다.
     */
    public function getSymptoms(RepairGrade $grade): JsonResponse
    {
        return $this->successResponse([
            'item' => $grade->load('repairSymptoms'),
        ]);
    }

    /**
     * 등급과 증상의 관계를 동기화합니다.
     */
    public function syncSymptoms(Request $request, RepairGrade $grade): JsonResponse
    {
        $validated = $request->validate([
            'symptom_ids' => 'nullable|array',
            'symptom_ids.*' => 'integer|exists:repair_symptoms,id',
        ]);

        $grade->repairSymptoms()->sync($validated['symptom_ids'] ?? []);

        return $this->successResponse([
            'item' => $grade->load('repairSymptoms'),
        ]);
    }
}
