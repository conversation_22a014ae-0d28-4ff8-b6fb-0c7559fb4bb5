<?php

namespace App\Http\Controllers\WMS\Settings\Repairs;

use App\Http\Controllers\Controller;
use App\Models\RepairSymptom;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class SymptomController extends Controller
{
    public function index(): JsonResponse
    {
        return $this->successResponse([
            'items' => RepairSymptom::with('repairProcesses')->get(),
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => ['required', Rule::in(['general', 'apple'])],
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:repair_symptoms,code',
            'default_repair_process_id' => 'nullable|integer|exists:repair_processes,id',
            'default_repair_grade_id' => 'nullable|integer|exists:repair_grades,id',
        ]);

        $symptom = RepairSymptom::create($validated);

        return $this->successResponse([
            'item' => $symptom,
        ], 201);
    }

    public function show(RepairSymptom $symptom): JsonResponse
    {
        return $this->successResponse([
            'item' => $symptom->load('repairProcesses.repairGrades'),
        ]);
    }

    public function update(Request $request, RepairSymptom $symptom): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => ['required', 'string', 'max:50', Rule::unique('repair_symptoms')->ignore($symptom->id)],
            'type' => ['required', Rule::in(['general', 'apple'])],
            'default_repair_process_id' => 'nullable|integer|exists:repair_processes,id',
            'default_repair_grade_id' => 'nullable|integer|exists:repair_grades,id',
        ]);

        $symptom->update($validated);

        return $this->successResponse([
            'item' => $symptom,
        ]);
    }

    public function destroy(RepairSymptom $symptom): JsonResponse
    {
        $symptom->repairProcesses()->detach(); // 피봇 테이블의 관계 먼저 해제
        $symptom->delete();

        return $this->successResponse([], 204);
    }

    /**
     * 동기화된 내용을 가져옵니다.
     */
    public function getProcesses(RepairSymptom $symptom): JsonResponse
    {
        return $this->successResponse([
            'item' => $symptom->load('repairProcesses'),
        ]);
    }

    /**
     * 증상과 처리 내용의 관계를 동기화합니다.
     */
    public function syncProcesses(Request $request, RepairSymptom $symptom): JsonResponse
    {
        $validated = $request->validate([
            'process_ids' => 'nullable|array',
            'process_ids.*' => 'integer|exists:repair_processes,id',
        ]);

        $symptom->repairProcesses()->sync($validated['process_ids'] ?? []);

        return $this->successResponse([
            'item' => $symptom->load('repairProcesses'),
        ]);
    }
}
