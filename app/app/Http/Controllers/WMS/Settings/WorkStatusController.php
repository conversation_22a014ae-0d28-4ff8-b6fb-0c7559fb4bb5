<?php

namespace App\Http\Controllers\WMS\Settings;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Models\WorkStatus;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Throwable;

class WorkStatusController extends Controller
{
    public function list(Request $request): JsonResponse
    {
        $data = [
            'keyword' => $request->input('keyword'),
            'pageSize' => $request->input('pageSize', 16),
        ];

        $query = WorkStatus::orderBy('code');

        if ($data['keyword']) {
            $query->where('name', 'like', '%'.$data['keyword'].'%')
                ->orWhere('description', 'like', '%'.$data['keyword'].'%');
        }

        $paginatedResult = $query->paginate($data['pageSize'] ?? 15)->withQueryString();

        return $this->successResponse([
            'items' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * @throws Throwable
     */
    public function store(Request $request): JsonResponse
    {

        try {
            DB::beginTransaction();

            $validatedData = $request->validate([
                'code' => 'required|string|max:255|unique:work_statuses,code',
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
            ], [
                'code.required' => '코드는 필수로 입력 해야 합니다.',
                'code.unique' => "이미 사용중인 코드(코드 중복)입니다.\n코드를 변경해 주세요.",
                'name.required' => '코드명은 필수로 입력해야 합니다.',
            ]);

            WorkStatus::create($validatedData);

            DB::commit();

            return $this->successResponse([
                'message' => '작업 상태코드가 생성되었습니다.',
            ]);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '작업 상태코드 생성에 실패했습니다.',
                'log_message' => '작업 상태코드 생성 실패: '.$e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * @throws Throwable
     */
    public function update(int $id, Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $validatedData = $request->validate([
                'code' => 'required|string|max:255|unique:work_statuses,code,'.$id,
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
            ], [
                'code.required' => '코드는 필수로 입력 해야 합니다.',
                'code.unique' => "이미 사용중인 코드(코드 중복)입니다.\n코드를 변경해 주세요.",
                'name.required' => '코드명은 필수로 입력해야 합니다.',
            ]);

            $StatusValue = WorkStatus::findOrFail($id);

            $StatusValue->update($validatedData);

            DB::commit();

            return $this->successResponse([
                'message' => '작업 상태코드가 업데이트 되었습니다.',
            ]);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '작업 상태코드 업데이트에 실패했습니다.',
                'log_message' => '작업 상태코드 업데이트 실패: '.$e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }

    /**
     * @throws Throwable
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $StatusValue = WorkStatus::findOrFail($id);
            $StatusValue->delete();

            DB::commit();

            return $this->successResponse([
                'message' => '작업 상태코드가 삭제되었습니다.',
            ]);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '작업 상태코드 삭제에 실패했습니다.',
                'log_message' => '작업 상태코드 삭제 실패: '.$e->getMessage(),
            ], $e, 'work_status', 500);
        }
    }
}
