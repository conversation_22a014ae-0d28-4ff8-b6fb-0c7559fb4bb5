<?php

namespace App\Http\Controllers\WMS\Settings;

use App\Http\Controllers\Controller;
use App\Models\Process;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProcessController extends Controller
{
    private int $userId;

    private array|User|Collection|Model $user;

    public function __construct(Request $request)
    {
        $this->userId = (int) $request->input('userId');
    }

    private function listQueryHelper($request): EloquentBuilder|QueryBuilder|User
    {
        $type = $request->input('type');
        $code = $request->input('code');
        $name = $request->input('name');

        $query = Process::query();
        if ($type) {
            $query->where('type', $type);
        }

        if ($code) {
            $query->where('code', 'like', "%{$code}%");
        }

        if ($name) {
            $query->where('name', 'like', "%{$name}%");
        }

        return $query->orderBy('id', 'DESC');
    }

    /**
     * (전체)관리자가 관리하는 점검코드 리스트
     */
    public function getProcessByAdmin(Request $request): JsonResponse
    {
        $processes = $this->listQueryHelper($request)->get();

        return $this->successResponse([
            'processes' => $processes,
        ]);
    }

    /**
     * 점검코드 등록
     */
    public function store(Request $request): JsonResponse
    {
        // 등록되어 있지 않다면 등록 진행
        $type = match ($request->input('type')) {
            'check' => Process::TYPE_CHECK,
            'repair' => Process::TYPE_REPAIR,
            'grade' => Process::TYPE_GRADE,
            'fix' => Process::TYPE_FIX,
            'charge' => Process::TYPE_CHARGE,
            default => false,
        };

        if ($type === false) {
            return $this->errorResponse([
                'message' => '존재하지 않는 타입입니다.',
            ]);
        }

        Process::create([
            'type' => $type,
            'code' => $request->input('code'),
            'name' => $request->input('name'),
        ]);

        return $this->successResponse();
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        // 등록되어 있지 않다면 등록 진행
        $type = match ($request->input('type')) {
            'check' => Process::TYPE_CHECK,
            'repair' => Process::TYPE_REPAIR,
            'grade' => Process::TYPE_GRADE,
            'fix' => Process::TYPE_FIX,
            'charge' => Process::TYPE_CHARGE,
            default => false,
        };

        if ($type === false) {
            return $this->errorResponse([
                'message' => '존재하지 않는 타입입니다.',
            ]);
        }

        // 정보 업데이트
        $process = Process::findOrFail($id);

        $process->type = $type;
        $process->code = $request->input('code');
        $process->name = $request->input('name');

        $process->save();

        return $this->successResponse([
            'data' => $process,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        $process = Process::findOrFail($id);
        $process->delete();

        return $this->successResponse();
    }
}
