<?php

namespace App\Http\Controllers\WMS\Settings;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Attendance\ListRequest as AttendanceListRequest;
use App\Http\Requests\Attendance\StoreRequest as AttendanceStoreRequest;
use App\Http\Requests\Member\ListRequest;
use App\Http\Requests\Member\StoreRequest;
use App\Imports\AttendanceImport;
use App\Models\User;
use App\Models\UserAttendance;
use App\Services\SimpleLogService;
use App\Services\UserService;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response;

class MemberController extends Controller
{
    private int $userId;

    private array|User|Collection|Model $user;

    public function __construct(Request $request)
    {
        $this->userId = (int) $request->input('userId');
    }

    private function isAdmin(): bool
    {
        $admin = Auth::user();

        return $admin['role'] === 'Super-Admin' || $admin['role'] === 'Admin';
    }

    /**
     * (전체)관리자가 관리하는 멤버 리스트
     */
    public function list(ListRequest $request): JsonResponse
    {
        // MemberListRequest에서 검증된 데이터 사용
        $validated = $request->validated();

        $userService = new UserService;
        $members = $userService->getList($validated);
        $paginatedResult = $members->paginate($validated['pageSize'])->withQueryString();

        return $this->successResponse([
            'members' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * (전체)관리자가 관리하는 활성화된 멤버 리스트
     */
    public function activeList(ListRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $validated['status'] = User::MEMBER_STATUS_ACTIVE;
        $validated['role'] = null;

        $userService = new UserService;
        $members = $userService->getList($validated);
        $paginatedResult = $members->paginate($validated['pageSize'])->withQueryString();

        return $this->successResponse([
            'items' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * 직원 등록
     *
     * Method: POST
     */
    public function store(StoreRequest $request): JsonResponse
    {
        // MemberRequest에서 이미 검증된 데이터 사용
        $validated = $request->validated();

        try {
            $userService = new UserService;
            $userService->storeUser($validated);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '직원 등록에 실패했습니다.',
                'log_message' => '직원 등록 실패: '.$e->getMessage(),
            ], $e, 'setting', 500);
        }
    }

    /**
     * 직원 정보 보기
     *
     * Method: GET
     */
    public function show(string $id): JsonResponse
    {
        $userService = new UserService;
        $member = $userService->getUserById($id);

        return $this->successResponse([
            'member' => $member,
        ]);
    }

    /**
     * 직원 정보 수정
     *
     * Method: PUT
     */
    public function update(StoreRequest $request, string $id): JsonResponse
    {
        // MemberRequest에서 이미 검증된 데이터 사용
        $validated = $request->validated();

        try {
            $userService = new UserService;
            $userService->updateUser($id, $validated);

            return $this->successResponse([
                'response' => $validated,
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '직원 정보 수정에 실패했습니다.',
                'log_message' => '직원 정보 수정 실패: '.$e->getMessage(),
                'context' => [
                    'validated' => $validated,
                ],
            ], $e, 'setting', 500);
        }
    }

    /**
     * 직원 소프트 삭제
     *
     * Method: DELETE
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $userService = new UserService;
            $userService->deleteUser($id);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '직원 삭제에 실패했습니다.',
                'log_message' => '직원 삭제 실패: '.$e->getMessage(),
            ], $e, 'setting', 500);
        }
    }

    /**
     * 직원 소프트 삭제 - 복구
     *
     * Method: PUT
     */
    public function restore(string $id): JsonResponse
    {
        $userService = new UserService;
        $userService->restoreUser($id);

        return $this->successResponse();
    }

    /**
     * 근태 관리: 엑셀 업로드 후 근태 데이터 입력
     */
    public function attendanceStore(AttendanceStoreRequest $request): JsonResponse
    {
        try {
            $file = $request->file('excel');

            $import = new AttendanceImport;

            try {
                Excel::import($import, $file);
            } catch (Exception $importException) {
                SimpleLogService::error('setting', '근태관리 엑셀파일 가져오기 실패', [], $importException);

                throw $importException;
            }

            // 성공 메시지와 함께 응답
            return $this->successResponse([
                'message' => '근태 데이터가 성공적으로 저장되었습니다.',
            ]);
        } catch (Exception $e) {
            SimpleLogService::error('setting', '근태 관리 저장 실패', [], $e);

            // SQL 오류 코드를 HTTP 상태 코드로 사용하지 않도록 수정
            // 23000은 SQL 오류 코드로 중복 키 또는 외래 키 제약 조건 위반을 나타냄
            $errorCode = $e->getCode();
            if (is_numeric($errorCode) && $errorCode >= 1000) {
                // SQL 오류 코드인 경우 기본 HTTP 오류 코드 사용
                $httpCode = Response::HTTP_INTERNAL_SERVER_ERROR;

                // 오류 메시지 상세화
                $errorMessage = $e->getMessage();
                if ($errorCode == 23000) {
                    $errorMessage = '데이터베이스 제약 조건 위반: '.$errorMessage;
                }
            } else {
                // 일반 예외인 경우 전달된 코드 사용
                $httpCode = $errorCode ?: Response::HTTP_INTERNAL_SERVER_ERROR;
                $errorMessage = $e->getMessage();
            }

            return $this->errorResponse([
                'message' => $errorMessage,
            ], $e, 'setting', $httpCode);
        }
    }

    /**
     * 근태 관리: 근태 데이터 목록 조회
     */
    public function attendanceList(AttendanceListRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();

            $query = UserAttendance::with('user');

            // 필터링 적용
            if ($validated['user_id']) {
                $query->where('user_id', $validated['user_id']);
            }

            if ($validated['start_date']) {
                $query->where('work_date', '>=', $validated['start_date']);
            }

            if ($validated['end_date']) {
                $query->where('work_date', '<=', $validated['end_date']);
            }

            // 정렬: 근무일자 역순
            $query->orderBy('work_date', 'desc');

            $attendances = $query->paginate($validated['pageSize'])->withQueryString();

            return $this->successResponse([
                'attendances' => $attendances,
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'setting', 500);
        }
    }
}
