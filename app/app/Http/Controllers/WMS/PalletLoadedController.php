<?php

namespace App\Http\Controllers\WMS;

use App\Http\Controllers\Controller;
use App\Http\Requests\StorePalletProductRequest;
use App\Models\Pallet;
use App\Models\PalletProduct;
use App\Models\Product;
use App\Services\LoadedService;
use App\Services\ProcessService;
use App\Services\ProductService;
use App\Services\RepairService;
use App\Traits\Product\SearchTrait as ProductSearchTrait;
use App\Traits\Repair\AppleTrait;
use App\Traits\Repair\GeneralTrait;
use App\Traits\Repair\GradeTrait;
use App\Traits\Repair\ProcessTrait;
use App\Traits\Repair\SymptomTrait;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Throwable;

class PalletLoadedController extends Controller
{
    use AppleTrait, GeneralTrait;
    use GradeTrait, ProcessTrait, SymptomTrait;
    use ProductSearchTrait;

    private LoadedService $loadedService;

    private ProcessService $processService;

    private ProductService $productService;

    private RepairService $repairService;

    public function __construct(
        LoadedService $loadedService,
        ProcessService $processService,
        ProductService $productService,
        RepairService $repairService
    ) {
        $this->loadedService = $loadedService;
        $this->processService = $processService;
        $this->productService = $productService;
        $this->repairService = $repairService;
    }

    /**
     * 적재중인 팔레트 리스트
     */
    public function getLoadedList(Request $request): JsonResponse
    {
        $data = [
            'level' => $request->input('level'),
            'column' => $request->input('column'),
        ];

        $items = $this->loadedService->getLoadedPalletsWithData($data['level'], $data['column']);

        return $this->successResponse([
            'items' => $items,
            'level' => $data['level'],
            'column' => $data['column'],
        ]);
    }

    /**
     * 팔레트 확정(근데 저장하지는 않는다) 즉 이 팔레트를 사용할 거라고 기록해 두는 것<br>
     * 기존 코드를 보니 팔레트 코드(palletCode)만 만들어 두고
     * 실제 생성은 점검완료 버튼을 누를 때 생성(또는 업데이트) 된다.
     */
    public function setLocationCode(string $place, string $code): JsonResponse
    {
        $result = $this->loadedService->getSelectedPallet([
            'place' => $place,
            'code' => $code,
        ]);

        return $this->successResponse([
            'isLocation' => $result['isLocation'],
            'palletGradeCode' => $result['palletGradeCode'],
            'palletProdCount' => $result['palletProdCount'],
            'palletRecentProducts' => $result['palletRecentProducts'],
        ]);
    }

    /**
     * 팔레트 적재시 적재 가능한 상품인지 확인
     *
     * @throws Exception
     */
    public function checkProduct(string $qaid): JsonResponse
    {
        try {
            $qaid = mb_strtoupper(trim($qaid));
            $this->existsProductByQaid($qaid);

            $product = $this->loadedService->getLoadedProductCheck($qaid);
            if ($product === null) {
                throw new Exception("등록된 상품[$qaid]을 확인 할 수 없습니다. 상품을 검수해 주시기 바랍니다.");
            }

            // 잠금이 되어 있는지 확인
            if ($product->isLocked()) {
                throw new Exception("잠금된 상품입니다. ($product->memo) 관리자에게 문의하세요. (QAID: {$product->qaid})");
            }

            if ($product->status !== Product::STATUS_REPAIRED) {
                if ($product->status < Product::STATUS_REPAIRED) {
                    throw new Exception("등록된 상품[$qaid]은 아직 수리/점검되지 않았습니다. 상품의 점검을 먼저 해 주세요.");
                }

                if ($product->status > Product::STATUS_REPAIRED) {
                    // 이미 적재된 qaid인 경우
                    $palletProduct = $product->palletProducts
                        ->where('status', '!=', PalletProduct::STATUS_DELETED)
                        ->first();
                    if ($palletProduct) {
                        $pallet = $palletProduct->pallet;
                        if ($pallet && $pallet->status !== Pallet::STATUS_DELETED) {
                            throw new Exception('조회된 상품(QAID='.$qaid.')은 이미 적재된 상품입니다.');
                        }
                    }
                }
            }

            if ($product->duplicated === 'Y') {
                throw new Exception('같은 QAID='.$qaid.'로 중복된 상품(보류처리)이 있습니다.');
            }

            if ($product->checked_status == Product::CHECKED_STATUS_UNDELIVERED ||
                ($product->checked_status == Product::CHECKED_STATUS_CHECKED && $product->checked_at == null)) {
                throw new Exception('조회된 상품(QAID='.$qaid.')은 미입고 상품으로 점검완료가 불가합니다.');
            }

            if ($product->status == Product::STATUS_HELD) {
                throw new Exception('조회된 상품(QAID='.$qaid.')은 보류처리되어 점검완료가 불가합니다.');
            }

            if (in_array($product->status, [
                Product::STATUS_CARRIED_OUT,
                Product::STATUS_CARRIED_OUT_WAITING,
                Product::STATUS_CARRIED_OUT_REPAIRED,
            ])) {
                throw new Exception('조회된 상품(QAID='.$qaid.')은 외주반출(외부수리의뢰)처리되어 점검완료가 불가합니다.');
            }

            $product->req_at = empty($product->req_at) ? '' : Carbon::parse($product->req_at)->format('Y년 n월 j일');

            $grades = $this->getGradesByReqType($product->req->req_type);
            $symptomsInfo = $this->getSymptomsAndProcessesByGrade(
                $product->repairProduct->repair_grade_id,
                $product->req->req_type
            );
            $symptoms = $symptomsInfo['symptoms'];
            $processes = $this->getProcesses($product->repairProduct->repair_symptom_id);

            // cost range list
            $repairCostInfo = $this->repairService->getDisplayableRepairCostInfo($product);

            return $this->successResponse([
                'product' => $product,
                'grades' => $grades,
                'symptoms' => $symptoms,
                'processes' => $processes,
                'cost_info' => [
                    'cost_type' => $repairCostInfo['cost_type'],
                    'cost_unit' => $repairCostInfo['cost_unit'],
                    'cost_range_list' => $repairCostInfo['cost_range_list'],
                    'cost_range_selected' => $repairCostInfo['cost_range_selected'],
                ],
            ]);
        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'pallet', 400);
        }
    }

    /**
     * 수리 비용 체크
     */
    public function checkInvoice(string $productId, string $processCode, string $osInstall): JsonResponse
    {
        try {
            $product = $this->findProductById((int) $productId);

            // 점검 완료된 상품인지 확인
            $isAlreadyChecked = $this->loadedService->isAlreadyChecked($product);

            // 점검완료된 상품이 아닌 경우
            if ($isAlreadyChecked === null) {
                $isAlreadyChecked = false;
                $message = '';
            } else {
                $isAlreadyChecked = true;
                $message = '등록된 상품('.$product->qaid.' - '.$product->name.')은 이미 점검완료 하였습니다.';
            }

            // cost range list
            $data['process_code'] = $processCode;
            $data['os_reinstall'] = $osInstall === 'true';
            $repairCostResult = $this->repairService->calculateRepairCostOnce($product, $data);
            $invoice3 = $this->repairService->calculateInvoice3($product, $data); // OS 재설치비
            $repairCostInfo = $this->repairService->getDisplayableRepairCostInfo($product);

            return $this->successResponse([
                'isAlreadyChecked' => $isAlreadyChecked,
                'message' => $message,
                'invoice1' => $repairCostResult['invoice1'],
                'invoice3' => $invoice3,
                'cost_info' => [
                    'cost_type' => $repairCostInfo['cost_type'],
                    'cost_unit' => $repairCostInfo['cost_unit'],
                    'cost_range_list' => $repairCostInfo['cost_range_list'],
                    'cost_range_selected' => $repairCostInfo['cost_range_selected'],
                    'cost_id' => $repairCostResult['repair_cost_info']['repair_cost_id'] ?? null,
                    'cost_basis' => $repairCostResult['repair_cost_info']['calculation_basis'] ?? null,
                    'cost_details' => $repairCostResult['repair_cost_info']['calculation_details'] ?? null,
                ],
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'pallet', 400);
        }
    }

    /**
     * 수리비용2: 별도 수리비
     */
    public function otherExpenses(string $type, string $code): JsonResponse
    {
        $code = mb_strtoupper($code);

        $process = $this->processService->getProcessByTypeAndCode($type, $code);

        return $this->successResponse([
            'name' => $process->name ?? null,
        ]);
    }

    /**
     * 출고 팔레트에 상품 적재
     *
     * @throws Exception|Throwable
     */
    public function store(StorePalletProductRequest $request): JsonResponse
    {
        $data = $request->validated();

        try {
            $this->loadedService->storeProductOnPallet($data, auth()->user());

            return $this->successResponse([
                'message' => '정상 처리 되었습니다.',
                // 'requests' => $request->all(),
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '[쿠팡RP상품]출고 팔레트 적재 처리에 실패하였습니다. 관리자에게 문의해 주세요.',
                'log_message' => '[쿠팡RP상품]출고 팔레트 적재 처리에 실패(PalletProduct::Store)'.$e->getMessage(),
            ], $e, 'pallet', 400);
        }
    }
}
