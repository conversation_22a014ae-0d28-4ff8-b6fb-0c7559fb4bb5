<?php

namespace App\Http\Controllers\WMS\Notification;

use App\Http\Controllers\Controller;
use App\Http\Requests\Notification\SendRequest;
use App\Services\NotificationSendService;
use App\Exceptions\ValidationException;
use App\Exceptions\ResourceNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Throwable;

/**
 * 알림 전송 컨트롤러
 *
 * 관리자가 알림을 작성하여 대상에게 전송하는 기능을 제공합니다.
 */
class SendController extends Controller
{
    public function __construct(
        private NotificationSendService $sendService
    ) {}

    /**
     * 알림 전송
     *
     * @param SendRequest $request 알림 전송 요청 데이터
     * @return JsonResponse 전송 결과
     */
    public function send(SendRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();

            // 발신자 정보 추가
            $notificationData = array_merge($validated, [
                'sender_id' => Auth::id()
            ]);

            $result = $this->sendService->sendNotification($notificationData);

            return $this->successResponse($result, '알림이 성공적으로 전송되었습니다.');

        } catch (ValidationException $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
                'log_message' => '알림 전송 유효성 검증 실패: ' . $e->getMessage(),
            ], $e, 'notification', 422);

        } catch (ResourceNotFoundException $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
                'log_message' => '알림 전송 리소스 오류: ' . $e->getMessage(),
            ], $e, 'notification', 404);

        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => '알림 전송 중 오류가 발생했습니다.',
                'log_message' => '알림 전송 시스템 오류: ' . $e->getMessage(),
                'context' => [
                    'user_id' => Auth::id(),
                    'target_type' => $request->input('target_type'),
                    'target_ids_count' => count($request->input('target_ids', [])),
                ]
            ], $e, 'notification', 500);
        }
    }

    /**
     * 알림 전송 통계 조회
     *
     * @param string $notificationId 알림 ID
     * @return JsonResponse 전송 통계
     */
    public function stats(string $notificationId): JsonResponse
    {
        try {
            $stats = $this->sendService->getNotificationStats($notificationId);

            return $this->successResponse($stats);

        } catch (ResourceNotFoundException $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
                'log_message' => '알림 통계 조회 실패: ' . $e->getMessage(),
            ], $e, 'notification', 404);

        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => '알림 통계 조회 중 오류가 발생했습니다.',
                'log_message' => '알림 통계 조회 오류: ' . $e->getMessage(),
                'context' => [
                    'notification_id' => $notificationId,
                    'user_id' => Auth::id(),
                ]
            ], $e, 'notification', 500);
        }
    }
}
