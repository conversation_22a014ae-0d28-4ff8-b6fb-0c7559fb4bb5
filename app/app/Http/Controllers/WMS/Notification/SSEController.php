<?php

namespace App\Http\Controllers\WMS\Notification;

use App\Http\Controllers\Controller;
use App\Services\SSEService;
use App\Services\SSEBroadcastService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redis;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Throwable;

/**
 * SSE(Server-Sent Events) 컨트롤러
 *
 * 실시간 알림 전달을 위한 SSE 연결을 관리합니다.
 */
class SSEController extends Controller
{
    public function __construct(
        private SSEService $sseService,
        private SSEBroadcastService $sseBroadcastService
    ) {}

    /**
     * SSE 연결 및 미읽음 알림 전송
     *
     * @param Request $request
     * @return StreamedResponse
     */
    public function connect(Request $request): StreamedResponse
    {
        $userId = Auth::id();

        // 디버깅 로그 추가
        \Log::info("SSE 연결 시도", ['user_id' => $userId]);

        return new StreamedResponse(function () use ($userId) {
            try {
                // 실행 시간 제한 해제
                set_time_limit(0);
                ignore_user_abort(false);

                // SSE 헤더 설정
                header('Content-Type: text/event-stream');

                // 출력 버퍼링 비활성화
                if (ob_get_level()) {
                    ob_end_clean();
                }

                \Log::info("SSE 헤더 설정 완료", ['user_id' => $userId]);

                // 연결 ID 생성 및 등록
                $connectionId = uniqid('sse_', true);
                $this->sseBroadcastService->registerConnection($userId, $connectionId);

                // 연결 확인 메시지 전송
                echo $this->sseService->formatSSEData('connected', [
                    'message' => 'SSE 연결이 성공적으로 설정되었습니다.',
                    'user_id' => $userId,
                    'connection_id' => $connectionId
                ]);

                flush();

                \Log::info("초기 알림 전송 시작", ['user_id' => $userId]);

                // 초기 미읽음 알림 전송
                $this->sendInitialNotifications($userId);

                // 간단한 폴링 방식으로 변경 (Redis 의존성 제거)
                $heartbeatInterval = 30; // 30초마다 하트비트
                $checkInterval = 5; // 5초마다 새 알림 확인
                $counter = 0;

                \Log::info("SSE 메인 루프 시작", ['user_id' => $userId]);

                // 즉시 첫 번째 하트비트 전송
                echo $this->sseService->createHeartbeat();
                flush();
                \Log::info("초기 하트비트 전송", ['user_id' => $userId]);

                $lastHeartbeat = time();
                $lastCheck = time();

                while (true) {
                    // 연결 상태 확인
                    if (connection_aborted()) {
                        \Log::info("SSE 연결 중단됨", ['user_id' => $userId]);
                        break;
                    }

                    $currentTime = time();

                    // 하트비트 전송 (30초마다)
                    if ($currentTime - $lastHeartbeat >= $heartbeatInterval) {
                        $this->sseBroadcastService->updateHeartbeat($userId);
                        echo $this->sseService->createHeartbeat();
                        flush();

                        $lastHeartbeat = $currentTime;
                        \Log::debug("하트비트 전송", ['user_id' => $userId, 'time' => $currentTime]);
                    }

                    // 대기 중인 알림 확인 (5초마다)
                    if ($currentTime - $lastCheck >= $checkInterval) {
                        $pendingNotifications = $this->sseService->getPendingNotifications($userId);
                        if (!empty($pendingNotifications)) {
                            echo $this->sseService->createNotificationEvent($pendingNotifications);
                            flush();

                            // 전송된 알림들을 전달 완료로 표시
                            $notificationIds = array_column($pendingNotifications, 'id');
                            $this->sseService->markMultipleAsDelivered($userId, $notificationIds);

                            \Log::info("새 알림 전송", ['user_id' => $userId, 'count' => count($pendingNotifications)]);
                        }
                        $lastCheck = $currentTime;
                    }

                    // CPU 사용량을 줄이기 위해 짧은 대기 (usleep 사용)
                    usleep(100000); // 0.1초 대기
                }

            } catch (Throwable $e) {
                \Log::error("SSE 연결 오류", [
                    'user_id' => $userId,
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);

                // SSE 연결 중 오류 발생 시 에러 이벤트 전송
                echo $this->sseService->formatSSEData('error', [
                    'message' => 'SSE 연결 중 오류가 발생했습니다.',
                    'error_code' => 'SSE_CONNECTION_ERROR'
                ]);

                flush();

                // 로그 기록
                $this->logSSEError($e, $userId);
            } finally {
                \Log::info("SSE 연결 종료", ['user_id' => $userId]);
                // 연결 해제 시 정리
                $this->sseBroadcastService->unregisterConnection($userId);
            }
        });
    }

    /**
     * 알림 읽음 처리
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function markAsRead(Request $request)
    {
        try {
            $request->validate([
                'notification_id' => 'required|string|uuid'
            ]);

            $userId = Auth::id();
            $notificationId = $request->input('notification_id');

            $success = $this->sseService->markAsRead($userId, $notificationId);

            if ($success) {
                return $this->successResponse([], '알림이 읽음 처리되었습니다.');
            } else {
                return $this->errorResponse([
                    'message' => '알림 읽음 처리에 실패했습니다.',
                    'log_message' => "알림 읽음 처리 실패 - User: {$userId}, Notification: {$notificationId}"
                ], null, 'notification', 400);
            }

        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => '알림 읽음 처리 중 오류가 발생했습니다.',
                'log_message' => '알림 읽음 처리 오류: ' . $e->getMessage(),
                'context' => [
                    'user_id' => Auth::id(),
                    'notification_id' => $request->input('notification_id')
                ]
            ], $e, 'notification', 500);
        }
    }

    /**
     * 미읽음 알림 개수 조회
     *
     * @return JsonResponse
     */
    public function getUnreadCount()
    {
        try {
            $userId = Auth::id();
            $count = $this->sseService->getUnreadCount($userId);

            return $this->successResponse([
                'unread_count' => $count
            ]);

        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => '미읽음 알림 개수 조회 중 오류가 발생했습니다.',
                'log_message' => '미읽음 알림 개수 조회 오류: ' . $e->getMessage(),
                'context' => [
                    'user_id' => Auth::id()
                ]
            ], $e, 'notification', 500);
        }
    }

    /**
     * 초기 미읽음 알림 전송
     *
     * @param int $userId
     * @return void
     */
    private function sendInitialNotifications(int $userId): void
    {
        try {
            $notifications = $this->sseService->getPendingNotifications($userId);

            if (!empty($notifications)) {
                echo $this->sseService->createNotificationEvent($notifications);

                if (ob_get_level()) {
                    ob_end_flush();
                }
                flush();

                // 전송된 알림들을 전달 완료로 표시
                $notificationIds = array_column($notifications, 'id');
                $this->sseService->markMultipleAsDelivered($userId, $notificationIds);
            }

        } catch (Throwable $e) {
            $this->logSSEError($e, $userId, 'initial_notifications');
        }
    }

    /**
     * 새로운 알림 확인 및 전송
     *
     * @param int $userId
     * @param \Carbon\Carbon $lastCheckTime
     * @return void
     */
    private function checkAndSendNewNotifications(int $userId, $lastCheckTime): void
    {
        try {
            if ($this->sseService->hasNewNotifications($userId, $lastCheckTime->toISOString())) {
                $notifications = $this->sseService->getPendingNotifications($userId);

                if (!empty($notifications)) {
                    echo $this->sseService->createNotificationEvent($notifications);

                    if (ob_get_level()) {
                        ob_end_flush();
                    }
                    flush();

                    // 전송된 알림들을 전달 완료로 표시
                    $notificationIds = array_column($notifications, 'id');
                    $this->sseService->markMultipleAsDelivered($userId, $notificationIds);
                }
            }

        } catch (Throwable $e) {
            $this->logSSEError($e, $userId, 'new_notifications_check');
        }
    }

    /**
     * SSE 관련 오류 로깅
     *
     * @param Throwable $e
     * @param int $userId
     * @param string|null $context
     * @return void
     */
    private function logSSEError(Throwable $e, int $userId, ?string $context = null): void
    {
        $logMessage = 'SSE 연결 오류';
        if ($context) {
            $logMessage .= " ({$context})";
        }
        $logMessage .= ': ' . $e->getMessage();

        // SimpleLogService를 통해 로깅 (Controller의 errorResponse 메서드 로직 참조)
        \App\Services\SimpleLogService::log(
            'notification',
            $logMessage,
            'error',
            [
                'user_id' => $userId,
                'context' => $context,
                'exception' => get_class($e),
                'file' => $e->getFile() . ':' . $e->getLine(),
            ],
            $e
        );
    }
}
