<?php

namespace App\Http\Controllers\WMS\Notification;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\NotificationTemplate\ListRequest;
use App\Http\Requests\NotificationTemplate\StoreRequest;
use App\Http\Requests\NotificationTemplate\UpdateRequest;
use App\Services\TemplateService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Throwable;

/**
 * 알림 템플릿 관리 컨트롤러
 *
 * 알림 템플릿의 CRUD 작업과 사용 통계 관리를 담당합니다.
 * 관리자 권한이 필요한 작업들을 처리합니다.
 */
class TemplateController extends Controller
{
    /**
     * 생성자 - 의존성 주입을 통한 서비스 초기화
     *
     * @param  TemplateService  $templateService  템플릿 서비스
     */
    public function __construct(
        private readonly TemplateService $templateService
    ) {}

    /**
     * 템플릿 목록 조회
     *
     * 페이지네이션, 검색, 필터링, 정렬 기능을 제공합니다.
     *
     * 지원하는 쿼리 파라미터:
     * - search: 템플릿명과 제목에서 부분 일치 검색
     * - priority: 우선순위 필터 (low, normal, high, urgent)
     * - sortBy: 정렬 기준 (usage_count, created_at, name, title)
     * - sortDirection: 정렬 방향 (asc, desc)
     * - page: 페이지 번호
     * - pageSize: 페이지 크기
     *
     * @param  ListRequest  $request  검증된 요청 데이터
     * @return JsonResponse 템플릿 목록과 페이지네이션 정보
     */
    public function list(ListRequest $request): JsonResponse
    {
        try {
            $validated = $request->validated();
            $userId = Auth::id();

            $paginatedResult = $this->templateService->getTemplates($userId, $validated);

            // 검색 메타데이터 추가
            $searchMeta = [
                'applied_filters' => [
                    'search' => $validated['search'] ?? null,
                    'priority' => $validated['priority'] ?? null,
                    'sort_by' => $validated['sortBy'] ?? 'created_at',
                    'sort_direction' => $validated['sortDirection'] ?? 'desc',
                ],
                'total_results' => $paginatedResult->total(),
                'filtered_results' => $paginatedResult->count(),
                'has_filters' => ! empty($validated['search']) || ! empty($validated['priority']),
            ];

            return $this->successResponse([
                'items' => $paginatedResult->items(),
                'pagination' => PaginationHelper::optimize($paginatedResult),
                'search_meta' => $searchMeta,
            ], '템플릿 목록을 성공적으로 조회했습니다.');

        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '템플릿 목록 조회에 실패했습니다.',
                'log_message' => '템플릿 목록 조회 실패: '.$e->getMessage(),
            ], $e, 'notification', 500);
        }
    }

    /**
     * 특정 템플릿 상세 조회
     *
     * @param  int  $id  템플릿 ID
     * @return JsonResponse 템플릿 상세 정보
     */
    public function show(int $id): JsonResponse
    {
        try {
            $userId = Auth::id();
            $template = $this->templateService->getTemplateById($userId, $id);

            return $this->successResponse([
                'template' => $template,
            ], '템플릿을 성공적으로 조회했습니다.');

        } catch (Exception $e) {
            if (str_contains($e->getMessage(), 'not found')) {
                return $this->notFoundResponse();
            }

            return $this->errorResponse([
                'message' => '템플릿 조회에 실패했습니다.',
                'log_message' => '템플릿 조회 실패: '.$e->getMessage(),
            ], $e, 'notification', 500);
        }
    }

    /**
     * 새 템플릿 생성
     *
     * @param  StoreRequest  $request  검증된 생성 요청 데이터
     * @return JsonResponse 생성된 템플릿 정보
     *
     * @throws Throwable
     */
    public function store(StoreRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $validated['created_by'] = Auth::id();

        DB::beginTransaction();
        try {
            $template = $this->templateService->createTemplate(Auth::id(), $validated);

            DB::commit();

            return $this->successResponse([
                'template' => $template,
            ], '템플릿이 성공적으로 생성되었습니다.', 201);

        } catch (Exception $e) {
            DB::rollBack();

            // 중복 이름 오류 처리
            if (str_contains($e->getMessage(), 'duplicate') || str_contains($e->getMessage(), 'unique')) {
                return $this->errorResponse([
                    'message' => '이미 존재하는 템플릿명입니다.',
                    'log_message' => '템플릿 생성 실패 - 중복 이름: '.$e->getMessage(),
                ], $e, 'notification', 422);
            }

            return $this->errorResponse([
                'message' => '템플릿 생성에 실패했습니다.',
                'log_message' => '템플릿 생성 실패: '.$e->getMessage(),
            ], $e, 'notification', 500);
        }
    }

    /**
     * 템플릿 수정
     *
     * @param  UpdateRequest  $request  검증된 수정 요청 데이터
     * @param  int  $id  수정할 템플릿 ID
     * @return JsonResponse 수정된 템플릿 정보
     *
     * @throws Throwable
     */
    public function update(UpdateRequest $request, int $id): JsonResponse
    {
        $validated = $request->validated();

        DB::beginTransaction();
        try {
            $template = $this->templateService->updateTemplate(Auth::id(), $id, $validated);

            DB::commit();

            return $this->successResponse([
                'template' => $template,
            ], '템플릿이 성공적으로 수정되었습니다.');

        } catch (Exception $e) {
            DB::rollBack();

            if (str_contains($e->getMessage(), 'not found')) {
                return $this->notFoundResponse();
            }

            // 중복 이름 오류 처리
            if (str_contains($e->getMessage(), 'duplicate') || str_contains($e->getMessage(), 'unique')) {
                return $this->errorResponse([
                    'message' => '이미 존재하는 템플릿명입니다.',
                    'log_message' => '템플릿 수정 실패 - 중복 이름: '.$e->getMessage(),
                ], $e, 'notification', 422);
            }

            return $this->errorResponse([
                'message' => '템플릿 수정에 실패했습니다.',
                'log_message' => '템플릿 수정 실패: '.$e->getMessage(),
            ], $e, 'notification', 500);
        }
    }

    /**
     * 템플릿 삭제
     *
     * @param  int  $id  삭제할 템플릿 ID
     * @return JsonResponse 삭제 결과
     *
     * @throws Throwable
     */
    public function destroy(int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $this->templateService->deleteTemplate(Auth::id(), $id);

            DB::commit();

            return $this->successResponse([
                'message' => '템플릿이 성공적으로 삭제되었습니다.',
            ], '템플릿이 성공적으로 삭제되었습니다.');

        } catch (Exception $e) {
            DB::rollBack();

            if (str_contains($e->getMessage(), 'not found')) {
                return $this->notFoundResponse();
            }

            return $this->errorResponse([
                'message' => '템플릿 삭제에 실패했습니다.',
                'log_message' => '템플릿 삭제 실패: '.$e->getMessage(),
            ], $e, 'notification', 500);
        }
    }

    /**
     * 템플릿 사용 횟수 증가
     *
     * 템플릿이 실제로 사용될 때 호출되어 사용 통계를 업데이트합니다.
     *
     * @param  int  $id  사용된 템플릿 ID
     * @return JsonResponse 업데이트 결과와 통계 데이터
     */
    public function incrementUsage(int $id): JsonResponse
    {
        try {
            $result = $this->templateService->incrementUsage(Auth::id(), $id);

            return $this->successResponse([
                'template' => $result['template'],
                'statistics' => $result['statistics'],
            ], '사용 횟수가 업데이트되었습니다.');

        } catch (Exception $e) {
            if (str_contains($e->getMessage(), 'not found')) {
                return $this->notFoundResponse();
            }

            return $this->errorResponse([
                'message' => '사용 횟수 업데이트에 실패했습니다.',
                'log_message' => '사용 횟수 업데이트 실패: '.$e->getMessage(),
            ], $e, 'notification', 500);
        }
    }

    /**
     * 모든 템플릿 조회 (페이지네이션 없음)
     *
     * 드롭다운이나 선택 목록용으로 사용됩니다.
     *
     * @return JsonResponse 전체 템플릿 목록
     */
    public function all(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $templates = $this->templateService->getAllTemplates($userId);

            return $this->successResponse([
                'templates' => $templates,
            ], '전체 템플릿 목록을 성공적으로 조회했습니다.');

        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '전체 템플릿 목록 조회에 실패했습니다.',
                'log_message' => '전체 템플릿 목록 조회 실패: '.$e->getMessage(),
            ], $e, 'notification', 500);
        }
    }
}
