<?php

namespace App\Http\Controllers\WMS\Notification;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Notification\ListRequest;
use App\Http\Requests\Notification\StoreRequest;
use App\Http\Requests\Notification\UpdateRequest;
use App\Models\NotificationGroup;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Throwable;

class GroupController extends Controller
{
    public function list(ListRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $pageSize = $validated['pageSize'];
        $keyword = $validated['search'];

        $query = NotificationGroup::query();
        if ($keyword) {
            $query = NotificationGroup::where('name', 'like', '%'.$keyword.'%');
        }
        $paginatedResult = $query
            ->with(['members.user:id,name'])
            ->withCount('members')
            ->paginate($pageSize)->withQueryString();

        return $this->successResponse([
            'items' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    public function show(int $id): JsonResponse
    {
        return $this->successResponse([
            'group' => NotificationGroup::with('members.user')->findOrFail($id),
        ]);
    }

    /**
     * @throws Throwable
     */
    public function store(StoreRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $validated['created_by'] = auth()->id();

        DB::beginTransaction();
        try {
            $group = NotificationGroup::create($validated);
            $group->members()->createMany(
                collect($validated['member_ids'] ?? [])
                    ->map(function ($memberId) {
                        return ['user_id' => $memberId];
                    })
                    ->toArray()
            );
            DB::commit();

            return $this->successResponse([
                'group' => $group,
            ]);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '그룹 생성에 실패했습니다.',
                'log_message' => '그룹 생성 실패: '.$e->getMessage(),
            ], $e, 'notification', 500);
        }
    }

    /**
     * @throws Throwable
     */
    public function update(UpdateRequest $request, int $id): JsonResponse
    {
        $validated = $request->validated();

        DB::beginTransaction();
        try {
            $group = NotificationGroup::findOrFail($id);

            // 기본 필드 부분 갱신
            $updatable = [];
            foreach (['name', 'description', 'is_active'] as $field) {
                if (array_key_exists($field, $validated)) {
                    $updatable[$field] = $validated[$field];
                }
            }
            if (! empty($updatable)) {
                $group->update($updatable);
            }

            // 멤버 동기화 (전달된 경우에만)
            if (array_key_exists('member_ids', $validated)) {
                $newIds = collect($validated['member_ids'] ?? [])->unique()->values();
                $existingIds = $group->members()->pluck('user_id');

                $toDelete = $existingIds->diff($newIds);
                $toAdd = $newIds->diff($existingIds);

                if ($toDelete->isNotEmpty()) {
                    $group->members()->whereIn('user_id', $toDelete)->delete();
                }
                if ($toAdd->isNotEmpty()) {
                    $group->members()->createMany(
                        $toAdd->map(fn ($uid) => ['user_id' => $uid])->all()
                    );
                }
            }

            DB::commit();

            return $this->successResponse([
                'group' => $group->fresh(['members.user']),
            ]);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '그룹 수정에 실패했습니다.',
                'log_message' => '그룹 수정 실패: '.$e->getMessage(),
            ], $e, 'notification', 500);
        }
    }

    /**
     * @param  UpdateRequest  $request
     *
     * @throws Throwable
     */
    public function addMembers(Request $request, int $groupId): JsonResponse
    {
        DB::beginTransaction();
        try {
            $validated = $request->validate([
                'member_ids' => 'required|array',
                'member_ids.*' => 'exists:users,id',
            ]);
            $group = NotificationGroup::findOrFail($groupId);
            $group->members()->createMany(
                collect($validated['member_ids'] ?? [])
                    ->map(fn ($uid) => ['user_id' => $uid])
                    ->all()
            );

            DB::commit();

            return $this->successResponse([
                'group' => $group->fresh(['members.user']),
            ]);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '멤버 추가에 실패했습니다.',
                'log_message' => '멤버 추가 실패: '.$e->getMessage(),
            ], $e, 'notification', 500);
        }
    }

    /**
     * @throws Throwable
     */
    public function destroy(int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            NotificationGroup::destroy($id);

            DB::commit();

            return $this->successResponse([
                'message' => '그룹이 삭제되었습니다.',
            ]);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '그룹 삭제에 실패했습니다.',
                'log_message' => '그룹 삭제 실패: '.$e->getMessage(),
            ], $e, 'notification', 500);
        }
    }

    /**
     * @throws Throwable
     */
    public function removeMember(int $groupId, int $userId): JsonResponse
    {
        DB::beginTransaction();
        try {
            NotificationGroup::findOrFail($groupId)->members()->where('user_id', $userId)->delete();

            DB::commit();

            return $this->successResponse([
                'message' => '그룹에서 멤버가 삭제되었습니다.',
            ]);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '멤버 삭제에 실패했습니다.',
                'log_message' => '멤버 삭제 실패: '.$e->getMessage(),
            ], $e, 'notification', 500);
        }
    }
}
