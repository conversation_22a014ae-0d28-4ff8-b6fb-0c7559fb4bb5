<?php

namespace App\Http\Controllers\WMS;

use App\Http\Controllers\Controller;
use App\Imports\VidImport;
use App\Services\SimpleLogService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class VidController extends Controller
{
    /**
     * Vid.xlsx 파일 업로드 처리
     */
    public function upload(Request $request): JsonResponse
    {
        try {
            // 파일 검증
            $request->validate([
                'vid_file' => 'required|file|mimes:xlsx,xls|max:2048', // 최대 2MB
            ], [
                'vid_file.required' => 'Vid.xlsx 파일을 선택해주세요.',
                'vid_file.file' => '올바른 파일을 업로드해주세요.',
                'vid_file.mimes' => 'Excel 파일(.xlsx, .xls)만 업로드 가능합니다.',
                'vid_file.max' => '파일 크기는 최대 2MB까지 가능합니다.',
            ]);

            $file = $request->file('vid_file');

            // 파일 저장
            $fileName = 'vid_'.now()->format('Y-m-d_H-i-s').'_'.uniqid().'.'.$file->getClientOriginalExtension();
            $filePath = $file->storeAs('temp/vid', $fileName);

            SimpleLogService::info('vid', '엑셀 업로드 시작', [
                'original_name' => $file->getClientOriginalName(),
                'stored_name' => $fileName,
                'file_size' => $file->getSize(),
                'user_id' => auth()->id(),
            ]);

            // Excel 임포트 실행 (첫 번째 시트, 2행부터 시작)
            $import = new VidImport(2); // 2행부터 시작 (헤더 제외)
            Excel::import($import, $filePath, null, \Maatwebsite\Excel\Excel::XLSX);

            // 임시 파일 삭제
            Storage::delete($filePath);

            SimpleLogService::info('vid', '엑셀 파일 업로드 완료', [
                'file_path' => $filePath,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => '엑셀 파일이 성공적으로 업로드되었습니다.',
                'data' => [
                    'processed_rows' => $import->totalProcessed,
                    'created_records' => $import->totalCreated,
                    'skipped_records' => $import->totalSkipped,
                ],
            ]);

        } catch (Exception $e) {
            SimpleLogService::error('vid', '엑셀 파일 업로드 실패', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
            ], $e);

            return response()->json([
                'success' => false,
                'message' => '엑셀 파일 업로드 중 오류가 발생했습니다: '.$e->getMessage(),
            ], 500);
        }
    }

    /**
     * ProductLink 통계 정보 조회
     */
    public function statistics(): JsonResponse
    {
        try {
            $totalLinks = \App\Models\ProductLink::count();
            $linksWithProductId = \App\Models\ProductLink::whereNotNull('product_id')->count();
            $linksWithItemId = \App\Models\ProductLink::whereNotNull('item_id')->count();
            $completeLinks = \App\Models\ProductLink::whereNotNull('product_id')
                ->whereNotNull('item_id')
                ->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_links' => $totalLinks,
                    'links_with_product_id' => $linksWithProductId,
                    'links_with_item_id' => $linksWithItemId,
                    'complete_links' => $completeLinks,
                    'completion_rate' => $totalLinks > 0 ? round(($completeLinks / $totalLinks) * 100, 2) : 0,
                ],
            ]);

        } catch (Exception $e) {
            SimpleLogService::error('vid', 'ProductLink 통계 조회 실패', [
                'error' => $e->getMessage(),
            ], $e);

            return response()->json([
                'success' => false,
                'message' => '통계 정보 조회 중 오류가 발생했습니다.',
            ], 500);
        }
    }
}
