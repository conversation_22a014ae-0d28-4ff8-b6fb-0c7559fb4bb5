<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Monitor\IndexRequest;
use App\Http\Requests\Monitor\UpdateRequest;
use App\Services\MonitorSizeLookupService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class MonitorSizeLookupController extends Controller
{
    public function __construct(
        private readonly MonitorSizeLookupService $service
    ) {}

    /**
     * 모니터 사이즈 목록 검색
     */
    public function index(IndexRequest $request): JsonResponse
    {
        $validated = $request->validated();
        try {
            $paginated = $this->service->search($validated);

            return $this->successResponse([
                'items' => $paginated->items(),
                'pagination' => PaginationHelper::optimize($paginated),
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '모니터 사이즈 조회에 실패했습니다.',
                'log_message' => '모니터 사이즈 조회 실패: '.$e->getMessage(),
            ], $e, 'monitor', 500);
        }
    }

    /**
     * 브랜드/사이즈/unit 수정 (name, name_hash 불변)
     */
    public function update(UpdateRequest $request, int $id): JsonResponse
    {
        $validated = $request->validated();
        try {
            $item = $this->service->updateAttributes($id, $validated);

            return $this->successResponse([
                'item' => $item,
            ], '모니터 사이즈가 수정되었습니다.');
        } catch (ValidationException $e) {
            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors(),
            ], $e, 'monitor', 422);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '모니터 사이즈 수정에 실패했습니다.',
                'log_message' => '모니터 사이즈 수정 실패: '.$e->getMessage(),
                'error_code' => 'MONITOR_SIZE_UPDATE_FAILED',
            ], $e, 'monitor', 500);
        }
    }
}
