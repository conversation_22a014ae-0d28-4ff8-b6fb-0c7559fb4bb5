<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\DateHelper;
use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Pallet\CloseOpenRequest;
use App\Http\Requests\Pallet\ExportRequest;
use App\Http\Requests\Pallet\SaveExportDateRequest;
use App\Services\PalletQueryBuilderService;
use App\Services\PalletService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Throwable;

class PalletController extends Controller
{
    protected PalletQueryBuilderService $queryBuilderService;

    protected palletService $palletService;

    public function __construct(PalletQueryBuilderService $queryBuilderService, palletService $palletService)
    {
        $this->queryBuilderService = $queryBuilderService;
        $this->palletService = $palletService;
    }

    /**
     * 팔레트 리스트
     */
    public function list(Request $request): JsonResponse
    {
        $data = [
            'status' => $request->input('status'),
            'beginAt' => $request->input('beginAt'),
            'endAt' => $request->input('endAt'),
            'exported_at' => $request->input('exported_at'),
            'keyword' => $request->input('keyword'),
            'pageSize' => $request->input('pageSize', 16),
        ];

        if (! empty($data['beginAt']) && ! empty($data['endAt'])) {
            [, $data['beginAt'], $data['endAt']] = DateHelper::getBetweenDate(
                $request->input('beginAt'),
                $request->input('endAt')
            );
        }

        $query = $this->queryBuilderService->getPalletList($data);
        $paginatedResult = $query->paginate($data['pageSize'] ?? 15)->withQueryString();

        return $this->successResponse([
            'pallets' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * 팔레트 마감
     * method: put
     *
     * @throws Exception|Throwable
     */
    public function closePallet(CloseOpenRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $validatedData = $request->validated();
            $this->palletService->updateClosePallet($validatedData, $user);

            return $this->successResponse([
                'message' => '마감처리 완료',
                // 'response' => $request->all()
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'pallet', $e->getCode());
        }
    }

    /**
     * 팔레트 마감 취소(재오픈)
     * method: put
     *
     * @throws Exception|Throwable
     */
    public function openPallet(CloseOpenRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $validatedData = $request->validated();
            $this->palletService->updateOpenPallet($validatedData, $user);

            return $this->successResponse([
                'message' => '마감 취소처리 완료',
                // 'response' => $request->all()
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'pallet', $e->getCode());
        }
    }

    /**
     * 출고
     * method: post
     *
     * @throws Exception|Throwable
     */
    public function exportPallets(ExportRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $palletIds = $request->validated('palletIds');
            $this->palletService->updateExportPallets($palletIds, $user);

            return $this->successResponse([
                'message' => '정상 처리 되었습니다.',
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '쿠팡PL팔레트 출고에 실패하였습니다.',
            ], $e, 'pallet', 500);
        }
    }

    /**
     * 출고 취소
     * method: put
     *
     * @throws Exception|Throwable
     */
    public function rollbackExportPallets(ExportRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $palletIds = $request->validated('palletIds');
            $this->palletService->updateRollbackExportPallets($palletIds, $user);

            return $this->successResponse([
                'message' => '정상 처리 되었습니다.',
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '쿠팡PL팔레트 출고에 실패하였습니다.',
            ], $e, 'pallet', 500);
        }
    }

    /**
     * 출고 날짜 저장
     * method: put
     *
     * @throws Exception|Throwable
     */
    public function saveExportDate(SaveExportDateRequest $request): JsonResponse
    {
        try {
            // 유효성 검증된 데이터: 배열 형태
            $validatedData = $request->validated();

            $user = Auth::user();

            $this->palletService->updatePalletExportDate($validatedData, $user);

            return $this->successResponse([
                'message' => '정상 처리 되었습니다.',
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '쿠팡PL팔레트 출고날짜 저장에 실패하였습니다.',
            ], $e, 'pallet', 500);
        }
    }
}
