<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\DateHelper;
use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Req\StoreRequest;
use App\Http\Requests\Req\UpdateRequest;
use App\Jobs\ReqJob;
use App\Models\Req;
use App\Models\ReqCount;
use App\Models\User;
use App\Services\CountService;
use App\Services\ReqService;
use App\Services\SimpleLogService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

/**
 * 쿠팡에서 점검 요청을 받은 리스트(엑셀)를 입력한다.
 */
class ReqController extends Controller
{
    protected ReqService $reqService;

    public function __construct(ReqService $reqService)
    {
        $this->reqService = $reqService;
    }

    /**
     * 엑셀 업로드 및 상품 입력 Job 처리
     *
     * @throws Exception
     */
    private function processExcel(Req $req, User $user, StoreRequest|UpdateRequest $request): void
    {
        $file = $request->file('excel');

        if (! $file) {
            throw new Exception('엑셀 파일이 업로드되지 않았습니다.', Response::HTTP_BAD_REQUEST);
        }

        $extension = $file->guessExtension(); // 확장자
        $originalName = $file->getClientOriginalName(); // 원본 파일 이름

        $allowedExtensions = ['xlsx', 'xls'];
        if (! in_array($extension, $allowedExtensions)) {
            throw new Exception('업로드된 파일은 엑셀 파일이 아닙니다.(엑셀 파일 오류)', Response::HTTP_BAD_REQUEST);
        }

        $maxFileSize = 2 * 1024 * 1024; // 파일 크기 체크 (2MB = 2 * 1024 * 1024 = 2097152 바이트)
        if ($file->getSize() > $maxFileSize) {
            throw new Exception('파일 크기는 2MB를 초과할 수 없습니다.', Response::HTTP_BAD_REQUEST);
        }

        if (! $file->isValid()) {
            throw new Exception('파일 업로드 실패(서버 오류)', Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        $dir = 'uploads';
        Storage::makeDirectory($dir);
        $path = $file->store($dir);

        $redirect = $request->input('redirect');
        $startRow = (int) $request->input('start_row', 3);

        // 큐잡 작업 - products 테이블에 데이터 입력
        ReqJob::dispatch($req, $user, $path, $originalName, $redirect, $startRow);
    }

    public function reqList(Request $request): JsonResponse
    {
        [, $beginAt, $endAt] = DateHelper::getBetweenDate(
            $request->input('beginAt'),
            $request->input('endAt')
        );
        $reqType = $request->input('reqType');
        $perPage = intval($request->input('pageSize', 16));

        $items = $this->reqService->getReqList([
            'beginAt' => $beginAt,
            'endAt' => $endAt,
            'reqType' => $reqType,
        ]);
        $paginatedResult = $items->paginate($perPage)->withQueryString();

        return $this->successResponse([
            'items' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * 검수대기 요청서 리스트: 요청 리스트 중 아직 완료되지 않은 리스트만 뽑아온다.
     */
    public function uncheckedList(Request $request): JsonResponse
    {
        $items = $this->reqService->getUncheckedList(['id', 'req_at'], 'req_at');

        return $this->successResponse([
            'items' => $items,
        ]);
    }

    /**
     * 요청 리스트 정보
     */
    public function show(int $id): JsonResponse
    {
        return $this->successResponse([
            'item' => Req::find($id),
        ]);
    }

    /**
     * 요청 리스트 저장
     *
     * @throws Exception|Throwable
     */
    public function store(StoreRequest $request): JsonResponse
    {
        $data = $request->validated();

        try {
            $user = Auth::user();

            if (! $user) {
                throw new Exception('인증된 사용자를 찾을 수 없습니다.', Response::HTTP_UNAUTHORIZED);
            }

            $req = $this->reqService->create($user, $data);

            // 상품 등록 오류가 날 경우 카운터 테이블이 생성 되지 않아 오류가 남
            // 따라서 미리 만들어 둔다.
            $counter = new ReqCount;
            $counter->req_id = $req->id;
            $counter->save();

            // 업로드 한 파일이 있다면 DB에 입력
            if ($request->hasFile('excel')) {
                $this->processExcel($req, $user, $request);
            }

            return $this->successResponse([
                'req' => $req,
                'memo' => $req->memo ?? '메모 없음',
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '입고 목록 저장에 실패했습니다.',
                'log_message' => '입고 목록 저장 실패: '.$e->getMessage(),
            ], $e, 'req', 500);
        }
    }

    /**
     * 요청 리스트 수정
     *
     * @throws Exception|Throwable
     */
    public function update(UpdateRequest $request, int $id): JsonResponse
    {
        $data = $request->validated();
        SimpleLogService::debug('req', '입고 수정 요청', [
            'req_id' => $id,
            'data' => $data,
        ]);

        try {
            $user = Auth::user();

            if (! $user) {
                throw new Exception('인증된 사용자를 찾을 수 없습니다.', Response::HTTP_UNAUTHORIZED);
            }

            $req = $this->reqService->update($id, $user, $data);

            // 업로드 한 파일이 있다면 DB에 입력
            if ($request->hasFile('excel')) {
                $this->processExcel($req, $user, $request);
            }

            return $this->successResponse([
                'req' => $req,
                'memo' => $req->memo ?? '메모 없음',
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '입고 목록 수정에 실패했습니다.',
                'log_message' => '입고 목록 수정 실패: '.$e->getMessage(),
            ], $e, 'req', 500);
        }
    }

    /**
     * 요청 리스트 삭제
     * 요청 리스트의 상품들은 외래키 제약사항중 on delete cascade 관계를 가지고 있어
     * 삭제하면 자동으로 모든 상품들이 삭제되므로 따로 작업하지 않아도 된다.
     *
     * @throws Throwable
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $user = Auth::user();

            if (! $user) {
                throw new Exception('인증된 사용자를 찾을 수 없습니다.', Response::HTTP_UNAUTHORIZED);
            }

            $this->reqService->destroy($id, $user);

            SimpleLogService::info('req', "입고정보($id) 삭제 완료");

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '입고 정보삭제에 실패하였습니다.',
                'log_message' => "입고 정보($id) 삭제 실패.",
            ], $e, 'req', 500);
        }
    }

    /**
     * req_id가 있는 요청서의 카운터만 업데이트
     */
    public function updateReqCount(int $id): JsonResponse
    {
        try {
            $countService = new CountService;
            $countService->updateReqCount($id);

            return $this->successResponse([
                'message' => '카운터가 업데이트 되었습니다.',
            ]);
        } catch (Throwable $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'req', 500);
        }
    }
}
