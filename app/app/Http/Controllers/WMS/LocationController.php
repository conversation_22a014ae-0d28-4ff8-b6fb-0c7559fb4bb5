<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Services\LocationService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class LocationController extends Controller
{
    protected LocationService $locationService;

    public function __construct(LocationService $locationService)
    {
        $this->locationService = $locationService;
    }

    public function list(Request $request): JsonResponse
    {
        $data = [
            'enable' => $request->input('enable'),
            'store' => mb_strtoupper($request->input('store', '')),
            'line' => $request->input('line', ''),
            'rack' => $request->input('rack', ''),
            'level' => mb_strtoupper($request->input('level', '')),
            'column' => $request->input('column', ''),
            'pageSize' => $request->input('pageSize', 100),
        ];

        $query = $this->locationService->getList($data);
        $paginatedResult = $query->paginate($data['pageSize'])->withQueryString();

        return $this->successResponse([
            'items' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * 팔레트 정보 업데이트
     * method: put
     *
     * @throws Exception|Throwable
     */
    public function update(Request $request): JsonResponse
    {
        try {
            $data = [
                'id' => $request->input('id'),
                'store' => $request->input('store', 'A'),
                'line' => $request->input('line', '1'),
                'rack' => $request->input('rack', '1'),
                'level' => $request->input('level'),
                'column' => $request->input('column'),
                'name' => $request->input('name'),
                'enable' => $request->input('enable'),
            ];

            $this->locationService->updateLocation($data);

            return $this->successResponse([
                'message' => '정상 처리 되었습니다.',
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '팔레트 정보 업데이트 실패',
            ], $e, 'pallet', 500);
        }
    }

    public function generateCode(string $place): JsonResponse
    {
        if (! $place) {
            return $this->errorResponse([
                'message' => 'place not found',
            ]);
        }

        $code = $this->locationService->createCode($place);

        return $this->successResponse([
            'level' => $code['level'],
            'column' => $code['column'],
        ]);
    }
}
