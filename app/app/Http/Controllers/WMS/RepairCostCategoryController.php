<?php

namespace App\Http\Controllers\WMS;

use App\Http\Controllers\Controller;
use App\Models\Cate4;
use App\Models\Cate5;
use App\Models\RepairCostCategory;
use App\Models\RepairCostPolicy;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class RepairCostCategoryController extends Controller
{
    /**
     * 시스템별 카테고리 목록 조회
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $systemId = $request->query('system_id');
            $isActive = $request->query('is_active');

            $query = RepairCostCategory::with(['policy', 'cate4', 'cate5', 'ranges']);

            // 시스템별 필터링
            if ($systemId) {
                $query->bySystem($systemId);
            }

            // 활성화 상태 필터링
            if ($isActive !== null) {
                if ($isActive === 'true' || $isActive === '1') {
                    $query->active();
                } elseif ($isActive === 'false' || $isActive === '0') {
                    $query->where('is_active', false);
                }
            }

            $categories = $query->orderBy('id', 'desc')->get();

            return $this->successResponse([
                'categories' => $categories,
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '카테고리 목록 조회에 실패했습니다.',
                'log_message' => '카테고리 목록 조회 실패: '.$e->getMessage(),
                'error_code' => 'CATEGORY_LIST_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 새로운 카테고리 생성
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'repair_cost_policy_id' => 'required|exists:repair_cost_policies,id',
                'cate4_id' => 'required|exists:cate4,id',
                'cate5_id' => 'nullable|exists:cate5,id',
                'pricing_criteria' => [
                    'required',
                    Rule::in([
                        RepairCostCategory::PRICING_CRITERIA_SIZE,
                        RepairCostCategory::PRICING_CRITERIA_PRICE,
                        RepairCostCategory::PRICING_CRITERIA_COMMON,
                    ]),
                ],
                'is_active' => 'boolean',
            ]);

            // 중복 매핑 검증
            $existingCategory = RepairCostCategory::byMapping(
                $validated['cate4_id'],
                $validated['cate5_id'] ?? null
            )->bySystem($validated['repair_cost_policy_id'])->first();

            if ($existingCategory) {
                return $this->errorResponse([
                    'message' => '이미 해당 카테고리 매핑이 존재합니다.',
                    'error_code' => 'DUPLICATE_CATEGORY_MAPPING',
                ], null, 'repair', 422);
            }

            // 시스템과 가격 기준 호환성 검증
            $system = RepairCostPolicy::findOrFail($validated['repair_cost_policy_id']);
            if (! $this->validatePricingCriteriaCompatibility($system, $validated['pricing_criteria'])) {
                return $this->errorResponse([
                    'message' => '선택한 가격 기준이 시스템과 호환되지 않습니다.',
                    'error_code' => 'INCOMPATIBLE_PRICING_CRITERIA',
                ], null, 'repair', 422);
            }

            // Cate5가 지정된 경우 Cate4와의 관계 검증
            if (isset($validated['cate5_id'])) {
                $cate5 = Cate5::findOrFail($validated['cate5_id']);
                if ($cate5->cate4_id != $validated['cate4_id']) {
                    return $this->errorResponse([
                        'message' => 'Cate5가 지정된 Cate4에 속하지 않습니다.',
                        'error_code' => 'INVALID_CATE5_MAPPING',
                    ], null, 'repair', 422);
                }
            }

            $validated['is_active'] = $validated['is_active'] ?? true;

            DB::beginTransaction();

            $category = RepairCostCategory::create($validated);
            $category->load(['policy', 'cate4', 'cate5']);

            DB::commit();

            return $this->successResponse([
                'category' => $category,
            ]);
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'CATEGORY_VALIDATION_FAILED',
                'errors' => $e->errors(),
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '카테고리 생성에 실패했습니다.',
                'error_code' => 'CATEGORY_CREATE_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 특정 카테고리 조회
     */
    public function show(int $id): JsonResponse
    {
        try {
            $category = RepairCostCategory::with(['policy', 'cate4', 'cate5', 'ranges.costs'])
                ->findOrFail($id);

            $category->ranges->each(function ($range) {
                $range->costs_summary = $range->costs->mapWithKeys(function ($cost) {
                    return [$cost->repair_type => $cost->amount];
                });
                $range->display_name = $range->display_name;
            });

            return $this->successResponse([
                'category' => $category,
            ]);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => '카테고리를 찾을 수 없습니다.',
                'error_code' => 'CATEGORY_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '카테고리 조회에 실패했습니다.',
                'error_code' => 'CATEGORY_SHOW_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 카테고리 정보 수정
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $category = RepairCostCategory::findOrFail($id);

            $validated = $request->validate([
                'repair_cost_policy_id' => 'sometimes|exists:repair_cost_policies,id',
                'cate4_id' => 'sometimes|exists:cate4,id',
                'cate5_id' => 'nullable|exists:cate5,id',
                'pricing_criteria' => [
                    'sometimes',
                    Rule::in([
                        RepairCostCategory::PRICING_CRITERIA_SIZE,
                        RepairCostCategory::PRICING_CRITERIA_PRICE,
                        RepairCostCategory::PRICING_CRITERIA_COMMON,
                    ]),
                ],
                'is_active' => 'sometimes|boolean',
            ]);

            // 매핑 변경 시 중복 검증
            if (isset($validated['cate4_id']) || isset($validated['cate5_id']) || isset($validated['repair_cost_policy_id'])) {
                $cate4Id = $validated['cate4_id'] ?? $category->cate4_id;
                $cate5Id = $validated['cate5_id'] ?? $category->cate5_id;
                $systemId = $validated['repair_cost_policy_id'] ?? $category->repair_cost_policy_id;

                $existingCategory = RepairCostCategory::byMapping($cate4Id, $cate5Id)
                    ->bySystem($systemId)
                    ->where('id', '!=', $id)
                    ->first();

                if ($existingCategory) {
                    return $this->errorResponse([
                        'message' => '이미 해당 카테고리 매핑이 존재합니다.',
                        'error_code' => 'DUPLICATE_CATEGORY_MAPPING',
                    ], null, 'repair', 422);
                }
            }

            // 시스템과 가격 기준 호환성 검증
            if (isset($validated['pricing_criteria']) || isset($validated['repair_cost_policy_id'])) {
                $systemId = $validated['repair_cost_policy_id'] ?? $category->repair_cost_policy_id;
                $pricingCriteria = $validated['pricing_criteria'] ?? $category->pricing_criteria;

                $system = RepairCostPolicy::findOrFail($systemId);
                if (! $this->validatePricingCriteriaCompatibility($system, $pricingCriteria)) {
                    return $this->errorResponse([
                        'message' => '선택한 가격 기준이 시스템과 호환되지 않습니다.',
                        'error_code' => 'INCOMPATIBLE_PRICING_CRITERIA',
                    ], null, 'repair', 422);
                }
            }

            // Cate5와 Cate4 관계 검증
            if (isset($validated['cate5_id']) && $validated['cate5_id']) {
                $cate4Id = $validated['cate4_id'] ?? $category->cate4_id;
                $cate5 = Cate5::findOrFail($validated['cate5_id']);
                if ($cate5->cate4_id != $cate4Id) {
                    return $this->errorResponse([
                        'message' => 'Cate5가 지정된 Cate4에 속하지 않습니다.',
                        'error_code' => 'INVALID_CATE5_MAPPING',
                    ], null, 'repair', 422);
                }
            }

            DB::beginTransaction();

            $category->update($validated);
            $category->load(['policy', 'cate4', 'cate5']);

            DB::commit();

            return $this->successResponse([
                'category' => $category,
            ]);
        } catch (ModelNotFoundException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '카테고리를 찾을 수 없습니다.',
                'error_code' => 'CATEGORY_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'CATEGORY_VALIDATION_FAILED',
                'errors' => $e->errors(),
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '카테고리 수정에 실패했습니다.',
                'error_code' => 'CATEGORY_UPDATE_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 카테고리 삭제
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $category = RepairCostCategory::findOrFail($id);

            // 연관된 범위가 있는지 확인
            if ($category->ranges()->count() > 0) {
                return $this->errorResponse([
                    'message' => '연관된 수리비 범위가 있어 삭제할 수 없습니다.',
                    'error_code' => 'CATEGORY_HAS_RANGES',
                ], null, 'repair', 422);
            }

            DB::beginTransaction();

            $category->delete();

            DB::commit();

            return $this->successResponse([], '카테고리가 성공적으로 삭제되었습니다.');
        } catch (ModelNotFoundException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '카테고리를 찾을 수 없습니다.',
                'error_code' => 'CATEGORY_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '카테고리 삭제에 실패했습니다.',
                'error_code' => 'CATEGORY_DELETE_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 매핑 가능한 Cate4, Cate5 목록 조회
     */
    public function getAvailableCategories(Request $request): JsonResponse
    {
        try {
            $systemId = $request->query('system_id');

            // Cate4 목록 조회 (이미 매핑된 것 제외)
            $cate4Query = Cate4::select('id', 'name')
                ->with(['cate5:id,cate4_id,name']);

            // 시스템이 지정된 경우 해당 시스템에서 이미 매핑된 Cate4 제외
            if ($systemId) {
                $mappedCate4Ids = RepairCostCategory::where('repair_cost_policy_id', $systemId)
                    ->whereNull('cate5_id')
                    ->pluck('cate4_id')
                    ->toArray();

                if (! empty($mappedCate4Ids)) {
                    $cate4Query->whereNotIn('id', $mappedCate4Ids);
                }
            }

            $cate4List = $cate4Query->orderBy('name')->get();

            // Cate5 목록 조회 (이미 매핑된 것 제외)
            $cate5Query = Cate5::select('id', 'cate4_id', 'name')
                ->with(['cate4:id,name']);

            // 시스템이 지정된 경우 해당 시스템에서 이미 매핑된 Cate5 제외
            if ($systemId) {
                $mappedCate5Ids = RepairCostCategory::where('repair_cost_policy_id', $systemId)
                    ->whereNotNull('cate5_id')
                    ->pluck('cate5_id')
                    ->toArray();

                if (! empty($mappedCate5Ids)) {
                    $cate5Query->whereNotIn('id', $mappedCate5Ids);
                }
            }

            $cate5List = $cate5Query->orderBy('name')->get();

            // 현재 매핑된 카테고리 정보도 함께 반환
            $currentMappings = [];
            if ($systemId) {
                $currentMappings = RepairCostCategory::with(['cate4', 'cate5'])
                    ->where('repair_cost_policy_id', $systemId)
                    ->get()
                    ->map(function ($category) {
                        return [
                            'id' => $category->id,
                            'cate4' => $category->cate4,
                            'cate5' => $category->cate5,
                            'pricing_criteria' => $category->pricing_criteria,
                            'is_active' => $category->is_active,
                        ];
                    });
            }

            return $this->successResponse([
                'available_cate4' => $cate4List,
                'available_cate5' => $cate5List,
                'current_mappings' => $currentMappings,
            ], '매핑 가능한 카테고리 목록을 성공적으로 조회했습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '매핑 가능한 카테고리 목록 조회에 실패했습니다.',
                'error_code' => 'AVAILABLE_CATEGORIES_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 카테고리 매핑 일괄 설정
     */
    public function bulkMapping(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'repair_cost_policy_id' => 'required|exists:repair_cost_policies,id',
                'mappings' => 'required|array|min:1',
                'mappings.*.cate4_id' => 'required|exists:cate4,id',
                'mappings.*.cate5_id' => 'nullable|exists:cate5,id',
                'mappings.*.pricing_criteria' => [
                    'required',
                    Rule::in([
                        RepairCostCategory::PRICING_CRITERIA_SIZE,
                        RepairCostCategory::PRICING_CRITERIA_PRICE,
                        RepairCostCategory::PRICING_CRITERIA_COMMON,
                    ]),
                ],
                'mappings.*.is_active' => 'boolean',
            ]);

            $systemId = $validated['repair_cost_policy_id'];
            $system = RepairCostPolicy::findOrFail($systemId);

            // 매핑 유효성 검증
            $mappingValidation = $this->validateBulkMappings($system, $validated['mappings']);
            if (! $mappingValidation['valid']) {
                return $this->errorResponse([
                    'message' => $mappingValidation['message'],
                    'error_code' => 'BULK_MAPPING_VALIDATION_FAILED',
                    'errors' => $mappingValidation['errors'],
                ], null, 'repair', 422);
            }

            DB::beginTransaction();

            $createdCategories = [];
            foreach ($validated['mappings'] as $mapping) {
                $mapping['repair_cost_policy_id'] = $systemId;
                $mapping['is_active'] = $mapping['is_active'] ?? true;

                $category = RepairCostCategory::create($mapping);
                $category->load(['policy', 'cate4', 'cate5']);
                $createdCategories[] = $category;
            }

            DB::commit();

            return $this->successResponse([
                'created_count' => count($createdCategories),
                'categories' => $createdCategories,
            ], '카테고리 매핑이 성공적으로 설정되었습니다.');
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'CATEGORY_VALIDATION_FAILED',
                'data' => null,
                'errors' => $e->errors(),
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '카테고리 일괄 매핑에 실패했습니다.',
                'error_code' => 'BULK_MAPPING_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 카테고리 매핑 상태 조회
     */
    public function getMappingStatus(int $systemId): JsonResponse
    {
        try {
            $system = RepairCostPolicy::findOrFail($systemId);

            // 전체 Cate4, Cate5 수
            $totalCate4Count = Cate4::count();
            $totalCate5Count = Cate5::count();

            // 매핑된 카테고리 수
            $mappedCate4Count = RepairCostCategory::where('repair_cost_policy_id', $systemId)
                ->whereNull('cate5_id')
                ->count();

            $mappedCate5Count = RepairCostCategory::where('repair_cost_policy_id', $systemId)
                ->whereNotNull('cate5_id')
                ->count();

            // 활성화된 매핑 수
            $activeMappingCount = RepairCostCategory::where('repair_cost_policy_id', $systemId)
                ->where('is_active', true)
                ->count();

            // 매핑 완료율 계산
            $cate4MappingRate = $totalCate4Count > 0 ? round(($mappedCate4Count / $totalCate4Count) * 100, 2) : 0;
            $cate5MappingRate = $totalCate5Count > 0 ? round(($mappedCate5Count / $totalCate5Count) * 100, 2) : 0;

            return $this->successResponse([
                'system' => $system,
                'mapping_status' => [
                    'cate4' => [
                        'total' => $totalCate4Count,
                        'mapped' => $mappedCate4Count,
                        'mapping_rate' => $cate4MappingRate,
                    ],
                    'cate5' => [
                        'total' => $totalCate5Count,
                        'mapped' => $mappedCate5Count,
                        'mapping_rate' => $cate5MappingRate,
                    ],
                    'active_mappings' => $activeMappingCount,
                    'total_mappings' => $mappedCate4Count + $mappedCate5Count,
                ],
            ]);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => '시스템을 찾을 수 없습니다.',
                'error_code' => 'SYSTEM_NOT_FOUND',
                'data' => null,
            ], $e, 'repair', 404);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '매핑 상태 조회에 실패했습니다.',
                'error_code' => 'MAPPING_STATUS_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 일괄 매핑 유효성 검증
     */
    private function validateBulkMappings(RepairCostPolicy $system, array $mappings): array
    {
        $errors = [];
        $seenMappings = [];

        foreach ($mappings as $index => $mapping) {
            // 중복 매핑 검증
            $mappingKey = $mapping['cate4_id'].'_'.($mapping['cate5_id'] ?? 'null');
            if (in_array($mappingKey, $seenMappings)) {
                $errors["mappings.{$index}"] = '중복된 매핑이 있습니다.';

                continue;
            }
            $seenMappings[] = $mappingKey;

            // 기존 매핑 존재 여부 확인
            $existingCategory = RepairCostCategory::byMapping(
                $mapping['cate4_id'],
                $mapping['cate5_id'] ?? null
            )->bySystem($system->id)->first();

            if ($existingCategory) {
                $errors["mappings.{$index}"] = '이미 매핑된 카테고리입니다.';

                continue;
            }

            // 시스템과 가격 기준 호환성 검증
            if (! $this->validatePricingCriteriaCompatibility($system, $mapping['pricing_criteria'])) {
                $errors["mappings.{$index}.pricing_criteria"] = '시스템과 호환되지 않는 가격 기준입니다.';

                continue;
            }

            // Cate5와 Cate4 관계 검증
            if (isset($mapping['cate5_id']) && $mapping['cate5_id']) {
                $cate5 = Cate5::find($mapping['cate5_id']);
                if (! $cate5 || $cate5->cate4_id != $mapping['cate4_id']) {
                    $errors["mappings.{$index}.cate5_id"] = 'Cate5가 지정된 Cate4에 속하지 않습니다.';
                }
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'message' => empty($errors) ? '유효성 검증 통과' : '매핑 데이터에 오류가 있습니다.',
        ];
    }

    /**
     * 시스템과 가격 기준 호환성 검증
     */
    private function validatePricingCriteriaCompatibility(RepairCostPolicy $system, string $pricingCriteria): bool
    {
        // 모니터 시스템은 크기 기준만 허용
        if ($system->code === RepairCostPolicy::POLICY_MONITOR_GENERAL) {
            return $pricingCriteria === RepairCostCategory::PRICING_CRITERIA_SIZE;
        }

        // OS 설치, 소프트웨어 시스템은 공통 금액만 허용
        if ($system->code == RepairCostPolicy::POLICY_OS_INSTALL_PRICE) {
            return $pricingCriteria === RepairCostCategory::PRICING_CRITERIA_COMMON;
        }

        // 일반, 애플, 기타 시스템은 가격 기준 또는 공통 금액 허용
        if (in_array($system->code, [RepairCostPolicy::POLICY_GENERAL_PRICE, RepairCostPolicy::POLICY_APPLE, RepairCostPolicy::POLICY_DEFAULT])) {
            return in_array($pricingCriteria, [
                RepairCostCategory::PRICING_CRITERIA_PRICE,
                RepairCostCategory::PRICING_CRITERIA_COMMON,
            ]);
        }

        return true;
    }
}
