<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Carryout\ProductExportRequest;
use App\Http\Requests\Carryout\ProductImportRequest;
use App\Http\Requests\Carryout\ProductListRequest;
use App\Services\CarryoutQueryBuilderService;
use App\Services\CarryoutService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Throwable;

class CarryoutProductController extends Controller
{
    protected CarryoutQueryBuilderService $queryBuilderService;

    protected CarryoutService $carryoutService;

    public function __construct(
        CarryoutQueryBuilderService $queryBuilderService,
        CarryoutService $carryoutService,
    ) {
        $this->queryBuilderService = $queryBuilderService;
        $this->carryoutService = $carryoutService;
    }

    public function list(ProductListRequest $request): JsonResponse
    {
        $data = $request->validated();

        $carryout = $this->carryoutService->getCarryoutById($data, ['status', 'carryout_at']);

        $builder = $this->queryBuilderService->getCarryoutProductList($data);
        $paginatedResult = $builder->paginate($data['pageSize'])->withQueryString();

        return $this->successResponse([
            'carryout' => $carryout,
            'items' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * 반출
     * method: put
     *
     * @throws Exception|Throwable
     */
    public function export(ProductExportRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $user = Auth::user();
            $this->carryoutService->exportCarryoutProduct($data, $user);

            return $this->successResponse([
                'message' => '외부 점검을 위한 외주 반출 성공',
            ]);
        } catch (Exception $e) {
            // 외부 점검을 위한 외주 반출에 실패한 경우
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'carryout', $e->getCode());
        }
    }

    /**
     * 반입
     * method: put
     *
     * @throws Exception|Throwable
     */
    public function import(ProductImportRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();
            $user = Auth::user();
            $this->carryoutService->importCarryoutProduct($data, $user);

            return $this->successResponse([
                'message' => '외주 반출 상품에 대한 반입 처리 성공',
            ]);
        } catch (Exception $e) {
            // 외주 반출 상품에 대한 반입 처리에 실패한 경우
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'carryout', $e->getCode());
        }
    }

    /**
     * 삭제
     * method: post
     *
     * @throws Exception|Throwable
     */
    public function destroy(Request $request): JsonResponse
    {
        try {
            $data['ids'] = $request->input('ids');
            $user = Auth::user();

            $this->carryoutService->destroyCarryoutProduct($data, $user);

            return $this->successResponse([
                'message' => '외주 반출 대상 삭제 성공',
            ]);
        } catch (Exception $e) {
            // 외주 반출 대상 삭제에 실패한 경우
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'carryout', $e->getCode());
        }
    }
}
