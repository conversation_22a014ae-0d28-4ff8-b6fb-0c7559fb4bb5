<?php

namespace App\Http\Controllers\WMS;

use App\Http\Controllers\Controller;
use App\Http\Requests\Pallet\DeliveryInspectionRequest;
use App\Http\Requests\Pallet\ProductRequest;
use App\Services\PalletQueryBuilderService;
use App\Services\PalletService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Throwable;

class PalletProductController extends Controller
{
    protected PalletQueryBuilderService $queryBuilderService;

    protected palletService $palletService;

    public function __construct(PalletQueryBuilderService $queryBuilderService, palletService $palletService)
    {
        $this->queryBuilderService = $queryBuilderService;
        $this->palletService = $palletService;
    }

    public function list(Request $request): JsonResponse
    {
        $data = [
            'pallet_id' => $request->input('pallet_id'),
            'checked_status' => $request->input('checked_status'),
            'search_type' => $request->input('search_type'),
            'keyword' => $request->input('keyword'),
            'pageSize' => $request->input('pageSize', 800),
        ];

        $query = $this->queryBuilderService->getPalletProductList($data);

        return $this->successResponse([
            'pallet' => $this->palletService->getPallet($data['pallet_id']),
            'items' => $query->get(),
        ]);
    }

    /**
     * 팔레트 상품 출고검수 처리
     * method: patch
     *
     * @throws Exception|Throwable
     */
    public function deliveryInspection(DeliveryInspectionRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $validatedData = $request->validated();

            $product = $this->palletService->patchDeliveryInspection($validatedData, $user);

            return $this->successResponse([
                'message' => '검수처리 완료',
                'product_name' => $product->name,
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'pallet', $e->getCode());
        }
    }

    /**
     * 점검 취소(다시 입고 검수완료 상태로 보냄)
     * method: patch
     *
     * @throws Exception|Throwable
     */
    public function excludeFromPallet(ProductRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $productIds = $request->validated('productIds');

            $this->palletService->excludeFromPallet($productIds, $user);

            return $this->successResponse([
                'message' => '점검 취소 완료',
                // 'response' => $request->all()
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'pallet', $e->getCode());
        }
    }
}
