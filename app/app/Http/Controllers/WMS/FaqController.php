<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\CacheHelper;
use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\FaqResource;
use App\Models\Faq;
use App\Models\User;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Throwable;

class FaqController extends Controller
{
    // 캐시 관련 상수
    private const CACHE_KEY_FAQ_INDEX = 'faq_index';

    private const CACHE_KEY_FAQ_SHOW = 'faq_show';

    private const CACHE_TTL_FAQ = 86400; // 24시간

    private const CACHE_TTL_FAQ_SHOW = 86400; // 24시간

    public function index(Request $request): JsonResponse
    {
        $keyword = $request->input('keyword');
        $pageSize = (int) $request->input('pageSize', 16);

        // 검색 조건을 포함한 캐시 키 생성
        $cacheKey = self::CACHE_KEY_FAQ_INDEX.':'.md5(serialize([
            'keyword' => $keyword,
            'pageSize' => $pageSize,
        ]));

        // 안전한 캐시 갱신 (캐시 스톰 방지)
        $cachedData = CacheHelper::rememberSafe($cacheKey, self::CACHE_TTL_FAQ, function () use ($keyword, $pageSize) {
            // 일반 공지사항
            $query = Faq::query();

            if ($keyword) {
                $query->where('subject', 'like', '%'.$keyword.'%')
                    ->orWhere('content', 'like', '%'.$keyword.'%');
            }
            $query->orderBy('id', 'desc');
            $paginatedResult = $query->paginate($pageSize)->withQueryString();

            return [
                'items' => FaqResource::collection($paginatedResult->items()),
                'pagination' => PaginationHelper::optimize($paginatedResult),
            ];
        }, ['faq']);

        return $this->successResponse($cachedData);
    }

    /**
     * (관리자만)FAQ 저장 (미들웨어로 처리중)
     *
     * @throws Throwable
     */
    public function store(Request $request): JsonResponse
    {
        $data = [
            'subject' => $request->input('subject'),
            'content' => $request->input('content'),
            'cate1' => $request->input('cate1'),
            'cate2' => $request->input('cate2'),
            'solution_code' => $request->input('solution_code'),
        ];

        try {
            DB::beginTransaction();

            Faq::create($data);

            DB::commit();

            CacheHelper::flushTags(['faq']);

            return $this->successResponse([
                'message' => '정상 처리 되었습니다',
            ]);
        } catch (Throwable $e) {
            DB::rollback();

            return $this->errorResponse([
                'message' => 'FAQ 등록에 실패하였습니다.',
                'log_message' => 'FAQ 등록 오류: ',
            ], $e, 'board', 500);
        }
    }

    public function show(int $id): JsonResponse
    {
        // 개별 FAQ 캐시 키 생성
        $cacheKey = self::CACHE_KEY_FAQ_SHOW."_{$id}";

        try {
            // 캐시에서 데이터 조회 (24시간 캐시)
            $cachedData = CacheHelper::rememberSafe($cacheKey, self::CACHE_TTL_FAQ_SHOW, function () use ($id) {
                $article = Faq::findOrFail($id);

                return [
                    'article' => $article,
                ];
            }, ['faq']);

            return $this->successResponse($cachedData);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => 'FAQ를 찾을 수 없습니다.',
                'log_message' => "[$id]번 FAQ를 찾을 수 없습니다.",
            ], $e, 'board', 404);
        }
    }

    /**
     * (관리자만)공지사항 수정<br>
     * 미들웨어로 처리중
     *
     * @throws Throwable
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $data = [
            'subject' => $request->input('subject'),
            'content' => $request->input('content'),
            'cate1' => $request->input('cate1'),
            'cate2' => $request->input('cate2'),
            'solution_code' => $request->input('solution_code'),
        ];

        try {
            DB::beginTransaction();

            Faq::where('id', $id)->update($data);

            DB::commit();

            CacheHelper::flushTags(['faq']);

            return $this->successResponse([
                'message' => '정상 처리 되었습니다',
            ]);
        } catch (Throwable $e) {
            DB::rollback();

            return $this->errorResponse([
                'message' => 'FQA 수정에 실패하였습니다.',
                'log_message' => 'FAQ 수정 오류: ',
            ], $e, 'board', 500);
        }
    }

    /**
     * (관리자만)공지사항 삭제<br>
     * 미들웨어로 처리중
     *
     * @throws Throwable
     */
    public function delete(int $id): JsonResponse
    {
        $user = Auth::user();

        // 관리자 권한 검사 (Super-Admin 또는 Admin만 삭제 가능)
        if ($user->role !== User::ROLE_SUPER_ADMIN
            && $user->role !== User::ROLE_ADMIN) {
            return $this->errorResponse([
                'message' => 'FAQ를 삭제할 권한이 없습니다.',
                'log_message' => "권한 없는 사용자({$user->id}:{$user->name})가 FAQ({$id}) 삭제를 시도했습니다.",
            ], null, 'board', 403);
        }

        try {
            DB::beginTransaction();

            Faq::destroy($id);

            DB::commit();

            CacheHelper::flushTags(['faq']);

            return $this->successResponse([
                'message' => 'FAQ가 성공적으로 삭제되었습니다.',
            ]);
        } catch (Throwable $e) {
            DB::rollback();

            return $this->errorResponse([
                'message' => 'FAQ 삭제에 실패하였습니다.',
                'log_message' => "FAQ({$id}) 삭제 중 오류가 발생했습니다: ",
            ], $e, 'board', 500);
        }
    }
}
