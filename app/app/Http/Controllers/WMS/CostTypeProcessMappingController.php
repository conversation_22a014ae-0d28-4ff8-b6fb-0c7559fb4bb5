<?php

namespace App\Http\Controllers\WMS;

use App\Http\Controllers\Controller;
use App\Models\RepairCostTypeProcessMapping;
use App\Services\RepairCostTypeProcessMappingService;
use App\Services\SimpleLogService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class CostTypeProcessMappingController extends Controller
{
    /**
     * 수리 프로세스 매핑 서비스
     */
    protected RepairCostTypeProcessMappingService $mappingService;

    public function __construct(RepairCostTypeProcessMappingService $mappingService)
    {
        $this->mappingService = $mappingService;
    }

    /**
     * 수리 프로세스 매핑 목록 조회
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = RepairCostTypeProcessMapping::with('repairProcess');

            // 수리 유형별 필터링
            if ($request->has('repair_type')) {
                $query->byRepairType($request->repair_type);
            }

            // 활성화 상태별 필터링
            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            // 프로세스 코드별 검색
            if ($request->has('process_code')) {
                $query->where('process_code', 'like', '%'.$request->process_code.'%');
            }

            $mappings = $query->orderBy('process_code')->get();

            // 수리 유형별로 그룹화
            $groupedMappings = $mappings->groupBy('repair_type')->map(function ($mappings, $type) {
                return [
                    'repair_type' => $type,
                    'repair_type_name' => RepairCostTypeProcessMapping::getRepairTypes()[$type] ?? $type,
                    'mappings' => $mappings->values(),
                ];
            })->values();

            return $this->successResponse([
                'mappings' => $mappings,
                'grouped_mappings' => $groupedMappings,
                'repair_types' => RepairCostTypeProcessMapping::getRepairTypes(),
                'statistics' => $this->mappingService->getMappingStatistics(),
            ], '수리 프로세스 매핑 목록을 성공적으로 조회했습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리 프로세스 매핑 목록 조회에 실패했습니다.',
                'log_message' => '수리 프로세스 매핑 목록 조회 실패: '.$e->getMessage(),
            ], $e, 'process_mapping', 500);
        }
    }

    /**
     * 새로운 수리 프로세스 매핑 생성
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'process_code' => [
                    'required',
                    'string',
                    'max:50',
                    'exists:repair_processes,code',
                    Rule::unique('repair_process_mappings', 'process_code'),
                ],
                'repair_type' => [
                    'required',
                    'string',
                    Rule::in(RepairCostTypeProcessMapping::getRepairTypeValues()),
                ],
                'is_active' => 'boolean',
            ]);

            // 1:1 매핑 제약 검증
            $existingMapping = RepairCostTypeProcessMapping::where('repair_type', $validated['repair_type'])
                ->where('is_active', true)
                ->first();

            if ($existingMapping) {
                return $this->errorResponse([
                    'message' => '해당 수리 유형에 이미 매핑된 프로세스가 있습니다.',
                    'error_code' => 'DUPLICATE_REPAIR_TYPE_MAPPING',
                    'data' => [
                        'existing_mapping' => $existingMapping->load('repairProcess'),
                    ],
                ], null, 'repair', 422);
            }

            $mapping = RepairCostTypeProcessMapping::create($validated);

            return $this->successResponse([
                'mapping' => $mapping->load('repairProcess'),
            ], '수리 프로세스 매핑이 성공적으로 생성되었습니다.', 201);
        } catch (ValidationException $e) {
            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors(),
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리 프로세스 매핑 생성에 실패했습니다.',
                'log_message' => '수리 프로세스 매핑 생성 실패: '.$e->getMessage(),
            ], $e, 'repair', 500);
        }
    }

    /**
     * 수리 프로세스 매핑 조회
     */
    public function show(int $id): JsonResponse
    {
        try {
            $mapping = RepairCostTypeProcessMapping::with('repairProcess')->findOrFail($id);

            return $this->successResponse([
                'mapping' => $mapping,
            ], '수리 프로세스 매핑을 성공적으로 조회했습니다.');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => '해당 수리 프로세스 매핑을 찾을 수 없습니다.',
                'error_code' => 'PROCESS_MAPPING_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리 프로세스 매핑 조회에 실패했습니다.',
                'log_message' => '수리 프로세스 매핑 조회 실패: '.$e->getMessage(),
            ], $e, 'repair', 500);
        }
    }

    /**
     * 수리 프로세스 매핑 수정
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $mapping = RepairCostTypeProcessMapping::findOrFail($id);

            $validated = $request->validate([
                'process_code' => [
                    'sometimes',
                    'string',
                    'max:50',
                    'exists:repair_processes,code',
                    Rule::unique('repair_process_mappings', 'process_code')->ignore($id),
                ],
                'repair_type' => [
                    'sometimes',
                    'string',
                    Rule::in(RepairCostTypeProcessMapping::getRepairTypeValues()),
                ],
                'is_active' => 'sometimes|boolean',
            ]);

            // 1:1 매핑 제약 검증 (수리 유형이 변경되는 경우)
            if (isset($validated['repair_type']) && $validated['repair_type'] !== $mapping->repair_type) {
                $existingMapping = RepairCostTypeProcessMapping::where('repair_type', $validated['repair_type'])
                    ->where('is_active', true)
                    ->where('id', '!=', $id)
                    ->first();

                if ($existingMapping) {
                    return $this->errorResponse([
                        'message' => '해당 수리 유형에 이미 매핑된 프로세스가 있습니다.',
                        'error_code' => 'DUPLICATE_REPAIR_TYPE_MAPPING',
                        'data' => [
                            'existing_mapping' => $existingMapping->load('repairProcess'),
                        ],
                    ], null, 'repair', 422);
                }
            }

            $mapping->update($validated);

            return $this->successResponse([
                'mapping' => $mapping->fresh()->load('repairProcess'),
            ], '수리 프로세스 매핑이 성공적으로 수정되었습니다.');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => '해당 수리 프로세스 매핑을 찾을 수 없습니다.',
                'error_code' => 'PROCESS_MAPPING_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (ValidationException $e) {
            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors(),
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리 프로세스 매핑 수정에 실패했습니다.',
                'log_message' => '수리 프로세스 매핑 수정 실패: '.$e->getMessage(),
                'error_code' => 'PROCESS_MAPPING_UPDATE_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 수리 프로세스 매핑 삭제
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $mapping = RepairCostTypeProcessMapping::findOrFail($id);
            $mapping->delete();

            return $this->successResponse([], '수리 프로세스 매핑이 성공적으로 삭제되었습니다.');
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => '해당 수리 프로세스 매핑을 찾을 수 없습니다.',
                'error_code' => 'PROCESS_MAPPING_NOT_FOUND',
            ], $e, 'repair', 404);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리 프로세스 매핑 삭제에 실패했습니다.',
                'log_message' => '수리 프로세스 매핑 삭제 실패: '.$e->getMessage(),
                'error_code' => 'PROCESS_MAPPING_DELETE_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 대량 매핑 설정
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'mappings' => 'required|array|min:1',
                'mappings.*.process_code' => [
                    'required',
                    'string',
                    'max:50',
                    'exists:repair_processes,code',
                ],
                'mappings.*.repair_type' => [
                    'required',
                    'string',
                    Rule::in(RepairCostTypeProcessMapping::getRepairTypeValues()),
                ],
                'mappings.*.is_active' => 'boolean',
            ]);

            DB::beginTransaction();

            $results = [];
            $errors = [];

            foreach ($validated['mappings'] as $index => $mappingData) {
                try {
                    // 기존 매핑 확인
                    $existingMapping = RepairCostTypeProcessMapping::where('process_code', $mappingData['process_code'])->first();

                    if ($existingMapping) {
                        // 기존 매핑 업데이트
                        $existingMapping->update($mappingData);
                        $results[] = [
                            'index' => $index,
                            'action' => 'updated',
                            'mapping' => $existingMapping->fresh()->load('repairProcess'),
                        ];
                    } else {
                        // 새 매핑 생성
                        $newMapping = RepairCostTypeProcessMapping::create($mappingData);
                        $results[] = [
                            'index' => $index,
                            'action' => 'created',
                            'mapping' => $newMapping->load('repairProcess'),
                        ];
                    }
                } catch (Exception $e) {
                    $errors[] = [
                        'index' => $index,
                        'process_code' => $mappingData['process_code'],
                        'error' => $e->getMessage(),
                    ];
                }
            }

            if (! empty($errors)) {
                DB::rollBack();

                return $this->errorResponse([
                    'message' => '일부 매핑 처리에 실패했습니다.',
                    'error_code' => 'BULK_UPDATE_PARTIAL_FAILED',
                    'data' => [
                        'results' => $results,
                        'errors' => $errors,
                    ],
                ], null, 'repair', 422);
            }

            DB::commit();

            return $this->successResponse([
                'results' => $results,
                'statistics' => $this->mappingService->getMappingStatistics(),
            ], '대량 매핑이 성공적으로 처리되었습니다.');
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors(),
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '대량 매핑 처리에 실패했습니다.',
                'log_message' => '대량 매핑 처리 실패: '.$e->getMessage(),
                'error_code' => 'BULK_UPDATE_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 매핑되지 않은 프로세스 목록 조회
     */
    public function getUnmappedProcesses(): JsonResponse
    {
        try {
            $unmappedProcesses = $this->mappingService->getUnmappedProcesses();

            return $this->successResponse([
                'unmapped_processes' => $unmappedProcesses,
                'count' => $unmappedProcesses->count(),
            ], '매핑되지 않은 프로세스 목록을 성공적으로 조회했습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '매핑되지 않은 프로세스 목록 조회에 실패했습니다.',
                'log_message' => '매핑되지 않은 프로세스 목록 조회 실패: '.$e->getMessage(),
            ], $e, 'repair', 500);
        }
    }

    /**
     * 매핑 통계 조회
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $statistics = $this->mappingService->getMappingStatistics();

            return $this->successResponse($statistics, '매핑 통계를 성공적으로 조회했습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '매핑 통계 조회에 실패했습니다.',
                'log_message' => '매핑 통계 조회 실패: '.$e->getMessage(),
            ], $e, 'repair', 500);
        }
    }

    /**
     * 매핑 충돌 감지 및 해결
     */
    public function detectConflicts(): JsonResponse
    {
        try {
            $conflicts = $this->mappingService->detectMappingConflicts();

            return $this->successResponse([
                'conflicts' => $conflicts,
                'conflict_count' => count($conflicts),
            ], '매핑 충돌을 성공적으로 감지했습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '매핑 충돌 감지에 실패했습니다.',
                'log_message' => '매핑 충돌 감지 실패: '.$e->getMessage(),
            ], $e, 'repair', 500);
        }
    }

    /**
     * 매핑 상태 일괄 변경
     */
    public function bulkStatusUpdate(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'mapping_ids' => 'required|array|min:1',
                'mapping_ids.*' => 'integer|exists:repair_process_mappings,id',
                'is_active' => 'required|boolean',
                'reason' => 'nullable|string|max:500',
            ]);

            DB::beginTransaction();

            $updatedCount = 0;
            $errors = [];

            foreach ($validated['mapping_ids'] as $mappingId) {
                try {
                    $mapping = RepairCostTypeProcessMapping::findOrFail($mappingId);
                    $mapping->update([
                        'is_active' => $validated['is_active'],
                    ]);
                    $updatedCount++;
                } catch (Exception $e) {
                    $errors[] = [
                        'mapping_id' => $mappingId,
                        'error' => $e->getMessage(),
                    ];
                }
            }

            if (! empty($errors)) {
                DB::rollBack();

                return $this->errorResponse([
                    'message' => '일부 매핑 상태 변경에 실패했습니다.',
                    'error_code' => 'BULK_STATUS_UPDATE_PARTIAL_FAILED',
                    'data' => [
                        'updated_count' => $updatedCount,
                        'errors' => $errors,
                    ],
                ], null, 'repair', 422);
            }

            DB::commit();

            // 상태 변경 이력 기록
            if ($validated['reason']) {
                SimpleLogService::info('repair', '매핑 상태 일괄 변경', [
                    'mapping_ids' => $validated['mapping_ids'],
                    'is_active' => $validated['is_active'],
                    'reason' => $validated['reason'],
                    'updated_count' => $updatedCount,
                ]);
            }

            return $this->successResponse([
                'updated_count' => $updatedCount,
                'statistics' => $this->mappingService->getMappingStatistics(),
            ], '매핑 상태가 성공적으로 변경되었습니다.');
        } catch (ValidationException $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors(),
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            DB::rollBack();

            return $this->errorResponse([
                'message' => '매핑 상태 일괄 변경에 실패했습니다.',
                'log_message' => '매핑 상태 일괄 변경 실패: '.$e->getMessage(),
                'error_code' => 'BULK_STATUS_UPDATE_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 매핑 검증 및 리포트 생성
     */
    public function generateValidationReport(): JsonResponse
    {
        try {
            $report = $this->mappingService->generateValidationReport();

            return $this->successResponse($report, '매핑 검증 리포트를 성공적으로 생성했습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '매핑 검증 리포트 생성에 실패했습니다.',
                'log_message' => '매핑 검증 리포트 생성 실패: '.$e->getMessage(),
                'error_code' => 'VALIDATION_REPORT_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 수리 유형별 매핑 현황 조회
     */
    public function getMappingsByRepairType(string $repairType): JsonResponse
    {
        try {
            $mappings = $this->mappingService->getMappingsByRepairType($repairType, false);

            return $this->successResponse([
                'repair_type' => $repairType,
                'repair_type_name' => RepairCostTypeProcessMapping::getRepairTypes()[$repairType] ?? $repairType,
                'mappings' => $mappings,
                'count' => $mappings->count(),
                'active_count' => $mappings->where('is_active', true)->count(),
            ], '수리 유형별 매핑 현황을 성공적으로 조회했습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '수리 유형별 매핑 현황 조회에 실패했습니다.',
                'log_message' => '수리 유형별 매핑 현황 조회 실패: '.$e->getMessage(),
                'error_code' => 'REPAIR_TYPE_MAPPINGS_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 매핑 이력 조회
     */
    public function getMappingHistory(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'mapping_id' => 'nullable|integer|exists:repair_process_mappings,id',
                'process_code' => 'nullable|string|max:50',
                'repair_type' => 'nullable|string',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'limit' => 'nullable|integer|min:1|max:100',
            ]);

            $history = $this->mappingService->getMappingHistory($validated);

            return $this->successResponse([
                'history' => $history,
                'count' => $history->count(),
                'filters' => $validated,
            ], '매핑 이력을 성공적으로 조회했습니다.');
        } catch (ValidationException $e) {
            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors(),
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '매핑 이력 조회에 실패했습니다.',
                'log_message' => '매핑 이력 조회 실패: '.$e->getMessage(),
                'error_code' => 'MAPPING_HISTORY_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 매핑 백업 및 복원
     */
    public function backupMappings(): JsonResponse
    {
        try {
            $backup = $this->mappingService->createMappingBackup();

            return $this->successResponse([
                'backup_id' => $backup['backup_id'],
                'backup_date' => $backup['backup_date'],
                'mapping_count' => $backup['mapping_count'],
            ], '매핑 백업을 성공적으로 생성했습니다.');
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '매핑 백업 생성에 실패했습니다.',
                'log_message' => '매핑 백업 생성 실패: '.$e->getMessage(),
                'error_code' => 'MAPPING_BACKUP_FAILED',
            ], $e, 'repair', 500);
        }
    }

    /**
     * 매핑 복원
     */
    public function restoreMappings(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'backup_id' => 'required|string',
                'confirm_restore' => 'required|boolean',
            ]);

            if (! $validated['confirm_restore']) {
                return $this->errorResponse([
                    'message' => '복원을 확인해주세요.',
                    'error_code' => 'RESTORE_NOT_CONFIRMED',
                ], null, 'repair', 422);
            }

            $restoreResult = $this->mappingService->restoreMappingBackup($validated['backup_id']);

            return $this->successResponse($restoreResult, '매핑이 성공적으로 복원되었습니다.');
        } catch (ValidationException $e) {
            return $this->errorResponse([
                'message' => '입력 데이터가 유효하지 않습니다.',
                'error_code' => 'VALIDATION_FAILED',
                'errors' => $e->errors(),
            ], $e, 'repair', 422);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '매핑 복원에 실패했습니다.',
                'log_message' => '매핑 복원 실패: '.$e->getMessage(),
                'error_code' => 'MAPPING_RESTORE_FAILED',
            ], $e, 'repair', 500);
        }
    }
}
