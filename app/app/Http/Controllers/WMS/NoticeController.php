<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\CacheHelper;
use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Http\Resources\BoardResource;
use App\Models\Board;
use App\Models\BoardStat;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Throwable;

class NoticeController extends Controller
{
    // 캐시 관련 상수
    private const CACHE_KEY_BOARD_INDEX = 'board_index';

    private const CACHE_KEY_BOARD_SHOW = 'board_show';

    private const CACHE_TTL_BOARD = 86400; // 24시간

    private const CACHE_TTL_BOARD_SHOW = 86400; // 24시간

    public function index(Request $request): JsonResponse
    {
        $beginAt = $request->input('beginAt');
        $endAt = $request->input('endAt');
        $searchType = $request->input('searchType', 'subject');
        $keyword = $request->input('keyword');
        $page = (int) $request->input('page', 1);
        $pageSize = (int) $request->input('pageSize', 15);

        // 검색 조건을 포함한 캐시 키 생성
        $cacheKey = self::CACHE_KEY_BOARD_INDEX.':'.md5(serialize([
            'beginAt' => $beginAt,
            'endAt' => $endAt,
            'searchType' => $searchType,
            'keyword' => $keyword,
            'page' => $page,
            'pageSize' => $pageSize,
        ]));

        // 안전한 캐시 갱신 (캐시 스톰 방지)
        $cachedData = CacheHelper::rememberSafe($cacheKey, self::CACHE_TTL_BOARD, function () use ($beginAt, $endAt, $searchType, $keyword, $page, $pageSize) {
            // 중요 공지사항
            $notices = null;
            if ($page === 1) {
                $notices = Board::withCount('comments')
                    ->where('status', Board::STATUS_REGISTERED)
                    ->where('f_notice', 'Y')
                    ->orderBy('created_at', 'desc')
                    ->get();
            }

            // 일반 공지사항
            $query = Board::withCount('comments')
                ->where('status', Board::STATUS_REGISTERED)
                ->orderBy('created_at', 'desc');

            if ($beginAt && $endAt) {
                $beginAt = $beginAt.' 00:00:00';
                $endAt = $endAt.' 23:59:59';
                $query->whereBetween('created_at', [$beginAt, $endAt]);
            }

            $searchTypeArray = ['subject', 'content', 'name'];
            if ($keyword && in_array($searchType, $searchTypeArray)) {
                $query->where($searchType, 'like', '%'.$keyword.'%');
            }

            $paginatedResult = $query->paginate($pageSize)->withQueryString();

            return [
                'notices' => $notices ? BoardResource::collection($notices) : null,
                'articles' => BoardResource::collection($paginatedResult->items()),
                'pagination' => PaginationHelper::optimize($paginatedResult),
            ];
        }, ['board']);

        return $this->successResponse($cachedData);
    }

    /**
     * (관리자만)공지사항 저장<br>
     * 미들웨어로 처리중
     *
     * @throws Throwable
     */
    public function store(Request $request): JsonResponse
    {
        $user = Auth::user();

        $data = [
            'type' => 'notice',
            'user_id' => $user->id,
            'name' => $user->name,
            'subject' => $request->input('subject'),
            'content' => $request->input('content'),
            'status' => Board::STATUS_REGISTERED,
            'f_notice' => $request->input('notice', 'N'),
            'f_show' => $request->input('show', 'Y'),
            'open_at' => $request->input('open_at', Carbon::now()),
            'hits' => 0,
            'user_agent' => $request->header('User-Agent'),
            'ip' => $request->getClientIp(),
        ];

        try {
            DB::beginTransaction();

            Board::create($data);

            DB::commit();

            CacheHelper::flushTags(['board']);

            return $this->successResponse([
                'message' => '정상 처리 되었습니다',
            ]);
        } catch (Throwable $e) {
            DB::rollback();

            return $this->errorResponse([
                'message' => '공지사항 등록에 실패하였습니다.',
            ], $e, 'board', 500);
        }
    }

    public function show(Request $request, int $id): JsonResponse
    {
        $user = Auth::user();

        // 개별 글 캐시 키 생성
        $cacheKey = self::CACHE_KEY_BOARD_SHOW."_{$id}";

        try {
            // 캐시에서 데이터 조회 (상세 조회는 더 긴 캐시 시간)
            $cachedData = CacheHelper::rememberSafe($cacheKey, self::CACHE_TTL_BOARD_SHOW, function () use ($id, $user, $request) {
                $article = Board::findOrFail($id);

                $stat = BoardStat::where('board_id', $id)
                    ->where('user_id', $user->id)
                    ->first();

                if ($stat === null) {
                    $stat = new BoardStat;

                    $stat->board_id = $id;
                    $stat->user_id = $user->id;
                    $stat->ip = $request->getClientIp();
                    $stat->user_agent = $request->header('User-Agent');

                    $stat->save();

                    $article->hits++;
                    $article->save();
                }

                return [
                    'article' => $article,
                ];
            }, ['board']);

            return $this->successResponse($cachedData);
        } catch (ModelNotFoundException $e) {
            return $this->errorResponse([
                'message' => '공지사항을 찾을 수 없습니다.',
                'log_message' => "[$id]번 공지사항을 찾을 수 없습니다.",
            ], $e, 'board', 404);
        }
    }

    /**
     * (관리자만)공지사항 수정<br>
     * 미들웨어로 처리중
     *
     * @throws Throwable
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $user = Auth::user();

        $data = [
            'type' => 'notice',
            'user_id' => $user->id,
            'name' => $user->name,
            'subject' => $request->input('subject'),
            'content' => $request->input('content'),
            'status' => Board::STATUS_REGISTERED,
            'open_at' => $request->input('open_at', Carbon::now()),
            'f_notice' => $request->input('notice', 'N'),
            'f_show' => $request->input('show', 'Y'),
            'user_agent' => $request->header('User-Agent'),
            'ip' => $request->getClientIp(),
        ];

        try {
            DB::beginTransaction();

            Board::where('id', $id)->update($data);

            DB::commit();

            CacheHelper::flushTags(['board']);

            return $this->successResponse([
                'message' => '정상 처리 되었습니다',
            ]);
        } catch (Throwable $e) {
            DB::rollback();

            return $this->errorResponse([
                'message' => '공지사항 수정에 실패하였습니다.',
            ], $e, 'board', 500);
        }
    }

    /**
     * (관리자만)공지사항 삭제<br>
     * 미들웨어로 처리중
     *
     * @throws Throwable
     */
    public function delete(int $id): JsonResponse
    {
        $user = Auth::user();

        // 관리자 권한 검사 (Super-Admin 또는 Admin만 삭제 가능)
        if ($user->role !== User::ROLE_SUPER_ADMIN
            && $user->role !== User::ROLE_ADMIN) {
            return $this->errorResponse([
                'message' => '공지사항을 삭제할 권한이 없습니다.',
                'log_message' => "권한 없는 사용자({$user->id}:{$user->name})가 공지사항({$id}) 삭제를 시도했습니다.",
            ], null, 'board', 403);
        }

        try {
            DB::beginTransaction();

            Board::destroy($id);

            DB::commit();

            CacheHelper::flushTags(['board']);

            return $this->successResponse([
                'message' => '공지사항이 성공적으로 삭제되었습니다.',
            ]);
        } catch (Throwable $e) {
            DB::rollback();

            return $this->errorResponse([
                'message' => '공지사항 삭제에 실패하였습니다.',
                'log_message' => "공지사항({$id}) 삭제 중 오류가 발생했습니다",
            ], $e, 'board', 500);
        }
    }
}
