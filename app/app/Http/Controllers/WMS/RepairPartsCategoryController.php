<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\CacheHelper;
use App\Http\Controllers\Controller;
use App\Models\RepairPartsCategory;
use Illuminate\Http\Request;

class RepairPartsCategoryController extends Controller
{
    private const CACHE_KEY = 'repair_parts_categories_parents_null';

    public function index()
    {
        $categories = RepairPartsCategory::with('children')
            ->whereNull('parent_id')
            ->orderBy('order_no')
            ->get();

        return $this->successResponse([
            'items' => $categories,
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:repair_parts_categories,id',
            'level' => 'nullable|integer',
            'order_no' => 'nullable|integer',
        ]);

        if ($validated['parent_id']) {
            $parentCategory = RepairPartsCategory::find($validated['parent_id']);
            $validated['level'] = $parentCategory->level + 1;
        }

        $category = RepairPartsCategory::create($validated);

        CacheHelper::forgetCache(self::CACHE_KEY);

        return $this->successResponse([
            'item' => $category,
        ], 201);
    }

    public function update(Request $request, RepairPartsCategory $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:repair_parts_categories,id',
            'level' => 'nullable|integer',
            'order_no' => 'nullable|integer',
        ]);

        $category->update($validated);

        CacheHelper::forgetCache(self::CACHE_KEY);

        return $this->successResponse([
            'item' => $category,
        ]);
    }

    public function destroy(RepairPartsCategory $category)
    {
        $category->delete();

        CacheHelper::forgetCache(self::CACHE_KEY);

        return $this->successResponse([], 204);
    }
}
