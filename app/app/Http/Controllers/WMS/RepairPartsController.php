<?php

namespace App\Http\Controllers\WMS;

use App\Helpers\PaginationHelper;
use App\Http\Controllers\Controller;
use App\Services\RepairPartsService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Throwable;

class RepairPartsController extends Controller
{
    private RepairPartsService $repairPartsService;

    public function __construct(RepairPartsService $repairPartsService)
    {
        $this->repairPartsService = $repairPartsService;
    }

    public function partsList(Request $request): JsonResponse
    {
        $data = [
            'keyword' => $request->input('keyword'),
            'pageSize' => $request->input('pageSize', 16),
        ];

        $items = $this->repairPartsService->getPartsList($data);
        $paginatedResult = $items->paginate($data['pageSize'])->withQueryString();

        return $this->successResponse([
            'items' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * @throws Throwable
     */
    public function storeParts(Request $request): JsonResponse
    {
        $data = $request->all();

        try {
            $this->repairPartsService->storeParts($data);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '구성품 저장에 실패하였습니다.',
                'log_message' => '구성품 저장 오류: '.$e->getMessage(),
            ], $e, 'repair', 500);
        }
    }

    /**
     * @throws Throwable
     */
    public function updateParts(int $id, Request $request): JsonResponse
    {
        $data = $request->all();
        $data['id'] = $id;

        try {
            $this->repairPartsService->updateParts($data);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '구성품 수정에 실패하였습니다.',
                'log_message' => '구성품 수정 오류: '.$e->getMessage(),
            ], $e, 'repair', 500);
        }
    }

    /**
     * @throws Throwable
     */
    public function destroyParts(int $id): JsonResponse
    {
        try {
            $this->repairPartsService->destroyParts($id);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '구성품 삭제에 실패하였습니다.',
                'log_message' => '구성품 삭제 오류: '.$e->getMessage(),
            ], $e, 'repair', 500);
        }
    }
}
