<?php

namespace App\Http\Controllers;

use App\Helpers\PaginationHelper;
use App\Models\Product;
use App\Models\ProductLog;
use App\Models\WarehousePallet;
use App\Models\WarehousePalletItem;
use App\Models\WorkStatus;
use App\Services\LoadedService;
use App\Services\ProductService;
use App\Services\WorkStatusService;
use App\Traits\Product\SearchTrait as ProductSearchTrait;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Throwable;

class WarehousePalletItemController extends Controller
{
    use ProductSearchTrait;

    protected ProductService $productService;

    protected LoadedService $loadedService;

    protected WorkStatusService $workStatusService;

    public function __construct(
        ProductService $productService,
        LoadedService $loadedService,
        WorkStatusService $workStatusService
    ) {
        $this->productService = $productService;
        $this->loadedService = $loadedService;
        $this->workStatusService = $workStatusService;
    }

    public function list(Request $request)
    {
        $data = [
            'pallet_id' => $request->input('pallet_id'),
            'keyword' => $request->input('keyword'),
            'pageSize' => $request->input('pageSize', 1000),
        ];

        $pallet = WarehousePallet::with([
            'creator:id,name',
            'shippedInBy:id,name',
            'shippedOutBy:id,name',
        ])->where('id', $data['pallet_id'])
            ->first();

        $query = WarehousePalletItem::with(['product'])
            ->where('warehouse_pallet_id', $data['pallet_id']);

        if ($data['keyword']) {
            $query->whereHas('product', function ($subQuery) use ($data) {
                $subQuery->where('qaid', $data['keyword'])
                    ->orWhere('barcode', $data['keyword'])
                    ->orWhere('name', 'like', '%'.$data['keyword'].'%');
            });
        }

        $paginatedResult = $query->paginate($data['pageSize'])->withQueryString();

        return $this->successResponse([
            'pallet' => $pallet,
            'items' => $paginatedResult->items(),
            'pagination' => PaginationHelper::optimize($paginatedResult),
        ]);
    }

    /**
     * 팔레트 적재시 적재 가능한 상품인지 확인
     *
     * @throws Exception
     */
    public function checkProduct(string $qaid): JsonResponse
    {
        try {
            $qaid = mb_strtoupper(trim($qaid));
            $this->existsProductByQaid($qaid);

            $product = $this->loadedService->getLoadedProductCheck($qaid);
            if ($product === null) {
                throw new Exception("등록된 상품[$qaid]을 확인 할 수 없습니다. 상품을 검수해 주시기 바랍니다.");
            }

            if ($product->duplicated === 'Y') {
                throw new Exception('같은 QAID='.$qaid.'로 중복된 상품(보류처리)이 있습니다.');
            }

            $item = WarehousePalletItem::where('product_id', $product->id)
                ->where('status', WarehousePalletItem::STATUS_STORED)
                ->first();
            // 이미 입고 팔레트에 등록된 qaid인 경우
            if ($item !== null) {
                $pallet = $item->warehousePallet->first();
                if ($pallet) {
                    throw new Exception('조회된 상품(QAID='.$qaid.")은 이미 팔레트[$pallet->pallet_number]에 등록된 상품입니다.");
                }
            }

            if ($product->checked_status == Product::CHECKED_STATUS_UNDELIVERED ||
                ($product->checked_status == Product::CHECKED_STATUS_CHECKED && $product->checked_at == null)) {
                throw new Exception('조회된 상품(QAID='.$qaid.')은 미입고 상품으로 입고검수가 불가합니다.');
            }

            if ($product->status == Product::STATUS_HELD) {
                throw new Exception('조회된 상품(QAID='.$qaid.')은 보류처리되어 입고검수가 불가합니다.');
            }

            if (in_array($product->status, [
                Product::STATUS_CARRIED_OUT,
                Product::STATUS_CARRIED_OUT_WAITING,
                Product::STATUS_CARRIED_OUT_REPAIRED,
            ])) {
                throw new Exception('조회된 상품(QAID='.$qaid.')은 외주반출(외부수리의뢰)처리되어 입고검수가 불가합니다.');
            }

            $product->req_at = empty($product->req_at) ? '' : Carbon::parse($product->req_at)->format('Y년 n월 j일');

            return $this->successResponse([
                'product' => $product,
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => $e->getMessage(),
            ], $e, 'warehouse', 500);
        }
    }

    /**
     * 팔레트에 상품 저장
     * - 팔레트의 상태가 사용 가능일 경우 적재중으로 변경
     *
     * @throws Exception|Throwable
     */
    public function storeItem(Request $request): JsonResponse
    {
        try {
            $productId = $request->input('product_id');
            $user = auth()->user();

            $data = [
                'warehouse_pallet_id' => $request->input('pallet_id'),
                'product_id' => $request->input('product_id'),
                'quantity' => 1,
                'status' => WarehousePalletItem::STATUS_STORED,
                'created_by' => $user->id,
                'description' => $request->input('memo'),
            ];

            $palletItem = WarehousePalletItem::create($data);
            $pallet = $palletItem->warehousePallet->first();

            $statusIds = $this->workStatusService->getIds([
                WorkStatus::LINK_WAREHOUSE_PALLET_STATUS_CHANGE,
                WorkStatus::LINK_WAREHOUSE_PALLET_ITEM_CREATE,
            ]);
            $productLog = [];
            $now = now();
            if ($pallet->status === WarehousePallet::STATUS_AVAILABLE) {
                $pallet->status = WarehousePallet::STATUS_IN_USE;
                $pallet->save();

                $palletStatusName = WarehousePallet::$STATUS_NAME[$pallet->status];
                $productLog[] = [
                    'product_id' => null,
                    'model_type' => 'App\Models\WarehousePallet',
                    'model_id' => $pallet->id,
                    'work_status_id' => $statusIds[WorkStatus::LINK_WAREHOUSE_PALLET_STATUS_CHANGE],
                    'user_id' => $user->id,
                    'memo' => "입고 팔레트 [$pallet->pallet_number] 상태 변경: $palletStatusName (변경한 직원: $user->name)",
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            $productLog[] = [
                'product_id' => $productId,
                'model_type' => 'App\Models\WarehousePalletItem',
                'model_id' => $palletItem->id,
                'work_status_id' => $statusIds[WorkStatus::LINK_WAREHOUSE_PALLET_ITEM_CREATE],
                'user_id' => $user->id,
                'memo' => "입고 팔레트 [$pallet->pallet_number] 에 저장(등록한 직원: $user->name)",
                'created_at' => $now,
                'updated_at' => $now,
            ];

            ProductLog::insert($productLog);

            return $this->successResponse([
                'message' => '상품 저장 완료',
            ]);
        } catch (Exception $e) {
            return $this->errorResponse([
                'message' => '입고 팔레트에 상품을 적재하는데 실패하였습니다.',
            ], $e, 'warehouse', 500);
        }
    }
}
