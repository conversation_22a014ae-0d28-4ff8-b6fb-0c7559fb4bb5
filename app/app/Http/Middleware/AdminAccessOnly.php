<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminAccessOnly
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 사용자가 로그인을 한 후엔 유효한 사용자 정보가 있어야 합니다.
        // 이 점을 확인해보세요. 만약 없다면 로그인 페이지로 리다이렉트하거나
        // 403 Forbidden 응답을 반환해야 합니다.
        $user = $request->user();

        $allowedRoles = ['Super-Admin', 'Admin'];

        if (! in_array($user->role, $allowedRoles)) {
            throw new \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException('Access denied');
        }

        // 사용자 상태 확인 (활성 상태인지)
        if ($user->status !== \App\Models\User::MEMBER_STATUS_ACTIVE) {
            throw new \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException('Access denied');
        }

        // 삭제된 사용자 확인
        if ($user->trashed()) {
            throw new \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException('Access denied');
        }

        return $next($request);
    }
}
