<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

/**
 * 에러 처리 미들웨어
 *
 * API 요청에 대한 전역 에러 처리를 담당합니다.
 * 표준화된 에러 응답 형식을 보장합니다.
 */
class ErrorHandlingMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $response = $next($request);

            // JSON 응답이 아닌 경우 그대로 반환
            if (! $request->expectsJson()) {
                return $response;
            }

            // 성공 응답인 경우 표준화된 형식으로 변환
            if ($response->isSuccessful() && $response instanceof JsonResponse) {
                return $this->formatSuccessResponse($response);
            }

            return $response;

        } catch (Throwable $exception) {
            // 예외가 발생한 경우 Handler에서 처리하도록 다시 던짐
            throw $exception;
        }
    }

    /**
     * 성공 응답을 표준화된 형식으로 변환합니다.
     */
    protected function formatSuccessResponse(JsonResponse $response): JsonResponse
    {
        $originalData = $response->getData(true);

        // 이미 표준화된 형식인 경우 그대로 반환
        if (isset($originalData['success'])) {
            return $response;
        }

        // 표준화된 성공 응답 형식으로 변환
        $standardizedData = [
            'success' => true,
            'message' => $this->getSuccessMessage($response->getStatusCode()),
            'data' => $originalData,
        ];

        return new JsonResponse($standardizedData, $response->getStatusCode());
    }

    /**
     * HTTP 상태 코드에 따른 성공 메시지를 반환합니다.
     */
    protected function getSuccessMessage(int $statusCode): string
    {
        return match ($statusCode) {
            200 => '요청이 성공적으로 처리되었습니다.',
            201 => '리소스가 성공적으로 생성되었습니다.',
            202 => '요청이 접수되었습니다.',
            204 => '요청이 성공적으로 처리되었습니다.',
            default => '요청이 성공적으로 처리되었습니다.'
        };
    }
}
