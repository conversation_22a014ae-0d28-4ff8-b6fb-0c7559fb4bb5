<?php

namespace App\Http\Requests\Monitor;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class IndexRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['nullable', 'string'],
            'name_hash' => ['nullable', 'string'],
            'brand' => ['nullable', 'in:brand,general'],
            'unit' => ['nullable', 'in:INCH,CM'],
            'min_size' => ['nullable', 'numeric'],
            'max_size' => ['nullable', 'numeric'],
            // 정렬
            'sortBy' => ['nullable', 'in:name,created_at,updated_at'],
            'sortDir' => ['nullable', 'in:asc,desc'],
            'page' => ['nullable', 'integer', 'min:1'],
            'pageSize' => ['nullable', 'integer', 'min:1', 'max:200'],
        ];
    }

    /**
     * 유효성 검사 실패시 일관된 포맷으로 응답
     */
    protected function failedValidation(Validator $validator)
    {
        throw new ValidationException($validator, response()->json([
            'success' => false,
            'message' => '입력 데이터가 유효하지 않습니다.',
            'error_code' => 'VALIDATION_FAILED',
            'errors' => $validator->errors(),
        ], 422));
    }
}
