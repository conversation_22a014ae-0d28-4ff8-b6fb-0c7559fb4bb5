<?php

namespace App\Http\Requests\Attendance;

use Illuminate\Foundation\Http\FormRequest;
use Symfony\Component\HttpFoundation\Response;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = auth()->user();

        return $user && ($user->role == 'Admin' || $user->role == 'Super-Admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'excel' => [
                'bail',
                'required',
                'file',
                'mimes:xlsx,xls',
                'max:2048', // 2MB
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'excel.required' => '업로드할 엑셀 파일이 없습니다.',
            'excel.file' => '업로드된 파일이 유효하지 않습니다.',
            'excel.mimes' => '업로드된 파일은 엑셀 파일이 아닙니다. (.xlsx, .xls 파일만 허용)',
            'excel.max' => '파일 크기는 2MB를 초과할 수 없습니다.',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        throw new \Illuminate\Validation\ValidationException($validator, response()->json([
            'message' => '파일 업로드 검증에 실패했습니다.',
            'errors' => $validator->errors(),
        ], Response::HTTP_BAD_REQUEST));
    }
}
