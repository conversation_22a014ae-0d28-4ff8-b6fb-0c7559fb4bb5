<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WorkCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // 권한 체크는 미들웨어에서 처리
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $categoryId = $this->route('category'); // 라우트 파라미터에서 ID 가져오기

        return [
            'code' => [
                'required',
                'string',
                'max:50',
                Rule::unique('work_categories', 'code')->ignore($categoryId),
            ],
            'name' => 'required|string|max:100',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'code.required' => '카테고리 코드는 필수입니다.',
            'code.string' => '카테고리 코드는 문자열이어야 합니다.',
            'code.max' => '카테고리 코드는 최대 50자까지 가능합니다.',
            'code.unique' => '이미 사용 중인 카테고리 코드입니다.',
            'name.required' => '카테고리 이름은 필수입니다.',
            'name.string' => '카테고리 이름은 문자열이어야 합니다.',
            'name.max' => '카테고리 이름은 최대 100자까지 가능합니다.',
            'description.string' => '카테고리 설명은 문자열이어야 합니다.',
            'is_active.boolean' => '활성 상태는 true 또는 false여야 합니다.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'code' => '카테고리 코드',
            'name' => '카테고리 이름',
            'description' => '카테고리 설명',
            'is_active' => '활성 상태',
        ];
    }
}
