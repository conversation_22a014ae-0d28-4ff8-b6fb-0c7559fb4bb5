<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class NotificationHistoryRequest extends FormRequest
{
    /**
     * 사용자가 이 요청을 수행할 권한이 있는지 확인
     */
    public function authorize(): bool
    {
        // 로그인된 사용자가 관리자인지 확인
        return auth()->check() && auth()->user()->isAdmin();
    }

    /**
     * 요청에 적용할 검증 규칙
     */
    public function rules(): array
    {
        return [
            'page' => 'nullable|integer|min:1',
            'pageSize' => 'nullable|integer|min:1|max:320',
            'search' => 'nullable|string|max:255',
            'priority' => 'nullable|string|in:low,normal,high,urgent',
            'status' => 'nullable|string|in:draft,sent,cancelled',
            'target_type' => 'nullable|string|in:all,group,individual',
            'date_from' => 'nullable|date|date_format:Y-m-d',
            'date_to' => 'nullable|date|date_format:Y-m-d|after_or_equal:date_from',
        ];
    }

    /**
     * 검증 실패 시 사용자 정의 에러 메시지
     */
    public function messages(): array
    {
        return [
            'page.integer' => '페이지 번호는 정수여야 합니다.',
            'page.min' => '페이지 번호는 1 이상이어야 합니다.',
            'pageSize.integer' => '페이지 크기는 정수여야 합니다.',
            'pageSize.min' => '페이지 크기는 1 이상이어야 합니다.',
            'pageSize.max' => '페이지 크기는 100 이하여야 합니다.',
            'search.string' => '검색어는 문자열이어야 합니다.',
            'search.max' => '검색어는 255자 이하여야 합니다.',
            'priority.in' => '우선순위는 low, normal, high, urgent 중 하나여야 합니다.',
            'status.in' => '상태는 draft, sent, cancelled 중 하나여야 합니다.',
            'target_type.in' => '대상 유형은 all, group, individual 중 하나여야 합니다.',
            'date_from.date' => '시작 날짜 형식이 올바르지 않습니다. (YYYY-MM-DD 형식으로 입력해주세요)',
            'date_from.date_format' => '시작 날짜는 YYYY-MM-DD 형식으로 입력해주세요.',
            'date_to.date' => '종료 날짜 형식이 올바르지 않습니다. (YYYY-MM-DD 형식으로 입력해주세요)',
            'date_to.date_format' => '종료 날짜는 YYYY-MM-DD 형식으로 입력해주세요.',
            'date_to.after_or_equal' => '종료 날짜는 시작 날짜보다 늦거나 같아야 합니다.',
        ];
    }

    /**
     * 검증 실패 시 사용자 정의 속성명
     */
    public function attributes(): array
    {
        return [
            'page' => '페이지 번호',
            'pageSize' => '페이지 크기',
            'search' => '검색어',
            'priority' => '우선순위',
            'status' => '상태',
            'target_type' => '대상 유형',
            'date_from' => '시작 날짜',
            'date_to' => '종료 날짜',
        ];
    }
}
