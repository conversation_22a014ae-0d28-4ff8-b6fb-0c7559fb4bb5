<?php

namespace App\Http\Requests\Carryout;

use Illuminate\Foundation\Http\FormRequest;

class ProductImportRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    // 검증 규칙
    public function rules(): array
    {
        return [
            'qaid' => [
                'required',
                'string',
                'max:50',
                'regex:/^[Q]/', // Q 시작하는지 확인
            ],
        ];
    }

    // 먼저 실행
    public function prepareForValidation(): void
    {
        $this->merge([
            'qaid' => mb_strtoupper(trim($this->qaid ?? '')),
        ]);
    }

    // 검증 실패 시 출력할 메시지
    public function messages(): array
    {
        return [
            'qaid.required' => 'QAID는 필수 입력 항목입니다.',
            'qaid.string' => 'QAID는 문자열이어야 합니다.',
            'qaid.max' => 'QAID는 50자를 초과할 수 없습니다.',
            'qaid.regex' => 'QAID는 반드시 [Q]로 시작해야 합니다.',
        ];
    }
}
