<?php

namespace App\Http\Requests\Carryout;

use Illuminate\Foundation\Http\FormRequest;

class ProductDestroyRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'ids' => 'required|array|min:1',
            'ids.*' => 'integer|exists:carryout_products,id',
        ];
    }

    public function messages(): array
    {
        return [
            'ids.required' => '삭제할 외주 반출 상품 ID 목록은 필수 입력 항목입니다.',
            'ids.array' => '외주 반출 상품 ID 목록은 배열이어야 합니다.',
            'ids.min' => '최소 1개 이상의 외주 반출 상품을 선택해야 합니다.',
            'ids.*.integer' => '외주 반출 상품 ID는 숫자여야 합니다.',
            'ids.*.exists' => '존재하지 않는 외주 반출 상품입니다.',
        ];
    }
}
