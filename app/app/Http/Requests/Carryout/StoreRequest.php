<?php

namespace App\Http\Requests\Carryout;

use App\Models\Carryout;
use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // 권한 검증은 별도 미들웨어에서 처리
    }

    /**
     * 유효성 검사 규칙
     *
     * 참고 : 상태가 NULL 인 경우, Carryout::STATUS_CARRIED_OUT (10)을 기본값을 제공
     */
    public function rules(): array
    {
        return [
            'id' => 'nullable|integer|exists:carryouts,id',
            'carryout_at' => 'required|date',
            'status' => 'nullable|integer|in:'.Carryout::STATUS_CARRIED_OUT.','.Carryout::STATUS_CARRIED_IN.','.Carryout::STATUS_CANCELED,
            'memo' => 'nullable|string|max:1000',
        ];
    }

    /**
     * Get the validation messages that apply to the request.
     */
    public function messages(): array
    {
        return [
            'carryout_at.required' => '반출 날짜는 필수 입력 항목입니다.',
            'carryout_at.date' => '올바른 날짜 형식을 입력해주세요.',
            'status.integer' => '상태 값은 숫자여야 합니다.',
            'status.in' => '올바른 상태 값을 선택해주세요. ('.
                Carryout::STATUS_CARRIED_OUT.': 반출, '.
                Carryout::STATUS_CARRIED_IN.': 반입, '.
                Carryout::STATUS_CANCELED.': 취소)',
            'memo.string' => '메모는 문자열이어야 합니다.',
            'memo.max' => '메모는 1000자를 초과할 수 없습니다.',
            'id.integer' => 'ID는 숫자여야 합니다.',
            'id.exists' => '존재하지 않는 외주 반출 정보입니다.',
        ];
    }
}
