<?php

namespace App\Http\Requests\Carryout;

use Illuminate\Foundation\Http\FormRequest;

class ProductExportRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'carryoutId' => 'required|integer|exists:carryouts,id',
            'qaid' => [
                'required',
                'string',
                'max:50',
                'regex:/^[Q]/', // Q 시작하는지 확인
            ],
        ];
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'qaid' => mb_strtoupper(trim($this->qaid ?? '')),
        ]);
    }

    public function messages(): array
    {
        return [
            'carryoutId.required' => '외주 반출 ID는 필수 입력 항목입니다.',
            'carryoutId.integer' => '외주 반출 ID는 숫자여야 합니다.',
            'carryoutId.exists' => '존재하지 않는 외주 반출 정보입니다.',
            'qaid.required' => 'QAID는 필수 입력 항목입니다.',
            'qaid.string' => 'QAID는 문자열이어야 합니다.',
            'qaid.max' => 'QAID는 50자를 초과할 수 없습니다.',
            'qaid.regex' => 'QAID는 반드시 [Q]로 시작해야 합니다.',
        ];
    }
}
