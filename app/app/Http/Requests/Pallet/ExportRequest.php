<?php

namespace App\Http\Requests\Pallet;

use Illuminate\Foundation\Http\FormRequest;

class ExportRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // 권한 체크가 필요하면 적절히 구현
    }

    public function rules(): array
    {
        return [
            'palletIds' => ['required', 'array', 'min:1'],
            'palletIds.*' => ['integer', 'distinct', 'exists:pallets,id'],
        ];
    }

    public function messages(): array
    {
        return [
            'palletIds.required' => '팔레트 ID 목록이 필요합니다.',
            'palletIds.array' => '팔레트 ID는 배열이어야 합니다.',
            'palletIds.min' => '최소 1개 이상의 팔레트 ID가 필요합니다.',
            'palletIds.*.integer' => '각 팔레트 ID는 숫자여야 합니다.',
            'palletIds.*.distinct' => '중복된 팔레트 ID가 포함되어 있습니다.',
            'palletIds.*.exists' => '존재하지 않는 팔레트가 포함되어 있습니다.',
        ];
    }

    /**
     * 서비스가 기대하는 형태([['palletId'=>...], ...])로 변환하여 돌려줍니다.
     */
    public function rows(): array
    {
        $ids = $this->validated('palletIds', []);

        return array_map(fn (int $id) => ['palletId' => $id], $ids);
    }
}
