<?php

namespace App\Http\Requests\Pallet;

use Illuminate\Foundation\Http\FormRequest;

class ProductRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // 권한 체크가 필요하면 적절히 구현
    }

    public function rules(): array
    {
        return [
            'productIds' => ['required', 'array', 'min:1'],
            'productIds.*' => ['integer', 'distinct', 'exists:pallet_products,product_id'],
        ];
    }

    public function messages(): array
    {
        return [
            'productIds.required' => '출고 상품 ID 목록이 필요합니다.',
            'productIds.array' => '출고 상품 ID는 배열이어야 합니다.',
            'productIds.min' => '최소 1개 이상의 출고 상품 ID가 필요합니다.',
            'productIds.*.integer' => '각 출고 상품 ID는 숫자여야 합니다.',
            'productIds.*.distinct' => '중복된 출고 상품 ID가 포함되어 있습니다.',
            'productIds.*.exists' => '존재하지 않는 출고 상품이 포함되어 있습니다.',
        ];
    }
}
