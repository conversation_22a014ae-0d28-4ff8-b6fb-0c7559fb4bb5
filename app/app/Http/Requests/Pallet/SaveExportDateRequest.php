<?php

namespace App\Http\Requests\Pallet;

use Illuminate\Foundation\Http\FormRequest;

class SaveExportDateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // 권한 체크가 필요하면 적절히 구현
    }

    public function rules(): array
    {
        // 루트가 배열이며, 각 원소에 palletId, exportDate가 있어야 함
        return [
            '*.palletId' => ['required', 'integer', 'exists:pallets,id'],     // 팔레트 존재 검증
            '*.exportDate' => ['required', 'date_format:Y-m-d'],                // YYYY-MM-DD 형식
        ];
    }

    public function messages(): array
    {
        return [
            '*.palletId.required' => '팔레트 ID가 필요합니다.',
            '*.palletId.integer' => '팔레트 ID는 숫자여야 합니다.',
            '*.palletId.exists' => '존재하지 않는 팔레트입니다.',
            '*.exportDate.required' => '출고일이 필요합니다.',
            '*.exportDate.date_format' => '출고일 형식은 YYYY-MM-DD 이어야 합니다.',
        ];
    }
}
