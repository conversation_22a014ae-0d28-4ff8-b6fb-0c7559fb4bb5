<?php

namespace App\Http\Requests\Pallet;

use Illuminate\Foundation\Http\FormRequest;

class CloseOpenRequest extends FormRequest
{
    public function authorize(): bool
    {
        // 인증된 사용자만 접근하도록 추가 로직이 필요하면 수정하세요.
        return true;
    }

    public function rules(): array
    {
        return [
            'pallet_id' => ['required', 'integer'],
        ];
    }

    public function messages(): array
    {
        return [
            'pallet_id.required' => '팔레트 ID는 필수입니다.',
            'pallet_id.integer' => '팔레트 ID 형식이 올바르지 않습니다.',
        ];
    }
}
