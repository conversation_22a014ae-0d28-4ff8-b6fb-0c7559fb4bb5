<?php

namespace App\Http\Requests\NotificationTemplate;

use Illuminate\Contracts\Validation\ValidationRule;

/**
 * 알림 템플릿 사용 횟수 증가 요청 검증
 */
class IncrementUsageRequest extends BaseTemplateRequest
{
    /**
     * 사용자 권한 확인
     */
    public function authorize(): bool
    {
        $user = auth()->user();

        return $user && in_array($user->role, ['Super-Admin', 'Admin', 'User']);
    }

    /**
     * 유효성 검증 규칙
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'increment_by' => 'sometimes|integer|min:1|max:10',
        ];
    }

    /**
     * 기본값 설정
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'increment_by' => $this->input('increment_by', 1),
        ]);
    }

    /**
     * 유효성 검증 오류 메시지
     */
    public function messages(): array
    {
        return array_merge($this->getCommonMessages(), [
            'increment_by.max' => '증가값은 10 이하여야 합니다.',
        ]);
    }
}
