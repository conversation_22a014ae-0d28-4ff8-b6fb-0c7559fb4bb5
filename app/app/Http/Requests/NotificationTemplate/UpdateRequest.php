<?php

namespace App\Http\Requests\NotificationTemplate;

use App\Rules\NotificationTemplate\ValidTemplateContentRule;
use App\Rules\NotificationTemplate\ValidTemplateNameRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Validation\Rule;

/**
 * 알림 템플릿 수정 요청 검증
 */
class UpdateRequest extends BaseTemplateRequest
{
    /**
     * 사용자 권한 확인
     */
    public function authorize(): bool
    {
        $user = auth()->user();

        return $user && in_array($user->role, ['Super-Admin', 'Admin']);
    }

    /**
     * 유효성 검증 규칙
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        $templateId = $this->route('id');

        return [
            'name' => [
                'sometimes',
                'required',
                'string',
                'min:1',
                'max:50',
                'regex:/^[a-zA-Z0-9가-힣\s\-_]+$/', // 한글, 영문, 숫자, 공백, 하이픈, 언더스코어만 허용
                Rule::unique('notification_templates', 'name')->ignore($templateId),
                new ValidTemplateNameRule,
            ],
            'title' => [
                'sometimes',
                'required',
                'string',
                'min:1',
                'max:200',
            ],
            'content' => [
                'sometimes',
                'required',
                'string',
                'min:1',
                'max:5000',
                new ValidTemplateContentRule,
            ],
            'priority' => [
                'sometimes',
                'string',
                'in:low,normal,high,urgent',
            ],
        ];
    }

    /**
     * 유효성 검증 오류 메시지
     */
    public function messages(): array
    {
        return array_merge($this->getCommonMessages(), [
            'name.regex' => '템플릿명은 한글, 영문, 숫자, 공백, 하이픈(-), 언더스코어(_)만 사용할 수 있습니다.',
            'priority.in' => '우선순위는 low, normal, high, urgent 중 하나여야 합니다.',
        ]);
    }
}
