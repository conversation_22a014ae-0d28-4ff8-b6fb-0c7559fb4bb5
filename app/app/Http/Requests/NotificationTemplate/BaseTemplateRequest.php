<?php

namespace App\Http\Requests\NotificationTemplate;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\JsonResponse;

/**
 * 알림 템플릿 요청 기본 클래스
 *
 * 공통 유효성 검증 로직과 에러 처리를 담당합니다.
 */
abstract class BaseTemplateRequest extends FormRequest
{
    /**
     * 유효성 검증 실패 시 처리
     *
     * @throws HttpResponseException
     */
    protected function failedValidation(Validator $validator): void
    {
        $errors = $validator->errors()->toArray();

        // 첫 번째 에러 메시지를 주 메시지로 사용
        $firstError = $validator->errors()->first();

        $response = new JsonResponse([
            'success' => false,
            'message' => $firstError ?: '입력 데이터가 올바르지 않습니다.',
            'errors' => $errors,
            'error_code' => 'VALIDATION_ERROR',
        ], 422);

        throw new HttpResponseException($response);
    }

    /**
     * 권한 검증 실패 시 처리
     */
    protected function failedAuthorization(): void
    {
        $response = new JsonResponse([
            'success' => false,
            'message' => '이 작업을 수행할 권한이 없습니다.',
            'error_code' => 'PERMISSION_DENIED',
        ], 403);

        throw new HttpResponseException($response);
    }

    /**
     * 공통 데이터 전처리
     */
    protected function prepareForValidation(): void
    {
        // 문자열 필드의 앞뒤 공백 제거
        $stringFields = ['name', 'title', 'content', 'priority'];

        foreach ($stringFields as $field) {
            if ($this->has($field) && is_string($this->input($field))) {
                $this->merge([
                    $field => trim($this->input($field)),
                ]);
            }
        }

        // 빈 문자열을 null로 변환 (선택적 필드의 경우)
        $optionalFields = ['priority'];

        foreach ($optionalFields as $field) {
            if ($this->has($field) && $this->input($field) === '') {
                $this->merge([
                    $field => null,
                ]);
            }
        }
    }

    /**
     * 공통 유효성 검증 규칙
     */
    protected function getCommonRules(): array
    {
        return [
            // 공통적으로 사용되는 규칙들을 여기에 정의
        ];
    }

    /**
     * 공통 에러 메시지
     */
    protected function getCommonMessages(): array
    {
        return [
            'required' => ':attribute은(는) 필수 입력 항목입니다.',
            'string' => ':attribute은(는) 문자열이어야 합니다.',
            'integer' => ':attribute은(는) 정수여야 합니다.',
            'min' => ':attribute은(는) 최소 :min자 이상이어야 합니다.',
            'max' => ':attribute은(는) :max자를 초과할 수 없습니다.',
            'unique' => '이미 존재하는 :attribute입니다.',
            'exists' => '존재하지 않는 :attribute입니다.',
            'in' => ':attribute의 값이 올바르지 않습니다.',
            'regex' => ':attribute의 형식이 올바르지 않습니다.',
        ];
    }

    /**
     * 필드명 한글화
     */
    public function attributes(): array
    {
        return [
            'name' => '템플릿명',
            'title' => '제목',
            'content' => '내용',
            'priority' => '우선순위',
            'page' => '페이지 번호',
            'pageSize' => '페이지 크기',
            'search' => '검색어',
            'sortBy' => '정렬 기준',
            'sortDirection' => '정렬 방향',
            'created_by' => '생성자',
            'usage_min' => '최소 사용 횟수',
            'usage_max' => '최대 사용 횟수',
            'created_from' => '시작 날짜',
            'created_to' => '종료 날짜',
            'increment_by' => '증가값',
        ];
    }
}
