<?php

namespace App\Http\Requests\NotificationTemplate;

use Illuminate\Contracts\Validation\ValidationRule;

/**
 * 알림 템플릿 목록 조회 요청 검증
 */
class ListRequest extends BaseTemplateRequest
{
    /**
     * 사용자 권한 확인
     */
    public function authorize(): bool
    {
        $user = auth()->user();

        return $user && in_array($user->role, ['Super-Admin', 'Admin']);
    }

    /**
     * 유효성 검증 규칙
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            // 페이지네이션
            'page' => 'sometimes|integer|min:1',
            'pageSize' => 'sometimes|integer|min:1|max:100',

            // 검색 및 필터링
            'search' => 'nullable|string|max:100',
            'priority' => 'sometimes|string|in:low,normal,high,urgent',
            'created_by' => 'sometimes|integer|exists:users,id',
            'usage_min' => 'sometimes|integer|min:0',
            'usage_max' => 'sometimes|integer|min:0|gte:usage_min',
            'created_from' => 'sometimes|date',
            'created_to' => 'sometimes|date|after_or_equal:created_from',

            // 정렬
            'sortBy' => 'sometimes|string|in:usage_count,created_at,name,title,priority',
            'sortDirection' => 'sometimes|string|in:asc,desc',

            // 검색 옵션
            'search_fields' => 'sometimes|array',
            'search_fields.*' => 'string|in:name,title,content',
        ];
    }

    /**
     * 기본값 설정
     */
    protected function prepareForValidation(): void
    {
        // 클라이언트가 보내는 snake_case를 내부 camelCase로 매핑 + 정규화
        $sortBy = $this->input('sort_by', 'created_at');
        if (is_string($sortBy)) {
            $sortBy = strtolower(trim($sortBy));
        }

        $sortDirection = $this->input('sort_order', 'desc');
        if (is_string($sortDirection)) {
            $sortDirection = strtolower(trim($sortDirection));
        }

        $this->merge([
            'page' => $this->input('page', 1),
            'pageSize' => $this->input('pageSize', 20),
            'sortBy' => $sortBy,
            'sortDirection' => $sortDirection,
        ]);
    }

    /**
     * 유효성 검증 오류 메시지
     */
    public function messages(): array
    {
        return array_merge($this->getCommonMessages(), [
            'pageSize.max' => '페이지 크기는 100 이하여야 합니다.',
            'priority.in' => '우선순위는 low, normal, high, urgent 중 하나여야 합니다.',
            'usage_max.gte' => '최대 사용 횟수는 최소 사용 횟수보다 크거나 같아야 합니다.',
            'created_to.after_or_equal' => '종료 날짜는 시작 날짜보다 늦거나 같아야 합니다.',
            'sortBy.in' => '정렬 기준은 usage_count, created_at, name, title, priority 중 하나여야 합니다.',
            'sortDirection.in' => '정렬 방향은 asc 또는 desc여야 합니다.',
            'search_fields.array' => '검색 필드는 배열이어야 합니다.',
            'search_fields.*.in' => '검색 필드는 name, title, content 중 하나여야 합니다.',
        ]);
    }
}
