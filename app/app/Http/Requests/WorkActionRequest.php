<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WorkActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // 권한 체크는 미들웨어에서 처리
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $actionId = $this->route('action'); // 라우트 파라미터에서 ID 가져오기

        $rules = [
            'category_id' => 'required|exists:work_categories,id',
            'code' => 'required|string|max:50',
            'name' => 'required|string|max:100',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:work_actions,id',
            'sort_order' => 'integer|min:0',
            'is_active' => 'boolean',
        ];

        // 수정 시에만 자기 자신을 부모로 설정할 수 없도록 체크
        if ($actionId) {
            $rules['parent_id'] = [
                'nullable',
                'exists:work_actions,id',
                Rule::notIn([$actionId]), // 자기 자신을 부모로 설정할 수 없음
            ];
        }

        return $rules;
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $this->validateCircularReference($validator);
        });
    }

    /**
     * 순환 참조 체크
     */
    private function validateCircularReference($validator): void
    {
        $parentId = $this->input('parent_id');
        $actionId = $this->route('action');

        if (! $parentId || ! $actionId) {
            return;
        }

        // 순환 참조 체크 로직
        $currentParentId = $parentId;
        $checkedIds = [$actionId];

        while ($currentParentId) {
            if (in_array($currentParentId, $checkedIds)) {
                $validator->errors()->add('parent_id', '순환 참조가 발생할 수 없습니다.');

                return;
            }

            $checkedIds[] = $currentParentId;
            $parent = \App\Models\WorkAction::find($currentParentId);

            if (! $parent) {
                break;
            }

            $currentParentId = $parent->parent_id;
        }
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'category_id.required' => '카테고리는 필수입니다.',
            'category_id.exists' => '존재하지 않는 카테고리입니다.',
            'code.required' => '액션 코드는 필수입니다.',
            'code.string' => '액션 코드는 문자열이어야 합니다.',
            'code.max' => '액션 코드는 최대 50자까지 가능합니다.',
            'name.required' => '액션 이름은 필수입니다.',
            'name.string' => '액션 이름은 문자열이어야 합니다.',
            'name.max' => '액션 이름은 최대 100자까지 가능합니다.',
            'description.string' => '액션 설명은 문자열이어야 합니다.',
            'parent_id.exists' => '존재하지 않는 부모 액션입니다.',
            'parent_id.not_in' => '자기 자신을 부모로 설정할 수 없습니다.',
            'sort_order.integer' => '정렬 순서는 정수여야 합니다.',
            'sort_order.min' => '정렬 순서는 0 이상이어야 합니다.',
            'is_active.boolean' => '활성 상태는 true 또는 false여야 합니다.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'category_id' => '카테고리',
            'code' => '액션 코드',
            'name' => '액션 이름',
            'description' => '액션 설명',
            'parent_id' => '부모 액션',
            'sort_order' => '정렬 순서',
            'is_active' => '활성 상태',
        ];
    }
}
