<?php

namespace App\Http\Requests\Notification;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class SendRequest extends FormRequest
{
    /**
     * 사용자가 이 요청을 수행할 권한이 있는지 확인
     *
     * 권한 확인은 미들웨어에서 처리하므로 여기서는 항상 true 반환
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 요청에 적용할 유효성 검증 규칙
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:200',
            'content' => 'required|string|max:2000',
            'target_type' => 'required|in:all,group,individual',
            'target_ids' => 'nullable|array|required_if:target_type,individual,group',
            'target_ids.*' => 'integer',
            'priority' => 'nullable|in:low,normal,high,urgent',
            'action_url' => 'nullable|string|max:500',
        ];
    }

    /**
     * 유효성 검증 규칙에 대한 오류 메시지
     */
    public function messages(): array
    {
        return [
            'title.required' => '알림 제목은 필수 입력 항목입니다.',
            'title.string' => '알림 제목은 문자열이어야 합니다.',
            'title.max' => '알림 제목은 200자를 초과할 수 없습니다.',
            'content.required' => '알림 내용은 필수 입력 항목입니다.',
            'content.string' => '알림 내용은 문자열이어야 합니다.',
            'content.max' => '알림 내용은 2000자를 초과할 수 없습니다.',
            'target_type.required' => '대상 타입은 필수 입력 항목입니다.',
            'target_type.in' => '대상 타입은 all, group, individual 중 하나여야 합니다.',
            'target_ids.required_if' => '개별 대상(individual) 선택 시 target_id는 필수입니다.',
            'target_ids.array' => 'target_ids는 배열이어야 합니다. 각 요소는 정수여야 합니다.',
            'priority.in' => '우선순위는 low, normal, high, urgent 중 하나여야 합니다.',
            'action_url.string' => '액션 URL은 문자열이어야 합니다.',
            'action_url.max' => '액션 URL은 500자를 초과할 수 없습니다.',
        ];
    }

    /**
     * 유효성 검증 후 추가 검증 로직
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $targetType = $this->input('target_type');
            $targetId = $this->input('target_ids');

            // individual일 때 target_id 필수 검증 (rules의 required_if로도 검증되지만 메시지 보강)
            if (in_array($targetType, ['individual', 'group'], true) && empty($targetId)) {
                $validator->errors()->add(
                    'target_id',
                    '개별 대상(individual) 또는 그룹(group) 대상 선택 시 target_id는 필수입니다.'
                );
            }
        });
    }
}
