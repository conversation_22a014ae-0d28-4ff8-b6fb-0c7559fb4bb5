<?php

namespace App\Http\Requests\Notification;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = auth()->user();

        return $user && ($user->role == 'Admin' || $user->role == 'Super-Admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|unique:notification_groups,name',
            'description' => 'required|string|max:1000',
            'is_active' => 'required|boolean',
            'member_ids' => 'required|array',
            'member_ids.*' => 'exists:users,id',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'name.required' => '알림 그룹명은 필수 입력 항목입니다.',
            'name.string' => '알림 그룹명은 문자열이어야 합니다.',
            'name.exists' => '존재하지 않는 알림 그룹입니다.',
            'name.unique' => '이미 등록된 알림 그룹입니다.',
            'description.required' => '알림 그룹 설명은 필수 입력 항목입니다.',
            'description.string' => '알림 그룹 설명은 문자열이어야 합니다.',
            'description.max' => '알림 그룹 설명은 1000자를 초과할 수 없습니다.',
            'is_active.required' => '활성 여부는 필수 입력 항목입니다.',
            'is_active.boolean' => '활성 여부는 boolean이어야 합니다.',
            'member_ids.required' => '알림 그룹 멤버는 필수 입력 항목입니다.',
            'member_ids.array' => '알림 그룹 멤버는 배열이어야 합니다.',
            'member_ids.*.exists' => '존재하지 않는 사용자입니다.',
        ];
    }
}
