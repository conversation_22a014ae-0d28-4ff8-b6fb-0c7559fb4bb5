<?php

namespace App\Http\Requests\Notification;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class ListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = auth()->user();

        return $user && ($user->role == 'Admin' || $user->role == 'Super-Admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'search' => 'nullable|string|max:100',
            'pageSize' => 'nullable|integer|min:1|max:320',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'search.max' => '검색어는 :max자 이하여야 합니다.',
            'pageSize.integer' => '페이지 크기는 숫자여야 합니다.',
            'pageSize.min' => '페이지 크기는 최소 :min 이상이어야 합니다.',
            'pageSize.max' => '페이지 크기는 최대 :max 이하여야 합니다.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // pageSize가 제공되지 않은 경우 기본값 설정
        if (! $this->has('pageSize')) {
            $this->merge(['pageSize' => 100]);
        }

        // 검색어 필드가 빈 문자열인 경우 null로 변환
        if ($this->input('search') === '') {
            $this->merge(['search' => null]);
        }
    }
}
