<?php

namespace App\Http\Requests\Notification;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = auth()->user();

        return $user && ($user->role == 'Admin' || $user->role == 'Super-Admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        // 라우트 파라미터에서 현재 리소스 ID를 유연하게 탐색
        $groupId = $this->route('notification_group')
            ?? $this->route('notificationGroup')
            ?? $this->route('id')
            ?? $this->route('group');

        return [
            'name' => [
                'sometimes',
                'string',
                Rule::unique('notification_groups', 'name')->ignore($groupId),
            ],
            'description' => 'sometimes|string|max:1000',
            'is_active' => 'sometimes|boolean',
            'member_ids' => 'sometimes|array',
            'member_ids.*' => 'exists:users,id',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'name.string' => '알림 그룹명은 문자열이어야 합니다.',
            'name.unique' => '이미 등록된 알림 그룹입니다.',
            'description.string' => '알림 그룹 설명은 문자열이어야 합니다.',
            'description.max' => '알림 그룹 설명은 1000자를 초과할 수 없습니다.',
            'is_active.boolean' => '활성 여부는 boolean이어야 합니다.',
            'member_ids.array' => '알림 그룹 멤버는 배열이어야 합니다.',
            'member_ids.*.exists' => '존재하지 않는 사용자입니다.',
        ];
    }
}
