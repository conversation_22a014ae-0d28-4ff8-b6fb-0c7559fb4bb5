<?php

namespace App\Http\Requests\Member;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = auth()->user();

        return $user && ($user->role == 'Admin' || $user->role == 'Super-Admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        if ($this->getMethod() == 'POST') {
            // 등록시 규칙
            $rules = [
                'username' => ['bail', 'required', 'unique:users', 'max:50'],
                'name' => ['bail', 'required', 'max:50'],
                'email' => ['bail', 'unique:users', 'email', 'max:100'],
                'cellphone' => ['bail', 'required', 'unique:users'],
                'role' => ['bail', 'required', 'string', 'max:50'],
                'caps_id' => ['bail', 'nullable', 'string', 'max:100'],
                'line_number' => ['bail', 'nullable', 'string', 'max:20'],
                'status' => ['bail', 'required', 'integer', 'in:1,2,8,9'],
                'position' => ['bail', 'nullable', 'string', 'max:100'],
                'part' => ['bail', 'nullable', 'string', 'max:100'],
                'menu' => ['bail', 'nullable', 'string'],
            ];
        } else {
            // 수정시 규칙
            $rules = [
                'username' => ['bail', 'required', 'unique:users,username,'.$this->route('id'), 'max:50'],
                'name' => ['bail', 'required', 'max:50'],
                'email' => ['bail', 'unique:users,email,'.$this->route('id'), 'email', 'max:100'],
                'cellphone' => ['bail', 'required', 'unique:users,cellphone,'.$this->route('id')],
                'role' => ['bail', 'required', 'string', 'max:50'],
                'caps_id' => ['bail', 'nullable', 'string', 'max:100'],
                'line_number' => ['bail', 'nullable', 'string', 'max:20'],
                'status' => ['bail', 'required', 'integer', 'in:1,2,8,9'],
                'position' => ['bail', 'nullable', 'string', 'max:100'],
                'part' => ['bail', 'nullable', 'string', 'max:100'],
                'menu' => ['bail', 'nullable', 'string'],
            ];
        }

        if ($this->getMethod() == 'POST') {
            // 등록시에는 비밀번호 필수
            $rules['password'] = [
                'bail', 'required',
                function ($attribute, $value, $fail) {
                    if (! preg_match('/^.{8,}$/', $value)) {
                        $fail('비밀번호는 최소 8자 이상이어야 합니다.');
                    } elseif (! preg_match('/[a-zA-Z]/', $value)) {
                        $fail('비밀번호에는 문자가 하나 이상 포함되어야 합니다.');
                    } elseif (! preg_match('/(?=.*[a-z])(?=.*[A-Z])/', $value)) {
                        $fail('비밀번호에는 대소문자가 모두 포함되어야 합니다.');
                    } elseif (! preg_match('/\d/', $value)) {
                        $fail('비밀번호에는 숫자가 하나 이상 포함되어야 합니다.');
                    } elseif (! preg_match('/[~`!@#$%^&*(){}\[\];:,<.>\/?\"\'|_+=-]/', $value)) {
                        $fail('비밀번호에는 특수 문자가 하나 이상 포함되어야 합니다.');
                    }
                },
            ];
        } else {
            // 수정시에는 비밀번호 선택적
            if ($this->filled('password')) {
                $rules['password'] = [
                    'bail', 'required',
                    function ($attribute, $value, $fail) {
                        if (! preg_match('/^.{8,}$/', $value)) {
                            $fail('비밀번호는 최소 8자 이상이어야 합니다.');
                        } elseif (! preg_match('/[a-zA-Z]/', $value)) {
                            $fail('비밀번호에는 문자가 하나 이상 포함되어야 합니다.');
                        } elseif (! preg_match('/(?=.*[a-z])(?=.*[A-Z])/', $value)) {
                            $fail('비밀번호에는 대소문자가 모두 포함되어야 합니다.');
                        } elseif (! preg_match('/\d/', $value)) {
                            $fail('비밀번호에는 숫자가 하나 이상 포함되어야 합니다.');
                        } elseif (! preg_match('/[~`!@#$%^&*(){}\[\];:,<.>\/?\"\'|_+=-]/', $value)) {
                            $fail('비밀번호에는 특수 문자가 하나 이상 포함되어야 합니다.');
                        }
                    },
                ];
            }
        }

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'username.required' => '로그인에 사용할 ID를 입력해 주세요.',
            'username.unique' => '이미 등록되어 있는 ID 입니다.',
            'username.max' => 'ID는 :max자 이하여야 합니다.',
            'name.required' => '이름을 입력해 주세요.',
            'name.max' => '이름은 :max자 이하여야 합니다.',
            'email.required' => '메일주소를 입력해 주세요.',
            'email.unique' => '이미 등록되어 있는 메일 주소입니다.',
            'email.email' => '메일주소가 email 형식과 다릅니다. 확인 부탁 드립니다.',
            'email.max' => '메일주소는 :max자리 이하여야 합니다.',
            'cellphone.required' => '연락처(휴대폰 번호)를 입력해 주세요.',
            'cellphone.unique' => '이미 등록되어 있는 연락처(휴대폰 번호) 입니다.',
            'role.required' => '권한을 선택해 주세요.',
            'role.string' => '권한은 문자열이어야 합니다.',
            'role.max' => '권한은 :max자 이하여야 합니다.',
            'caps_id.string' => 'CAPS ID는 문자열이어야 합니다.',
            'caps_id.max' => 'CAPS ID는 :max자 이하여야 합니다.',
            'status.required' => '상태를 선택해 주세요.',
            'status.integer' => '상태는 숫자여야 합니다.',
            'status.in' => '유효하지 않은 상태값입니다.',
            'position.string' => '직책은 문자열이어야 합니다.',
            'position.max' => '직책은 :max자 이하여야 합니다.',
            'part.string' => '부서는 문자열이어야 합니다.',
            'part.max' => '부서는 :max자 이하여야 합니다.',
            'menu.string' => '메뉴는 문자열이어야 합니다.',
            'password.required' => '로그인에 사용할 비밀번호를 입력해 주세요.',
            'password.min' => '비밀번호는 :min자리 이상이어야 합니다.',
            'password.letters' => '비밀번호에는 문자가 하나 이상 포함되어야 합니다.',
            'password.mixed_case' => '비밀번호에는 대소문자가 모두 포함되어야 합니다.',
            'password.numbers' => '비밀번호에는 숫자가 하나 이상 포함되어야 합니다.',
            'password.symbols' => '비밀번호에는 특수 문자가 하나 이상 포함되어야 합니다.',
            'password.uncompromised' => '이 비밀번호는 유출된 적이 있는 비밀번호로 안전하지 않습니다.(해킹 당할 수 있음)',
        ];
    }
}
