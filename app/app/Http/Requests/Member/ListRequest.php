<?php

namespace App\Http\Requests\Member;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;

class ListRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = auth()->user();

        return $user && ($user->role == 'Admin' || $user->role == 'Super-Admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'pageSize' => ['nullable', 'integer', 'min:1', 'max:100'],
            'role' => ['nullable', 'string', 'in:'.implode(',', [
                User::ROLE_SUPER_ADMIN,
                User::ROLE_ADMIN,
                User::ROLE_RECEIVING_MANAGER,
                User::ROLE_PALLET_MANAGER,
                User::ROLE_CARRYOUT_MANAGER,
                User::ROLE_EMPLOYEE,
                User::ROLE_GUEST,
            ])],
            'status' => ['nullable', 'integer', 'in:'.implode(',', [
                User::MEMBER_STATUS_ACTIVE,
                User::MEMBER_STATUS_PAUSE,
                User::MEMBER_STATUS_UNAVAILABLE,
                User::MEMBER_STATUS_DELETED,
            ])],
            'keyword' => ['nullable', 'string', 'max:100'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'pageSize.integer' => '페이지 크기는 숫자여야 합니다.',
            'pageSize.min' => '페이지 크기는 최소 :min 이상이어야 합니다.',
            'pageSize.max' => '페이지 크기는 최대 :max 이하여야 합니다.',
            'role.string' => '권한은 문자열이어야 합니다.',
            'role.in' => '유효하지 않은 권한입니다.',
            'status.integer' => '상태는 숫자여야 합니다.',
            'status.in' => '유효하지 않은 상태값입니다.',
            'keyword.string' => '검색어는 문자열이어야 합니다.',
            'keyword.max' => '검색어는 :max자 이하여야 합니다.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // pageSize가 제공되지 않은 경우 기본값 설정
        if (! $this->has('pageSize')) {
            $this->merge(['pageSize' => 16]);
        }

        // status가 빈 문자열인 경우 활성화된 유저를 반환
        if ($this->input('status') === '') {
            $this->merge(['status' => User::MEMBER_STATUS_ACTIVE]);
        }
    }
}
