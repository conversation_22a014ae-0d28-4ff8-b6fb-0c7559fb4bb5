<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BoardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'user_id' => $this->user_id,
            'name' => $this->name,
            'subject' => $this->subject,
            // 'content' => $this->content, // 제외 - 상세 조회에서만 필요
            'status' => $this->status,
            'f_notice' => $this->f_notice,
            'f_show' => $this->f_show,
            'open_at' => $this->open_at,
            'hits' => $this->hits,
            'comments_count' => $this->comments_count,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
