<?php

namespace App\Http\Resources;

use App\Models\RepairGrade;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserWorkLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $repairProduct = $this->product->repairProduct;
        if ($repairProduct->waiting_user_id && ! $repairProduct->completee_user_id) {
            // 수리/점검 대기(구성품 신청 중): WT
            $repairGradeName = RepairGrade::$GRADE_NAME[RepairGrade::GRADE_WAITING];
        } elseif ($repairProduct->completed_user_id) {
            $repairGradeName = $repairProduct?->repairGrade->name;
        } else {
            $repairGradeName = '';
        }

        return [
            'created_at' => $this->created_at,
            'memo' => $this->memo,
            'product' => [
                'qaid' => $this->product->qaid,
                'name' => $this->product->name,
                'grade_name' => $repairGradeName,
            ],
        ];
    }
}
