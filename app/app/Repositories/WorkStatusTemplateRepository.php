<?php

namespace App\Repositories;

use App\Models\WorkStatusTemplate;
use App\Repositories\Interfaces\WorkStatusTemplateRepositoryInterface;
use Exception;
use Illuminate\Database\Eloquent\Collection;

class WorkStatusTemplateRepository implements WorkStatusTemplateRepositoryInterface
{
    /**
     * 모든 활성 템플릿 조회
     */
    public function getAllActive(): Collection
    {
        return WorkStatusTemplate::where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 모든 템플릿 조회 (비활성 포함)
     */
    public function getAll(): Collection
    {
        return WorkStatusTemplate::orderBy('created_at', 'desc')->get();
    }

    /**
     * ID로 템플릿 조회
     */
    public function findById(int $id): ?WorkStatusTemplate
    {
        return WorkStatusTemplate::find($id);
    }

    /**
     * 카테고리별 활성 템플릿 조회
     */
    public function findActiveByCategoryId(int $categoryId): Collection
    {
        return WorkStatusTemplate::where('category_id', $categoryId)
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 액션별 활성 템플릿 조회
     */
    public function findActiveByActionId(int $actionId): Collection
    {
        return WorkStatusTemplate::where('action_id', $actionId)
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 카테고리와 액션으로 활성 템플릿 조회
     */
    public function findActiveByCategoryAndAction(int $categoryId, int $actionId): Collection
    {
        return WorkStatusTemplate::where('category_id', $categoryId)
            ->where('action_id', $actionId)
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 조건에 맞는 최적의 템플릿 조회
     */
    public function findBestMatch(int $categoryId, int $actionId, array $context): ?WorkStatusTemplate
    {
        $templates = WorkStatusTemplate::where('category_id', $categoryId)
            ->where('action_id', $actionId)
            ->where('is_active', true)
            ->get();

        $matchingTemplates = [];

        foreach ($templates as $template) {
            if ($this->matchesConditions($template, $context)) {
                $matchingTemplates[] = $template;
            }
        }

        if (empty($matchingTemplates)) {
            return null;
        }

        // 우선순위가 높은 템플릿 선택 (더 구체적인 조건을 가진 템플릿)
        usort($matchingTemplates, function ($a, $b) {
            return $b->priority - $a->priority;
        });

        return $matchingTemplates[0];
    }

    /**
     * 카테고리, 액션, 템플릿 코드로 템플릿 조회
     */
    public function findByKeys(int $categoryId, int $actionId, string $templateCode): ?WorkStatusTemplate
    {
        return WorkStatusTemplate::where('category_id', $categoryId)
            ->where('action_id', $actionId)
            ->where('template_code', $templateCode)
            ->first();
    }

    /**
     * 생성자별 템플릿 조회
     */
    public function findByCreator(int $creatorId): Collection
    {
        return WorkStatusTemplate::where('created_by', $creatorId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 템플릿 생성
     */
    public function create(array $data): WorkStatusTemplate
    {
        return WorkStatusTemplate::create($data);
    }

    /**
     * 템플릿 수정
     */
    public function update(int $id, array $data): WorkStatusTemplate
    {
        $template = WorkStatusTemplate::findOrFail($id);
        $template->update($data);

        return $template;
    }

    /**
     * 템플릿 삭제
     *
     * @throws Exception
     */
    public function delete(int $id): bool
    {
        $template = WorkStatusTemplate::findOrFail($id);

        if (! $template->canDelete()) {
            throw new Exception('이 템플릿을 사용하는 상태가 있어 삭제할 수 없습니다.');
        }

        return $template->delete();
    }

    /**
     * 관계 포함 템플릿 목록 조회
     */
    public function getAllWithRelations(): Collection
    {
        return WorkStatusTemplate::with([
            'category',
            'action',
            'creator',
            'workStatuses',
        ])
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 통계 정보 포함 템플릿 목록 조회
     */
    public function getAllWithCounts(): Collection
    {
        return WorkStatusTemplate::where('is_active', true)
            ->withCount('workStatuses')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 사용되지 않는 템플릿 조회
     */
    public function getUnusedTemplates(): Collection
    {
        return WorkStatusTemplate::whereDoesntHave('workStatuses')
            ->where('is_active', true)
            ->with(['category', 'action'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 템플릿 복사
     */
    public function duplicate(WorkStatusTemplate $template, string $newTemplateCode, ?int $newCreatorId = null): WorkStatusTemplate
    {
        $newTemplate = $template->replicate();
        $newTemplate->template_code = $newTemplateCode;
        $newTemplate->created_by = $newCreatorId ?? auth()->id();
        $newTemplate->save();

        return $newTemplate;
    }

    /**
     * 템플릿 우선순위 계산 (더 구체적인 조건을 가진 템플릿이 우선)
     */
    public function getPriority(WorkStatusTemplate $template): int
    {
        if (! $template->conditions || empty($template->conditions)) {
            return 0; // 기본 템플릿 (가장 낮은 우선순위)
        }

        $priority = 0;
        foreach ($template->conditions as $key => $value) {
            if (is_array($value)) {
                $priority += count($value); // 배열 값의 개수만큼 우선순위 추가
            } else {
                $priority += 1; // 단일 값은 1 우선순위 추가
            }
        }

        return $priority;
    }

    /**
     * 템플릿별 생성된 상태 개수 조회
     */
    public function getGeneratedStatusCount(int $templateId): int
    {
        return WorkStatusTemplate::find($templateId)?->workStatuses()->count() ?? 0;
    }

    /**
     * 조건부 생성 규칙 검증
     */
    public function validateConditions(array $conditions = []): bool
    {
        if (! is_array($conditions)) {
            return false;
        }

        foreach ($conditions as $key => $value) {
            if (! is_string($key) || empty($key)) {
                return false;
            }

            if (! is_string($value) && ! is_array($value) && ! is_numeric($value) && ! is_bool($value)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 템플릿 이름에 컨텍스트 변수 치환
     */
    public function generateName(WorkStatusTemplate $template, array $context): string
    {
        $name = $template->name;

        // 컨텍스트 변수 치환 ({{variable}} 형태)
        foreach ($context as $key => $value) {
            $name = str_replace("{{$key}}", $value, $name);
        }

        return $name;
    }

    /**
     * 템플릿 설명에 컨텍스트 변수 치환
     */
    public function generateDescription(WorkStatusTemplate $template, array $context): string
    {
        $description = $template->description ?? '';

        // 컨텍스트 변수 치환 ({{variable}} 형태)
        foreach ($context as $key => $value) {
            $description = str_replace("{{$key}}", $value, $description);
        }

        return $description;
    }

    /**
     * 조건 매칭 검사
     */
    public function matchesConditions(WorkStatusTemplate $template, array $context): bool
    {
        if (! $template->conditions || empty($template->conditions)) {
            return true; // 조건이 없으면 기본 템플릿으로 매칭
        }

        foreach ($template->conditions as $key => $expectedValue) {
            if (! isset($context[$key])) {
                return false; // 컨텍스트에 필요한 키가 없음
            }

            // 배열 값 비교
            if (is_array($expectedValue)) {
                if (! in_array($context[$key], $expectedValue)) {
                    return false;
                }
            } else {
                if ($context[$key] !== $expectedValue) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 템플릿 비활성화 시 관련 데이터 체크
     */
    public function canDeactivate(WorkStatusTemplate $template): bool
    {
        // 관련된 WorkStatus가 있는지 확인
        $hasWorkStatuses = $template->workStatuses()->exists();

        // 사용중인 템플릿은 비활성화 불가
        return ! $hasWorkStatuses;
    }

    /**
     * 템플릿 삭제 가능 여부 확인
     */
    public function canDelete(WorkStatusTemplate $template): bool
    {
        // 관련된 WorkStatus가 있는지 확인
        $hasWorkStatuses = $template->workStatuses()->exists();

        return ! $hasWorkStatuses;
    }
}
