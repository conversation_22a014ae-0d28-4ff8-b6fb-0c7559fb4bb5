<?php

namespace App\Repositories;

use App\Models\WorkAction;
use App\Repositories\Interfaces\WorkActionRepositoryInterface;
use Exception;
use Illuminate\Database\Eloquent\Collection;

class WorkActionRepository implements WorkActionRepositoryInterface
{
    /**
     * 모든 활성 액션 조회
     */
    public function getAllActive(): Collection
    {
        return WorkAction::where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * 모든 액션 조회 (비활성 포함)
     */
    public function getAll(): Collection
    {
        return WorkAction::orderBy('sort_order')->get();
    }

    /**
     * ID로 액션 조회
     */
    public function findById(int $id): ?WorkAction
    {
        return WorkAction::find($id);
    }

    /**
     * 카테고리별 활성 액션 조회
     */
    public function findActiveByCategoryId(int $categoryId): Collection
    {
        return WorkAction::where('category_id', $categoryId)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * 카테고리와 코드로 액션 조회
     */
    public function findByCategoryAndCode(int $categoryId, string $code): ?WorkAction
    {
        return WorkAction::where('category_id', $categoryId)
            ->where('code', $code)
            ->first();
    }

    /**
     * 활성 상태 카테고리와 코드로 액션 조회
     */
    public function findActiveByCategoryAndCode(int $categoryId, string $code): ?WorkAction
    {
        return WorkAction::where('category_id', $categoryId)
            ->where('code', $code)
            ->where('is_active', true)
            ->first();
    }

    /**
     * 최상위 액션들 조회 (부모가 없는 액션)
     */
    public function getTopLevelActions(int $categoryId): Collection
    {
        return WorkAction::where('category_id', $categoryId)
            ->whereNull('parent_id')
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * 자식 액션들 조회
     */
    public function getChildrenActions(int $parentId): Collection
    {
        return WorkAction::where('parent_id', $parentId)
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * 액션 생성
     */
    public function create(array $data): WorkAction
    {
        return WorkAction::create($data);
    }

    /**
     * 액션 수정
     */
    public function update(int $id, array $data): WorkAction
    {
        $action = WorkAction::findOrFail($id);
        $action->update($data);

        return $action;
    }

    /**
     * 액션 삭제
     *
     * @throws Exception
     */
    public function delete(int $id): bool
    {
        if (! $this->canDelete($id)) {
            throw new Exception('관련 데이터가 있어 삭제할 수 없습니다.');
        }

        $action = WorkAction::findOrFail($id);

        return $action->delete();
    }

    /**
     * 관계 포함 액션 목록 조회
     */
    public function getAllWithRelations(): Collection
    {
        return WorkAction::with([
            'category',
            'parent',
            'children' => function ($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            },
            'statusTemplates' => function ($query) {
                $query->where('is_active', true);
            },
        ])
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * 통계 정보 포함 액션 목록 조회
     */
    public function getAllWithCounts(): Collection
    {
        return WorkAction::where('is_active', true)
            ->withCount([
                'children',
                'statusTemplates',
                'workStatuses',
            ])
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * 순환 참조 체크
     */
    public function checkCircularReference(int $actionId, int $parentId): bool
    {
        if ($parentId == $actionId) {
            return true;
        }

        $parent = WorkAction::find($parentId);
        if (! $parent) {
            return false;
        }

        return $this->checkCircularReference($actionId, $parent->parent_id);
    }

    /**
     * 액션 비활성화 가능 여부 확인
     */
    public function canDeactivate(int $actionId): bool
    {
        $action = WorkAction::find($actionId);
        if (! $action) {
            return false;
        }

        // 관련된 활성 자식 액션이 있는지 확인
        $hasActiveChildren = $action->activeChildren()->exists();

        // 관련된 활성 템플릿이 있는지 확인
        $hasActiveTemplates = $action->statusTemplates()
            ->where('is_active', true)
            ->exists();

        return ! $hasActiveChildren && ! $hasActiveTemplates;
    }

    /**
     * 액션 삭제 가능 여부 확인
     */
    public function canDelete(int $actionId): bool
    {
        $action = WorkAction::find($actionId);
        if (! $action) {
            return false;
        }

        // 관련된 WorkStatus가 있는지 확인
        $hasWorkStatuses = $action->workStatuses()->exists();

        // 관련된 자식 액션이 있는지 확인
        $hasChildren = $action->children()->exists();

        return ! $hasWorkStatuses && ! $hasChildren;
    }

    /**
     * 액션의 전체 경로 조회 (부모 -> 자식 순)
     */
    public function getFullPath(int $actionId): string
    {
        $action = WorkAction::find($actionId);
        if (! $action) {
            return '';
        }

        $path = [];
        $current = $action;

        while ($current) {
            array_unshift($path, $current->name);
            $current = $current->parent;
        }

        return implode(' > ', $path);
    }

    /**
     * 액션의 깊이 레벨 조회
     */
    public function getDepth(int $actionId): int
    {
        $action = WorkAction::find($actionId);
        if (! $action) {
            return 0;
        }

        $depth = 0;
        $current = $action;

        while ($current->parent) {
            $depth++;
            $current = $current->parent;
        }

        return $depth;
    }

    /**
     * 액션별 자식 개수 조회
     */
    public function getChildrenCount(int $actionId): int
    {
        return WorkAction::find($actionId)?->children()->count() ?? 0;
    }

    /**
     * 액션별 활성 자식 개수 조회
     */
    public function getActiveChildrenCount(int $actionId): int
    {
        return WorkAction::find($actionId)?->activeChildren()->count() ?? 0;
    }

    /**
     * 액션별 템플릿 개수 조회
     */
    public function getTemplatesCount(int $actionId): int
    {
        return WorkAction::find($actionId)?->statusTemplates()->count() ?? 0;
    }
}
