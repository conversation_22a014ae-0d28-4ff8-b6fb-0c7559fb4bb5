<?php

namespace App\Repositories\Interfaces;

use App\Models\WorkStatusTemplate;
use Illuminate\Database\Eloquent\Collection;

interface WorkStatusTemplateRepositoryInterface
{
    /**
     * 모든 활성 템플릿 조회
     */
    public function getAllActive(): Collection;

    /**
     * 모든 템플릿 조회 (비활성 포함)
     */
    public function getAll(): Collection;

    /**
     * ID로 템플릿 조회
     */
    public function findById(int $id): ?WorkStatusTemplate;

    /**
     * 카테고리별 활성 템플릿 조회
     */
    public function findActiveByCategoryId(int $categoryId): Collection;

    /**
     * 액션별 활성 템플릿 조회
     */
    public function findActiveByActionId(int $actionId): Collection;

    /**
     * 카테고리와 액션으로 활성 템플릿 조회
     */
    public function findActiveByCategoryAndAction(int $categoryId, int $actionId): Collection;

    /**
     * 조건에 맞는 최적의 템플릿 조회
     */
    public function findBestMatch(int $categoryId, int $actionId, array $context): ?WorkStatusTemplate;

    /**
     * 카테고리, 액션, 템플릿 코드로 템플릿 조회
     */
    public function findByKeys(int $categoryId, int $actionId, string $templateCode): ?WorkStatusTemplate;

    /**
     * 생성자별 템플릿 조회
     */
    public function findByCreator(int $creatorId): Collection;

    /**
     * 템플릿 생성
     */
    public function create(array $data): WorkStatusTemplate;

    /**
     * 템플릿 수정
     */
    public function update(int $id, array $data): WorkStatusTemplate;

    /**
     * 템플릿 삭제
     */
    public function delete(int $id): bool;

    /**
     * 관계 포함 템플릿 목록 조회
     */
    public function getAllWithRelations(): Collection;

    /**
     * 통계 정보 포함 템플릿 목록 조회
     */
    public function getAllWithCounts(): Collection;

    /**
     * 사용되지 않는 템플릿 조회
     */
    public function getUnusedTemplates(): Collection;

    /**
     * 템플릿 복사
     */
    public function duplicate(WorkStatusTemplate $template, string $newTemplateCode, ?int $newCreatorId = null): WorkStatusTemplate;

    /**
     * 템플릿 이름에 컨텍스트 변수 치환
     */
    public function generateName(WorkStatusTemplate $template, array $context): string;

    /**
     * 템플릿 설명에 컨텍스트 변수 치환
     */
    public function generateDescription(WorkStatusTemplate $template, array $context): string;

    /**
     * 조건 매칭 검사
     */
    public function matchesConditions(WorkStatusTemplate $template, array $context): bool;

    /**
     * 템플릿 비활성화 시 관련 데이터 체크
     */
    public function canDeactivate(WorkStatusTemplate $template): bool;

    /**
     * 템플릿 삭제 가능 여부 확인
     */
    public function canDelete(WorkStatusTemplate $template): bool;
}
