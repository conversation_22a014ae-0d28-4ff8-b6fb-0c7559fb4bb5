<?php

namespace App\Repositories\Interfaces;

use App\Models\NotificationTemplate;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

interface NotificationTemplateRepositoryInterface
{
    /**
     * 모든 템플릿 조회
     */
    public function findAll(): Collection;

    /**
     * ID로 템플릿 조회
     */
    public function findById(int $id): ?NotificationTemplate;

    /**
     * 템플릿명으로 조회
     */
    public function findByName(string $name): ?NotificationTemplate;

    /**
     * 우선순위별 템플릿 조회
     */
    public function findByPriority(string $priority): Collection;

    /**
     * 템플릿 생성
     */
    public function create(array $data): NotificationTemplate;

    /**
     * 템플릿 수정
     */
    public function update(int $id, array $data): NotificationTemplate;

    /**
     * 템플릿 삭제
     */
    public function delete(int $id): bool;

    /**
     * 페이지네이션된 템플릿 목록 조회
     */
    public function getPaginated(int $page = 1, int $perPage = 20, array $filters = []): LengthAwarePaginator;

    /**
     * 검색 및 필터링된 템플릿 조회
     */
    public function search(string $search = '', array $filters = []): Collection;

    /**
     * 사용 횟수 기준 정렬된 템플릿 조회
     */
    public function getOrderedByUsage(string $direction = 'desc'): Collection;

    /**
     * 생성일 기준 정렬된 템플릿 조회
     */
    public function getOrderedByCreated(string $direction = 'desc'): Collection;

    /**
     * 생성자별 템플릿 조회
     */
    public function findByCreator(int $creatorId): Collection;

    /**
     * 사용 횟수 증가
     */
    public function incrementUsage(int $id): bool;

    /**
     * 템플릿명 중복 검증
     */
    public function isDuplicateName(string $name, ?int $excludeId = null): bool;

    /**
     * 관계 포함 템플릿 목록 조회
     */
    public function getAllWithRelations(): Collection;

    /**
     * 통계 정보 포함 페이지네이션 조회
     */
    public function getPaginatedWithStats(int $page = 1, int $perPage = 20, array $filters = []): LengthAwarePaginator;

    /**
     * 인기 템플릿 조회 (사용 횟수 상위)
     */
    public function getPopularTemplates(int $limit = 10): Collection;

    /**
     * 최근 생성된 템플릿 조회
     */
    public function getRecentTemplates(int $limit = 10): Collection;

    /**
     * 사용되지 않은 템플릿 조회
     */
    public function getUnusedTemplates(): Collection;

    /**
     * 우선순위별 템플릿 개수 조회
     */
    public function getCountByPriority(): array;

    /**
     * 생성자별 템플릿 개수 조회
     */
    public function getCountByCreator(): Collection;

    /**
     * 사용 통계 요약 정보 조회
     */
    public function getUsageStatistics(): array;

    /**
     * 사용 빈도별 템플릿 분포 조회
     */
    public function getUsageDistribution(): array;

    /**
     * 사용 횟수 범위별 템플릿 조회
     */
    public function getTemplatesByUsageRange(int $minUsage = 0, ?int $maxUsage = null): Collection;
}
