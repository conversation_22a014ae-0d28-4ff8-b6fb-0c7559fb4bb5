<?php

namespace App\Repositories\Interfaces;

use App\Models\WorkStatus;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface WorkStatusRepositoryInterface
{
    /**
     * 모든 WorkStatus 조회
     */
    public function getAll(): Collection;

    /**
     * ID로 WorkStatus 조회
     */
    public function findById(int $id): ?WorkStatus;

    /**
     * 링크 코드로 WorkStatus 조회
     */
    public function findByLinkCode(string $linkCode): ?WorkStatus;

    /**
     * 자동 생성된 WorkStatus 조회
     */
    public function getAutoGenerated(): Collection;

    /**
     * 수동 생성된 WorkStatus 조회
     */
    public function getManualGenerated(): Collection;

    /**
     * 카테고리별 WorkStatus 조회
     */
    public function findByCategoryId(int $categoryId): Collection;

    /**
     * 액션별 WorkStatus 조회
     */
    public function findByActionId(int $actionId): Collection;

    /**
     * 템플릿별 WorkStatus 조회
     */
    public function findByTemplateId(int $templateId): Collection;

    /**
     * WorkStatus 생성 또는 조회
     */
    public function firstOrCreate(array $attributes, array $values = []): WorkStatus;

    /**
     * WorkStatus 생성
     */
    public function create(array $data): WorkStatus;

    /**
     * WorkStatus 수정
     */
    public function update(int $id, array $data): WorkStatus;

    /**
     * WorkStatus 삭제
     */
    public function delete(int $id): bool;

    /**
     * 관계 포함 WorkStatus 목록 조회
     */
    public function getAllWithRelations(): Collection;

    /**
     * 페이지네이션된 WorkStatus 목록 조회
     */
    public function getPaginated(int $perPage = 20, array $filters = []): LengthAwarePaginator;

    /**
     * 최근 생성된 WorkStatus 조회
     */
    public function getRecent(int $limit = 10): Collection;

    /**
     * 자주 사용되는 WorkStatus 조회
     */
    public function getFrequentlyUsed(int $limit = 10): Collection;

    /**
     * 통계 정보 조회
     */
    public function getStats(): array;

    /**
     * 카테고리별 통계 조회
     */
    public function getStatsByCategory(int $categoryId): array;

    /**
     * 템플릿별 사용 개수 조회
     */
    public function getCountByTemplate(int $templateId): int;

    /**
     * WorkStatus 생성 통계 조회
     */
    public function getGenerationStats(): array;

    /**
     * 조건에 맞는 자동 생성된 WorkStatus 조회
     */
    public function getAutoGeneratedByCondition(int $categoryId, int $actionId): array;

    /**
     * 이름 생성 (컨텍스트 기반)
     */
    public function generateName(array $context): string;

    /**
     * 배치로 여러 코드의 이름을 한 번에 조회 (성능 최적화)
     */
    public function generateNameBatch(array $contexts): array;
}
