<?php

namespace App\Repositories\Interfaces;

use App\Models\MonitorSizeLookup;
use Illuminate\Pagination\LengthAwarePaginator;

interface MonitorSizeLookupRepositoryInterface
{
    public function search(array $filters, int $perPage = 20): LengthAwarePaginator;

    public function findById(int $id): ?MonitorSizeLookup;

    public function updateById(int $id, array $attributes): MonitorSizeLookup;
}
