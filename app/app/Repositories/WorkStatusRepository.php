<?php

namespace App\Repositories;

use App\Models\RepairGrade;
use App\Models\RepairProcess;
use App\Models\RepairSymptom;
use App\Models\WorkStatus;
use App\Repositories\Interfaces\WorkStatusRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class WorkStatusRepository implements WorkStatusRepositoryInterface
{
    public function __construct(
        private readonly RepairSymptom $repairSymptom,
        private readonly RepairProcess $repairProcess,
        private readonly RepairGrade $repairGrade
    ) {}

    /**
     * 모든 WorkStatus 조회
     */
    public function getAll(): Collection
    {
        return WorkStatus::orderBy('code')->get();
    }

    /**
     * ID로 WorkStatus 조회
     */
    public function findById(int $id): ?WorkStatus
    {
        return WorkStatus::find($id);
    }

    /**
     * 링크 코드로 WorkStatus 조회
     */
    public function findByLinkCode(string $linkCode): ?WorkStatus
    {
        return WorkStatus::where('link_code', $linkCode)->first();
    }

    /**
     * 자동 생성된 WorkStatus 조회
     */
    public function getAutoGenerated(): Collection
    {
        return WorkStatus::where('auto_generated', true)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 수동 생성된 WorkStatus 조회
     */
    public function getManualGenerated(): Collection
    {
        return WorkStatus::where('auto_generated', false)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 카테고리별 WorkStatus 조회
     */
    public function findByCategoryId(int $categoryId): Collection
    {
        return WorkStatus::where('category_id', $categoryId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 액션별 WorkStatus 조회
     */
    public function findByActionId(int $actionId): Collection
    {
        return WorkStatus::where('action_id', $actionId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 템플릿별 WorkStatus 조회
     */
    public function findByTemplateId(int $templateId): Collection
    {
        return WorkStatus::where('template_id', $templateId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * WorkStatus 생성 또는 조회
     */
    public function firstOrCreate(array $attributes, array $values = []): WorkStatus
    {
        return WorkStatus::firstOrCreate($attributes, $values);
    }

    /**
     * WorkStatus 생성
     */
    public function create(array $data): WorkStatus
    {
        return WorkStatus::create($data);
    }

    /**
     * WorkStatus 수정
     */
    public function update(int $id, array $data): WorkStatus
    {
        $workStatus = WorkStatus::findOrFail($id);
        $workStatus->update($data);

        return $workStatus;
    }

    /**
     * WorkStatus 삭제
     */
    public function delete(int $id): bool
    {
        $workStatus = WorkStatus::findOrFail($id);

        return $workStatus->delete();
    }

    /**
     * 관계 포함 WorkStatus 목록 조회
     */
    public function getAllWithRelations(): Collection
    {
        return WorkStatus::with(['category', 'action', 'template', 'logs'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 페이지네이션된 WorkStatus 목록 조회
     */
    public function getPaginated(int $perPage = 20, array $filters = []): LengthAwarePaginator
    {
        $query = WorkStatus::query();

        // 관계 포함
        $query->with(['category', 'action', 'template']);

        // 필터 적용
        if (isset($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (isset($filters['action_id'])) {
            $query->where('action_id', $filters['action_id']);
        }

        if (isset($filters['template_id'])) {
            $query->where('template_id', $filters['template_id']);
        }

        if (isset($filters['auto_generated'])) {
            $query->where('auto_generated', $filters['auto_generated']);
        }

        if (isset($filters['keyword'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%'.$filters['keyword'].'%')
                    ->orWhere('description', 'like', '%'.$filters['keyword'].'%')
                    ->orWhere('code', 'like', '%'.$filters['keyword'].'%');
            });
        }

        return $query->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * 최근 생성된 WorkStatus 조회
     */
    public function getRecent(int $limit = 10): Collection
    {
        return WorkStatus::where('auto_generated', true)
            ->with(['category', 'action', 'template'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * 자주 사용되는 WorkStatus 조회
     */
    public function getFrequentlyUsed(int $limit = 10): Collection
    {
        return WorkStatus::withCount('logs')
            ->where('auto_generated', true)
            ->orderBy('logs_count', 'desc')
            ->limit($limit)
            ->with(['category', 'action', 'template'])
            ->get();
    }

    /**
     * 통계 정보 조회
     */
    public function getStats(): array
    {
        $autoGenerated = WorkStatus::where('auto_generated', true)->count();
        $manualGenerated = WorkStatus::where('auto_generated', false)->count();
        $totalStatuses = $autoGenerated + $manualGenerated;

        return [
            'auto_generated' => $autoGenerated,
            'manual_generated' => $manualGenerated,
            'total_statuses' => $totalStatuses,
        ];
    }

    /**
     * 카테고리별 통계 조회
     */
    public function getStatsByCategory(int $categoryId): array
    {
        $statuses = WorkStatus::where('category_id', $categoryId)
            ->where('auto_generated', true)
            ->with(['action', 'template'])
            ->get();

        return $statuses->groupBy('action.code')
            ->map(function ($statuses, $actionCode) {
                return [
                    'action_code' => $actionCode,
                    'count' => $statuses->count(),
                    'templates' => $statuses->groupBy('template.template_code')->keys()->toArray(),
                ];
            })
            ->toArray();
    }

    /**
     * 템플릿별 사용 개수 조회
     */
    public function getCountByTemplate(int $templateId): int
    {
        return WorkStatus::where('template_id', $templateId)->count();
    }

    /**
     * WorkStatus 생성 통계 조회
     */
    public function getGenerationStats(): array
    {
        // 자동 생성된 전체 WorkStatus 개수
        $totalGenerated = WorkStatus::where('auto_generated', true)->count();

        // 카테고리별 통계 정보
        $categoriesStats = WorkStatus::where('auto_generated', true)
            ->with(['category', 'action'])
            ->get()
            ->groupBy('category.code')
            ->map(function ($statuses, $categoryCode) {
                return [
                    'category_code' => $categoryCode,
                    'category_name' => $statuses->first()->category->name ?? '',
                    'count' => $statuses->count(),
                    'actions' => $statuses->groupBy('action.code')->keys()->toArray(),
                ];
            })
            ->values()
            ->toArray();

        // 사용된 템플릿 개수
        $templatesUsed = WorkStatus::where('auto_generated', true)
            ->distinct('template_id')
            ->count();

        // 최근 7일 내 생성된 WorkStatus 개수
        $recentGenerationCount = WorkStatus::where('auto_generated', true)
            ->where('created_at', '>=', now()->subDays(7))
            ->count();

        return [
            'total_generated' => $totalGenerated,
            'categories' => $categoriesStats,
            'templates_used' => $templatesUsed,
            'recent_generation_count' => $recentGenerationCount,
        ];
    }

    /**
     * 조건에 맞는 자동 생성된 WorkStatus 조회
     */
    public function getAutoGeneratedByCondition(int $categoryId, int $actionId): array
    {
        return WorkStatus::where('category_id', $categoryId)
            ->where('action_id', $actionId)
            ->where('auto_generated', true)
            ->with(['category', 'action', 'template'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * 증상 코드에 따른 이름 조회 (캐싱 적용)
     */
    private function getSymptomName(string $symptomCode): string
    {
        $cacheKey = "symptom_name:{$symptomCode}";

        return cache()->remember($cacheKey, 3600, function () use ($symptomCode) {
            $symptom = $this->repairSymptom->where('code', $symptomCode)->first();

            return $symptom ? $symptom->name : $symptomCode;
        });
    }

    /**
     * 프로세스 코드에 따른 이름 조회 (캐싱 적용)
     */
    private function getProcessName(string $processCode): string
    {
        $cacheKey = "process_name:{$processCode}";

        return cache()->remember($cacheKey, 3600, function () use ($processCode) {
            $process = $this->repairProcess->where('code', $processCode)->first();

            return $process ? $process->name : $processCode;
        });
    }

    /**
     * 등급 코드에 따른 이름 조회 (캐싱 적용)
     */
    private function getGradeName(string $gradeCode): string
    {
        $cacheKey = "grade_name:{$gradeCode}";

        return cache()->remember($cacheKey, 3600, function () use ($gradeCode) {
            $grade = $this->repairGrade->where('code', $gradeCode)->first();

            return $grade ? $grade->name : $gradeCode;
        });
    }

    /**
     * 이름 생성 (컨텍스트 기반) - 배치 최적화
     */
    public function generateName(array $context): string
    {
        $nameParts = [];

        // 컨텍스트에서 이름 구성 요소 추출
        if (isset($context['detail']) && ! empty($context['detail'])) {
            $nameParts[] = $context['detail'];
        }

        if (isset($context['symptom_code']) && ! empty($context['symptom_code'])) {
            $nameParts[] = $this->getSymptomName($context['symptom_code']);
        }

        if (isset($context['process_code']) && ! empty($context['process_code'])) {
            $nameParts[] = $this->getProcessName($context['process_code']);
        }

        if (isset($context['grade_code']) && ! empty($context['grade_code'])) {
            $nameParts[] = $this->getGradeName($context['grade_code']);
        }

        if (isset($context['defect_type']) && ! empty($context['defect_type'])) {
            $nameParts[] = $context['defect_type'];
        }

        // 이름 생성 (기본값 포함)
        return ! empty($nameParts) ? implode(' - ', $nameParts) : '자동 생성 상태';
    }

    /**
     * 배치로 여러 코드의 이름을 한 번에 조회 (성능 최적화)
     */
    public function generateNameBatch(array $contexts): array
    {
        $results = [];

        // 모든 코드 수집
        $symptomCodes = [];
        $processCodes = [];
        $gradeCodes = [];

        foreach ($contexts as $index => $context) {
            if (isset($context['symptom_code'])) {
                $symptomCodes[] = $context['symptom_code'];
            }
            if (isset($context['process_code'])) {
                $processCodes[] = $context['process_code'];
            }
            if (isset($context['grade_code'])) {
                $gradeCodes[] = $context['grade_code'];
            }
        }

        // 배치로 데이터 조회
        $symptoms = $this->repairSymptom->whereIn('code', array_unique($symptomCodes))
            ->pluck('name', 'code');
        $processes = $this->repairProcess->whereIn('code', array_unique($processCodes))
            ->pluck('name', 'code');
        $grades = $this->repairGrade->whereIn('code', array_unique($gradeCodes))
            ->pluck('name', 'code');

        // 각 컨텍스트에 대해 이름 생성
        foreach ($contexts as $index => $context) {
            $nameParts = [];

            if (isset($context['detail']) && ! empty($context['detail'])) {
                $nameParts[] = $context['detail'];
            }

            if (isset($context['symptom_code']) && ! empty($context['symptom_code'])) {
                $nameParts[] = $symptoms[$context['symptom_code']] ?? $context['symptom_code'];
            }

            if (isset($context['process_code']) && ! empty($context['process_code'])) {
                $nameParts[] = $processes[$context['process_code']] ?? $context['process_code'];
            }

            if (isset($context['grade_code']) && ! empty($context['grade_code'])) {
                $nameParts[] = $grades[$context['grade_code']] ?? $context['grade_code'];
            }

            if (isset($context['defect_type']) && ! empty($context['defect_type'])) {
                $nameParts[] = $context['defect_type'];
            }

            $results[$index] = ! empty($nameParts) ? implode(' - ', $nameParts) : '자동 생성 상태';
        }

        return $results;
    }
}
