<?php

namespace App\Repositories;

use App\Models\NotificationTemplate;
use App\Repositories\Interfaces\NotificationTemplateRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class NotificationTemplateRepository implements NotificationTemplateRepositoryInterface
{
    /**
     * 모든 템플릿 조회
     */
    public function findAll(): Collection
    {
        return NotificationTemplate::orderBy('created_at', 'desc')->get();
    }

    /**
     * ID로 템플릿 조회
     */
    public function findById(int $id): ?NotificationTemplate
    {
        return NotificationTemplate::find($id);
    }

    /**
     * 템플릿명으로 조회
     */
    public function findByName(string $name): ?NotificationTemplate
    {
        return NotificationTemplate::where('name', $name)->first();
    }

    /**
     * 우선순위별 템플릿 조회
     */
    public function findByPriority(string $priority): Collection
    {
        return NotificationTemplate::byPriority($priority)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 템플릿 생성
     */
    public function create(array $data): NotificationTemplate
    {
        return NotificationTemplate::create($data);
    }

    /**
     * 템플릿 수정
     */
    public function update(int $id, array $data): NotificationTemplate
    {
        $template = NotificationTemplate::findOrFail($id);
        $template->update($data);

        return $template;
    }

    /**
     * 템플릿 삭제
     */
    public function delete(int $id): bool
    {
        $template = NotificationTemplate::findOrFail($id);

        return $template->delete();
    }

    /**
     * 페이지네이션된 템플릿 목록 조회
     */
    public function getPaginated(int $page = 1, int $perPage = 20, array $filters = []): LengthAwarePaginator
    {
        $query = $this->applyFiltersAndSorting(
            $this->baseQuery(false),
            $filters
        );

        return $query->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * 검색 및 필터링된 템플릿 조회
     */
    public function search(string $search = '', array $filters = []): Collection
    {
        $query = $this->applyFiltersAndSorting(
            $this->baseQuery(false),
            array_merge($filters, ['search' => $search])
        );

        return $query->get();
    }

    /**
     * 사용 횟수 기준 정렬된 템플릿 조회
     */
    public function getOrderedByUsage(string $direction = 'desc'): Collection
    {
        return NotificationTemplate::orderByUsage($direction)->get();
    }

    /**
     * 생성일 기준 정렬된 템플릿 조회
     */
    public function getOrderedByCreated(string $direction = 'desc'): Collection
    {
        return NotificationTemplate::orderByCreated($direction)->get();
    }

    /**
     * 생성자별 템플릿 조회
     */
    public function findByCreator(int $creatorId): Collection
    {
        return NotificationTemplate::where('created_by', $creatorId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 사용 횟수 증가
     */
    public function incrementUsage(int $id): bool
    {
        $template = NotificationTemplate::findOrFail($id);

        return $template->incrementUsage();
    }

    /**
     * 템플릿명 중복 검증
     */
    public function isDuplicateName(string $name, ?int $excludeId = null): bool
    {
        return NotificationTemplate::isDuplicateName($name, $excludeId);
    }

    /**
     * 관계 포함 템플릿 목록 조회
     */
    public function getAllWithRelations(): Collection
    {
        return NotificationTemplate::with(['creator'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 통계 정보 포함 페이지네이션 조회
     */
    public function getPaginatedWithStats(int $page = 1, int $perPage = 20, array $filters = []): LengthAwarePaginator
    {
        $query = $this->applyFiltersAndSorting(
            $this->baseQuery(true),
            $filters
        );

        return $query->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * 인기 템플릿 조회 (사용 횟수 상위)
     */
    public function getPopularTemplates(int $limit = 10): Collection
    {
        return NotificationTemplate::orderByUsage('desc')
            ->limit($limit)
            ->get();
    }

    /**
     * 최근 생성된 템플릿 조회
     */
    public function getRecentTemplates(int $limit = 10): Collection
    {
        return NotificationTemplate::orderByCreated('desc')
            ->limit($limit)
            ->get();
    }

    /**
     * 사용되지 않은 템플릿 조회
     */
    public function getUnusedTemplates(): Collection
    {
        return NotificationTemplate::where('usage_count', 0)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * 우선순위별 템플릿 개수 조회
     */
    public function getCountByPriority(): array
    {
        $counts = [];

        foreach (NotificationTemplate::PRIORITIES as $priority => $name) {
            $counts[$priority] = NotificationTemplate::byPriority($priority)->count();
        }

        return $counts;
    }

    /**
     * 생성자별 템플릿 개수 조회
     */
    public function getCountByCreator(): Collection
    {
        return NotificationTemplate::selectRaw('created_by, COUNT(*) as template_count')
            ->with(['creator:id,name'])
            ->groupBy('created_by')
            ->orderBy('template_count', 'desc')
            ->get();
    }

    /**
     * 템플릿 복사
     */
    public function duplicate(NotificationTemplate $template, string $newName, ?int $newCreatorId = null): NotificationTemplate
    {
        $newTemplate = $template->replicate();
        $newTemplate->name = $newName;
        $newTemplate->usage_count = 0; // 사용 횟수는 0으로 초기화
        $newTemplate->created_by = $newCreatorId ?? auth()->id();
        $newTemplate->save();

        return $newTemplate;
    }

    /**
     * 벌크 삭제
     */
    public function bulkDelete(array $ids): int
    {
        return NotificationTemplate::whereIn('id', $ids)->delete();
    }

    /**
     * 벌크 우선순위 업데이트
     */
    public function bulkUpdatePriority(array $ids, string $priority): int
    {
        return NotificationTemplate::whereIn('id', $ids)
            ->update(['priority' => $priority]);
    }

    /**
     * 템플릿 존재 여부 확인
     */
    public function exists(int $id): bool
    {
        return NotificationTemplate::where('id', $id)->exists();
    }

    /**
     * 템플릿명으로 존재 여부 확인
     */
    public function existsByName(string $name): bool
    {
        return NotificationTemplate::where('name', $name)->exists();
    }

    /**
     * 사용 통계 요약 정보 조회
     */
    public function getUsageStatistics(): array
    {
        $totalTemplates = NotificationTemplate::count();
        $totalUsage = NotificationTemplate::sum('usage_count');
        $averageUsage = $totalTemplates > 0 ? round($totalUsage / $totalTemplates, 2) : 0;
        $mostUsedTemplate = NotificationTemplate::orderByUsage('desc')->first();
        $unusedCount = NotificationTemplate::where('usage_count', 0)->count();

        return [
            'total_templates' => $totalTemplates,
            'total_usage' => $totalUsage,
            'average_usage' => $averageUsage,
            'most_used_template' => $mostUsedTemplate ? [
                'id' => $mostUsedTemplate->id,
                'name' => $mostUsedTemplate->name,
                'usage_count' => $mostUsedTemplate->usage_count,
            ] : null,
            'unused_templates_count' => $unusedCount,
            'usage_rate' => $totalTemplates > 0 ? round((($totalTemplates - $unusedCount) / $totalTemplates) * 100, 2) : 0,
        ];
    }

    /**
     * 사용 빈도별 템플릿 분포 조회
     */
    public function getUsageDistribution(): array
    {
        $distribution = [
            'unused' => 0,      // 0회
            'low' => 0,         // 1-10회
            'medium' => 0,      // 11-50회
            'high' => 0,        // 51-100회
            'very_high' => 0,    // 100회 초과
        ];

        $templates = NotificationTemplate::select('usage_count')->get();

        foreach ($templates as $template) {
            $usage = $template->usage_count;

            if ($usage === 0) {
                $distribution['unused']++;
            } elseif ($usage <= 10) {
                $distribution['low']++;
            } elseif ($usage <= 50) {
                $distribution['medium']++;
            } elseif ($usage <= 100) {
                $distribution['high']++;
            } else {
                $distribution['very_high']++;
            }
        }

        return $distribution;
    }

    /**
     * 사용 횟수 범위별 템플릿 조회
     */
    public function getTemplatesByUsageRange(int $minUsage = 0, ?int $maxUsage = null): Collection
    {
        $query = NotificationTemplate::where('usage_count', '>=', $minUsage);

        if ($maxUsage !== null) {
            $query->where('usage_count', '<=', $maxUsage);
        }

        return $query->orderByUsage('desc')->get();
    }

    /**
     * 공통 베이스 쿼리 빌더
     */
    private function baseQuery(bool $withRelations = false): Builder
    {
        $query = NotificationTemplate::query();

        if ($withRelations) {
            $query->with(['creator']);
        }

        return $query;
    }

    /**
     * 공통 필터/검색/정렬 적용
     */
    private function applyFiltersAndSorting(Builder $query, array $filters): Builder
    {
        // 검색어
        if (! empty($filters['search'])) {
            $query->search($filters['search']);
        }

        // 우선순위
        if (! empty($filters['priority'])) {
            $query->byPriority($filters['priority']);
        }

        // 생성자
        if (! empty($filters['created_by'])) {
            $query->where('created_by', $filters['created_by']);
        }

        // 사용 횟수 범위
        if (! empty($filters['min_usage'])) {
            $query->where('usage_count', '>=', $filters['min_usage']);
        }
        if (! empty($filters['max_usage'])) {
            $query->where('usage_count', '<=', $filters['max_usage']);
        }

        // 생성일 범위
        if (! empty($filters['created_from'])) {
            $query->where('created_at', '>=', $filters['created_from']);
        }
        if (! empty($filters['created_to'])) {
            $query->where('created_at', '<=', $filters['created_to']);
        }

        // 정렬
        if (! empty($filters['sortBy'])) {
            $direction = $filters['sortDirection'] ?? 'desc';

            switch ($filters['sortBy']) {
                case 'usage_count':
                    $query->orderByUsage($direction);
                    break;
                case 'created_at':
                    $query->orderByCreated($direction);
                    break;
                case 'name':
                    $query->orderBy('name', $direction);
                    break;
                case 'priority':
                    $query->orderBy('priority', $direction);
                    break;
                default:
                    $query->orderBy('created_at', 'desc');
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }

        return $query;
    }
}
