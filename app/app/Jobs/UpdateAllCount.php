<?php

namespace App\Jobs;

use App\Models\Req;
use App\Services\CountService;
use App\Services\SimpleLogService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class UpdateAllCount implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * 모든 요청서의 카운터 업데이트
     */
    public function handle(): void
    {
        try {
            // 6개월 전 입고목록부터 카운터 계산
            $targetDate = now()->subMonths(6)->startOfMonth()->toDateString();
            $reqs = Req::select('id')
                ->where('req_at', '>=', $targetDate)
                ->orderByDesc('id')
                ->get();

            $allTasksCompleted = true;
            $countService = new CountService;
            foreach ($reqs as $req) {
                try {
                    $countService->updateReqCount($req->id);
                } catch (Throwable $e) {
                    $allTasksCompleted = false;
                    SimpleLogService::error('req', '카운트 업데이트 작업 도중 에러 발생', [
                        'req_id' => $req->id,
                    ], $e);
                }
            }

            if ($allTasksCompleted) {
                SimpleLogService::info('req', '[스케줄러-카운터 업데이트]모든 작업이 성공적으로 완료되었습니다.');
            } else {
                SimpleLogService::warning('req', '[스케줄러-카운터 업데이트]일부 작업에 에러가 발생하였습니다.');
            }
        } catch (Throwable $e) {
            SimpleLogService::error('req', '[스케줄러-카운터 업데이트]실패', [], $e);
        }
    }
}
