<?php

namespace App\Jobs;

use App\Services\ProductService;
use App\Services\SimpleLogService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class UpdateDuplicateProducts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * 중복 상품을 찾아 Y로 변경하는 코드(단 기존에 Y로 변경된 것은 제외)
     */
    public function handle(): void
    {
        try {
            $productService = app(ProductService::class);
            $productService->markDuplicateProducts();
        } catch (Throwable $e) {
            SimpleLogService::error('product', '[스케줄러-중복상품 처리]실패', [], $e);
        }
    }
}
