<?php

namespace App\Exceptions;

/**
 * 템플릿 관련 예외 클래스
 *
 * 알림 템플릿 관련 비즈니스 로직 오류 시 발생하는 예외입니다.
 */
class TemplateException extends BusinessException
{
    protected string $module = 'template';

    /**
     * 템플릿을 찾을 수 없는 경우
     *
     * @param  int  $templateId  템플릿 ID
     */
    public static function notFound(int $templateId): self
    {
        $message = "템플릿 [ID: {$templateId}]을 찾을 수 없습니다.";

        return new self($message, [
            'template_id' => $templateId,
            'type' => 'not_found',
        ]);
    }

    /**
     * 중복된 템플릿명인 경우
     *
     * @param  string  $templateName  템플릿명
     */
    public static function duplicateName(string $templateName): self
    {
        $message = "이미 존재하는 템플릿명 [{$templateName}]입니다.";

        return new self($message, [
            'template_name' => $templateName,
            'type' => 'duplicate_name',
        ]);
    }

    /**
     * 템플릿 생성 실패 시
     *
     * @param  string  $templateName  템플릿명
     * @param  string  $reason  실패 사유
     */
    public static function createFailed(string $templateName, string $reason = ''): self
    {
        $message = "템플릿 [{$templateName}] 생성에 실패했습니다.";
        if ($reason) {
            $message .= " 사유: {$reason}";
        }

        return new self($message, [
            'template_name' => $templateName,
            'reason' => $reason,
            'type' => 'create_failed',
        ]);
    }

    /**
     * 템플릿 수정 실패 시
     *
     * @param  int  $templateId  템플릿 ID
     * @param  string  $reason  실패 사유
     */
    public static function updateFailed(int $templateId, string $reason = ''): self
    {
        $message = "템플릿 [ID: {$templateId}] 수정에 실패했습니다.";
        if ($reason) {
            $message .= " 사유: {$reason}";
        }

        return new self($message, [
            'template_id' => $templateId,
            'reason' => $reason,
            'type' => 'update_failed',
        ]);
    }

    /**
     * 템플릿 삭제 실패 시
     *
     * @param  int  $templateId  템플릿 ID
     * @param  string  $reason  실패 사유
     */
    public static function deleteFailed(int $templateId, string $reason = ''): self
    {
        $message = "템플릿 [ID: {$templateId}] 삭제에 실패했습니다.";
        if ($reason) {
            $message .= " 사유: {$reason}";
        }

        return new self($message, [
            'template_id' => $templateId,
            'reason' => $reason,
            'type' => 'delete_failed',
        ]);
    }

    /**
     * 사용 횟수 증가 실패 시
     *
     * @param  int  $templateId  템플릿 ID
     */
    public static function incrementUsageFailed(int $templateId): self
    {
        $message = "템플릿 [ID: {$templateId}] 사용 횟수 증가에 실패했습니다.";

        return new self($message, [
            'template_id' => $templateId,
            'type' => 'increment_usage_failed',
        ]);
    }

    /**
     * 권한 부족으로 템플릿 접근 불가 시
     *
     * @param  int  $templateId  템플릿 ID
     * @param  int  $userId  사용자 ID
     */
    public static function accessDenied(int $templateId, int $userId): self
    {
        $message = "템플릿 [ID: {$templateId}]에 대한 접근 권한이 없습니다.";

        return new self($message, [
            'template_id' => $templateId,
            'user_id' => $userId,
            'type' => 'access_denied',
        ]);
    }

    /**
     * 잘못된 우선순위 값인 경우
     *
     * @param  string  $priority  잘못된 우선순위 값
     */
    public static function invalidPriority(string $priority): self
    {
        $validPriorities = implode(', ', array_keys(\App\Models\NotificationTemplate::PRIORITIES));
        $message = "유효하지 않은 우선순위 [{$priority}]입니다. 유효한 값: {$validPriorities}";

        return new self($message, [
            'invalid_priority' => $priority,
            'valid_priorities' => array_keys(\App\Models\NotificationTemplate::PRIORITIES),
            'type' => 'invalid_priority',
        ]);
    }

    /**
     * 템플릿 데이터 유효성 검증 실패 시
     *
     * @param  array  $validationErrors  유효성 검증 오류 목록
     */
    public static function validationFailed(array $validationErrors): self
    {
        $message = '템플릿 데이터 유효성 검증에 실패했습니다.';

        return new self($message, [
            'validation_errors' => $validationErrors,
            'type' => 'validation_failed',
        ]);
    }
}
