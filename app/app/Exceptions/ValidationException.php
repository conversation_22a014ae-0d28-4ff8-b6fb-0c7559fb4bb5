<?php

namespace App\Exceptions;

/**
 * 데이터 검증 예외 클래스
 *
 * 입력 데이터 검증 실패 시 발생하는 예외입니다.
 * HTTP 상태 코드 422 (Unprocessable Entity)와 함께 사용됩니다.
 */
class ValidationException extends WmsException
{
    protected string $module = 'validation';

    public function __construct(string $message, array $context = [], ?\Throwable $previous = null)
    {
        parent::__construct($message, 422, $previous, $context);
    }

    /**
     * 필드별 검증 오류를 위한 정적 생성자
     */
    public static function forField(string $field, string $message): self
    {
        return new self($message, ['field' => $field]);
    }

    /**
     * 여러 필드 검증 오류를 위한 정적 생성자
     */
    public static function forFields(array $errors): self
    {
        $message = '입력 데이터 검증에 실패했습니다.';

        return new self($message, ['errors' => $errors]);
    }
}
