<?php

namespace App\Exceptions;

use Exception;
use Throwable;

/**
 * WMS 시스템의 기본 예외 클래스
 *
 * 모든 WMS 관련 예외의 기본 클래스로 사용됩니다.
 * 모듈 정보와 컨텍스트 정보를 포함할 수 있습니다.
 */
abstract class WmsException extends Exception
{
    protected string $module;

    protected array $context = [];

    public function __construct(string $message, int $code = 0, ?Throwable $previous = null, array $context = [])
    {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    /**
     * 예외가 발생한 모듈을 반환합니다.
     */
    public function getModule(): string
    {
        return $this->module;
    }

    /**
     * 예외 컨텍스트 정보를 반환합니다.
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * 컨텍스트 정보를 설정합니다.
     */
    public function setContext(array $context): self
    {
        $this->context = $context;

        return $this;
    }

    /**
     * 컨텍스트에 정보를 추가합니다.
     */
    public function addContext(string $key, mixed $value): self
    {
        $this->context[$key] = $value;

        return $this;
    }
}
