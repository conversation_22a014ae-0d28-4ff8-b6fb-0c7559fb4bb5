<?php

namespace App\Exceptions;

use App\Services\SimpleLogService;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * 에러 코드 상수 정의
     */
    public const ERROR_CODES = [
        // 일반 에러
        'INTERNAL_SERVER_ERROR' => 'INTERNAL_SERVER_ERROR',
        'VALIDATION_ERROR' => 'VALIDATION_ERROR',
        'UNAUTHORIZED' => 'UNAUTHORIZED',
        'FORBIDDEN' => 'FORBIDDEN',
        'NOT_FOUND' => 'NOT_FOUND',
        'METHOD_NOT_ALLOWED' => 'METHOD_NOT_ALLOWED',
        'UNPROCESSABLE_ENTITY' => 'UNPROCESSABLE_ENTITY',

        // 템플릿 관련 에러
        'TEMPLATE_NOT_FOUND' => 'TEMPLATE_NOT_FOUND',
        'TEMPLATE_DUPLICATE_NAME' => 'TEMPLATE_DUPLICATE_NAME',
        'TEMPLATE_CREATE_FAILED' => 'TEMPLATE_CREATE_FAILED',
        'TEMPLATE_UPDATE_FAILED' => 'TEMPLATE_UPDATE_FAILED',
        'TEMPLATE_DELETE_FAILED' => 'TEMPLATE_DELETE_FAILED',
        'TEMPLATE_ACCESS_DENIED' => 'TEMPLATE_ACCESS_DENIED',
        'TEMPLATE_VALIDATION_FAILED' => 'TEMPLATE_VALIDATION_FAILED',
        'TEMPLATE_INVALID_PRIORITY' => 'TEMPLATE_INVALID_PRIORITY',
        'TEMPLATE_INCREMENT_USAGE_FAILED' => 'TEMPLATE_INCREMENT_USAGE_FAILED',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            // WMS 예외에 대한 로깅 처리
            if ($e instanceof WmsException) {
                $this->logWmsException($e);
            }
        });

        // 전역 예외 렌더링 처리
        $this->renderable(function (Throwable $e, Request $request) {
            if ($request->expectsJson()) {
                return $this->renderExceptionAsJson($e, $request);
            }
        });
    }

    /**
     * WMS 예외를 로깅합니다.
     */
    protected function logWmsException(WmsException $exception): void
    {
        $module = $exception->getModule();
        $context = $exception->getContext();

        // 예외 타입에 따라 로그 레벨 결정
        if ($exception instanceof \App\Exceptions\ValidationException || $exception instanceof BusinessException) {
            // 비즈니스 로직 오류는 warning 레벨
            SimpleLogService::warning($module, $exception->getMessage(), $context, $exception);
        } elseif ($exception instanceof ResourceNotFoundException) {
            // 리소스 없음은 info 레벨
            SimpleLogService::info($module, $exception->getMessage(), $context);
        } else {
            // 기타 시스템 오류는 error 레벨
            SimpleLogService::error($module, $exception->getMessage(), $context, $exception);
        }
    }

    /**
     * 모든 예외를 표준화된 JSON 응답으로 렌더링합니다.
     */
    protected function renderExceptionAsJson(Throwable $exception, Request $request): JsonResponse
    {
        // 알림 히스토리 API에 대한 특별한 에러 처리
        if ($this->isNotificationHistoryApiRequest($request)) {
            return $this->renderNotificationHistoryExceptionAsJson($exception);
        }

        // WMS 예외인 경우 기존 로직 사용
        if ($exception instanceof WmsException) {
            return $this->renderWmsExceptionAsJson($exception);
        }

        // 기타 Laravel 예외들을 표준화된 형식으로 변환
        return $this->renderStandardExceptionAsJson($exception);
    }

    /**
     * WMS 예외를 JSON 응답으로 렌더링합니다.
     */
    protected function renderWmsExceptionAsJson(WmsException $exception): JsonResponse
    {
        $errorCode = $this->getErrorCodeFromException($exception);
        $statusCode = $this->getHttpStatusCodeFromException($exception);

        $response = [
            'success' => false,
            'error' => [
                'code' => $errorCode,
                'message' => $exception->getMessage(),
                'module' => $exception->getModule(),
            ],
        ];

        // 컨텍스트 정보가 있으면 포함
        if (! empty($exception->getContext())) {
            $response['error']['details'] = $exception->getContext();
        }

        // 개발 환경에서는 추가 정보 포함
        if ($this->isDebugMode()) {
            $response['error']['trace'] = $exception->getTraceAsString();
            $response['error']['file'] = $exception->getFile();
            $response['error']['line'] = $exception->getLine();
        }

        return new JsonResponse($response, $statusCode);
    }

    /**
     * 표준 Laravel 예외를 JSON 응답으로 렌더링합니다.
     */
    protected function renderStandardExceptionAsJson(Throwable $exception): JsonResponse
    {
        $statusCode = $this->getHttpStatusCodeFromException($exception);
        $errorCode = $this->getErrorCodeFromHttpStatus($statusCode);
        $message = $this->getMessageFromException($exception);

        $response = [
            'success' => false,
            'error' => [
                'code' => $errorCode,
                'message' => $message,
            ],
        ];

        // ValidationException의 경우 상세 오류 정보 포함
        if ($exception instanceof ValidationException) {
            $response['error']['details'] = [
                'validation_errors' => $exception->errors(),
            ];
        }

        // ModelNotFoundException의 경우 리소스 정보 포함
        if ($exception instanceof ModelNotFoundException) {
            $response['error']['details'] = [
                'model' => class_basename($exception->getModel()),
                'ids' => $exception->getIds(),
            ];
        }

        // 개발 환경에서는 추가 정보 포함
        if ($this->isDebugMode()) {
            $response['error']['trace'] = $exception->getTraceAsString();
            $response['error']['file'] = $exception->getFile();
            $response['error']['line'] = $exception->getLine();
        }

        return new JsonResponse($response, $statusCode);
    }

    /**
     * WMS 예외로부터 에러 코드를 추출합니다.
     */
    protected function getErrorCodeFromException(WmsException $exception): string
    {
        // 템플릿 예외인 경우
        if ($exception instanceof TemplateException) {
            $context = $exception->getContext();
            $type = $context['type'] ?? 'unknown';

            return match ($type) {
                'not_found' => self::ERROR_CODES['TEMPLATE_NOT_FOUND'],
                'duplicate_name' => self::ERROR_CODES['TEMPLATE_DUPLICATE_NAME'],
                'create_failed' => self::ERROR_CODES['TEMPLATE_CREATE_FAILED'],
                'update_failed' => self::ERROR_CODES['TEMPLATE_UPDATE_FAILED'],
                'delete_failed' => self::ERROR_CODES['TEMPLATE_DELETE_FAILED'],
                'access_denied' => self::ERROR_CODES['TEMPLATE_ACCESS_DENIED'],
                'validation_failed' => self::ERROR_CODES['TEMPLATE_VALIDATION_FAILED'],
                'invalid_priority' => self::ERROR_CODES['TEMPLATE_INVALID_PRIORITY'],
                'increment_usage_failed' => self::ERROR_CODES['TEMPLATE_INCREMENT_USAGE_FAILED'],
                default => self::ERROR_CODES['INTERNAL_SERVER_ERROR']
            };
        }

        // 기타 WMS 예외
        if ($exception instanceof \App\Exceptions\ValidationException) {
            return self::ERROR_CODES['VALIDATION_ERROR'];
        }

        if ($exception instanceof ResourceNotFoundException) {
            return self::ERROR_CODES['NOT_FOUND'];
        }

        return self::ERROR_CODES['INTERNAL_SERVER_ERROR'];
    }

    /**
     * HTTP 상태 코드로부터 에러 코드를 가져옵니다.
     */
    protected function getErrorCodeFromHttpStatus(int $statusCode): string
    {
        return match ($statusCode) {
            400 => self::ERROR_CODES['VALIDATION_ERROR'],
            401 => self::ERROR_CODES['UNAUTHORIZED'],
            403 => self::ERROR_CODES['FORBIDDEN'],
            404 => self::ERROR_CODES['NOT_FOUND'],
            405 => self::ERROR_CODES['METHOD_NOT_ALLOWED'],
            422 => self::ERROR_CODES['UNPROCESSABLE_ENTITY'],
            default => self::ERROR_CODES['INTERNAL_SERVER_ERROR']
        };
    }

    /**
     * 예외로부터 HTTP 상태 코드를 가져옵니다.
     */
    protected function getHttpStatusCodeFromException(Throwable $exception): int
    {
        // WMS 예외인 경우
        if ($exception instanceof WmsException) {
            if ($exception instanceof TemplateException) {
                $context = $exception->getContext();
                $type = $context['type'] ?? 'unknown';

                return match ($type) {
                    'not_found' => 404,
                    'duplicate_name' => 409,
                    'access_denied' => 403,
                    'validation_failed' => 422,
                    'invalid_priority' => 422,
                    default => 500
                };
            }

            if ($exception instanceof \App\Exceptions\ValidationException) {
                return 422;
            }

            if ($exception instanceof ResourceNotFoundException) {
                return 404;
            }

            return $exception->getCode() ?: 500;
        }

        // Laravel 기본 예외들
        if ($exception instanceof AuthenticationException) {
            return 401;
        }

        if ($exception instanceof AccessDeniedHttpException) {
            return 403;
        }

        if ($exception instanceof NotFoundHttpException || $exception instanceof ModelNotFoundException) {
            return 404;
        }

        if ($exception instanceof ValidationException) {
            return 422;
        }

        return 500;
    }

    /**
     * 예외로부터 사용자 친화적인 메시지를 가져옵니다.
     */
    protected function getMessageFromException(Throwable $exception): string
    {
        if ($exception instanceof AuthenticationException) {
            return '인증이 필요합니다.';
        }

        if ($exception instanceof AccessDeniedHttpException) {
            return '접근 권한이 없습니다.';
        }

        if ($exception instanceof NotFoundHttpException) {
            return '요청한 리소스를 찾을 수 없습니다.';
        }

        if ($exception instanceof ModelNotFoundException) {
            return '요청한 데이터를 찾을 수 없습니다.';
        }

        if ($exception instanceof ValidationException) {
            return '입력 데이터가 유효하지 않습니다.';
        }

        // 개발 환경에서는 실제 메시지, 운영 환경에서는 일반적인 메시지
        if ($this->isDebugMode()) {
            return $exception->getMessage();
        }

        return '서버 내부 오류가 발생했습니다.';
    }

    /**
     * 알림 히스토리 API 요청인지 확인합니다.
     */
    protected function isNotificationHistoryApiRequest(Request $request): bool
    {
        return $request->is('wms/notifications/histories*');
    }

    /**
     * 알림 히스토리 API에 특화된 예외 처리를 수행합니다.
     */
    protected function renderNotificationHistoryExceptionAsJson(Throwable $exception): JsonResponse
    {
        $statusCode = $this->getHttpStatusCodeFromException($exception);

        $response = [
            'success' => false,
            'message' => $this->getNotificationHistoryErrorMessage($exception),
        ];

        // 404 에러 처리
        if ($exception instanceof ModelNotFoundException || $exception instanceof NotFoundHttpException) {
            $response['message'] = '알림을 찾을 수 없습니다.';
            $statusCode = 404;
        }

        // 422 에러 처리 (Validation 에러)
        elseif ($exception instanceof ValidationException) {
            $response['message'] = '입력값이 올바르지 않습니다.';
            $response['errors'] = $exception->errors();
            $statusCode = 422;
        }

        // 401 에러 처리 (인증 에러)
        elseif ($exception instanceof AuthenticationException) {
            $response['message'] = '로그인이 필요합니다.';
            $statusCode = 401;
        }

        // 403 에러 처리 (권한 에러)
        elseif ($exception instanceof AccessDeniedHttpException) {
            $response['message'] = '관리자 권한이 필요합니다.';
            $statusCode = 403;
        }

        // 기타 서버 에러
        else {
            $response['message'] = '서버 내부 오류가 발생했습니다.';
            $statusCode = 500;
        }

        // 개발 환경에서는 디버그 정보 추가
        if ($this->isDebugMode()) {
            $response['debug'] = [
                'exception' => get_class($exception),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
            ];
        }

        return new JsonResponse($response, $statusCode);
    }

    /**
     * 알림 히스토리 API에 특화된 에러 메시지를 가져옵니다.
     */
    protected function getNotificationHistoryErrorMessage(Throwable $exception): string
    {
        if ($exception instanceof ModelNotFoundException) {
            return '알림을 찾을 수 없습니다.';
        }

        if ($exception instanceof NotFoundHttpException) {
            return '요청한 페이지를 찾을 수 없습니다.';
        }

        if ($exception instanceof ValidationException) {
            return '입력값이 올바르지 않습니다.';
        }

        if ($exception instanceof AuthenticationException) {
            return '로그인이 필요합니다.';
        }

        if ($exception instanceof AccessDeniedHttpException) {
            return '관리자 권한이 필요합니다.';
        }

        // 개발 환경에서는 실제 메시지, 운영 환경에서는 일반적인 메시지
        if ($this->isDebugMode()) {
            return $exception->getMessage();
        }

        return '서버 내부 오류가 발생했습니다.';
    }

    /**
     * 디버그 모드인지 확인합니다.
     */
    public function isDebugMode(): bool
    {
        try {
            return config('app.debug', false);
        } catch (\Throwable $e) {
            // 테스트 환경에서 config가 없을 경우 false 반환
            return false;
        }
    }
}
