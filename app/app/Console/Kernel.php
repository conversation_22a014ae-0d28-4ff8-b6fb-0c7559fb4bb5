<?php

namespace App\Console;

use App\Jobs\UpdateAllCount;
use App\Jobs\UpdateDuplicateProducts;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // 0: 일요일, 1: 월요일, 2: 화요일, 3: 수요일, 4: 목요일, 5: 금요일, 6: 토요일
        // $schedule->job(new UpdateDuplicateProducts)
        //     ->days([3, 4, 5, 6])
        //     ->at('04:00');

        $schedule->job(new UpdateAllCount)
            ->days([0, 2, 3, 4, 5, 6])
            ->at('05:00');

        // // SSE 오프라인 알림 정리 (매일 새벽 2시)
        // $schedule->command('sse:cleanup-notifications')
        //     ->daily()
        //     ->at('02:00')
        //     ->withoutOverlapping()
        //     ->runInBackground();
        //
        // // 메모리 최적화 작업 (매시간 실행)
        // $schedule->command('memory:optimize schedule')
        //     ->hourly()
        //     ->withoutOverlapping()
        //     ->runInBackground();
        //
        // // 알림 설정 캐시 최적화 (매일 새벽 3시)
        // $schedule->command('notification:optimize-cache cleanup --days=7')
        //     ->daily()
        //     ->at('03:00')
        //     ->withoutOverlapping()
        //     ->runInBackground();
        //
        // // SSE 알림 히스토리 정리 (매일 새벽 1시)
        // $schedule->command('sse:cleanup-history --days=30')
        //     ->daily()
        //     ->at('01:00')
        //     ->withoutOverlapping()
        //     ->runInBackground()
        //     ->onSuccess(function () {
        //         \Log::info('SSE 알림 히스토리 정리 작업 완료');
        //     })
        //     ->onFailure(function () {
        //         \Log::error('SSE 알림 히스토리 정리 작업 실패');
        //     });
        //
        // // SSE 알림 설정 동기화 (매주 일요일 새벽 2시)
        // $schedule->command('sse:sync-settings --repair')
        //     ->weekly()
        //     ->sundays()
        //     ->at('02:00')
        //     ->withoutOverlapping()
        //     ->runInBackground()
        //     ->onSuccess(function () {
        //         \Log::info('SSE 알림 설정 동기화 작업 완료');
        //     })
        //     ->onFailure(function () {
        //         \Log::error('SSE 알림 설정 동기화 작업 실패');
        //     });
        //
        // // SSE 데이터 버전 히스토리 정리 (매주 토요일 새벽 3시)
        // $schedule->command('sse:manage-versions cleanup --type=all --days=7')
        //     ->weekly()
        //     ->saturdays()
        //     ->at('03:00')
        //     ->withoutOverlapping()
        //     ->runInBackground()
        //     ->onSuccess(function () {
        //         \Log::info('SSE 데이터 버전 히스토리 정리 작업 완료');
        //     })
        //     ->onFailure(function () {
        //         \Log::error('SSE 데이터 버전 히스토리 정리 작업 실패');
        //     });
        //
        // // SSE 메트릭 수집 작업들
        //
        // // 히스토리 메트릭 수집 (매시간)
        // $schedule->command('sse:collect-metrics --type=history')
        //     ->hourly()
        //     ->withoutOverlapping()
        //     ->runInBackground()
        //     ->onSuccess(function () {
        //         \Log::info('SSE 히스토리 메트릭 수집 완료');
        //     })
        //     ->onFailure(function () {
        //         \Log::error('SSE 히스토리 메트릭 수집 실패');
        //     });
        //
        // // 동기화 메트릭 수집 (매시간)
        // $schedule->command('sse:collect-metrics --type=sync')
        //     ->hourly()
        //     ->withoutOverlapping()
        //     ->runInBackground()
        //     ->onSuccess(function () {
        //         \Log::info('SSE 동기화 메트릭 수집 완료');
        //     })
        //     ->onFailure(function () {
        //         \Log::error('SSE 동기화 메트릭 수집 실패');
        //     });
        //
        // // 캐시 메트릭 수집 (매 30분)
        // $schedule->command('sse:collect-metrics --type=cache')
        //     ->everyThirtyMinutes()
        //     ->withoutOverlapping()
        //     ->runInBackground()
        //     ->onSuccess(function () {
        //         \Log::info('SSE 캐시 메트릭 수집 완료');
        //     })
        //     ->onFailure(function () {
        //         \Log::error('SSE 캐시 메트릭 수집 실패');
        //     });
        //
        // // 전체 메트릭 수집 (매일 새벽 4시)
        // $schedule->command('sse:collect-metrics --type=all')
        //     ->daily()
        //     ->at('04:00')
        //     ->withoutOverlapping()
        //     ->runInBackground()
        //     ->onSuccess(function () {
        //         \Log::info('SSE 전체 메트릭 수집 완료');
        //     })
        //     ->onFailure(function () {
        //         \Log::error('SSE 전체 메트릭 수집 실패');
        //     });
        //
        // // SSE 메트릭 정리 (매주 일요일 새벽 5시 - 30일 이상 된 데이터 삭제)
        // $schedule->command('sse:cleanup-metrics --days=30 --force')
        //     ->weekly()
        //     ->sundays()
        //     ->at('05:00')
        //     ->withoutOverlapping()
        //     ->runInBackground()
        //     ->onSuccess(function () {
        //         \Log::info('SSE 메트릭 정리 작업 완료');
        //     })
        //     ->onFailure(function () {
        //         \Log::error('SSE 메트릭 정리 작업 실패');
        //     });
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
