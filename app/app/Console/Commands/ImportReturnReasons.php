<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Models\ReturnReason;
use App\Models\ReturnReasonB;
use App\Models\ReturnReasonM;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Throwable;

class ImportReturnReasons extends Command
{
    /**
     * 커맨드 시그니처
     */
    protected $signature = 'import:return-reasons
        {--delete-after : 처리 성공 시 파일 삭제}
        {--dry-run : 실제 DB 변경 없이 시뮬레이션만 수행}
        {--limit= : 처리(또는 시뮬레이션)할 최대 행 수(전체)}';

    /**
     * 커맨드 설명
     */
    protected $description = 'storage/app/import_return_reason 디렉토리의 CSV 파일을 읽어 반품 사유를 저장합니다.';

    // 배치/배치 플러그인용 캐시 및 플래시 설정
    protected bool $dryRunMode = false;

    protected array $batchBRNames = [];

    protected array $batchMRNames = [];

    protected array $categoryBNameToIdCache = [];

    protected array $categoryMNameToIdCache = [];

    protected int $categoryBatchFlushSize = 1000;

    /**
     * 커맨드 실행
     */
    public function handle(): int
    {
        $dir = storage_path('app/import_return_reason');

        if (! is_dir($dir)) {
            $this->error("디렉토리를 찾을 수 없습니다: {$dir}");

            return 1;
        }

        $files = glob($dir.'/*.csv');
        if (! $files || count($files) === 0) {
            $this->info('처리할 CSV 파일이 없습니다.');

            return 0;
        }

        // 옵션 처리: dry-run 이면 limit 기본값 20
        $dryRun = (bool) $this->option('dry-run');
        $this->dryRunMode = $dryRun;
        $limitOpt = $this->option('limit');
        $globalLimit = null;

        if ($limitOpt !== null && $limitOpt !== false && $limitOpt !== '') {
            $globalLimit = (int) $limitOpt;
            if ($globalLimit <= 0) {
                $globalLimit = null; // 0 이하이면 제한 없음으로 간주
            }
        } elseif ($dryRun) {
            $globalLimit = 20; // dry-run 기본 20
        }

        if ($dryRun) {
            $this->warn('DRY-RUN 모드: DB 변경 없이 시뮬레이션합니다.'.($globalLimit ? " (최대 {$globalLimit}행)" : ''));
            if ($this->option('delete-after')) {
                $this->warn('--delete-after 옵션은 DRY-RUN에서는 무시됩니다.');
            }
        }

        $totalProcessed = 0;
        $totalSkipped = 0;
        $totalFailed = 0;

        foreach ($files as $file) {
            // 남은 제한 계산
            $remainingLimit = $globalLimit;
            if ($globalLimit !== null) {
                $remainingLimit = max(0, $globalLimit - $totalProcessed);
                if ($remainingLimit === 0) {
                    break; // 전역 제한 도달
                }
            }

            $this->line('처리 시작: '.basename($file));
            [$processed, $skipped, $failed] = $this->processFile($file, $dryRun, $remainingLimit);
            $this->line("파일 결과 - 처리: {$processed}, 건너뜀: {$skipped}, 실패: {$failed}");

            $totalProcessed += $processed;
            $totalSkipped += $skipped;
            $totalFailed += $failed;

            if (! $dryRun && $this->option('delete-after') && $failed === 0) {
                @unlink($file);
            }

            // 전역 제한 도달 시 중단
            if ($globalLimit !== null && $totalProcessed >= $globalLimit) {
                break;
            }
        }

        $this->info("전체 결과 - 처리: {$totalProcessed}, 건너뜀: {$totalSkipped}, 실패: {$totalFailed}");

        return 0;
    }

    /**
     * 단일 파일 처리
     *
     * @param  int|null  $limit  전체 실행 기준 잔여 처리 제한 (null이면 무제한)
     */
    private function processFile(string $file, bool $dryRun, ?int $limit = null): array
    {
        $processed = 0;
        $skipped = 0;
        $failed = 0;

        $handle = fopen($file, 'r');
        if ($handle === false) {
            $this->error("파일을 열 수 없습니다: {$file}");

            return [0, 0, 1];
        }

        $rowNum = 0;
        $headerMap = null;

        while (($row = fgetcsv($handle)) !== false) {
            $rowNum++;

            // limit 도달 시 조기 종료
            if ($limit !== null && $processed >= $limit) {
                break;
            }

            // 첫 줄 헤더 처리
            if ($rowNum === 1) {
                if (isset($row[0])) {
                    $row[0] = $this->stripBom((string) $row[0]);
                }
                $headerMap = $this->buildHeaderMap($row);

                // 유효한 헤더라면 다음 줄부터 데이터 처리
                if ($this->isHeaderValid($headerMap)) {
                    continue;
                }

                // 헤더가 없다고 판단되면 고정 인덱스 매핑으로 현재 줄부터 데이터 처리
                $headerMap = [
                    'qaid' => 0,
                    'bcate' => 1,
                    'mcate' => 2,
                    'detailreason' => 3,
                ];
                // 이후 로직이 현재 줄(row)을 데이터로 처리
            }

            try {
                $qaid = $this->getCol($row, $headerMap, 'qaid');
                $bCateName = $this->getCol($row, $headerMap, 'bcate');
                $mCateName = $this->getCol($row, $headerMap, 'mcate');
                $detailReason = $this->getCol($row, $headerMap, 'detailreason');

                if ($qaid === null || $qaid === '') {
                    $skipped++;

                    continue;
                }

                // 최근 req_id 기준으로 Product 조회
                $product = Product::where('qaid', $qaid)->orderByDesc('req_id')->first();
                if (! $product) {
                    $skipped++;

                    continue;
                }

                if ($dryRun) {
                    // 이미 ReturnReason 존재 여부 확인 (건너뛰기)
                    $existingReason = ReturnReason::where('product_id', $product->id)->exists();
                    if ($existingReason) {
                        $this->line("[DRY-RUN] QAID={$qaid} - 이미 ReturnReason 존재 (product_id={$product->id}), 스킵");
                        $processed++;

                        continue;
                    }

                    // 카테고리 탐색(생성하지 않음)
                    $bId = null;
                    $mId = null;

                    if ($bCateName !== null && $bCateName !== '') {
                        $bId = ReturnReasonB::where('name', trim($bCateName))->value('id');
                    }
                    if ($mCateName !== null && $mCateName !== '') {
                        $mId = ReturnReasonM::where('name', trim($mCateName))->value('id');
                    }

                    // 시뮬레이션 출력
                    $msg = sprintf(
                        '[DRY-RUN] QAID=%s -> product_id=%d, set B=%s, M=%s%s',
                        $qaid,
                        $product->id,
                        $bId !== null ? "id={$bId}" : ($bCateName ? "NEW(\"{$bCateName}\")" : 'skip'),
                        $mId !== null ? "id={$mId}" : ($mCateName ? "NEW(\"{$mCateName}\")" : 'skip'),
                        ($detailReason !== null && $detailReason !== '') ? ', reason="'.trim($detailReason).'"' : ''
                    );
                    $this->line($msg);

                    $processed++;

                    continue;
                }

                // 실제 저장 로직
                // bCate, mCate 즉시 ID 확보(캐시/조회/삽입-재조회)
                $bId = null;
                $mId = null;

                if ($bCateName !== null && $bCateName !== '') {
                    $bId = $this->getOrCreateBIdByName(trim($bCateName));
                }
                if ($mCateName !== null && $mCateName !== '') {
                    $mId = $this->getOrCreateMIdByName(trim($mCateName));
                }

                // Product 업데이트 (존재하는 항목만)
                $updates = [];
                if ($bId !== null) {
                    $updates['return_reason_b_id'] = $bId;
                }
                if ($mId !== null) {
                    $updates['return_reason_m_id'] = $mId;
                }
                if (! empty($updates)) {
                    $product->fill($updates);
                    $product->save();
                }

                // ReturnReason 저장 (DetailReason -> reason) - 이미 존재하면 건너뛰기
                if ($detailReason !== null && $detailReason !== '') {
                    $exists = ReturnReason::where('product_id', $product->id)->exists();
                    if (! $exists) {
                        ReturnReason::create([
                            'product_id' => $product->id,
                            'reason' => trim($detailReason),
                        ]);
                    }
                }

                $processed++;
            } catch (Throwable $e) {
                $failed++;
                $this->error("행 {$rowNum} 처리 실패: ".$e->getMessage());
            }
        }

        // 남은 배치를 한번 더 처리
        $this->flushCategoryBatch();

        fclose($handle);

        return [$processed, $skipped, $failed];
    }

    /**
     * BOM 제거
     */
    private function stripBom(string $text): string
    {
        if (str_starts_with($text, "\xEF\xBB\xBF")) {
            return substr($text, 3);
        }

        return $text;
    }

    /**
     * 헤더를 정규화하여 컬럼 인덱스 매핑 구성
     */
    private function buildHeaderMap(array $header): array
    {
        $mapped = [];
        foreach ($header as $index => $name) {
            $key = strtolower(preg_replace('/[^a-z0-9_]/i', '', (string) $name));
            $mapped[$key] = $index;
        }

        return [
            'qaid' => $mapped['qaid'] ?? null,
            'bcate' => $mapped['bcate'] ?? null,
            'mcate' => $mapped['mcate'] ?? null,
            'detailreason' => $mapped['detailreason'] ?? null,
        ];
    }

    /**
     * 필수 컬럼이 모두 존재하는지 검사
     */
    private function isHeaderValid(array $map): bool
    {
        return $map['qaid'] !== null
            && $map['bcate'] !== null
            && $map['mcate'] !== null
            && $map['detailreason'] !== null;
    }

    /**
     * 컬럼 값 가져오기 (없으면 null)
     */
    private function flushCategoryBatch(): void
    {
        $bNames = array_values(array_unique(array_filter($this->batchBRNames, function ($n) {
            return $n !== null && $n !== '';
        })));
        $mNames = array_values(array_unique(array_filter($this->batchMRNames, function ($n) {
            return $n !== null && $n !== '';
        })));

        if (! $this->dryRunMode) {
            if (! empty($bNames)) {
                $rows = array_map(function ($name) {
                    return ['name' => $name];
                }, $bNames);
                DB::table('return_reason_bs')->insertOrIgnore($rows);
            }
            if (! empty($mNames)) {
                $rows = array_map(function ($name) {
                    return ['name' => $name];
                }, $mNames);
                DB::table('return_reason_ms')->insertOrIgnore($rows);
            }
        }

        foreach ($bNames as $name) {
            $id = ReturnReasonB::where('name', $name)->value('id');
            if ($id) {
                $this->categoryBNameToIdCache[$name] = $id;
            }
        }
        foreach ($mNames as $name) {
            $id = ReturnReasonM::where('name', $name)->value('id');
            if ($id) {
                $this->categoryMNameToIdCache[$name] = $id;
            }
        }

        $this->batchBRNames = [];
        $this->batchMRNames = [];
    }

    /**
     * B 카테고리 ID를 즉시 확보한다.
     * 순서: 캐시 → DB 조회 → (dry-run이 아니면) insertOrIgnore → 재조회 → 캐시 저장
     */
    private function getOrCreateBIdByName(string $name): ?int
    {
        $name = trim($name);
        if ($name === '') {
            return null;
        }

        // 1) 캐시
        if (isset($this->categoryBNameToIdCache[$name])) {
            return (int) $this->categoryBNameToIdCache[$name];
        }

        // 2) DB 조회
        $foundId = ReturnReasonB::where('name', $name)->value('id');
        if ($foundId) {
            $this->categoryBNameToIdCache[$name] = (int) $foundId;

            return (int) $foundId;
        }

        // 3) dry-run이면 생성하지 않음(배치 큐에만 모아둠)
        if ($this->dryRunMode) {
            $this->batchBRNames[] = $name;

            return null;
        }

        // 4) 삽입(중복 무시) 후 재조회
        DB::table('return_reason_bs')->insertOrIgnore([['name' => $name]]);
        $foundId = ReturnReasonB::where('name', $name)->value('id');
        if ($foundId) {
            $this->categoryBNameToIdCache[$name] = (int) $foundId;

            return (int) $foundId;
        }

        return null;
    }

    /**
     * M 카테고리 ID를 즉시 확보한다.
     * 순서: 캐시 → DB 조회 → (dry-run이 아니면) insertOrIgnore → 재조회 → 캐시 저장
     */
    private function getOrCreateMIdByName(string $name): ?int
    {
        $name = trim($name);
        if ($name === '') {
            return null;
        }

        if (isset($this->categoryMNameToIdCache[$name])) {
            return (int) $this->categoryMNameToIdCache[$name];
        }

        $foundId = ReturnReasonM::where('name', $name)->value('id');
        if ($foundId) {
            $this->categoryMNameToIdCache[$name] = (int) $foundId;

            return (int) $foundId;
        }

        if ($this->dryRunMode) {
            $this->batchMRNames[] = $name;

            return null;
        }

        DB::table('return_reason_ms')->insertOrIgnore([['name' => $name]]);
        $foundId = ReturnReasonM::where('name', $name)->value('id');
        if ($foundId) {
            $this->categoryMNameToIdCache[$name] = (int) $foundId;

            return (int) $foundId;
        }

        return null;
    }

    /**
     * 컬럼 값 가져오기 (없으면 null)
     */
    private function getCol(array $row, array $map, string $key): ?string
    {
        $idx = $map[$key] ?? null;
        if ($idx === null) {
            return null;
        }

        return isset($row[$idx]) ? trim((string) $row[$idx]) : null;
    }
}
