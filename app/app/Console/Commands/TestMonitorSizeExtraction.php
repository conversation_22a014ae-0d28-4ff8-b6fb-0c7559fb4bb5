<?php

namespace App\Console\Commands;

use App\Models\MonitorSizeLookup;
use App\Models\Product;
use App\Services\MonitorSizeExtractionService;
use Illuminate\Console\Command;

/**
 * 모니터 사이즈 추출 테스트 명령어
 *
 * 모니터 크기 추출 기능을 테스트합니다.
 */
class TestMonitorSizeExtraction extends Command
{
    /**
     * 명령어 시그니처
     */
    protected $signature = 'monitor:test-extraction
                           {--input= : 입력 CSV (storage/app 기준 경로). 기본값: monitor_test/monitors.csv}
                           {--output= : 결과 CSV (storage/app 기준 경로). 기본값: monitor_test/test_exports.csv}
                           {--save : MonitorSizeLookup에 저장 수행}
                           {--name= : 단일 상품명 테스트(지정 시 CSV 대신 이 값으로 테스트)}
                           {--pattern : 단일 상품명 패턴 매칭 결과만 출력}';

    /**
     * 명령어 설명
     */
    protected $description = '모니터 크기 추출 기능을 테스트합니다.';

    /**
     * 모니터 사이즈 추출 서비스
     */
    protected MonitorSizeExtractionService $monitorService;

    /**
     * 생성자
     */
    public function __construct(MonitorSizeExtractionService $monitorService)
    {
        parent::__construct();
        $this->monitorService = $monitorService;
    }

    /**
     * 명령어 실행
     */
    public function handle(): int
    {
        $name = $this->option('name');
        $patternOnly = (bool) $this->option('pattern');
        $inputRel = $this->option('input') ?: 'monitor_test/monitors.csv';
        $outputRel = $this->option('output') ?: 'monitor_test/test_exports.csv';
        $doSave = (bool) $this->option('save');

        try {
            if ($name) {
                // 단일 상품명 테스트 모드
                $this->info('단일 상품명 테스트 모드');
                $this->line("상품명: {$name}");
                $this->line('');
                if ($patternOnly) {
                    $this->testPattern($name);
                } else {
                    $this->testExtraction($name);
                }

                return self::SUCCESS;
            }

            // CSV 배치 처리 모드
            $inputPath = storage_path('app'.DIRECTORY_SEPARATOR.$inputRel);
            $outputPath = storage_path('app'.DIRECTORY_SEPARATOR.$outputRel);

            if (! file_exists($inputPath)) {
                $this->error("입력 CSV 파일을 찾을 수 없습니다: {$inputPath}");

                return self::FAILURE;
            }

            $this->info('CSV 배치 처리 시작');
            $this->line("입력: {$inputPath}");
            $this->line("출력: {$outputPath}");
            $this->line('저장 모드: '.($doSave ? 'ON (MonitorSizeLookup 저장)' : 'OFF (저장 안 함)'));
            $this->line('');

            $processed = $this->processCsv($inputPath, $outputPath, $doSave);
            $this->info("처리 완료: {$processed}건");

            return self::SUCCESS;
        } catch (\Exception $e) {
            $this->error('테스트 중 오류가 발생했습니다: '.$e->getMessage());

            return self::FAILURE;
        }
    }

    /**
     * CSV 파일을 처리하여 결과를 CSV로 저장합니다.
     *
     * @return int 처리 건수
     */
    protected function processCsv(string $inputPath, string $outputPath, bool $doSave): int
    {
        $in = fopen($inputPath, 'r');
        if ($in === false) {
            throw new \RuntimeException('입력 파일을 열 수 없습니다: '.$inputPath);
        }

        // 출력 디렉토리 생성
        $outDir = dirname($outputPath);
        if (! is_dir($outDir)) {
            mkdir($outDir, 0777, true);
        }
        $out = fopen($outputPath, 'w');
        if ($out === false) {
            fclose($in);
            throw new \RuntimeException('출력 파일을 열 수 없습니다: '.$outputPath);
        }

        // 헤더 파싱
        $header = fgetcsv($in);
        if ($header === false) {
            fclose($in);
            fclose($out);

            return 0;
        }
        // DESCRIPTION 컬럼 위치 찾기 (대소문자 무시)
        $descIdx = null;
        foreach ($header as $i => $col) {
            if (mb_strtoupper(trim($col)) === 'DESCRIPTION') {
                $descIdx = $i;
                break;
            }
        }
        if ($descIdx === null) {
            // 첫줄이 헤더가 아니고 바로 상품명이면 첫 칼럼로 간주
            $this->warn('헤더에 DESCRIPTION 컬럼이 없어 첫 칼럼을 상품명으로 처리합니다.');
            $descIdx = 0;
            // 첫 줄 자체도 데이터일 수 있으니 되돌리기 위해 포인터를 파일 처음으로 이동
            rewind($in);
        }

        // 출력 헤더 작성
        fputcsv($out, ['description', 'brand_model', 'size', 'unit', 'name_hash', 'monitor_size_lookup_id', 'note']);

        $count = 0;
        while (($row = fgetcsv($in)) !== false) {
            if (! isset($row[$descIdx])) {
                continue;
            }
            $name = trim($row[$descIdx]);
            if ($name === '' || mb_strtolower($name) === 'description') {
                continue;
            }
            $result = $this->processOneName($name, $doSave);
            fputcsv($out, [
                $name,
                $result['brand_model'] ?? '',
                $result['size'] ?? '',
                $result['unit'] ?? '',
                $result['name_hash'] ?? '',
                $result['monitor_size_lookup_id'] ?? '',
                $result['note'] ?? '',
            ]);
            $count++;
        }

        fclose($in);
        fclose($out);

        return $count;
    }

    /**
     * 단일 상품명을 처리합니다. ProductsImport::processMonitorSizeLookup 로직을 반영.
     */
    protected function processOneName(string $productName, bool $doSave): array
    {
        $nameHash = md5($productName);
        $lookupId = null;
        $note = '';

        if ($doSave) {
            $existing = MonitorSizeLookup::where('name_hash', $nameHash)->first();
            if ($existing) {
                $lookupId = $existing->id;
            }
        }

        $tempProduct = new Product;
        $tempProduct->name = $productName;

        $sizeInfo = $this->monitorService->extractSizeFromName($tempProduct);
        if (! $sizeInfo) {
            $sizeInfo = [
                'size' => MonitorSizeExtractionService::DEFAULT_SIZE,
                'unit' => MonitorSizeExtractionService::DEFAULT_UNIT,
            ];
        }
        $brandModel = $this->monitorService->getMonitorModel($tempProduct);

        if ($doSave && $lookupId === null) {
            $created = MonitorSizeLookup::create([
                'name' => $productName,
                'name_hash' => $nameHash,
                'brand' => $brandModel,
                'size' => $sizeInfo['size'],
                'unit' => $sizeInfo['unit'],
            ]);
            $lookupId = $created->id;
            $note = 'created';
        } elseif ($doSave && $lookupId !== null) {
            $note = 'exists';
        }

        return [
            'brand_model' => $brandModel,
            'size' => $sizeInfo['size'],
            'unit' => $sizeInfo['unit'],
            'name_hash' => $nameHash,
            'monitor_size_lookup_id' => $lookupId,
            'note' => $note,
        ];
    }

    /**
     * 패턴 매칭 테스트
     */
    protected function testPattern(string $productName): void
    {
        $this->info('패턴 매칭 테스트 결과:');

        // 직접적인 단위 패턴 테스트
        $this->testDirectUnitPatterns($productName);

        $results = $this->monitorService->testPattern($productName);

        if (empty($results)) {
            $this->warn('  매칭되는 패턴이 없습니다.');

            return;
        }

        foreach ($results as $patternType => $result) {
            $this->line("  {$patternType}:");
            $this->line("    - 매칭: {$result['full_match']}");
            $this->line("    - 크기: {$result['size']}");

            if (isset($result['prefix'])) {
                $this->line("    - 접두사: {$result['prefix']}");
            }
        }
    }

    /**
     * 직접적인 단위 패턴 테스트
     */
    protected function testDirectUnitPatterns(string $productName): void
    {
        $this->line('  직접적인 단위 패턴:');

        // CM 단위 패턴
        if (preg_match('/(\d{2,3}(?:\.\d+)?)(?:cm|센치|센티미터)/iu', $productName, $matches)) {
            $this->line("    - CM 패턴 매칭: {$matches[0]} (크기: {$matches[1]}cm)");
        }

        // 인치 단위 패턴
        if (preg_match('/(\d{2,3}(?:\.\d+)?)\s?(?:inch|인치|형)/iu', $productName, $matches)) {
            $this->line("    - 인치 패턴 매칭: {$matches[0]} (크기: {$matches[1]}인치)");
        }
    }

    /**
     * 전체 추출 테스트
     */
    protected function testExtraction(string $productName): void
    {
        $this->info('전체 추출 테스트 결과:');

        $result = $this->monitorService->testExtraction($productName);

        // 추출된 크기 정보
        $this->line('추출된 크기:');
        if ($result['extracted_size']) {
            $this->line("  - 크기: {$result['extracted_size']['size']}");
            $this->line("  - 단위: {$result['extracted_size']['unit']}");
        } else {
            $this->warn('  크기 추출 실패');
        }

        // 적용된 규칙들
        if (! empty($result['applied_rules'])) {
            $this->line('');
            $this->line('적용된 규칙들:');
            foreach ($result['applied_rules'] as $rule) {
                $this->line("  - {$rule['type']}: {$rule['description']}");
            }
        } else {
            $this->line('');
            $this->warn('적용된 규칙이 없습니다.');
        }

        // 오류 정보
        if (isset($result['error'])) {
            $this->line('');
            $this->error('오류: '.$result['error']);
        }
    }
}
