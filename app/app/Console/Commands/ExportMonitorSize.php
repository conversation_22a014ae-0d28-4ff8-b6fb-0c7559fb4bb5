<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Services\MonitorSizeExtractionService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

/**
 * 모니터 크기 추출 결과를 CSV로 내보내는 명령어
 *
 * 이 명령어는 실제 데이터베이스의 모니터 상품들에서 extractSizeFromName 메서드로
 * 크기를 추출하고, 중복을 제거한 후 CSV 파일로 저장하는 도구입니다.
 *
 * ## 사용법
 *
 * ### 기본 실행 (전체 상품 처리, CSV로 저장)
 * ```bash
 * docker compose exec laravel php artisan export:monitor-size
 * ```
 *
 * ### CSV 파일로 저장 (명시적)
 * ```bash
 * docker compose exec laravel php artisan export:monitor-size --to-csv
 * docker compose exec laravel php artisan export:monitor-size --to-csv --limit=5000
 * ```
 *
 * ### 데이터베이스 테이블에 저장
 * ```bash
 * docker compose exec laravel php artisan export:monitor-size --to-table
 * docker compose exec laravel php artisan export:monitor-size --to-table --table-name=my_monitor_sizes
 * ```
 *
 * ### 특정 개수만 처리
 * ```bash
 * docker compose exec laravel php artisan export:monitor-size --limit=5000
 * docker compose exec laravel php artisan export:monitor-size --limit=10000
 * ```
 *
 * ### 커스텀 파일명으로 저장
 * ```bash
 * docker compose exec laravel php artisan export:monitor-size --to-csv --limit=5000 --output=my_monitor_sizes.csv
 * ```
 *
 * ### 특정 날짜 이후 상품만 처리
 * ```bash
 * docker compose exec laravel php artisan export:monitor-size --since=2024-01-01
 * docker compose exec laravel php artisan export:monitor-size --since=2024-01-01 --limit=1000
 * docker compose exec laravel php artisan export:monitor-size --since=2024-01-01 --to-table
 * ```
 *
 * ### 단위별로 분리된 파일 생성 (inch와 cm 별도)
 * ```bash
 * docker compose exec laravel php artisan export:monitor-size --to-csv --limit=5000 --separate-units
 * docker compose exec laravel php artisan export:monitor-size --to-csv --limit=5000 --output=my_monitor_sizes.csv --separate-units
 * ```
 *
 * ### 프로젝트 루트에 직접 저장
 * ```bash
 * docker compose exec laravel php artisan export:monitor-size --to-csv --output=../monitor_sizes.csv
 * ```
 *
 * ## 기능
 *
 * 1. **중복 제거**: 동일한 상품명은 하나만 저장 (상품명을 키로 사용)
 * 2. **메모리 효율적 처리**: 청크 단위(1000개씩)로 처리하여 메모리 오류 방지
 * 3. **진행률 표시**: 실시간으로 처리 진행 상황을 보여줌
 * 4. **결과 요약**: 처리된 상품 수, 성공률, 크기 분포 등
 * 5. **CSV 형식**: 브랜드, 상품명, 크기, 단위 컬럼으로 구성
 * 6. **샘플 데이터**: 처음 5개 상품의 정보를 미리보기로 제공
 * 7. **단위별 분리**: inch와 cm를 별도 파일로 분리하여 저장 가능
 * 8. **테이블 저장**: CSV 대신 데이터베이스 테이블에 직접 저장 가능
 * 9. **날짜 필터링**: 특정 날짜 이후의 상품만 처리 가능
 *
 * ## CSV 파일 형식
 *
 * ### 통합 파일 (기본)
 * ```csv
 * 브랜드,상품명,크기,단위
 * brand,"LG 24인치 모니터",24,INCH
 * brand,"삼성 27센치 모니터",27,INCH
 * etc,"DELL 32 모니터",32,INCH
 * ```
 *
 * ### 단위별 분리 파일 (--separate-units 옵션 사용 시)
 *
 * **monitor_sizes_inch.csv:**
 * ```csv
 * 브랜드,상품명,크기,단위
 * brand,"LG 24인치 모니터",24,INCH
 * brand,"삼성 27인치 모니터",27,INCH
 * ```
 *
 * **monitor_sizes_cm.csv:**
 * ```csv
 * 브랜드,상품명,크기,단위
 * brand,"LG 60센치 모니터",60,CM
 * etc,"삼성 70센티미터 모니터",70,CM
 * ```
 *
 * ## 파일 저장 위치
 *
 * **Docker 환경에서:**
 * - 컨테이너 내부: `/var/www/html/storage/app/monitor_sizes.csv`
 * - 호스트 시스템: `./storage/app/monitor_sizes.csv` (프로젝트 루트 기준)
 *
 * ## 파일 확인 방법
 *
 * ```bash
 * # 파일 목록 확인
 * docker compose exec laravel ls -la storage/app/
 *
 * # 파일 내용 미리보기
 * docker compose exec laravel head -10 storage/app/monitor_sizes.csv
 *
 * # 호스트로 파일 복사
 * docker compose cp laravel:/var/www/html/storage/app/monitor_sizes.csv ./
 * ```
 *
 * ## 출력 예시
 *
 * ```
 * 총 모니터 상품 수: 81683
 * 크기 추출 및 CSV 생성을 시작합니다...
 * 81683/81683 [▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓] 100%
 *
 * === 처리 결과 요약 ===
 * 총 모니터 상품: 81683
 * 처리된 상품: 81683
 * 성공한 상품: 4850
 * 중복 제거 후 고유 상품: 3200
 * INCH 단위: 2800개
 * CM 단위: 400개
 * 브랜드 제품: 2500개
 * 기타 제품: 700개
 *
 * === 전체 크기 분포 ===
 * 20-29인치: 1200개
 * 50인치 이상: 800개
 * 30-39인치: 600개
 * 10-19인치: 400개
 * 40-49인치: 200개
 *
 * === INCH 단위 크기 분포 ===
 * 20-29인치: 1000개
 * 30-39인치: 500개
 * 50인치 이상: 800개
 * 10-19인치: 300개
 * 40-49인치: 200개
 *
 * === CM 단위 크기 분포 ===
 * 50-74cm: 200개
 * 75-99cm: 100개
 * 100-124cm: 50개
 * 25-49cm: 30개
 * 125cm 이상: 20개
 *
 * === 단위별 CSV 파일 생성 완료 ===
 * INCH 파일: /var/www/html/storage/app/monitor_sizes_inch.csv (2800개 레코드)
 * CM 파일: /var/www/html/storage/app/monitor_sizes_cm.csv (400개 레코드)
 * INCH 파일 크기: 120,000 bytes
 * CM 파일 크기: 30,000 bytes
 *
 * === 샘플 데이터 (처음 5개) ===
 * 상품명: LG 24인치 모니터
 * 브랜드: brand
 * 크기: 24 INCH
 * ---
 * ```
 *
 * ## 테이블 저장 기능
 *
 * `--to-table` 옵션을 사용하면 CSV 파일 대신 데이터베이스 테이블에 직접 저장됩니다.
 * **테이블은 미리 생성되어 있어야 합니다.**
 *
 * ### 성능 최적화
 * - **해시 기반 조회**: O(1) 시간복잡도로 매우 빠름
 * - **인덱스 최적화**: product_name_hash에 유니크 인덱스
 * - **브랜드별 조회**: brand + unit 복합 인덱스
 *
 * ### 사용 예시
 * ```php
 * // 새 상품 등록 시 빠른 크기 조회
 * $hash = md5($product->name);
 * $size = DB::table('monitor_size_lookups')
 *     ->where('product_name_hash', $hash)
 *     ->select('size', 'unit', 'brand')
 *     ->first();
 * ```
 *
 * ## 주의사항
 *
 * - 대용량 데이터 처리 시 메모리 사용량이 높을 수 있으므로 충분한 메모리가 필요합니다
 * - TelegramService 오류는 무시하고 계속 진행됩니다
 * - 중복 제거는 상품명을 기준으로 하므로, 동일한 상품명이지만 다른 QAID를 가진 상품은 하나만 저장됩니다
 * - CSV 파일의 상품명에는 따옴표가 포함되어 있어 Excel 등에서 올바르게 표시됩니다
 * - 테이블 저장 시 테이블이 미리 생성되어 있어야 합니다
 */
class ExportMonitorSize extends Command
{
    /**
     * 모니터 크기 추출 서비스
     */
    protected MonitorSizeExtractionService $monitorService;

    /**
     * 생성자
     */
    public function __construct(MonitorSizeExtractionService $monitorService)
    {
        parent::__construct();
        $this->monitorService = $monitorService;
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'export:monitor-size {--limit= : 처리할 상품 수 제한 (기본값: 전체)} {--since= : 특정 날짜 이후의 상품만 처리 (YYYY-MM-DD 형식)} {--to-csv : CSV 파일로 저장} {--to-table : 데이터베이스 테이블에 저장} {--output=monitor_sizes.csv : CSV 출력 파일명} {--table-name=monitor_size_lookups : 테이블 저장 시 테이블명} {--separate-units : inch와 cm를 별도로 분리}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '모니터 크기 추출 결과를 중복 제거하고 CSV 또는 데이터베이스 테이블로 출력합니다';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $limit = $this->option('limit');
        $since = $this->option('since');
        $outputFile = $this->option('output');
        $separateUnits = $this->option('separate-units');
        $toCsv = $this->option('to-csv');
        $toTable = $this->option('to-table');
        $tableName = $this->option('table-name');

        // limit이 설정되지 않았으면 null로 처리 (전체 데이터)
        if ($limit === '') {
            $limit = null;
        }

        // since 날짜 검증
        if ($since !== null && $since !== '') {
            if (! preg_match('/^\d{4}-\d{2}-\d{2}$/', $since)) {
                $this->error('날짜 형식이 올바르지 않습니다. YYYY-MM-DD 형식으로 입력해주세요. (예: 2024-01-01)');

                return 1;
            }

            try {
                $sinceDate = \Carbon\Carbon::parse($since)->startOfDay();
            } catch (\Exception $e) {
                $this->error('유효하지 않은 날짜입니다: '.$since);

                return 1;
            }
        } else {
            $sinceDate = null;
        }

        // 전체 모니터 상품 수 조회 (날짜 필터 적용)
        $query = Product::query()
            ->where(function ($query) {
                $query->where('cate4_id', 2)
                    ->orWhere('cate5_id', 423);
            });

        // 날짜 필터 적용
        if ($sinceDate !== null) {
            $query->where('created_at', '>=', $sinceDate);
            $this->info("날짜 필터 적용: {$since} 이후 상품만 처리");
        }

        $totalCount = $query->count();

        if ($totalCount === 0) {
            if ($sinceDate !== null) {
                $this->error("{$since} 이후의 모니터 상품이 데이터베이스에 없습니다.");
            } else {
                $this->error('실제 모니터 상품이 데이터베이스에 없습니다.');
            }

            return 1;
        }

        $this->info('총 모니터 상품 수: '.$totalCount);

        if ($toTable) {
            $this->info('크기 추출 및 테이블 저장을 시작합니다...');
        } else {
            $this->info('크기 추출 및 CSV 생성을 시작합니다...');
        }

        $uniqueResults = []; // 중복 제거를 위한 배열 (상품명을 키로 사용)
        $inchResults = []; // inch 단위 결과
        $cmResults = []; // cm 단위 결과
        $processedCount = 0;
        $chunkSize = 1000; // 한 번에 처리할 상품 수

        $progressBar = $this->output->createProgressBar($limit ? min($limit, $totalCount) : $totalCount);
        $progressBar->start();

        // 청크 단위로 처리 (날짜 필터 적용)
        $chunkQuery = Product::query()
            ->where(function ($query) {
                $query->where('cate4_id', 2)
                    ->orWhere('cate5_id', 423);
            })
            ->select('id', 'qaid', 'name', 'cate4_id', 'cate5_id', 'created_at');

        // 날짜 필터 적용
        if ($sinceDate !== null) {
            $chunkQuery->where('created_at', '>=', $sinceDate);
        }

        $chunkQuery->chunk($chunkSize, function ($products) use (&$uniqueResults, &$inchResults, &$cmResults, &$processedCount, $limit, $progressBar) {
            foreach ($products as $product) {
                // 제한된 수만큼만 처리 (limit이 설정된 경우에만)
                if ($limit !== null && $processedCount >= $limit) {
                    break;
                }

                $processedCount++;

                try {
                    $result = $this->monitorService->extractSizeFromName($product);
                } catch (\Exception $e) {
                    // TelegramService 오류는 무시하고 계속 진행
                    $result = null;
                }

                if ($result !== null) {
                    // 상품명을 키로 사용하여 중복 제거
                    $productName = trim($product->name);
                    if (! isset($uniqueResults[$productName])) {
                        // 브랜드 정보 가져오기
                        $brand = $this->monitorService->getMonitorModel($product);

                        $uniqueResults[$productName] = [
                            'name' => $productName,
                            'size' => $result['size'],
                            'unit' => $result['unit'],
                            'brand' => $brand,
                        ];

                        // 단위별로 분리 저장
                        if ($result['unit'] === 'INCH') {
                            $inchResults[$productName] = $uniqueResults[$productName];
                        } elseif ($result['unit'] === 'CM') {
                            $cmResults[$productName] = $uniqueResults[$productName];
                        }
                    }
                }

                $progressBar->advance();
            }
        });

        $progressBar->finish();
        $this->newLine(2);

        // 결과 요약
        $this->info('=== 처리 결과 요약 ===');
        if ($sinceDate !== null) {
            $this->info("처리 기간: {$since} 이후");
        }
        $this->info('총 모니터 상품: '.$totalCount);
        $this->info('처리된 상품: '.$processedCount);
        $this->info('성공한 상품: '.count($uniqueResults));
        $this->info('중복 제거 후 고유 상품: '.count($uniqueResults));
        $this->info('INCH 단위: '.count($inchResults).'개');
        $this->info('CM 단위: '.count($cmResults).'개');

        // 브랜드별 분석
        $brandCount = 0;
        $etcCount = 0;
        foreach ($uniqueResults as $result) {
            if ($result['brand'] === 'brand') {
                $brandCount++;
            } else {
                $etcCount++;
            }
        }
        $this->info('브랜드 제품: '.$brandCount.'개');
        $this->info('기타 제품: '.$etcCount.'개');

        // 전체 크기 분포 분석
        $sizeDistribution = [];
        foreach ($uniqueResults as $result) {
            $size = $result['size'];
            $sizeRange = '';

            if ($size < 10) {
                $sizeRange = '10인치 미만';
            } elseif ($size < 20) {
                $sizeRange = '10-19인치';
            } elseif ($size < 30) {
                $sizeRange = '20-29인치';
            } elseif ($size < 40) {
                $sizeRange = '30-39인치';
            } elseif ($size < 50) {
                $sizeRange = '40-49인치';
            } else {
                $sizeRange = '50인치 이상';
            }

            $sizeDistribution[$sizeRange] = ($sizeDistribution[$sizeRange] ?? 0) + 1;
        }

        $this->info("\n=== 전체 크기 분포 ===");
        foreach ($sizeDistribution as $range => $count) {
            $this->line("{$range}: {$count}개");
        }

        // INCH 단위 크기 분포 분석
        $inchSizeDistribution = [];
        foreach ($inchResults as $result) {
            $size = $result['size'];
            $sizeRange = '';

            if ($size < 10) {
                $sizeRange = '10인치 미만';
            } elseif ($size < 20) {
                $sizeRange = '10-19인치';
            } elseif ($size < 30) {
                $sizeRange = '20-29인치';
            } elseif ($size < 40) {
                $sizeRange = '30-39인치';
            } elseif ($size < 50) {
                $sizeRange = '40-49인치';
            } else {
                $sizeRange = '50인치 이상';
            }

            $inchSizeDistribution[$sizeRange] = ($inchSizeDistribution[$sizeRange] ?? 0) + 1;
        }

        $this->info("\n=== INCH 단위 크기 분포 ===");
        foreach ($inchSizeDistribution as $range => $count) {
            $this->line("{$range}: {$count}개");
        }

        // CM 단위 크기 분포 분석
        $cmSizeDistribution = [];
        foreach ($cmResults as $result) {
            $size = $result['size'];
            $sizeRange = '';

            if ($size < 25) {
                $sizeRange = '25cm 미만';
            } elseif ($size < 50) {
                $sizeRange = '25-49cm';
            } elseif ($size < 75) {
                $sizeRange = '50-74cm';
            } elseif ($size < 100) {
                $sizeRange = '75-99cm';
            } elseif ($size < 125) {
                $sizeRange = '100-124cm';
            } else {
                $sizeRange = '125cm 이상';
            }

            $cmSizeDistribution[$sizeRange] = ($cmSizeDistribution[$sizeRange] ?? 0) + 1;
        }

        $this->info("\n=== CM 단위 크기 분포 ===");
        foreach ($cmSizeDistribution as $range => $count) {
            $this->line("{$range}: {$count}개");
        }

        // 파일 또는 테이블 저장
        $this->info('=== 저장 옵션 확인 ===');
        $this->info('toTable: '.($toTable ? 'true' : 'false'));
        $this->info('toCsv: '.($toCsv ? 'true' : 'false'));

        if ($toTable) {
            $this->info('데이터베이스 테이블에 저장합니다...');
            // 데이터베이스 테이블에 저장
            $this->saveToTable($tableName, $uniqueResults, $inchResults, $cmResults, $separateUnits);
        } else {
            $this->info('CSV 파일에 저장합니다...');
            // CSV 파일 생성 (기본값)
            if ($separateUnits) {
                // 단위별로 분리된 파일 생성
                $this->createSeparateCsvFiles($outputFile, $inchResults, $cmResults);
            } else {
                // 통합 파일 생성
                $this->createUnifiedCsvFile($outputFile, $uniqueResults);
            }
        }

        // 샘플 데이터 출력 (처음 5개)
        $this->info("\n=== 샘플 데이터 (처음 5개) ===");
        $sampleCount = 0;
        foreach ($uniqueResults as $result) {
            if ($sampleCount >= 5) {
                break;
            }

            $this->line("상품명: {$result['name']}");
            $this->line("크기: {$result['size']} {$result['unit']}");
            $this->line('---');
            $sampleCount++;
        }

        return 0;
    }

    /**
     * 통합 CSV 파일 생성
     */
    private function createUnifiedCsvFile(string $outputFile, array $uniqueResults): void
    {
        $csvContent = "브랜드,상품명,크기,단위\n";

        foreach ($uniqueResults as $result) {
            $csvContent .= sprintf(
                '%s,"%s",%s,%s'."\n",
                $result['brand'],
                str_replace('"', '""', $result['name']), // CSV에서 따옴표 이스케이프
                $result['size'],
                $result['unit']
            );
        }

        // 파일 저장
        $filePath = storage_path('app/'.$outputFile);
        Storage::put($outputFile, $csvContent);

        $this->info("\n=== 통합 CSV 파일 생성 완료 ===");
        $this->info('파일 경로: '.$filePath);
        $this->info('파일 크기: '.number_format(strlen($csvContent)).' bytes');
        $this->info('총 레코드: '.count($uniqueResults).'개');
    }

    /**
     * 단위별로 분리된 CSV 파일 생성
     */
    private function createSeparateCsvFiles(string $outputFile, array $inchResults, array $cmResults): void
    {
        // 파일명에서 확장자 분리
        $pathInfo = pathinfo($outputFile);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? 'csv';

        // INCH 파일 생성
        $inchFileName = $baseName.'_inch.'.$extension;
        $inchCsvContent = "브랜드,상품명,크기,단위\n";

        foreach ($inchResults as $result) {
            $inchCsvContent .= sprintf(
                '%s,"%s",%s,%s'."\n",
                $result['brand'],
                str_replace('"', '""', $result['name']),
                $result['size'],
                $result['unit']
            );
        }

        $inchFilePath = storage_path('app/'.$inchFileName);
        Storage::put($inchFileName, $inchCsvContent);

        // CM 파일 생성
        $cmFileName = $baseName.'_cm.'.$extension;
        $cmCsvContent = "브랜드,상품명,크기,단위\n";

        foreach ($cmResults as $result) {
            $cmCsvContent .= sprintf(
                '%s,"%s",%s,%s'."\n",
                $result['brand'],
                str_replace('"', '""', $result['name']),
                $result['size'],
                $result['unit']
            );
        }

        $cmFilePath = storage_path('app/'.$cmFileName);
        Storage::put($cmFileName, $cmCsvContent);

        $this->info("\n=== 단위별 CSV 파일 생성 완료 ===");
        $this->info('INCH 파일: '.$inchFilePath.' ('.count($inchResults).'개 레코드)');
        $this->info('CM 파일: '.$cmFilePath.' ('.count($cmResults).'개 레코드)');
        $this->info('INCH 파일 크기: '.number_format(strlen($inchCsvContent)).' bytes');
        $this->info('CM 파일 크기: '.number_format(strlen($cmCsvContent)).' bytes');
    }

    /**
     * 데이터베이스 테이블에 저장
     */
    private function saveToTable(string $tableName, array $uniqueResults, array $inchResults, array $cmResults, bool $separateUnits): void
    {
        $this->info("\n=== 데이터베이스 테이블 저장 시작 ===");

        // 1. 데이터 적재
        $this->insertData($tableName, $uniqueResults);

        // 2. 단위별 분리 테이블 적재 (옵션)
        if ($separateUnits) {
            $this->insertSeparateData($tableName, $inchResults, $cmResults);
        }

        $this->info('✓ 데이터베이스 테이블 저장 완료!');
    }

    /**
     * 데이터 적재
     */
    private function insertData(string $tableName, array $uniqueResults): void
    {
        $this->info('1. 데이터 적재 중...');

        $progressBar = $this->output->createProgressBar(count($uniqueResults));
        $progressBar->start();

        $insertedCount = 0;
        $duplicateCount = 0;

        // 외래키 제약 조건 일시적으로 비활성화
        $this->info('  외래키 제약 조건을 일시적으로 비활성화합니다...');
        \DB::statement('SET FOREIGN_KEY_CHECKS = 0');

        // 기존 데이터 삭제
        \DB::table($tableName)->truncate();

        \DB::beginTransaction();

        try {
            foreach ($uniqueResults as $result) {
                $productNameHash = md5($result['name']);

                try {
                    \DB::table($tableName)->insert([
                        'name' => $result['name'],
                        'name_hash' => $productNameHash,
                        'brand' => $result['brand'],
                        'size' => $result['size'],
                        'unit' => $result['unit'],
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    $insertedCount++;
                } catch (\Exception $e) {
                    // 중복 키 오류는 무시
                    if (! str_contains($e->getMessage(), 'Duplicate entry')) {
                        throw $e;
                    }
                    $duplicateCount++;
                }

                $progressBar->advance();
            }

            \DB::commit();

            // 외래키 제약 조건 다시 활성화
            $this->info('  외래키 제약 조건을 다시 활성화합니다...');
            \DB::statement('SET FOREIGN_KEY_CHECKS = 1');

        } catch (\Exception $e) {
            \DB::rollBack();
            // 오류 발생 시에도 외래키 제약 조건 복원
            \DB::statement('SET FOREIGN_KEY_CHECKS = 1');
            throw $e;
        }

        $progressBar->finish();
        $this->newLine(2);

        $this->info('  ✓ 성공적으로 적재: '.number_format($insertedCount).'개');
        $this->info('  ✓ 중복 제거됨: '.number_format($duplicateCount).'개');
        $this->newLine();
    }

    /**
     * 단위별 분리 테이블에 데이터 적재
     */
    private function insertSeparateData(string $baseTableName, array $inchResults, array $cmResults): void
    {
        $this->info('2. 단위별 분리 테이블에 데이터 적재 중...');

        // INCH 테이블에 데이터 적재
        $inchTableName = $baseTableName.'_inch';
        $this->insertData($inchTableName, $inchResults);

        // CM 테이블에 데이터 적재
        $cmTableName = $baseTableName.'_cm';
        $this->insertData($cmTableName, $cmResults);

        $this->info("  ✓ INCH 테이블: {$inchTableName} (".count($inchResults).'개 레코드)');
        $this->info("  ✓ CM 테이블: {$cmTableName} (".count($cmResults).'개 레코드)');
    }
}
