<?php

namespace App\Console\Commands;

use App\Models\Cate4;
use App\Models\Cate5;
use App\Models\RepairCost;
use App\Models\RepairCostCategory;
use App\Models\RepairCostPolicy;
use App\Models\RepairCostRange;
use App\Models\RepairCostType;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use PhpOffice\PhpSpreadsheet\IOFactory;

/**
 * 수리비 데이터 통합 임포트 커맨드
 *
 * storage/app/import_price_policies 디렉토리의 모든 수리비 정책 파일을 순서대로 읽어와서
 * repair_costs 관련 테이블에 데이터를 저장합니다.
 * 정책(Policy)과 유형(Type)은 DB에 미리 정의된 값을 사용하며, 이 커맨드는 생성/수정하지 않습니다.
 *
 * ## 파일명 규칙
 * 파일명은 '순서_정책코드.확장자' 형식을 따라야 합니다.
 * 정책코드는 RepairCostPolicy 모델의 코드와 정확히 일치해야 합니다.
 * (예: 01_general_price.csv, 05_apple_macbook.xlsx)
 */
class ImportAllRepairCosts extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'repair:import-all-costs {--clear : 기존 수리비 데이터(카테고리,범위,비용)를 삭제하고 새로 임포트합니다.}';

    /**
     * The console command description.
     */
    protected $description = '수리비 정책 파일을 순서대로 읽어와 모든 수리비 데이터를 임포트합니다. 정책과 유형 자체는 수정하지 않습니다.';

    /**
     * 수리 유형 매핑 (CSV 컬럼명 -> RepairCostType 코드 상수)
     */
    private array $repairTypeMapping = [
        '수리_부품교체' => RepairCostType::TYPE_PARTS,
        '수리_세척' => RepairCostType::TYPE_CLEANING,
        '수리_SW' => RepairCostType::TYPE_SOFTWARE,
        '수리_기타' => RepairCostType::TYPE_OTHER,
        '검수' => RepairCostType::TYPE_INSPECTION,
    ];

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        if ($this->option('clear') && $this->confirm('정말로 모든 수리비 데이터(카테고리, 범위, 비용)를 삭제하시겠습니까? 정책, 유형 자체는 삭제되지 않습니다.')) {
            $this->clearRepairCostData();
        }

        try {
            $this->processImportFiles();
            $this->info(PHP_EOL.'모든 수리비 정책 파일 처리가 성공적으로 완료되었습니다.');
        } catch (\Exception $e) {
            $this->error(PHP_EOL.'작업 중 오류가 발생했습니다: '.$e->getMessage());
            $this->error('오류 위치: '.$e->getFile().':'.$e->getLine());

            return 1;
        }

        return 0;
    }

    /**
     * 수리비 관련 데이터(카테고리, 범위, 비용)만 삭제합니다.
     */
    private function clearRepairCostData(): void
    {
        $this->info('기존 수리비 데이터(카테고리, 범위, 비용)를 삭제하는 중...');
        DB::statement('SET FOREIGN_KEY_CHECKS=0');
        RepairCost::truncate();
        RepairCostRange::truncate();
        RepairCostCategory::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
        $this->info('수리비 데이터 삭제 완료.');
    }

    /**
     * 지정된 디렉토리의 정책 파일을 순서대로 처리합니다.
     */
    private function processImportFiles(): void
    {
        $directory = storage_path('app/import_price_policies');
        if (! File::isDirectory($directory)) {
            throw new \Exception("임포트 디렉토리를 찾을 수 없습니다: {$directory}");
        }

        $files = File::files($directory);
        sort($files, SORT_NATURAL);

        if (empty($files)) {
            $this->warn("임포트할 파일이 디렉토리에 없습니다: {$directory}");

            return;
        }

        foreach ($files as $file) {
            $this->line('');
            $this->info('>> 파일 처리 시작: '.$file->getFilename());
            try {
                $this->processFile($file->getPathname());
            } catch (\Exception $e) {
                $this->error("파일 처리 실패 '{$file->getFilename()}': ".$e->getMessage());
            }
        }
    }

    /**
     * 단일 파일을 처리합니다.
     */
    private function processFile(string $filePath): void
    {
        preg_match('/^\d+_(.+?)\./', basename($filePath), $matches);
        $policyCode = $matches[1] ?? null;

        if (! $policyCode) {
            throw new \Exception("파일명에서 정책 코드를 추출할 수 없습니다. '순서_정책코드.확장자' 형식을 따라야 합니다.");
        }

        $policy = RepairCostPolicy::byCode($policyCode)->firstOrFail();
        $data = $this->readFile($filePath);

        if (empty($data)) {
            $this->warn('파일에 처리할 데이터가 없습니다.');

            return;
        }

        $this->info('총 '.count($data).'개 행을 처리합니다.');
        $progressBar = $this->output->createProgressBar(count($data));
        $progressBar->start();

        foreach ($data as $row) {
            try {
                $this->processRow($row, $policy);
            } catch (\Exception $e) {
                $this->warn(PHP_EOL.'[경고] 행 처리 중 오류: '.$e->getMessage().' 데이터: '.json_encode($row, JSON_UNESCAPED_UNICODE));
            }
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
    }

    /**
     * 정책에 따라 단일 행을 처리합니다.
     */
    private function processRow(array $row, RepairCostPolicy $policy): void
    {
        $category = $this->getOrCreateCategoryForRow($row, $policy);
        $range = $this->getOrCreateRangeForRow($row, $category, $policy);
        $this->createCostsForRow($row, $range, $policy);
    }

    /**
     * 행 데이터와 정책에 따라 카테고리를 생성하거나 조회합니다.
     */
    private function getOrCreateCategoryForRow(array $row, RepairCostPolicy $policy): RepairCostCategory
    {
        if ($policy->code === RepairCostPolicy::POLICY_DEFAULT) {
            return RepairCostCategory::firstOrCreate(
                ['repair_cost_policy_id' => $policy->id, 'cate4_id' => null, 'cate5_id' => null],
                ['pricing_criteria' => $policy->pricing_type, 'is_active' => true]
            );
        }

        $cate4Col = '4차';
        $cate5Col = '5차';

        if ($policy->code === RepairCostPolicy::POLICY_APPLE_MACBOOK) {
            $row[$cate4Col] = '컴퓨터';
            $row[$cate5Col] = '노트북';
        } elseif ($policy->code === RepairCostPolicy::POLICY_APPLE_IMAC) {
            $row[$cate4Col] = '컴퓨터';
            $row[$cate5Col] = '일체형PC';
        }

        $cate4Name = trim($row[$cate4Col] ?? '');
        $cate5Name = trim($row[$cate5Col] ?? '');

        if (empty($cate4Name) || empty($cate5Name)) {
            throw new \Exception("필수 카테고리 데이터({$cate4Col}, {$cate5Col}) 누락");
        }

        $cate4 = Cate4::firstOrCreate(['name' => $cate4Name]);
        $cate5 = Cate5::firstOrCreate(['cate4_id' => $cate4->id, 'name' => $cate5Name]);

        return RepairCostCategory::firstOrCreate(
            ['repair_cost_policy_id' => $policy->id, 'cate4_id' => $cate4->id, 'cate5_id' => $cate5->id],
            ['pricing_criteria' => $policy->pricing_type, 'is_active' => true]
        );
    }

    /**
     * 행 데이터와 정책에 따라 범위를 생성하거나 조회합니다.
     */
    private function getOrCreateRangeForRow(array $row, RepairCostCategory $category, RepairCostPolicy $policy): RepairCostRange
    {
        $rangeColumn = '상품단가구간';
        $rangeName = trim($row[$rangeColumn] ?? '');
        if (empty($rangeName)) {
            throw new \Exception("필수 범위 데이터('{$rangeColumn}') 누락");
        }

        $rangeInfo = $this->parseRangeName($rangeName, $policy);

        $range = RepairCostRange::firstOrCreate(
            [
                'repair_cost_category_id' => $category->id,
                'range_name' => $rangeInfo['range_name'],
            ],
            [
                'min_value' => $rangeInfo['min_value'],
                'max_value' => $rangeInfo['max_value'],
                'unit' => $rangeInfo['unit'],
                'is_active' => true,
            ]
        );

        // 새로 생성된 크기 기반 범위의 경우, min_value를 동적으로 계산
        if ($range->wasRecentlyCreated && $policy->isSizeBasedPolicy() && $rangeInfo['max_value'] !== null) {
            $minValue = RepairCostRange::where('repair_cost_category_id', $category->id)
                ->where('unit', $rangeInfo['unit']) // 동일 단위 내에서만 조회
                ->where('id', '!=', $range->id)     // 자기 자신은 제외
                ->max('max_value') ?? 0;

            $range->min_value = $minValue;
            $range->save();
        }

        return $range;
    }

    /**
     * 행 데이터와 정책에 따라 수리비를 생성하거나 업데이트합니다.
     */
    private function createCostsForRow(array $row, RepairCostRange $range, RepairCostPolicy $policy): void
    {
        $costMap = [];
        switch ($policy->code) {
            case RepairCostPolicy::POLICY_OS_INSTALL_PRICE:
            case RepairCostPolicy::POLICY_OS_INSTALL_COMMON:
                $costMap = ['수리_SW' => RepairCostType::TYPE_SOFTWARE];
                break;

            case RepairCostPolicy::POLICY_APPLE_MACBOOK:
            case RepairCostPolicy::POLICY_APPLE_IMAC:
            case RepairCostPolicy::POLICY_APPLE_ETC:
                $costMap = ['검수' => RepairCostType::TYPE_INSPECTION];
                break;

            default:
                $costMap = $this->repairTypeMapping;
                break;
        }

        foreach ($costMap as $column => $typeCode) {
            if (isset($row[$column]) && $row[$column] !== '' && $row[$column] !== '-') {
                $amount = $this->parseAmount($row[$column]);
                if ($amount === null) {
                    continue;
                }

                $repairType = RepairCostType::byCode($typeCode)->firstOrFail();
                RepairCost::updateOrCreate(
                    ['repair_cost_range_id' => $range->id, 'repair_cost_type_id' => $repairType->id],
                    ['amount' => $amount]
                );
            }
        }
    }

    // MARK: - File & Data Parsing Helpers

    private function readFile(string $filePath): array
    {
        $format = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

        return match ($format) {
            'csv' => $this->readCsvFile($filePath),
            'xlsx', 'xls' => $this->readExcelFile($filePath),
            default => throw new \Exception("지원하지 않는 파일 형식: {$format}"),
        };
    }

    private function readCsvFile(string $filePath): array
    {
        $data = [];
        $handle = fopen($filePath, 'r');
        if ($handle === false) {
            throw new \Exception("CSV 파일을 열 수 없습니다: {$filePath}");
        }

        if (fgets($handle, 4) !== "\xef\xbb\xbf") {
            rewind($handle);
        }

        $headers = fgetcsv($handle);
        if (! $headers) {
            fclose($handle);

            return [];
        }
        $headers = array_map('trim', $headers);

        while (($row = fgetcsv($handle)) !== false) {
            if (count(array_filter($row)) == 0) {
                continue;
            }
            $row = array_pad($row, count($headers), '');
            $data[] = array_combine($headers, array_map('trim', $row));
        }
        fclose($handle);

        return $data;
    }

    private function readExcelFile(string $filePath): array
    {
        try {
            $rows = IOFactory::load($filePath)->getActiveSheet()->toArray(null, true, true, true);
            $headers = array_map('trim', array_shift($rows));
            $data = [];
            foreach ($rows as $row) {
                if (count(array_filter($row)) == 0) {
                    continue;
                }
                $data[] = array_combine($headers, array_map('trim', $row));
            }

            return $data;
        } catch (\Exception $e) {
            throw new \Exception('Excel 파일 읽기 실패: '.$e->getMessage());
        }
    }

    private function parseRangeName(string $rangeName, RepairCostPolicy $policy): array
    {
        $rangeName = trim($rangeName);
        $defaultUnit = $policy->isSizeBasedPolicy() ? 'inch' : 'won';
        $result = ['range_name' => $rangeName, 'min_value' => null, 'max_value' => null, 'unit' => $defaultUnit];

        if ($rangeName === '공통') {
            $result['unit'] = 'common';
        } elseif (preg_match('/^(\d+)미만$/', $rangeName, $matches)) {
            $result['max_value'] = (float) $matches[1];
            $result['min_value'] = 0;
            $result['unit'] = 'won';
        } elseif (preg_match('/^(\d+)이상$/', $rangeName, $matches)) {
            $result['min_value'] = (float) $matches[1];
            $result['max_value'] = 99999999;
            $result['unit'] = 'won';
        } elseif ($policy->isSizeBasedPolicy() && preg_match('/^~([\d\.]+)\s*(cm|inch|"{1,3})$/i', $rangeName, $matches)) {
            $value = (float) $matches[1];
            $unitIdentifier = str_replace(['"""', '""', '"'], 'inch', strtolower($matches[2]));

            if (str_contains($unitIdentifier, 'cm')) {
                $result['unit'] = 'cm';
            } else {
                $result['unit'] = 'inch';
            }
            $result['max_value'] = $value;
        }

        return $result;
    }

    private function parseAmount(string $amount): ?float
    {
        $cleanAmount = str_replace([',', ' '], '', trim($amount));

        return is_numeric($cleanAmount) ? (float) $cleanAmount : null;
    }
}
