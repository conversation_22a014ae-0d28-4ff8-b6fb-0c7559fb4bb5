<?php

namespace App\Console\Commands;

use App\Models\MonitorRule;
use App\Services\MonitorSizeExtractionService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 모니터 사이즈 규칙 생성 명령어
 *
 * 데이터베이스에 기본 모니터 크기 추출 규칙들을 생성합니다.
 */
class CreateMonitorSizeRules extends Command
{
    /**
     * 명령어 시그니처
     */
    protected $signature = 'monitor:create-rules
                            {--force : 기존 규칙을 삭제하고 새로 생성}
                            {--brand-only : 브랜드 규칙만 생성}
                            {--exclude-only : 제외 규칙만 생성}
                            {--size-only : 크기 패턴 규칙만 생성}';

    /**
     * 명령어 설명
     */
    protected $description = '모니터 크기 추출을 위한 기본 규칙들을 데이터베이스에 생성합니다.';

    /**
     * 패턴에서 제외할 키워드들
     */
    private const PATTERN_EXCLUDE_KEYWORDS = [
        'Hz', 'HDR', 'K', 'UHD', 'FHD', 'QHD', 'OLED', 'IPS', 'VA', 'TN',
        'Gaming', 'Curved', 'Ultra', 'Wide', 'Pro', 'Plus', 'Max',
        'PD', 'USB', 'HDMI', 'DP', 'VGA', 'DVI', 'W',
    ];

    /**
     * 모니터 사이즈 추출 서비스
     */
    protected MonitorSizeExtractionService $monitorService;

    /**
     * 제외 키워드 패턴 생성
     */
    private function getExcludePattern(): string
    {
        $keywords = array_map('preg_quote', self::PATTERN_EXCLUDE_KEYWORDS);

        return implode('|', $keywords);
    }

    /**
     * 앞/뒤 제외 키워드 패턴 생성
     */
    private function getExcludePatternWithLookaround(): string
    {
        $keywords = array_map('preg_quote', self::PATTERN_EXCLUDE_KEYWORDS);
        $pattern = implode('|', $keywords);

        return "(?<!($pattern))|(?!($pattern))";
    }

    /**
     * 생성자
     */
    public function __construct(MonitorSizeExtractionService $monitorService)
    {
        parent::__construct();
        $this->monitorService = $monitorService;
    }

    /**
     * 명령어 실행
     */
    public function handle(): int
    {
        $this->info('모니터 크기 추출 규칙 생성을 시작합니다...');

        try {
            // 기존 규칙 삭제 옵션
            if ($this->option('force')) {
                $this->warn('기존 규칙을 모두 삭제합니다...');
                $this->deleteExistingRules();
            }

            // 규칙 생성
            $this->createRules();

            // 캐시 초기화
            $this->monitorService->clearRulesCache();

            $this->info('모니터 크기 추출 규칙 생성이 완료되었습니다!');

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error('규칙 생성 중 오류가 발생했습니다: '.$e->getMessage());
            Log::error('모니터 규칙 생성 실패', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return self::FAILURE;
        }
    }

    /**
     * 기존 규칙 삭제
     */
    protected function deleteExistingRules(): void
    {
        $deletedCount = MonitorRule::count();
        MonitorRule::truncate();

        $this->info("기존 규칙 {$deletedCount}개를 삭제했습니다.");
    }

    /**
     * 규칙 생성
     */
    protected function createRules(): void
    {
        $brandOnly = $this->option('brand-only');
        $excludeOnly = $this->option('exclude-only');
        $sizeOnly = $this->option('size-only');

        // 브랜드 규칙 생성
        if (! $excludeOnly && ! $sizeOnly) {
            $this->createBrandRules();
        }

        // 제외 규칙 생성
        if (! $brandOnly && ! $sizeOnly) {
            $this->createExcludeRules();
        }

        // 크기 패턴 규칙 생성
        if (! $brandOnly && ! $excludeOnly) {
            $this->createSizePatternRules();
        }
    }

    /**
     * 브랜드 규칙 생성
     */
    protected function createBrandRules(): void
    {
        $this->info('브랜드 규칙을 생성합니다...');

        $brandKeywords = [
            'LG', '엘지', 'lg전자', '엘지전자', '삼성', '삼성전자', 'samsung', '룸앤', 'ROOM&TV',
        ];

        $createdCount = 0;
        foreach ($brandKeywords as $index => $keyword) {
            $rule = MonitorRule::updateOrCreate(
                ['rule_type' => MonitorRule::RULE_TYPE_BRAND, 'pattern' => $keyword],
                [
                    'description' => "브랜드 키워드: {$keyword}",
                    'priority' => $index + 1,
                    'is_active' => true,
                ]
            );

            if ($rule->wasRecentlyCreated) {
                $createdCount++;
                $this->line("  ✓ 브랜드 규칙 생성: {$keyword}");
            } else {
                $this->line("  - 브랜드 규칙 업데이트: {$keyword}");
            }
        }

        $this->info("브랜드 규칙 {$createdCount}개를 생성했습니다.");
    }

    /**
     * 제외 규칙 생성
     */
    protected function createExcludeRules(): void
    {
        $this->info('제외 규칙을 생성합니다...');

        $excludeKeywords = [
            'LG패널', 'LG 패널', '삼성패널', '삼성 패널', '정품패널', '정품 패널', '대기업', 'LG정품', '엘지정품', '삼성정품',
            'LG IPS', 'LGIPS', '삼성dex', '삼성 dex', 'lgc', 'dlg', '주연', '크로스오버', '한성', '디스플레이', '더함',
            '이노스', 'TCL', '프리즘', '인켈', '시티브', 'seetive', '로로테크', '이스트라', '홈플래닛', '아인츠', '샤오미',
            '클라인즈', '아남', '와이드뷰', '삼탠바이미', '와이드무빙뷰', 'Hisense', '아이리버', '라익미', '아이사',
            'artive', '유맥스', '이엔티비', 'hp', '모지', 'mozee', '레노버', 'lenovo', '델', 'dell', '벤큐', '카멜',
            '위드라이프', '제우스랩', 'zeuslab', '어드밴스원', '엠텍', 'thinkvision', '한솔시스템',
        ];

        $createdCount = 0;
        foreach ($excludeKeywords as $index => $keyword) {
            $rule = MonitorRule::updateOrCreate(
                ['rule_type' => MonitorRule::RULE_TYPE_EXCLUDE, 'pattern' => $keyword],
                [
                    'description' => "제외 키워드: {$keyword}",
                    'priority' => $index + 1,
                    'is_active' => true,
                ]
            );

            if ($rule->wasRecentlyCreated) {
                $createdCount++;
                $this->line("  ✓ 제외 규칙 생성: {$keyword}");
            } else {
                $this->line("  - 제외 규칙 업데이트: {$keyword}");
            }
        }

        $this->info("제외 규칙 {$createdCount}개를 생성했습니다.");
    }

    /**
     * 크기 패턴 규칙 생성
     */
    protected function createSizePatternRules(): void
    {
        $this->info('크기 패턴 규칙을 생성합니다...');

        $sizePatterns = [
            // 직접적인 단위 패턴들 (최우선)
            [
                'pattern' => '/(\d{2,3}(?:\.\d+)?)(?:cm|센치|센티미터)/iu',
                'description' => '직접적인 cm 단위 패턴',
            ],
            [
                'pattern' => '/(\d{1,3}(?:\.\d+)?)\s?(?:inch|인치|형)/iu',
                'description' => '직접적인 인치/형 단위 패턴',
            ],

            // 특수 패턴들 (우선순위 높음)
            [
                'pattern' => '/\bULTRON\s?(\d{2})\d{2}\b/i',
                'description' => '한성 ULTRON 패턴 (앞 두자리가 크기)',
            ],
            [
                'pattern' => '/\b[A-Z]+-(\d{2})\d\b/i',
                'description' => 'QUEEN-325 형식 (알파벳-숫자, 앞 두자리가 크기)',
            ],
            [
                'pattern' => '/\b[A-Z]{2}-(\d{2})\d{2}\b/i',
                'description' => 'CP-2775 형식 (2글자 알파벳-숫자, 앞 두자리가 크기)',
            ],

            // 숫자로 시작하는 패턴들 (우선순위 높음)
            [
                'pattern' => '/\b(\d{2})[A-Z]{2,3}\d\b/i',
                'description' => '2자리 숫자 + 2-3글자 알파벳 + 1자리 숫자 (24MP3, 24AM5 등)',
            ],
            [
                'pattern' => '/\b(\d{2})[A-Z]{2}\d\b/i',
                'description' => '2자리 숫자 + 2글자 알파벳 + 1자리 숫자 (24MP3 형식)',
            ],

            // 해상도 패턴들 (우선순위 높음)
            [
                'pattern' => '/\b[A-Z]{2}(\d{2})(?:QHD|FHD|UHD|HD)\b/i',
                'description' => '2글자 알파벳 + 2자리 숫자 + 해상도 (GL27QHD, X34UHD 등)',
            ],
            [
                'pattern' => '/\b[A-Z]+-(\d{2})\b/i',
                'description' => 'CURVED-27 형식 (알파벳-2자리 숫자)',
            ],

            // 알파벳으로 시작하는 패턴들
            [
                'pattern' => '/\b[A-Z]{3}(\d{2})[A-Z0-9]{2,}\b/i',
                'description' => 'TFG32U14PQ 형식 (3글자 알파벳 + 2자리 숫자 + 2글자 이상)',
            ],
            [
                'pattern' => '/\b[XYG](\d{2})[A-Z0-9]+[-\s]?[A-Z0-9]*\b/i',
                'description' => 'X/Y/G 시리즈 (X34QC-65W, Y27Q-20 등)',
            ],
            [
                'pattern' => '/\b[A-Z](\d{2})[-\s]?[A-Z0-9]+\b/i',
                'description' => '단일 알파벳 + 2자리 숫자 (G32-QC-10, T24t-20 등)',
            ],
            [
                'pattern' => '/\b[A-Z]{2}(\d{2})(?!('.$this->getExcludePattern().')\b)[A-Z0-9]+\b/i',
                'description' => '2글자 알파벳 + 2자리 숫자 (뒤 제외 키워드 제외)',
            ],
            [
                'pattern' => '/\b[A-Z]{2,4}(\d{2})(?!('.$this->getExcludePattern().')\b)[A-Z0-9]*\b/i',
                'description' => '2-4글자 알파벳 + 2자리 숫자 (뒤 제외 키워드 제외)',
            ],
            [
                'pattern' => '/\b(\d{2})[A-Z]{2}\d\b/i',
                'description' => '2자리 숫자 + 2글자 알파벳 + 1자리 숫자 (24MP3 형식)',
            ],
            [
                'pattern' => '/\b(?!('.$this->getExcludePattern().'))(\d{2,3})[A-Z0-9]+\b/i',
                'description' => '앞 제외 키워드 없는 숫자 + 알파벳/숫자',
            ],
            [
                'pattern' => '/\b[A-Z]+(\d{2})\b/i',
                'description' => '알파벳 + 2자리 숫자 (끝)',
            ],

            // 통합된 숫자 패턴들
            [
                'pattern' => '/\b(\d{2})\d{2}[A-Z][A-Z0-9]*\b/i',
                'description' => '4자리 숫자 + 알파벳 (3824Q 형식, 앞 두자리가 크기)',
            ],
            [
                'pattern' => '/\b(\d{2})00\b/i',
                'description' => '4자리 숫자 (2400 형식, 앞 두자리가 크기)',
            ],
            [
                'pattern' => '/\b(\d{2})\d[A-Z]{2,}[A-Z0-9]*\b/i',
                'description' => '3자리 숫자 + 2글자 이상 알파벳 (321URX 형식)',
            ],
            [
                'pattern' => '/\b(\d{2})\d[A-Z0-9]+\b/i',
                'description' => '3자리 숫자 + 알파벳/숫자 (246E9 형식, 앞 두자리가 크기)',
            ],
            [
                'pattern' => '/\b(\d{2})\d\b/i',
                'description' => '3자리 숫자 (320 형식, 앞 두자리가 크기)',
            ],
            [
                'pattern' => '/\b(\d{2})(?!('.$this->getExcludePattern().')\b)[A-Z]+\b/i',
                'description' => '2자리 숫자 + 알파벳 (뒤 제외 키워드 제외)',
            ],
            [
                'pattern' => '/\b(\d{2,3})(?!('.$this->getExcludePattern().')\b)[A-Z]+[A-Z0-9]*\b/i',
                'description' => '2-3자리 숫자 + 알파벳 (뒤 제외 키워드 제외)',
            ],

            // 특수 패턴들
            [
                'pattern' => '/휴대용|포터블/i',
                'description' => '휴대용/포터블 패턴 (23.8인치)',
            ],
        ];

        $createdCount = 0;
        foreach ($sizePatterns as $index => $patternData) {
            $rule = MonitorRule::updateOrCreate(
                ['rule_type' => MonitorRule::RULE_TYPE_SIZE_PATTERN, 'pattern' => $patternData['pattern']],
                [
                    'description' => $patternData['description'],
                    'priority' => $index + 1,
                    'is_active' => true,
                ]
            );

            if ($rule->wasRecentlyCreated) {
                $createdCount++;
                $this->line("  ✓ 크기 패턴 규칙 생성: {$patternData['description']}");
            } else {
                $this->line("  - 크기 패턴 규칙 업데이트: {$patternData['description']}");
            }
        }

        $this->info("크기 패턴 규칙 {$createdCount}개를 생성했습니다.");
    }
}
