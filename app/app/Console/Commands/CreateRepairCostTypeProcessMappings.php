<?php

namespace App\Console\Commands;

use App\Services\RepairCostTypeProcessMappingService;
use Illuminate\Console\Command;

class CreateRepairCostTypeProcessMappings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'repair:mappings:create
                            {--type= : 특정 수리 유형만 매핑 (parts, cleaning, software, other, inspection)}
                            {--dry-run : 실제 매핑 생성하지 않고 미리보기만}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '수리 프로세스와 수리 유형 간의 매핑을 생성합니다.';

    /**
     * Execute the console command.
     */
    public function handle(RepairCostTypeProcessMappingService $mappingService)
    {
        $this->info('수리 프로세스 매핑 생성을 시작합니다...');

        // 매핑 규칙 정의
        $mappings = [
            'parts' => [
                'RP_PART',    // 수리 또는 부품교체
                'RP_POWER',   // 전원부 수리/교체
            ],
            'cleaning' => [
                'RP_CLEAN',   // 오염 제거
            ],
            'software' => [
                'RP_SW',      // 소프트웨어 재설정(OS 재설치)
            ],
            'other' => [],
            'inspection' => [
                'RP_CONFIRM', // 점검
            ],
        ];

        $targetType = $this->option('type');
        $isDryRun = $this->option('dry-run');

        if ($targetType && ! isset($mappings[$targetType])) {
            $this->error("유효하지 않은 수리 유형입니다: {$targetType}");
            $this->info('사용 가능한 유형: '.implode(', ', array_keys($mappings)));

            return 1;
        }

        $typesToProcess = $targetType ? [$targetType => $mappings[$targetType]] : $mappings;

        $totalCreated = 0;
        $totalErrors = 0;

        foreach ($typesToProcess as $costTypeCode => $processCodes) {
            $this->info("\n처리 중: {$costTypeCode}");

            if ($isDryRun) {
                $this->info('  매핑할 프로세스들:');
                foreach ($processCodes as $processCode) {
                    $this->line("    - {$processCode}");
                }

                continue;
            }

            try {
                $result = $mappingService->createMapping($costTypeCode, $processCodes);

                $createdCount = count($result['created']);
                $errorCount = count($result['errors']);

                $totalCreated += $createdCount;
                $totalErrors += $errorCount;

                if ($createdCount > 0) {
                    $this->info("  ✓ {$createdCount}개 매핑 생성됨");
                }

                if ($errorCount > 0) {
                    $this->warn("  ⚠ {$errorCount}개 오류 발생:");
                    foreach ($result['errors'] as $error) {
                        $this->line("    - {$error}");
                    }
                }

            } catch (\Exception $e) {
                $this->error("  ✗ 매핑 생성 실패: {$e->getMessage()}");
                $totalErrors++;
            }
        }

        $this->info("\n매핑 생성 완료!");
        $this->info("생성된 매핑: {$totalCreated}개");
        $this->info("오류: {$totalErrors}개");

        if (! $isDryRun) {
            // 통계 출력
            $stats = $mappingService->getMappingStatistics();
            $this->info("\n매핑 통계:");
            foreach ($stats as $typeName => $count) {
                $this->line("  {$typeName}: {$count}개");
            }

            // 매핑되지 않은 프로세스 확인
            $unmappedProcesses = $mappingService->getUnmappedProcesses();
            if ($unmappedProcesses->count() > 0) {
                $this->warn("\n매핑되지 않은 프로세스가 있습니다:");
                foreach ($unmappedProcesses as $process) {
                    $this->line("  - {$process->name} ({$process->code})");
                }
            }
        }

        return 0;
    }
}
