<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ReqFinishNotification implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private string $message;

    private ?string $redirect;

    /**
     * Create a new event instance.
     */
    public function __construct(string $message, ?string $redirect = null)
    {
        $this->message = $message;
        $this->redirect = $redirect;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('cnsprowms'),
        ];
    }

    /**
     * Get the event name this event should broadcast as.
     */
    public function broadcastAs(): string
    {
        return 'admin-notification';
    }

    /**
     * 알림을 보낼 내용을 적는 곳
     */
    public function broadcastWith(): array
    {
        return [
            'success' => true,
            'redirect' => $this->redirect ?? null,
            'message' => $this->message,
        ];
    }
}
