<?php

namespace App\Events;

use App\Models\Notification;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * 알림 전송 이벤트
 * 
 * 알림이 DB에 저장된 후 발생하는 이벤트로,
 * SSE를 통해 실시간으로 클라이언트에 전달하기 위해 사용됩니다.
 */
class NotificationSent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public Notification $notification,
        public array $recipientIds
    ) {}
}