<?php

namespace App\Observers;

use App\Models\Cate4;
use App\Services\CategoryService;

class Cate4Observer
{
    protected CategoryService $categoryService;

    public function __construct(CategoryService $categoryService)
    {
        $this->categoryService = $categoryService;
    }

    /**
     * Cate4 모델이 생성된 후 캐시 무효화
     */
    public function created(Cate4 $cate4): void
    {
        $this->categoryService->flushCategoryCache();
    }

    /**
     * Cate4 모델이 업데이트된 후 캐시 무효화
     */
    public function updated(Cate4 $cate4): void
    {
        $this->categoryService->flushCategoryCache();
    }

    /**
     * Cate4 모델이 삭제된 후 캐시 무효화
     */
    public function deleted(Cate4 $cate4): void
    {
        $this->categoryService->flushCategoryCache();
    }

    /**
     * Cate4 모델이 복원된 후 캐시 무효화
     */
    public function restored(Cate4 $cate4): void
    {
        $this->categoryService->flushCategoryCache();
    }
}
