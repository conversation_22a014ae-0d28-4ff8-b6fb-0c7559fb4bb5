<?php

namespace App\Observers;

use App\Models\Cate5;
use App\Services\CategoryService;

class Cate5Observer
{
    protected CategoryService $categoryService;

    public function __construct(CategoryService $categoryService)
    {
        $this->categoryService = $categoryService;
    }

    /**
     * Cate5 모델이 생성된 후 캐시 무효화
     */
    public function created(Cate5 $cate5): void
    {
        $this->categoryService->flushCategoryCache();
    }

    /**
     * Cate5 모델이 업데이트된 후 캐시 무효화
     */
    public function updated(Cate5 $cate5): void
    {
        $this->categoryService->flushCategoryCache();
    }

    /**
     * Cate5 모델이 삭제된 후 캐시 무효화
     */
    public function deleted(Cate5 $cate5): void
    {
        $this->categoryService->flushCategoryCache();
    }

    /**
     * Cate5 모델이 복원된 후 캐시 무효화
     */
    public function restored(Cate5 $cate5): void
    {
        $this->categoryService->flushCategoryCache();
    }
}
