<?php

declare(strict_types=1);

namespace App\Providers;

use App\Services\SimpleLogService;
use Illuminate\Database\Events\TransactionBeginning;
use Illuminate\Database\Events\TransactionCommitted;
use Illuminate\Database\Events\TransactionRolledBack;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;

/**
 * SQL 쿼리 로그<br>
 * 주의: 프로덕션 환경에서는 성능 및 보안 문제로 인해 비활성화하는 것이 좋습니다!<br>
 *
 * @link https://freeblogger.tistory.com/45
 */
final class DatabaseQueryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        if (config('logging.sql.enable') !== true) {
            return;
        }

        $this->logSqlQuery();
        $this->logTransactionEvents();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }

    private function logSqlQuery(): void
    {
        DB::listen(function ($query): void {
            $sql = $query->sql;

            foreach ($query->bindings as $binding) {
                $binding = match (true) {
                    is_string($binding) => "'{$binding}'",
                    is_bool($binding) => $binding ? '1' : '0',
                    is_int($binding), is_float($binding) => (string) $binding,
                    is_null($binding) => 'NULL',
                    $binding instanceof \DateTimeInterface => "'{$binding->format('Y-m-d H:i:s')}'",
                    default => $binding
                };

                $sql = preg_replace('/\\?/', $binding, $sql, 1);
            }

            SimpleLogService::debug('daily', '쿼리 실행 시간', [
                'time' => "{$query->time} ms",
                'sql' => $sql,
            ]);
        });
    }

    private function logTransactionEvents(): void
    {
        Event::listen(TransactionBeginning::class, function (TransactionBeginning $event): void {
            SimpleLogService::debug('daily', '트랜잭션 시작');
        });

        Event::listen(TransactionCommitted::class, function (TransactionCommitted $event): void {
            SimpleLogService::debug('daily', '트랜잭션 커밋');
        });

        Event::listen(TransactionRolledBack::class, function (TransactionRolledBack $event): void {
            SimpleLogService::debug('daily', '트랜잭션 롤백');
        });
    }
}
