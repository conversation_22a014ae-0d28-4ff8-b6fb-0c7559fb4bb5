<?php

namespace App\Providers;

use App\Models\Cate4;
use App\Models\Cate5;
use App\Observers\Cate4Observer;
use App\Observers\Cate5Observer;
use App\Repositories\Interfaces\MonitorSizeLookupRepositoryInterface;
use App\Repositories\Interfaces\NotificationTemplateRepositoryInterface;
use App\Repositories\Interfaces\WorkActionRepositoryInterface;
use App\Repositories\Interfaces\WorkCategoryRepositoryInterface;
use App\Repositories\Interfaces\WorkStatusRepositoryInterface;
use App\Repositories\Interfaces\WorkStatusTemplateRepositoryInterface;
use App\Repositories\MonitorSizeLookupRepository;
use App\Repositories\NotificationTemplateRepository;
use App\Repositories\WorkActionRepository;
use App\Repositories\WorkCategoryRepository;
use App\Repositories\WorkStatusRepository;
use App\Repositories\WorkStatusTemplateRepository;
use App\Services\DynamicWorkStatusService;
use App\Services\TemplateCrudService;
use App\Services\TemplateLoggingService;
use App\Services\TemplatePermissionService;
use App\Services\TemplateSearchService;
use App\Services\TemplateService;
use App\Services\TemplateStatisticsService;
use App\Services\TemplateValidationService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Repository 인터페이스와 구현체 바인딩
        $this->app->bind(WorkCategoryRepositoryInterface::class, WorkCategoryRepository::class);
        $this->app->bind(WorkActionRepositoryInterface::class, WorkActionRepository::class);
        $this->app->bind(WorkStatusTemplateRepositoryInterface::class, WorkStatusTemplateRepository::class);
        $this->app->bind(WorkStatusRepositoryInterface::class, WorkStatusRepository::class);
        $this->app->bind(MonitorSizeLookupRepositoryInterface::class, MonitorSizeLookupRepository::class);
        $this->app->bind(NotificationTemplateRepositoryInterface::class, NotificationTemplateRepository::class);

        // DynamicWorkStatusService를 싱글톤으로 등록 (Facade 지원)
        $this->app->singleton(DynamicWorkStatusService::class, function ($app) {
            return new DynamicWorkStatusService(
                $app->make(WorkCategoryRepositoryInterface::class),
                $app->make(WorkActionRepositoryInterface::class),
                $app->make(WorkStatusTemplateRepositoryInterface::class),
                $app->make(WorkStatusRepositoryInterface::class)
            );
        });

        // 템플릿 서비스들 바인딩
        $this->registerTemplateServices();
    }

    /**
     * 템플릿 관련 서비스들을 서비스 컨테이너에 등록
     */
    private function registerTemplateServices(): void
    {
        // TemplateLoggingService를 싱글톤으로 등록 (여러 서비스에서 공유)
        $this->app->singleton(TemplateLoggingService::class);

        // TemplateValidationService 등록 (상태가 없는 유틸리티 서비스)
        $this->app->bind(TemplateValidationService::class);

        // TemplatePermissionService 등록
        $this->app->bind(TemplatePermissionService::class, function ($app) {
            return new TemplatePermissionService(
                $app->make(TemplateLoggingService::class)
            );
        });

        // TemplateCrudService 등록
        $this->app->bind(TemplateCrudService::class, function ($app) {
            return new TemplateCrudService(
                $app->make(NotificationTemplateRepositoryInterface::class),
                $app->make(TemplateValidationService::class),
                $app->make(TemplatePermissionService::class),
                $app->make(TemplateLoggingService::class)
            );
        });

        // TemplateSearchService 등록
        $this->app->bind(TemplateSearchService::class, function ($app) {
            return new TemplateSearchService(
                $app->make(NotificationTemplateRepositoryInterface::class),
                $app->make(TemplateValidationService::class),
                $app->make(TemplateLoggingService::class)
            );
        });

        // TemplateStatisticsService 등록
        $this->app->bind(TemplateStatisticsService::class, function ($app) {
            return new TemplateStatisticsService(
                $app->make(NotificationTemplateRepositoryInterface::class),
                $app->make(TemplateLoggingService::class)
            );
        });

        // 메인 TemplateService 등록 (파사드 패턴)
        $this->app->bind(TemplateService::class, function ($app) {
            return new TemplateService(
                $app->make(TemplateCrudService::class),
                $app->make(TemplateSearchService::class),
                $app->make(TemplateStatisticsService::class),
                $app->make(TemplatePermissionService::class)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // 카테고리 모델 Observer 등록
        Cate4::observe(Cate4Observer::class);
        Cate5::observe(Cate5Observer::class);
    }
}
