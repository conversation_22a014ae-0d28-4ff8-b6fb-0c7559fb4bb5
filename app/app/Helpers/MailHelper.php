<?php

namespace App\Helpers;

class MailHelper
{
    /**
     * 설정에서 수신자 목록을 가져와서 파싱하여 이름과 이메일 배열로 변환
     * 지원 형식:
     * - "홍길동<<EMAIL>>, 김철수<<EMAIL>>"
     * - "<EMAIL>, <EMAIL>"
     * - "홍길동<<EMAIL>>, <EMAIL>"
     */
    public static function parseRecipients(): array
    {
        // 설정 파일에서 수신자 목록 가져오기
        $recipients = config('const.mail.to', '');

        if (empty($recipients)) {
            return []; // 수신자가 설정되지 않은 경우 빈 배열 반환
        }

        $recipientList = [];
        $items = array_map('trim', explode(',', $recipients));

        foreach ($items as $item) {
            if (empty($item)) {
                continue;
            }

            // "이름<이메일>" 형식 체크
            if (preg_match('/^(.+?)<(.+?)>$/', $item, $matches)) {
                $name = trim($matches[1]);
                $email = trim($matches[2]);

                if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $recipientList[] = [
                        'name' => $name,
                        'email' => $email,
                    ];
                }
            }
            // 이메일만 있는 경우
            elseif (filter_var($item, FILTER_VALIDATE_EMAIL)) {
                $recipientList[] = [
                    'name' => null,
                    'email' => $item,
                ];
            }
        }

        return $recipientList;
    }
}
