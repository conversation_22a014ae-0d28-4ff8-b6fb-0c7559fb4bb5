<?php

namespace App\Helpers;

use Illuminate\Support\Carbon;

class DateHelper
{
    /**
     * 입력 날짜 문자열에서 Carbon 인스턴스를 만듭니다.
     * 입력 날짜가 예상 형식이 아닌 경우 기본 날짜 문자열에서 Carbon 인스턴스를 반환합니다.
     *
     * @param  string  $dateInput  The input date string
     * @param  string  $defaultDate  The default date string
     */
    public static function createDateFromInput(string $dateInput, string $defaultDate): string
    {
        try {
            return Carbon::createFromFormat('Y-m-d', $dateInput)->toDateString();
        } catch (\Exception $e) {
            return Carbon::createFromFormat('Y-m-d', $defaultDate)->toDateString();
        }
    }

    /**
     * 시작 날짜와 종료 날짜 사이의 날짜 배열을 가져옵니다.
     * 만약 시작 날짜 또는 종료 날짜가 예상 형식이 아닌 경우, 기본 시작 날짜 또는 현재 날짜를 사용합니다.
     *
     * @return array An array containing the current date, begin date, and end date
     */
    public static function getBetweenDate(?string $begin, ?string $end): array
    {
        $now = Carbon::now();
        $defaultDate = $now->copy()->subMonths(3)->format('Y-m-d');
        $today = $now->copy()->format('Y-m-d');

        if (empty($begin)) {
            $begin = $defaultDate;
        }

        if (empty($end)) {
            $end = $today;
        }

        $beginAt = self::createDateFromInput($begin, $defaultDate);
        $endAt = self::createDateFromInput($end, $today);

        return [$now, $beginAt, $endAt];
    }
}
