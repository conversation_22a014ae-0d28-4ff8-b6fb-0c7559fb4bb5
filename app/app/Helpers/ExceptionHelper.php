<?php

namespace App\Helpers;

use App\Exceptions\BusinessException;
use App\Exceptions\DataException;
use App\Exceptions\ResourceNotFoundException;
use App\Exceptions\ValidationException;

/**
 * 예외 처리 헬퍼 클래스
 *
 * 일관된 예외 생성과 처리를 위한 헬퍼 메서드들을 제공합니다.
 */
class ExceptionHelper
{
    /**
     * 리소스를 찾을 수 없을 때 예외를 발생시킵니다.
     *
     * @throws ResourceNotFoundException
     */
    public static function throwNotFound(string $resource, string $identifier): void
    {
        throw ResourceNotFoundException::forResource($resource, $identifier);
    }

    /**
     * ID로 리소스를 찾을 수 없을 때 예외를 발생시킵니다.
     *
     * @throws ResourceNotFoundException
     */
    public static function throwNotFoundById(string $resource, int $id): void
    {
        throw ResourceNotFoundException::forId($resource, $id);
    }

    /**
     * 입력 검증 실패 시 예외를 발생시킵니다.
     *
     * @throws ValidationException
     */
    public static function throwValidation(string $field, string $message): void
    {
        throw ValidationException::forField($field, $message);
    }

    /**
     * 여러 필드 검증 실패 시 예외를 발생시킵니다.
     *
     * @throws ValidationException
     */
    public static function throwValidationForFields(array $errors): void
    {
        throw ValidationException::forFields($errors);
    }

    /**
     * 비즈니스 로직 예외를 발생시킵니다.
     *
     * @throws BusinessException
     */
    public static function throwBusiness(string $message, array $context = []): void
    {
        throw new BusinessException($message, $context);
    }

    /**
     * 중복 처리 시도 시 예외를 발생시킵니다.
     *
     * @throws BusinessException
     */
    public static function throwDuplicate(string $resource, string $identifier): void
    {
        throw BusinessException::forDuplicate($resource, $identifier);
    }

    /**
     * 잘못된 상태에서의 작업 시도 시 예외를 발생시킵니다.
     *
     * @throws BusinessException
     */
    public static function throwInvalidStatus(string $resource, string $identifier, string $currentStatus, string $expectedStatus): void
    {
        throw BusinessException::forInvalidStatus($resource, $identifier, $currentStatus, $expectedStatus);
    }

    /**
     * 권한 부족 시 예외를 발생시킵니다.
     *
     * @throws BusinessException
     */
    public static function throwPermissionDenied(string $operation): void
    {
        throw BusinessException::forPermission($operation);
    }

    /**
     * 데이터베이스 트랜잭션 실패 시 예외를 발생시킵니다.
     *
     * @throws DataException
     */
    public static function throwTransactionFailed(string $operation, ?\Throwable $previous = null): void
    {
        throw DataException::forTransaction($operation, $previous);
    }

    /**
     * 데이터 무결성 위반 시 예외를 발생시킵니다.
     *
     * @throws DataException
     */
    public static function throwIntegrityViolation(string $constraint, ?\Throwable $previous = null): void
    {
        throw DataException::forIntegrity($constraint, $previous);
    }

    /**
     * 필수 필드 누락 시 예외를 발생시킵니다.
     *
     * @throws ValidationException
     */
    public static function throwRequiredField(string $field): void
    {
        $message = "필수 필드가 누락되었습니다: {$field}";
        throw ValidationException::forField($field, $message);
    }

    /**
     * 빈 값이나 null 값에 대한 예외를 발생시킵니다.
     *
     * @throws ValidationException
     */
    public static function throwEmptyValue(string $field): void
    {
        $message = "{$field}는 빈 값일 수 없습니다.";
        throw ValidationException::forField($field, $message);
    }

    /**
     * 예외가 WMS 예외인지 확인합니다.
     */
    public static function isWmsException(\Throwable $exception): bool
    {
        return $exception instanceof \App\Exceptions\WmsException;
    }

    /**
     * 예외가 비즈니스 예외인지 확인합니다.
     */
    public static function isBusinessException(\Throwable $exception): bool
    {
        return $exception instanceof BusinessException;
    }

    /**
     * 예외가 검증 예외인지 확인합니다.
     */
    public static function isValidationException(\Throwable $exception): bool
    {
        return $exception instanceof ValidationException;
    }

    /**
     * 예외가 리소스 없음 예외인지 확인합니다.
     */
    public static function isResourceNotFoundException(\Throwable $exception): bool
    {
        return $exception instanceof ResourceNotFoundException;
    }
}
