<?php

namespace App\Helpers;

use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * API 응답 헬퍼 클래스
 *
 * 표준화된 API 응답 형식을 제공합니다.
 */
class ApiResponseHelper
{
    /**
     * 성공 응답을 생성합니다.
     */
    public static function success(
        mixed $data = null,
        string $message = '요청이 성공적으로 처리되었습니다.',
        int $statusCode = 200
    ): JsonResponse {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return new JsonResponse($response, $statusCode);
    }

    /**
     * 페이지네이션된 성공 응답을 생성합니다.
     */
    public static function successWithPagination(
        LengthAwarePaginator $paginator,
        string $message = '목록을 성공적으로 조회했습니다.'
    ): JsonResponse {
        return self::success([
            'items' => $paginator->items(),
            'pagination' => [
                'current_page' => $paginator->currentPage(),
                'per_page' => $paginator->perPage(),
                'total' => $paginator->total(),
                'total_pages' => $paginator->lastPage(),
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
                'has_more_pages' => $paginator->hasMorePages(),
            ],
        ], $message);
    }

    /**
     * 생성 성공 응답을 생성합니다.
     */
    public static function created(
        mixed $data = null,
        string $message = '리소스가 성공적으로 생성되었습니다.'
    ): JsonResponse {
        return self::success($data, $message, 201);
    }

    /**
     * 수정 성공 응답을 생성합니다.
     */
    public static function updated(
        mixed $data = null,
        string $message = '리소스가 성공적으로 수정되었습니다.'
    ): JsonResponse {
        return self::success($data, $message, 200);
    }

    /**
     * 삭제 성공 응답을 생성합니다.
     */
    public static function deleted(
        string $message = '리소스가 성공적으로 삭제되었습니다.'
    ): JsonResponse {
        return self::success(null, $message, 200);
    }

    /**
     * 에러 응답을 생성합니다.
     */
    public static function error(
        string $code,
        string $message,
        mixed $details = null,
        int $statusCode = 500
    ): JsonResponse {
        $response = [
            'success' => false,
            'error' => [
                'code' => $code,
                'message' => $message,
            ],
        ];

        if ($details !== null) {
            $response['error']['details'] = $details;
        }

        return new JsonResponse($response, $statusCode);
    }

    /**
     * 유효성 검증 에러 응답을 생성합니다.
     */
    public static function validationError(
        array $errors,
        string $message = '입력 데이터가 유효하지 않습니다.'
    ): JsonResponse {
        return self::error(
            'VALIDATION_ERROR',
            $message,
            ['validation_errors' => $errors],
            422
        );
    }

    /**
     * 인증 에러 응답을 생성합니다.
     */
    public static function unauthorized(
        string $message = '인증이 필요합니다.'
    ): JsonResponse {
        return self::error('UNAUTHORIZED', $message, null, 401);
    }

    /**
     * 권한 에러 응답을 생성합니다.
     */
    public static function forbidden(
        string $message = '접근 권한이 없습니다.'
    ): JsonResponse {
        return self::error('FORBIDDEN', $message, null, 403);
    }

    /**
     * 리소스 없음 에러 응답을 생성합니다.
     */
    public static function notFound(
        string $message = '요청한 리소스를 찾을 수 없습니다.',
        mixed $details = null
    ): JsonResponse {
        return self::error('NOT_FOUND', $message, $details, 404);
    }

    /**
     * 중복 리소스 에러 응답을 생성합니다.
     */
    public static function conflict(
        string $message = '이미 존재하는 리소스입니다.',
        mixed $details = null
    ): JsonResponse {
        return self::error('CONFLICT', $message, $details, 409);
    }

    /**
     * 서버 내부 에러 응답을 생성합니다.
     */
    public static function internalServerError(
        string $message = '서버 내부 오류가 발생했습니다.',
        mixed $details = null
    ): JsonResponse {
        return self::error('INTERNAL_SERVER_ERROR', $message, $details, 500);
    }

    /**
     * 템플릿 관련 에러 응답을 생성합니다.
     */
    public static function templateError(
        string $type,
        string $message,
        mixed $details = null
    ): JsonResponse {
        $errorCodes = [
            'not_found' => ['TEMPLATE_NOT_FOUND', 404],
            'duplicate_name' => ['TEMPLATE_DUPLICATE_NAME', 409],
            'create_failed' => ['TEMPLATE_CREATE_FAILED', 500],
            'update_failed' => ['TEMPLATE_UPDATE_FAILED', 500],
            'delete_failed' => ['TEMPLATE_DELETE_FAILED', 500],
            'access_denied' => ['TEMPLATE_ACCESS_DENIED', 403],
            'validation_failed' => ['TEMPLATE_VALIDATION_FAILED', 422],
            'invalid_priority' => ['TEMPLATE_INVALID_PRIORITY', 422],
            'increment_usage_failed' => ['TEMPLATE_INCREMENT_USAGE_FAILED', 500],
        ];

        [$code, $statusCode] = $errorCodes[$type] ?? ['TEMPLATE_ERROR', 500];

        return self::error($code, $message, $details, $statusCode);
    }
}
