<?php

namespace App\Helpers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class HelperLibrary
{
    /**
     * storage 디렉토리 하위에 원하는 디렉토리를 생성
     *
     * @param  string  $dir  디렉토리 경로
     */
    public static function makeDir(string $dir): void
    {
        $storagePath = storage_path($dir);

        // 디렉토리가 있는지 확인
        if (! file_exists($storagePath)) {
            // 디렉토리가 없으면 생성
            mkdir($storagePath, 0755, true);
        }
    }

    /**
     * 파일 업로드(storage/app 디렉토리 하위에 생성됨)
     *
     * @param  string  $dir  업로드할 경로
     */
    public static function uploadFile(Request $request, string $fileKey = 'file', string $dir = 'uploads'): string
    {
        Storage::makeDirectory($dir);

        $file = $request->file($fileKey);

        return $file->storeAs(
            $dir, $file->hashName()
        );
    }

    /**
     * 파일 절대 경로 리턴(storage/app 디렉토리 하위)
     */
    public static function getAbsoluteFilePath(string $dir, string $filename): string
    {
        // 지정된 디렉토리가 없으면 생성
        Storage::makeDirectory($dir);

        // 절대 경로를 제공: 파일의 정확한 위치를 쉽게 파악하고 접근할 수 있게 해줌
        return Storage::path($dir.'/'.$filename);
    }

    /**
     * 토큰에 사용될 랜덤 문자열을 만든다.
     *
     * 이 함수는 주어진 문자들을 무작위로 선택하여 특정 길이의 문자열을 생성하는 역할을 합니다.
     * 함수의 파라미터는 아래와 같습니다:
     *
     * 1. $characters: 단일 문자열로, 이 문자열 안에 있는 문자들 중에서 무작위로 선택될 문자가 포함되어 있습니다.
     * 2. $length: 생성하려는 문자열의 길이입니다. 기본값으로 1이 설정되어 있습니다.
     *
     * 함수의 내부 동작은 아래와 같습니다:
     *
     * 1. collect(range(1, $length)): 주어진 길이($length)만큼의 범위를 가지는 배열을 생성하고, Laravel의 컬렉션 인스턴스로 변경합니다. 예를 들어, $length가 5라면 [1, 2, 3, 4, 5]라는 배열을 생성하고 이를 컬렉션으로 만듭니다.
     *
     * 2. ->map(function () use ($characters) {...}: 이 컬렉션의 각 항목에 대해 map 메서드를 사용하여 함수를 적용합니다. 이 함수는 $characters에서 무작위로 문자를 선택하여 반환합니다. mt_rand(0, strlen($characters) - 1)는 0과 문자열 $characters의 길이-1 사이의 임의의 수를 반환하는데, 이를 인덱스로 사용해 $characters 문자열에서 문자를 선택합니다.
     *
     * 3. ->implode(''): map 함수를 통해 생성된 각각의 문자들을 결합하여 하나의 문자열을 생성합니다.
     *
     * 따라서, 이 함수는 $characters 문자열에 포함된 문자들로 구성된 $length 길이를 가진 랜덤 문자열을 생성하고 반환하는 역할을 합니다.
     */
    public static function generateRandomString(string $characters, int $length = 1): string
    {
        return collect(range(1, $length))->map(function () use ($characters) {
            return $characters[mt_rand(0, strlen($characters) - 1)];
        })->implode('');
    }

    /**
     * 바코드 및 설명(상품명)으로 해시를 생성
     *
     * @param  string  $barcode  The barcode string.
     * @param  string  $description  The description string. (엑셀 파일에서 넘어오는 형식)
     * @return string The generated hash.
     */
    public static function makeHashForBarcode(string $barcode, string $description, string $wmsSkuId, string $externalSkuId): string
    {
        $barcode = trim($barcode);
        $description = mb_strtolower(preg_replace('/\s+/', '', trim($description)));
        $wmsSkuId = trim($wmsSkuId);
        $externalSkuId = trim($externalSkuId);

        return hash('sha256', $barcode.$description.$wmsSkuId.$externalSkuId);
    }

    /**
     * 바이트 단위를 사람이 읽기 쉬운 형태로 변환
     *
     * @param  int  $bytes  바이트 수
     * @return string 포맷된 문자열
     */
    public static function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2).' '.$units[$pow];
    }

    /**
     * UTF-8 문자열 정리
     */
    public static function sanitizeUtf8(string $string): string
    {
        // UTF-8 인코딩 정리(value를 UTF-8로 재인코딩)
        // 만약 깨진 UTF-8 문자열이 들어오면 가능한 범위 내에서 잘못된 바이트 교정
        $string = mb_convert_encoding($string, 'UTF-8', 'UTF-8');

        // 잘못된 UTF-8 시퀀스 제거
        // ASCII 컨트롤 문자 (NULL, BEL, VT 등) 및 DEL(0x7F) 전체 문자열에서 삭제
        $string = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $string);

        // 제어 문자 제거
        // Zero Width Space(0x200B) ~ Zero Width Joiner(0x200D), BOM(0xFEFF)
        // 문자열 전체에서 일괄 삭제 (앞뒤, 가운데 구분 없이)
        $string = preg_replace('/[\x{200B}-\x{200D}\x{FEFF}]/u', '', $string);

        return $string;
    }
}
