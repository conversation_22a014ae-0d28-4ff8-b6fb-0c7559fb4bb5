<?php

namespace App\Helpers;

use Illuminate\Contracts\Cache\LockTimeoutException;
use Illuminate\Support\Facades\Cache;

class CacheHelper
{
    /**
     * 캐시를 안전하게 갱신합니다 (캐시 스톰 방지)
     *
     * @param  string  $key  캐시 키
     * @param  int  $ttl  캐시 유효 시간 (초)
     * @param  callable  $callback  데이터 생성 콜백
     * @param  array  $tags  캐시 태그
     */
    public static function rememberSafe(string $key, int $ttl, callable $callback, array $tags = []): mixed
    {
        // 캐시에서 데이터 조회
        $cached = self::getCache($key, $tags);
        if ($cached !== null) {
            return $cached;
        }

        // 캐시가 없으면 락을 사용하여 동시 생성 방지
        $lockKey = "lock:{$key}";
        $lock = Cache::lock($lockKey, 10); // 10초 락

        try {
            // 락을 획득
            $lock->block(10); // 최대 10초 동안 락 획득을 시도

            // 락을 획득한 후 다시 한번 캐시 확인 (다른 프로세스가 생성했을 수 있음)
            $cached = self::getCache($key, $tags);
            if ($cached !== null) {
                return $cached;
            }

            // 새 데이터 생성
            $data = $callback();

            // 캐시 저장
            self::putCache($key, $data, $ttl, $tags);

            return $data;
        } catch (LockTimeoutException $e) {
            // 락을 획득하지 못한 경우, 다른 프로세스가 캐시를 생성하고 있을 수 있으므로
            // 잠시 대기 후 캐시를 재확인
            $retries = 5; // 최대 재시도 횟수
            while ($retries-- > 0) {
                $cached = self::getCache($key, $tags);
                if ($cached !== null) {
                    return $cached;
                }
                usleep(200000); // 200밀리초 대기
            }

            return null; // 캐시를 획득하지 못했으므로 null 반환
        } finally {
            // 락을 해제
            optional($lock)->release();
        }
    }

    /**
     * 캐시를 무효화합니다
     *
     * @param  string  $key  캐시 키
     * @param  array  $tags  캐시 태그
     */
    public static function forgetCache(string $key, array $tags = []): void
    {
        if (empty($tags)) {
            Cache::forget($key);
        } else {
            Cache::tags($tags)->forget($key);
        }
    }

    /**
     * 태그 기반 캐시를 무효화합니다
     *
     * @param  array  $tags  캐시 태그
     */
    public static function flushTags(array $tags): void
    {
        Cache::tags($tags)->flush();
    }

    /**
     * 캐시 존재 여부를 확인합니다.
     *
     * @param  string  $key  캐시 키
     * @param  array  $tags  캐시 태그
     */
    public static function has(string $key, array $tags = []): bool
    {
        return empty($tags) ? Cache::has($key) : Cache::tags($tags)->has($key);
    }

    /**
     * 태그에 따라 캐시에서 데이터를 조회합니다.
     *
     * @param  string  $key  캐시 키
     * @param  array  $tags  캐시 태그
     */
    public static function getCache(string $key, array $tags = []): mixed
    {
        return empty($tags) ? Cache::get($key) : Cache::tags($tags)->get($key);
    }

    /**
     * 내부 헬퍼: 태그에 따라 캐시에 데이터를 저장합니다.
     */
    public static function putCache(string $key, mixed $data, int $ttl, array $tags = []): void
    {
        if (empty($tags)) {
            Cache::put($key, $data, $ttl);
        } else {
            Cache::tags($tags)->put($key, $data, $ttl);
        }
    }
}
