<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BoardComment extends Model
{
    use HasFactory;

    protected $fillable = [
        'board_id',
        'user_id',
        'name',
        'comment',
        'status',
        'ip', 'user_agent',
    ];

    protected $hidden = [];

    protected $casts = [];

    protected $with = ['user'];

    public function board(): BelongsTo
    {
        return $this->belongsTo(Board::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class)->withTrashed();
    }
}
