<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class QaidReprint extends Model
{
    use HasFactory;

    protected $fillable = [
        'date',
        'qaid',
        'user_id',
        'print_count',
    ];

    protected $casts = [
        'date' => 'date:Y-m-d',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 이 재발행 기록을 생성한 사용자
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    public function latestProduct(): HasOne
    {
        return $this->hasOne(Product::class, 'qaid', 'qaid')
            ->orderByDesc('created_at')
            ->limit(1);

    }

    // 상품의 현재 상태
    public function logs(): MorphMany
    {
        return $this->MorphMany(ProductLog::class, 'model');
    }
}
