<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BoardStat extends Model
{
    use HasFactory;

    protected $fillable = [
        'board_id',
        'user_id',
        'checked_at',
        'ip', 'user_agent',
    ];

    protected $hidden = [];

    protected $casts = [
        'checked_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected $with = ['user'];

    public function board(): BelongsTo
    {
        return $this->belongsTo(Board::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class)->withTrashed();
    }
}
