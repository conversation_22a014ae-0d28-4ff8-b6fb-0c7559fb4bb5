<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 모니터 크기 조회 캐시 모델
 *
 * 이미 추출된 모니터 크기 정보를 캐시하여 성능을 향상시킴
 */
class MonitorSizeLookup extends Model
{
    use HasFactory;

    /**
     * 테이블명
     */
    protected $table = 'monitor_size_lookups';

    /**
     * 대량 할당 가능한 속성들
     */
    protected $fillable = [
        'name',
        'brand',
        'size',
        'unit',
        'name_hash',
    ];

    /**
     * 캐스팅할 속성들
     */
    protected $casts = [
        'size' => 'decimal:2',
    ];

    /**
     * 브랜드 구분
     */
    const BRAND_BRAND = 'brand';

    const BRAND_ETC = 'etc';

    /**
     * 단위 구분
     */
    const UNIT_INCH = 'INCH';

    const UNIT_CM = 'CM';

    /**
     * 상품명 해시로 조회
     */
    public static function findByHash(string $nameHash): ?self
    {
        return static::where('name_hash', $nameHash)->first();
    }

    /**
     * 상품명으로 조회
     */
    public static function findByName(string $name): ?self
    {
        $nameHash = md5($name);

        return static::findByHash($nameHash);
    }

    /**
     * 브랜드별 조회
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function findByBrand(string $brand)
    {
        return static::where('brand', $brand)->get();
    }

    /**
     * 단위별 조회
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function findByUnit(string $unit)
    {
        return static::where('unit', $unit)->get();
    }

    /**
     * 크기 범위로 조회
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function findBySizeRange(float $minSize, float $maxSize)
    {
        return static::whereBetween('size', [$minSize, $maxSize])->get();
    }

    /**
     * 캐시 정리 (오래된 데이터 삭제)
     */
    public static function cleanOldCache(int $days = 30): int
    {
        $cutoffDate = now()->subDays($days);

        return static::where('created_at', '<', $cutoffDate)->delete();
    }
}
