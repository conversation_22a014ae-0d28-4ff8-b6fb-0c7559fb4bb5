<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class WorkCategory extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'code',
        'name',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 카테고리에 속한 액션들
     */
    public function actions(): HasMany
    {
        return $this->hasMany(WorkAction::class, 'category_id');
    }

    /**
     * 카테고리에 속한 활성 액션들
     */
    public function activeActions(): HasMany
    {
        return $this->hasMany(WorkAction::class, 'category_id')
            ->where('is_active', true);
    }

    /**
     * 카테고리에 속한 상태 템플릿들
     */
    public function statusTemplates(): HasMany
    {
        return $this->hasMany(WorkStatusTemplate::class, 'category_id');
    }

    /**
     * 카테고리를 통해 생성된 WorkStatus들
     */
    public function workStatuses(): Has<PERSON>any
    {
        return $this->hasMany(WorkStatus::class, 'category_id');
    }

    /**
     * 카테고리 코드로 조회
     */
    public function scopeByCode($query, string $code)
    {
        return $query->where('code', $code);
    }

    /**
     * 활성 카테고리만 조회
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
