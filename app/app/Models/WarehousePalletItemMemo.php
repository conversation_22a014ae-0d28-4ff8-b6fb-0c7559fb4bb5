<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class WarehousePalletItemMemo extends Model
{
    use SoftDeletes;

    const STATUS_STORED = 'STORED';

    const STATUS_INTERNAL_USE = 'INTERNAL_USE';

    const STATUS_INTERNAL_PARTS = 'INTERNAL_PARTS';

    const STATUS_RETURNED = 'RETURNED';

    const STATUS_DISCARDED = 'DISCARDED';

    public static array $STATUS_NAME = [
        self::STATUS_STORED => '보관중',
        self::STATUS_INTERNAL_USE => '내부사용',
        self::STATUS_INTERNAL_PARTS => '내부부품',
        self::STATUS_RETURNED => '반납(쿠팡)',
        self::STATUS_DISCARDED => '폐기',
    ];

    protected $fillable = [
        'warehouse_pallet_item_id',
        'status',
        'created_by',
        'memo',
    ];

    public function palletItem(): BelongsTo
    {
        return $this->belongsTo(WarehousePalletItem::class, 'warehouse_pallet_item_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
