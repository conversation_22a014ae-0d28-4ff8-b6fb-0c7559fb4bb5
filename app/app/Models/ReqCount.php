<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReqCount extends Model
{
    use HasFactory;

    public $timestamps = false;

    /**
     * 카운트 이름
     */
    const COUNT_COLUMNS = [
        'undelivered', // 미입고
        'unchecked', // 입고(검수)대기
        'checked', // 검수완료, 점검대기
        'carryout',  // 외주반출 수리중
        'waiting', // 수리대기(구성품 신청)
        'repaired', // 수리/점검완료(창고)
        'checkout', // 적재중(점검완료)
        'exporting', // 출고대기(마감)
        'duplicated', // 중복등록
        'unlinked', // 미등록
        'completed', // 출고완료
        'deleted', // 보류(삭제)
    ];

    protected $fillable = [
        'req_id',
        'undelivered',
        'unchecked', 'checked',
        'carryout',
        'waiting', 'repaired',
        'checkout',
        'exporting', 'duplicated',
        'unlinked', 'completed',
        'deleted',
    ];

    protected $hidden = [];

    protected $casts = [];

    public function req(): belongsTo
    {
        return $this->belongsTo(Req::class);
    }
}
