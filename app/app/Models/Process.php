<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Process extends Model
{
    const TYPE_CHECK = 'check';

    const TYPE_REPAIR = 'repair';

    const TYPE_GRADE = 'grade';

    const TYPE_FIX = 'fix';

    const TYPE_CHARGE = 'charge';

    public static array $TYPE_NAME = [
        self::TYPE_CHECK => '증상내용',
        self::TYPE_REPAIR => '처리내용',
        self::TYPE_GRADE => '수리상태',
        self::TYPE_FIX => '별도수리비',
        self::TYPE_CHARGE => '추가비용',
    ];

    const GRADE_BEST = 'ST_BEST'; // 최상

    const GRADE_GOOD = 'ST_GOOD'; // 좋음

    const GRADE_NORMAL = 'ST_NORMAL'; // 일반

    const GRADE_REFURB = 'ST_REFURB'; // 리퍼

    const GRADE_XL = 'ST_XL'; // 폐기

    public static array $GRADE_NAME = [
        self::GRADE_BEST => 'CB',
        self::GRADE_GOOD => 'CG',
        self::GRADE_NORMAL => 'CN',
        self::GRADE_REFURB => 'RP',
        self::GRADE_XL => 'XL',
    ];

    public static array $GRADE_COLOR = [
        self::GRADE_BEST => "<span style='color: #29b8ff;'>■</span>",
        self::GRADE_GOOD => "<span style='color: #29b8ff;'>■</span>",
        self::GRADE_NORMAL => "<span style='color: #29b8ff;'>■</span>",
        self::GRADE_REFURB => "<span style='color: #f8d903'>■</span>",
        self::GRADE_XL => "<span style='color: #ff6666;'>■</span>",
    ];

    protected $fillable = [
        'type', 'code', 'name',
    ];

    protected $hidden = [];

    protected $casts = [];

    public function checkPalletProducts(): HasMany
    {
        return $this->hasMany(PalletProduct::class, 'process_check_id', 'id');
    }

    public function repairPalletProducts(): HasMany
    {
        return $this->hasMany(PalletProduct::class, 'process_repair_id', 'id');
    }

    public function gradePalletProducts(): HasMany
    {
        return $this->hasMany(PalletProduct::class, 'process_grade_id', 'id');
    }

    public function checkCarryoutProducts(): HasMany
    {
        return $this->hasMany(CarryoutProduct::class, 'process_check_id', 'id');
    }

    public function repairCarryoutProducts(): HasMany
    {
        return $this->hasMany(CarryoutProduct::class, 'process_repair_id', 'id');
    }

    public function gradeCarryoutProducts(): HasMany
    {
        return $this->hasMany(CarryoutProduct::class, 'process_grade_id', 'id');
    }
}
