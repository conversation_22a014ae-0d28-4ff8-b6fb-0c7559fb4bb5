<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RepairPartsLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'repair_parts_id',
        'cate4_id',
        'cate5_id',
        'user_id',
        'quantity',
        'amount',
    ];

    public function part(): BelongsTo
    {
        return $this->belongsTo(RepairParts::class, 'repair_parts_id', 'id');
    }

    public function cate4(): BelongsTo
    {
        return $this->belongsTo(Cate4::class, 'cate4_id', 'id');
    }

    public function cate5(): BelongsTo
    {
        return $this->belongsTo(Cate5::class, 'cate5_id', 'id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
