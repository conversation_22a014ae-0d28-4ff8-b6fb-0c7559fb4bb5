<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Company extends Model
{
    use HasFactory;

    protected $fillable = [
        'code', 'name', 'kr_name',
        'logo_uri', 'type', 'f_default',
        'description', 'status',
    ];

    protected $hidden = [];

    protected $casts = [];

    public function users(): HasMany
    {
        return $this->hasMany(User::class)->withTrashed();
    }
}
