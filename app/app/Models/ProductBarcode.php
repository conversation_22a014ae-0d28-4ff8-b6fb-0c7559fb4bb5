<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use <PERSON><PERSON>\Scout\Searchable;

class ProductBarcode extends Model
{
    use HasFactory, Searchable;

    public $timestamps = false;

    protected $fillable = [
        'barcode', 'wms_sku_id', 'external_wms_sku_id',
    ];

    protected $hidden = [];

    protected $casts = [];

    /**
     * Product와의 관계 (1:N)
     * 하나의 ProductBarcode는 여러 Product와 연결될 수 있음
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'product_barcode_id', 'id');
    }

    /**
     * null 값을 '-'로 변환하는 mutator
     */
    public function setWmsSkuIdAttribute($value): void
    {
        $this->attributes['wms_sku_id'] = $value ?? '-';
    }

    public function setExternalWmsSkuIdAttribute($value): void
    {
        $this->attributes['external_wms_sku_id'] = $value ?? '-';
    }

    /**
     * '-' 값을 null로 변환하는 accessor
     */
    public function getWmsSkuIdAttribute($value): ?string
    {
        return $value === '-' ? null : $value;
    }

    public function getExternalWmsSkuIdAttribute($value): ?string
    {
        return $value === '-' ? null : $value;
    }

    /**
     * 바코드로 ProductBarcode 찾기 (가장 최근 것 우선)
     */
    public static function findByBarcode(string $barcode): ?self
    {
        return static::where('barcode', $barcode)
            ->orderBy('id', 'desc')
            ->first();
    }

    /**
     * 바코드로 모든 ProductBarcode 찾기
     */
    public static function findAllByBarcode(string $barcode): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('barcode', $barcode)
            ->orderBy('id', 'desc')
            ->get();
    }

    /**
     * 바코드와 wms_sku_id로 정확한 ProductBarcode 찾기
     */
    public static function findByBarcodeAndWmsSkuId(string $barcode, ?string $wmsSkuId = null): ?self
    {
        $wmsSkuId = $wmsSkuId ?? '-';

        return static::where([
            'barcode' => $barcode,
            'wms_sku_id' => $wmsSkuId,
        ])->first();
    }

    /**
     * 바코드 정보를 문자열로 표현
     */
    public function __toString(): string
    {
        $wmsSkuId = $this->wms_sku_id ?? '-';
        $externalSkuId = $this->external_wms_sku_id ?? '-';

        return "Barcode: {$this->barcode}, WMS: {$wmsSkuId}, External: {$externalSkuId}";
    }
}
