<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationGroupMember extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'group_id',
        'user_id',
    ];

    public function group(): BelongsTo
    {
        // 그룹에 속한 멤버의 그룹 관계
        return $this->belongsTo(NotificationGroup::class, 'group_id');
    }

    public function user(): BelongsTo
    {
        // 멤버가 누구인지의 사용자 관계
        return $this->belongsTo(User::class, 'user_id');
    }
}
