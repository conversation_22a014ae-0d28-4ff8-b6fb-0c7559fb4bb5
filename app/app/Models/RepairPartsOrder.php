<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RepairPartsOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'repair_parts_id',
        'price',
        'quantity',
        'purchase_date',
        'memo',
    ];

    public function part(): BelongsTo
    {
        return $this->belongsTo(RepairParts::class, 'repair_parts_id', 'id');
    }
}
