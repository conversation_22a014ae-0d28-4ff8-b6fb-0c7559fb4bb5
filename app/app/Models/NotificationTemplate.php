<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationTemplate extends Model
{
    use HasFactory;

    /**
     * 테이블명
     */
    protected $table = 'notification_templates';

    /**
     * 대량 할당 가능한 속성
     */
    protected $fillable = [
        'name',
        'title',
        'content',
        'priority',
        'usage_count',
        'created_by',
    ];

    /**
     * 숨겨야 할 속성
     */
    protected $hidden = [];

    /**
     * 속성 캐스팅
     */
    protected $casts = [
        'usage_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 우선순위 상수
     */
    public const PRIORITY_LOW = 'low';

    public const PRIORITY_NORMAL = 'normal';

    public const PRIORITY_HIGH = 'high';

    public const PRIORITY_URGENT = 'urgent';

    /**
     * 우선순위 목록
     */
    public const PRIORITIES = [
        self::PRIORITY_LOW => '낮음',
        self::PRIORITY_NORMAL => '보통',
        self::PRIORITY_HIGH => '높음',
        self::PRIORITY_URGENT => '긴급',
    ];

    /**
     * 생성자와의 관계 (Many-to-One)
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 사용 횟수 증가
     */
    public function incrementUsage(): bool
    {
        return $this->increment('usage_count');
    }

    /**
     * 우선순위 한글명 반환
     */
    public function getPriorityNameAttribute(): string
    {
        return self::PRIORITIES[$this->priority] ?? '알 수 없음';
    }

    /**
     * 스코프: 우선순위별 필터링
     */
    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * 스코프: 검색 (이름, 제목에서 부분 일치)
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
                ->orWhere('title', 'like', "%{$search}%");
        });
    }

    /**
     * 스코프: 지정된 필드에서 검색
     */
    public function scopeSearchInFields($query, string $search, array $fields = ['name', 'title'])
    {
        return $query->where(function ($q) use ($search, $fields) {
            foreach ($fields as $field) {
                if (in_array($field, ['name', 'title', 'content'])) {
                    $q->orWhere($field, 'like', "%{$search}%");
                }
            }
        });
    }

    /**
     * 스코프: 생성자별 필터링
     */
    public function scopeByCreator($query, int $createdBy)
    {
        return $query->where('created_by', $createdBy);
    }

    /**
     * 스코프: 사용 횟수 범위 필터링
     */
    public function scopeByUsageRange($query, ?int $min = null, ?int $max = null)
    {
        if ($min !== null) {
            $query->where('usage_count', '>=', $min);
        }
        if ($max !== null) {
            $query->where('usage_count', '<=', $max);
        }

        return $query;
    }

    /**
     * 스코프: 생성일 범위 필터링
     */
    public function scopeByDateRange($query, ?string $from = null, ?string $to = null)
    {
        if ($from !== null) {
            $query->whereDate('created_at', '>=', $from);
        }
        if ($to !== null) {
            $query->whereDate('created_at', '<=', $to);
        }

        return $query;
    }

    /**
     * 스코프: 우선순위 기준 정렬
     */
    public function scopeOrderByPriority($query, string $direction = 'desc')
    {
        // 우선순위 순서: urgent > high > normal > low
        $priorityOrder = [
            'urgent' => 4,
            'high' => 3,
            'normal' => 2,
            'low' => 1,
        ];

        return $query->orderByRaw(
            "CASE priority 
                WHEN 'urgent' THEN 4 
                WHEN 'high' THEN 3 
                WHEN 'normal' THEN 2 
                WHEN 'low' THEN 1 
                ELSE 0 
            END ".$direction
        );
    }

    /**
     * 스코프: 사용횟수 기준 정렬
     */
    public function scopeOrderByUsage($query, string $direction = 'desc')
    {
        return $query->orderBy('usage_count', $direction);
    }

    /**
     * 스코프: 생성일 기준 정렬
     */
    public function scopeOrderByCreated($query, string $direction = 'desc')
    {
        return $query->orderBy('created_at', $direction);
    }

    /**
     * 데이터 유효성 검증
     *
     * @param  array  $data  검증할 데이터
     * @return array 검증 오류 목록
     */
    public static function validateData(array $data): array
    {
        $errors = [];

        // 템플릿명 검증
        if (empty($data['name']) || trim($data['name']) === '') {
            $errors[] = '템플릿명은 필수입니다.';
        } elseif (strlen($data['name']) > 50) {
            $errors[] = '템플릿명은 50자를 초과할 수 없습니다.';
        }

        // 제목 검증
        if (empty($data['title']) || trim($data['title']) === '') {
            $errors[] = '제목은 필수입니다.';
        } elseif (strlen($data['title']) > 200) {
            $errors[] = '제목은 200자를 초과할 수 없습니다.';
        }

        // 내용 검증
        if (empty($data['content']) || trim($data['content']) === '') {
            $errors[] = '내용은 필수입니다.';
        } elseif (strlen($data['content']) > 5000) {
            $errors[] = '내용은 5000자를 초과할 수 없습니다.';
        }

        // 우선순위 검증
        if (isset($data['priority']) && ! array_key_exists($data['priority'], self::PRIORITIES)) {
            $errors[] = '유효하지 않은 우선순위입니다.';
        }

        return $errors;
    }

    /**
     * 현재 인스턴스의 데이터 유효성 검증
     *
     * @return array 검증 오류 목록
     */
    public function validate(): array
    {
        return self::validateData([
            'name' => $this->name,
            'title' => $this->title,
            'content' => $this->content,
            'priority' => $this->priority,
        ]);
    }

    /**
     * 템플릿명 중복 검증
     *
     * @param  string  $name  검증할 템플릿명
     * @param  int|null  $excludeId  제외할 템플릿 ID (수정 시 사용)
     * @return bool 중복 여부 (true: 중복됨, false: 중복되지 않음)
     */
    public static function isDuplicateName(string $name, ?int $excludeId = null): bool
    {
        $query = self::where('name', $name);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * 생성 전 유효성 검증
     *
     * @param  array  $data  생성할 데이터
     * @return array 검증 오류 목록
     */
    public static function validateForCreate(array $data): array
    {
        $errors = self::validateData($data);

        // 템플릿명 중복 검증
        if (! empty($data['name']) && self::isDuplicateName($data['name'])) {
            $errors[] = '이미 존재하는 템플릿명입니다.';
        }

        // 생성자 ID 검증
        if (empty($data['created_by'])) {
            $errors[] = '생성자 정보는 필수입니다.';
        }

        return $errors;
    }

    /**
     * 수정 전 유효성 검증
     *
     * @param  array  $data  수정할 데이터
     * @param  int  $id  수정할 템플릿 ID
     * @return array 검증 오류 목록
     */
    public static function validateForUpdate(array $data, int $id): array
    {
        $errors = self::validateData($data);

        // 템플릿명 중복 검증 (자신 제외)
        if (! empty($data['name']) && self::isDuplicateName($data['name'], $id)) {
            $errors[] = '이미 존재하는 템플릿명입니다.';
        }

        return $errors;
    }
}
