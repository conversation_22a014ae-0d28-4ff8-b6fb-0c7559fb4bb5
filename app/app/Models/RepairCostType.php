<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

/**
 * 수리 유형 모델
 *
 * @property int $id
 * @property string $code 수리비 유형 코드
 * @property string $name 수리비 유형 표시 이름
 * @property string|null $description 수리비 유형 설명
 * @property bool $is_active 활성화 여부
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read Collection|RepairCost[] $repairCosts 수리비 목록
 * @property-read Collection|RepairCostTypeProcessMapping[] $processMappings 수리 프로세스 매핑 목록
 */
class RepairCostType extends Model
{
    use HasFactory;

    const TYPE_PARTS = 'parts';

    const TYPE_CLEANING = 'cleaning';

    const TYPE_SOFTWARE = 'software';

    const TYPE_OTHER = 'other';

    const TYPE_INSPECTION = 'inspection';

    protected $fillable = [
        'code',
        'name',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 수리비와의 관계 (1:N)
     */
    public function repairCosts(): HasMany
    {
        return $this->hasMany(RepairCost::class, 'repair_cost_type_id', 'id');
    }

    /**
     * 수리 프로세스 매핑과의 관계 (1:N)
     */
    public function costTypeProcessMappings(): HasMany
    {
        return $this->hasMany(RepairCostTypeProcessMapping::class, 'repair_cost_type_id', 'id');
    }

    /**
     * 수리 프로세스와의 관계 (매핑을 통해)
     */
    public function repairProcesses(): RepairCostType|HasManyThrough
    {
        return $this->hasManyThrough(
            RepairProcess::class,
            RepairCostTypeProcessMapping::class,
            'repair_cost_type_id', // RepairProcessMapping의 외래 키
            'id', // RepairProcess의 로컬 키
            'id', // RepairCostType의 로컬 키
            'repair_process_id' // RepairProcessMapping의 외래 키
        );
    }

    /**
     * 활성화된 수리 유형만 조회하는 스코프
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 코드로 조회하는 스코프
     */
    public function scopeByCode($query, string $code)
    {
        return $query->where('code', $code);
    }

    /**
     * 활성화된 모든 수리 유형 조회
     */
    public static function getActiveTypes(): Collection
    {
        return self::active()->orderBy('code')->get();
    }

    /**
     * 코드로 수리 유형 조회
     */
    public static function findByCode(string $code): ?self
    {
        return self::where('code', $code)->first();
    }

    /**
     * 수리 유형 유효성 검증 (DB 기반)
     */
    public static function validateCode(string $code): bool
    {
        return self::where('code', $code)->exists();
    }

    /**
     * 수리 유형 표시명 반환
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }

    /**
     * 수리 유형 코드 목록 반환 (DB 기반)
     */
    public static function getRepairTypeCodes(): array
    {
        return self::active()->pluck('code')->toArray();
    }

    /**
     * 수리 유형 목록 반환 (DB 기반)
     */
    public static function getRepairTypes(): array
    {
        return self::active()->pluck('name', 'code')->toArray();
    }
}
