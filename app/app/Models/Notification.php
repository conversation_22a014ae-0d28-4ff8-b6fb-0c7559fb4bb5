<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Str;

class Notification extends Model
{
    use HasFactory;

    /**
     * 모델의 ID가 자동 증가하지 않음을 나타냅니다.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * 모델의 기본 키 자료형을 나타냅니다.
     *
     * @var string
     */
    protected $keyType = 'string';

    protected $primaryKey = 'id';

    protected $fillable = [
        'title',
        'content',
        'priority',
        'action_url',
        'sender_id',
        'target_type',
        'target_id',
        'status',
        'sent_at',
    ];

    protected function casts(): array
    {
        return [
            'sent_at' => 'timestamp',
        ];
    }

    /**
     * 모델의 "부팅" 메서드.
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($model) {
            // ID가 설정되지 않았다면 UUID를 할당합니다.
            if (! $model->getKey()) {
                $model->setAttribute($model->getKeyName(), (string) Str::uuid());
            }
        });
    }

    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function recipients(): HasMany
    {
        return $this->hasMany(NotificationRecipient::class);
    }

    /**
     * 알림의 대상과의 Polymorphic 관계
     */
    public function targetable(): MorphTo
    {
        return $this->morphTo('target', 'target_type', 'target_id');
    }

    /**
     * 알림의 대상 그룹과의 관계 (target_type이 'group'인 경우)
     */
    public function targetGroup(): BelongsTo
    {
        return $this->belongsTo(NotificationGroup::class, 'target_id')
                    ->where('target_type', 'group');
    }

    /**
     * 알림의 대상 사용자와의 관계 (target_type이 'individual'인 경우)
     */
    public function targetUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'target_id')
                    ->where('target_type', 'individual');
    }
}
