<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class WarehousePallet extends Model
{
    use SoftDeletes;

    const TYPE_MANAGED = 'MANAGED';

    const TYPE_WAREHOUSING = 'WAREHOUSING';

    public static array $TYPE_NAME = [
        self::TYPE_MANAGED => '관리',
        self::TYPE_WAREHOUSING => '입고',
    ];

    const STATUS_AVAILABLE = 'available';

    const STATUS_IN_USE = 'in_use';

    const STATUS_PENDING = 'pending';

    const STATUS_COMPLETED = 'completed';

    public static array $STATUS_NAME = [
        self::STATUS_AVAILABLE => '사용 가능',
        self::STATUS_IN_USE => '적재중',
        self::STATUS_PENDING => '입고 대기중',
        self::STATUS_COMPLETED => '입고 완료',
    ];

    protected $fillable = [
        'type',
        'position_id',
        'barcode',
        'pallet_number',
        'area_code',
        'zone_code',
        'floor_code',
        'position_code',
        'status',
        'created_by',
        'loaded_by',
        'max_capacity',
        'current_capacity',
        'received_at',
        'shipped_at',
        'memo',
    ];

    protected function casts(): array
    {
        return [
            'max_capacity' => 'integer',
            'current_capacity' => 'integer',
            'received_at' => 'datetime',
            'shipped_at' => 'datetime',
        ];
    }

    public function position(): BelongsTo
    {
        return $this->belongsTo(Position::class);
    }

    public function palletItems(): HasMany
    {
        return $this->hasMany(WarehousePalletItem::class);
    }

    public function palletHistories(): HasMany
    {
        return $this->hasMany(WarehousePalletHistory::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function shippedInBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'shipped_in_by');
    }

    public function shippedOutBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'shipped_out_by');
    }
}
