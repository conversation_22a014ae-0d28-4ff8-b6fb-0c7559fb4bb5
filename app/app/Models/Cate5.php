<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Cate5 extends Model
{
    use HasFactory;

    protected $table = 'cate5';

    public $timestamps = false;

    protected $fillable = ['cate5_no', 'cate4_id', 'name'];

    protected $hidden = [];

    protected $casts = [];

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'cate5_id', 'id');
    }

    public function cate4(): BelongsTo
    {
        return $this->belongsTo(Cate4::class, 'cate4_id', 'id');
    }

    public function partsLogs(): HasMany
    {
        return $this->hasMany(RepairPartsLog::class, 'cate5_id', 'id');
    }

    /**
     * 수리비 카테고리와의 관계
     */
    public function repairCostCategories(): Has<PERSON>any
    {
        return $this->hasMany(RepairCostCategory::class, 'cate5_id', 'id');
    }

    /**
     * 활성화된 수리비 카테고리만 조회
     */
    public function activeRepairCostCategories(): Has<PERSON>any
    {
        return $this->repairCostCategories()->where('is_active', true);
    }
}
