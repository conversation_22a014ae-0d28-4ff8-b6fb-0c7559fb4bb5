<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductLink extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $fillable = ['external_wms_sku_id', 'vendor_item_id', 'product_id', 'item_id'];

    protected $hidden = [];

    protected $casts = [];

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'product_link_id', 'id');
    }

    /**
     * ProductLink 정보를 문자열로 표현
     */
    public function __toString(): string
    {
        return "External: {$this->external_wms_sku_id}, Vendor: {$this->vendor_item_id}, Product: {$this->product_id}, Item: {$this->item_id}";
    }

    /**
     * external_wms_sku_id와 vendor_item_id로 ProductLink 찾기
     */
    public static function findByExternalAndVendor(?string $externalWmsSkuId, ?string $vendorItemId): ?ProductLink
    {
        if (! $externalWmsSkuId || ! $vendorItemId) {
            return null;
        }

        return self::where([
            'external_wms_sku_id' => $externalWmsSkuId,
            'vendor_item_id' => $vendorItemId,
        ])->first();
    }
}
