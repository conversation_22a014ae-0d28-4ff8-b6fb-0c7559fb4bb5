<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RepairCostPolicy extends Model
{
    use HasFactory;

    // 가격 결정 방식 상수
    const PRICING_TYPE_SIZE = 'size';      // 크기 기준 (모니터)

    const PRICING_TYPE_PRICE = 'price';    // 판매가 기준 (일반 제품)

    const PRICING_TYPE_COMMON = 'common';    // 공통 (애플 맥북, 아이맥 제품)

    // 시스템 코드 상수
    const POLICY_GENERAL_PRICE = 'general_price';

    const POLICY_GENERAL_COMMON = 'general_common';

    const POLICY_MONITOR_BRAND = 'monitor_brand';

    const POLICY_MONITOR_GENERAL = 'monitor_general';

    const POLICY_APPLE_MACBOOK = 'apple_macbook';

    const POLICY_APPLE_IMAC = 'apple_imac';

    const POLICY_APPLE_ETC = 'apple_etc';

    const POLICY_OS_INSTALL_PRICE = 'os_install_price';

    const POLICY_OS_INSTALL_COMMON = 'os_install_common';

    const POLICY_DEFAULT = 'default'; // 기본 수리비 정책 코드

    protected $fillable = [
        'code',
        'name',
        'description',
        'pricing_type',
        'is_active',
        'order_no',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * 수리비 카테고리와의 관계
     */
    public function categories(): HasMany
    {
        return $this->hasMany(RepairCostCategory::class, 'repair_cost_policy_id', 'id');
    }

    /**
     * 활성화된 카테고리만 조회
     */
    public function activeCategories(): HasMany
    {
        return $this->categories()->where('is_active', true);
    }

    /**
     * 활성화된 시스템만 조회하는 스코프
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 시스템 코드로 조회하는 스코프
     */
    public function scopeByCode($query, string $code)
    {
        return $query->where('code', $code);
    }

    /**
     * 시스템명으로 조회하는 스코프 (기존 호환성)
     */
    public function scopeByName($query, string $name)
    {
        return $query->where('code', $name);
    }

    /**
     * 가격 결정 방식으로 조회하는 스코프
     */
    public function scopeByPricingType($query, string $pricingType)
    {
        return $query->where('pricing_type', $pricingType);
    }

    /**
     * 가격 결정 방식 목록 반환
     */
    public static function getPricingTypes(): array
    {
        return [
            self::PRICING_TYPE_SIZE => '크기 기준',
            self::PRICING_TYPE_PRICE => '판매가 기준',
        ];
    }

    /**
     * 시스템 코드 목록 반환
     */
    public static function getPolicyCodes(): array
    {
        return [
            self::POLICY_MONITOR_BRAND => 'brand_monitor',
            self::POLICY_MONITOR_GENERAL => 'general_monitor',
            self::POLICY_APPLE_MACBOOK => 'apple_macbook',
            self::POLICY_APPLE_IMAC => 'apple_imac',
            self::POLICY_APPLE_ETC => 'apple_etc',
            self::POLICY_GENERAL_PRICE => 'general',
            self::POLICY_OS_INSTALL_PRICE => 'os_install',
            self::POLICY_DEFAULT => 'default',
        ];
    }

    /**
     * 시스템명 목록 반환 (기존 호환성)
     */
    public static function getPolicyNames(): array
    {
        return [
            self::POLICY_MONITOR_BRAND => '브랜드 모니터',
            self::POLICY_MONITOR_GENERAL => '일반 모니터',
            self::POLICY_APPLE_MACBOOK => '애플 맥북',
            self::POLICY_APPLE_IMAC => '애플 아이맥',
            self::POLICY_APPLE_ETC => '애플 제품',
            self::POLICY_GENERAL_PRICE => '일반',
            self::POLICY_OS_INSTALL_PRICE => '운영체제 설치',
            self::POLICY_DEFAULT => '기본 수리비',
        ];
    }

    /**
     * 시스템이 모니터 타입인지 확인
     */
    public function isMonitorPolicy(): bool
    {
        return $this->code === self::POLICY_MONITOR_GENERAL;
    }

    /**
     * 시스템이 크기 기준 타입인지 확인
     */
    public function isSizeBasedPolicy(): bool
    {
        return $this->pricing_type === self::PRICING_TYPE_SIZE;
    }

    /**
     * 시스템이 가격 기준 타입인지 확인
     */
    public function isPriceBasedPolicy(): bool
    {
        return $this->pricing_type === self::PRICING_TYPE_PRICE;
    }

    /**
     * 시스템이 크기 기준 타입인지 확인 (별칭)
     */
    public function isSizeBased(): bool
    {
        return $this->isSizeBasedPolicy();
    }

    /**
     * 시스템이 가격 기준 타입인지 확인 (별칭)
     */
    public function isPriceBased(): bool
    {
        return $this->isPriceBasedPolicy();
    }

    /**
     * 시스템 표시명 반환 (기존 호환성)
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name;
    }
}
