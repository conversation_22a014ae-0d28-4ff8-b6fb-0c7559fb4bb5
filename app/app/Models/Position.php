<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Position extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'floor_id',
        'name',
        'code',
        'row',
        'column',
        'description',
        'is_active', // 활성 여부
        'is_occupied', // 점유 여부
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'is_occupied' => 'boolean',
        ];
    }

    public function floor(): BelongsTo
    {
        return $this->belongsTo(Floor::class);
    }

    public function warehousePallets(): HasMany
    {
        return $this->hasMany(WarehousePallet::class);
    }

    public function palletHistories(): HasMany
    {
        return $this->hasMany(WarehousePalletHistory::class);
    }
}
