<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WorkStatus extends Model
{
    use HasFactory;

    const LINK_LOG_IN = 'Login'; // 로그인

    const LINK_LOG_OUT = 'Logout'; // 로그아웃

    const LINK_REQ_CREATE = 'ReqService_Create'; // 입고목록 등록

    const LINK_REQ_UPDATE = 'ReqService_Update'; // 입고목록 수정

    const LINK_REQ_DELETE = 'ReqService_Delete'; // 입고목록 삭제

    const LINK_INSPECT = 'ProductsImport_Inspect'; // 검수대기

    const LINK_INSPECTION_COMPLETE = 'ProductService_Complete'; // 검수완료

    const LINK_INSPECTION_PASS = 'InspectionPassJob_Pass'; // 검수통과

    const LINK_UNDELIVERED = 'InspectionPassJob_Undelivered'; // 미입고

    const LINK_WAREHOUSE_PALLET_CREATE = 'WarehousePallet_Store'; // 입고 팔레트 등록(생성)

    const LINK_WAREHOUSE_PALLET_STATUS_CHANGE = 'WarehousePallet_Status_Change'; // 입고 팔레트 상태 변경

    const LINK_WAREHOUSE_PALLET_ITEM_CREATE = 'WarehousePalletItem_Store'; // 입고 팔레트에 상품 저장

    const LINK_DUPLICATE = 'ProductsImport_Duplicate'; // 중복상품

    const LINK_PRODUCT_DELETE = 'ProductService_Delete'; // 상품 삭제

    const LINK_PRODUCT_UNLINKED_CREATE = 'ProductService_Unlinked_Create'; // 미등록 상품 등록

    const LINK_PRODUCT_UNLINKED_UPDATE = 'ProductService_Unlinked_Update'; // 미등록 상품 수정

    const LINK_CARRYOUT_EXPORT = 'CarryoutService_Export'; // 외주 반출

    const LINK_CARRYOUT_IMPORT = 'CarryoutService_Import'; // 외주 반입

    const LINK_CARRYOUT_CANCEL = 'CarryoutService_Cancel'; // 외주 취소

    const LINK_REPAIR_WAITING = 'RepairService_Waiting'; // 수리/점검 대기(구성품 신청)

    const LINK_REPAIR_COMPLETE = 'LoadedService_Complete'; // 수리/점검 완료

    const LINK_PALLET_CREATE = 'PalletService_Create'; // 팔레트 등록(생성)

    const LINK_PALLET_PRODUCT_INSPECT = 'PalletService_Product_Inspect'; // 팔레트 상품 미검수

    const LINK_PALLET_PRODUCT_INSPECT_PASS = 'PalletService_Product_Inspect_Pass'; // 팔레트 상품 검수 완료

    const LINK_PALLET_OPEN = 'PalletService_Open'; // 적재 중

    const LINK_PALLET_PRODUCT_REGISTERED = 'PalletService_Product_Registered'; // 팔레트에 상품 적재

    const LINK_PALLET_CLOSED = 'PalletService_Closed'; // 적재 마감(출고 대기)

    const LINK_PALLET_EXPORTED = 'PalletService_Exported'; // 팔레트 출고 완료

    const LINK_PALLET_EXPORTED_DATE_SAVE = 'PalletService_Update_Pallet_Export_Date'; // 팔레트 출고 날짜 저장

    const LINK_PALLET_PRODUCT_EXPORTED = 'PalletService_Product_Exported'; // 팔레트 상품 출고

    const LINK_PALLET_DELETE = 'PalletService_Delete'; // 팔레트 삭제

    const LINK_PALLET_PRODUCT_PENDING = 'PalletService_Product_Pending'; // 팔레트 상품 출고 보류

    const LINK_PALLET_PRODUCT_DELETE = 'PalletService_Product_Delete'; // 팔레트 상품 삭제

    const LINK_QAID_REPRINT = 'QaidService_storeQaid'; // QAID 재발행

    const LINK_REPAIR_XL1 = 'REPAIR_XL1'; // XL1: 내외부 파손(작동불량 포함): CH_BROKEN

    const LINK_REPAIR_XL2 = 'REPAIR_XL2'; // XL2: 본품 및 구성품 (과다) 오염: CH_STAIN

    const LINK_REPAIR_XL3 = 'REPAIR_XL3'; // XL3: 구성품 부족: CH_COMPO

    const LINK_REPAIR_XL4 = 'REPAIR_XL4'; // XL4: 작동 및 기능 불량: CH_RUN

    const LINK_REPAIR_XL5 = 'REPAIR_XL5'; // XL5: 본품 상이: CH_DIFFER

    const LINK_REPAIR_XL6 = 'REPAIR_XL6'; // XL6: 본품 없음: CH_EMPTY

    const LINK_REPAIR_NORMAL = 'REPAIR_NORMAL'; // 점검(중)

    const LINK_REPAIR_GOOD = 'REPAIR_GOOD'; // 점검(상)

    const LINK_REPAIR_BEST = 'REPAIR_BEST'; // 점검(최상)

    const LINK_REPAIR_ISSUE1 = 'REPAIR_ISSUE1'; // 리퍼1(증상): 작동 및 기능 불량: CH_RUN

    const LINK_REPAIR_PROCESS1 = 'REPAIR_PROCESS1'; // 리퍼1(처리): 수리 또는 부품 교체: RP_PART

    const LINK_REPAIR_ISSUE2 = 'REPAIR_ISSUE2'; // 리퍼2(증상): 전원 불량: CH_POWER

    const LINK_REPAIR_PROCESS2 = 'REPAIR_PROCESS2'; // 리퍼2(처리): 전원부 수리 또는 교체: RP_POWER

    const LINK_REPAIR_ISSUE3 = 'REPAIR_ISSUE3'; // 리퍼3(증상): 눌림 또는 스크래치: CH_SCRATCH

    const LINK_REPAIR_PROCESS3 = 'REPAIR_PROCESS3'; // 리퍼3(처리): 훼손부 리퍼 완료: RP_REFUR

    const LINK_REPAIR_ISSUE4 = 'REPAIR_ISSUE4'; // 리퍼4(증상): 본품 및 구성품 (과다) 오염: CH_STAIN

    const LINK_REPAIR_PROCESS4 = 'REPAIR_PROCESS4'; // 리퍼4(처리): 오염부 리퍼 완료: RP_CLEAN

    const LINK_REPAIR_ISSUE5 = 'REPAIR_ISSUE5'; // 리퍼5(증상): 구성품 부족: CH_COMPO

    const LINK_REPAIR_PROCESS5 = 'REPAIR_PROCESS5'; // 리퍼5(처리): 구성품 완성: RP_COMPO

    const LINK_REPAIR_ISSUE6 = 'REPAIR_ISSUE6'; // 리퍼6(증상): 부팅 에러: CH_ERRBOOT

    const LINK_REPAIR_PROCESS6 = 'REPAIR_PROCESS6'; // 리퍼6(처리): S/W 재설정: RP_SW

    const LINK_REPAIR_PROCESS7 = 'REPAIR_PROCESS7'; // 리퍼7(처리): 구성품 청구(대기): RP_WAITING

    const LINK_REPAIR_OS_REINSTALL = 'REPAIR_OS_REINSTALL'; // 리퍼6(OS 재설치): OS REINSTALL

    const LINK_REPAIR_APPLE_BOOTX = 'APPLE_BOOTX'; // (애플)부팅시 반복적으로 다시 시작됨: AP_BOOTX

    const LINK_REPAIR_APPLE_OSX = 'APPLE_OSX'; // (애플)운영체제 실행이 되지 않음: AP_OSX

    const LINK_REPAIR_APPLE_POWERX = 'APPLE_POWERX'; // (애플)전원이 들어오지 않음: AP_POWERX

    const LINK_REPAIR_APPLE_CHARGEX = 'APPLE_CHARGEX'; // (애플)충전이 되지 않음: AP_CHARGEX

    const LINK_REPAIR_APPLE_WIFIX = 'APPLE_WIFIX'; // (애플)와이파이 문제(연결 불가/목록 없음): XP_WIFIX

    const LINK_REPAIR_APPLE_INPUTX = 'APPLE_INPUTX'; // (애플)입력장치 문제(키보드, 마우스, 터치, 마이크 작동 불가): AP_INPUTX

    const LINK_REPAIR_APPLE_PEACTX = 'APPLE_PEACTX'; // (애플)기기의 반응이 느리거나, 반응이 없음: AP_PEACTX

    const LINK_REPAIR_APPLE_BUTTONX = 'APPLE_BUTTONX'; // (애플)버튼 불량(전원 버튼, 음량 조절 버튼 작동 안됨): AP_BUTTONX

    const LINK_REPAIR_APPLE_USBX = 'APPLE_USBX'; // (애플)USB 포트/충전 포트 접촉 불량: AP_USBX

    const LINK_REPAIR_APPLE_SCREENX = 'APPLE_SCREENX'; // (애플)액정에 줄이 가 있음: AP_SCREENX

    const LINK_REPAIR_APPLE_PIXELX = 'APPLE_PIXELX'; // (애플)액정 불량화소(Dead Pixel): AP_PIXELX

    const LINK_REPAIR_APPLE_CASEX = 'APPLE_CASEX'; // (애플)에어팟 케이스 유격이 맞지 않아 닫히지 않음: AP_CASEX

    const LINK_REPAIR_APPLE_CAMERAX = 'APPLE_CAMERAX'; // (애플)카메라 불량(작동 X, 블러 현상): AP_CAMERAX

    const LINK_REPAIR_APPLE_SPACEX = 'APPLE_SPACEX'; // (애플)디스플레이와 본체 사이에 유격 존재(터치시 소리 발생): AP_SPACEX

    const LINK_REPAIR_APPLE_STRAPX = 'APPLE_STRAPX'; // (애플)애플워치 스트랩 연결 불량: AP_STRAPX

    const LINK_REPAIR_APPLE_ETC = 'APPLE_ETC'; // (애플)기타 메모(페어링, 화면 안 이물질, 빛 번짐, 백화/흑화 현상, 전원 유지 X): AP_ETC

    const LINK_REPAIR_ADD_COST = 'REPAIR_ADD_COST'; // 추가 수리 비용이 들어간 경우

    const LINK_REPAIR_ADD_PARTS = 'REPAIR_ADD_PARTS'; // 추가 구성품들이 들어간 경우(여러 개가 될 수 있음)

    const LINK_REPAIR_REFURB = 'REPAIR_REFURB'; // 리퍼 완료

    protected $fillable = [
        'code',
        'name',
        'description',
        'line_code',
        'category_id',
        'action_id',
        'template_id',
        'auto_generated',
        'generation_context',
        'link_code',
    ];

    protected $casts = [
        'auto_generated' => 'boolean',
        'generation_context' => 'array',
    ];

    /**
     * WorkStatus가 속한 카테고리
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(WorkCategory::class, 'category_id');
    }

    /**
     * WorkStatus가 속한 액션
     */
    public function action(): BelongsTo
    {
        return $this->belongsTo(WorkAction::class, 'action_id');
    }

    /**
     * WorkStatus를 생성한 템플릿
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(WorkStatusTemplate::class, 'template_id');
    }

    public function logs(): HasMany
    {
        return $this->hasMany(ProductLog::class, 'work_status_id');
    }

    /**
     * 자동 생성된 상태만 조회
     */
    public function scopeAutoGenerated($query)
    {
        return $query->where('auto_generated', true);
    }

    /**
     * 수동 생성된 상태만 조회
     */
    public function scopeManualGenerated($query)
    {
        return $query->where('auto_generated', false);
    }

    /**
     * 링크 코드로 조회
     */
    public function scopeByLinkCode($query, string $linkCode)
    {
        return $query->where('link_code', $linkCode);
    }
}
