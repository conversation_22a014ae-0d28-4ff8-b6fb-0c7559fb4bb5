<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Laravel\Scout\Searchable;

class Product extends Model
{
    use HasFactory, Searchable;

    /** 입고 검수 여부 */
    const CHECKED_STATUS_UNCHECKED = 10;

    const CHECKED_STATUS_CHECKED = 20;

    const CHECKED_STATUS_UNDELIVERED = 30;

    public static array $CHECK_STATUS_NAME = [
        self::CHECKED_STATUS_UNCHECKED => '검수대기',
        self::CHECKED_STATUS_CHECKED => '검수완료',
        self::CHECKED_STATUS_UNDELIVERED => '미입고',
    ];

    /** 상품 상태 */
    const STATUS_REGISTERED = 10;

    const STATUS_WAITING = 19;

    const STATUS_REPAIRED = 20;

    const STATUS_CHECKED_ON_PALLET = 30;

    const STATUS_CARRIED_OUT = 50;

    const STATUS_CARRIED_OUT_WAITING = 51;

    const STATUS_CARRIED_OUT_REPAIRED = 52;

    const STATUS_EXPORTED = 70;

    const STATUS_HELD = 80;

    const STATUS_DELETED = 90;

    public static array $STATUS_NAME = [
        self::STATUS_REGISTERED => '수리대기중(창고)', // 창고 저장
        self::STATUS_WAITING => '구성품 신청중(대기)', // 수리대기(구성품 신청)
        self::STATUS_REPAIRED => '수리/점검완료(창고)',
        self::STATUS_CHECKED_ON_PALLET => '점검완료(적재중)',
        self::STATUS_CARRIED_OUT => '반출중', // 외주 반출
        self::STATUS_CARRIED_OUT_WAITING => '반출중(수리대기)', // 외주 수리대기
        self::STATUS_CARRIED_OUT_REPAIRED => '반출중(수리완료)', // 외주 수리완료
        self::STATUS_EXPORTED => '출고완료',
        self::STATUS_HELD => '출고보류',
        self::STATUS_DELETED => '삭제',
    ];

    const IS_RG_Y = 'Y';

    const IS_RG_N = 'N';

    public static array $RG_NAME = [
        self::IS_RG_Y => 'RG',
        self::IS_RG_N => '일반',
    ];

    const IS_DUPLICATED_Y = 'Y';

    const IS_DUPLICATED_N = 'N';

    public static array $DUPLICATED_NAME = [
        self::IS_DUPLICATED_Y => '중복상품',
        self::IS_DUPLICATED_N => '단일상품',
    ];

    const AGING = [
        'rg' => 30,
        'normal' => 60,
    ];

    /**
     * status 는 상품의 전체적인 상태를 나타내고
     * checked_status 는 상품의 입고 검수 상태를 나타낸다.
     */
    protected $fillable = [
        'req_id', 'qaid',
        'barcode', 'product_barcode_id', 'name',
        'cate4_id', 'cate5_id', 'monitor_size_lookup_id',
        'quantity', 'amount', 'user_id',
        'status',
        'rg', 'duplicated',
        'checked_at', 'checked_status', 'checked_user_id',
        'product_lot_id', 'product_vendor_id', 'product_link_id',
        'return_reason_b_id', 'return_reason_m_id',
        'is_locked', 'locked_by', 'locked_at',
        'memo',
    ];

    protected $hidden = [];

    protected $casts = [
        'is_locked' => 'boolean',
        'locked_at' => 'datetime',
        'created_at' => 'datetime', // created_at을 항상 DateTime 객체로 캐스팅
        'updated_at' => 'datetime',
    ];

    protected $with = [
        'req:id,req_at,req_type,status',
        'cate4:id,name',
        'cate5:id,name',
        'lot:id,name',
        'vendor:id,name',
        'link',
        'user:id,name',
        'checkedUser:id,name',
        'palletProducts',
        'carryoutProducts',
        'repairProduct',
        'returnReason',
    ];

    /**
     * meilisearch 기본 인덱스 이름
     */
    public function searchableAs(): string
    {
        return 'product_index';
    }

    /**
     * meilisearch 리턴값을 설정
     */
    public function toSearchableArray(): array
    {
        // 상태가 삭제인 경우 검색에서 제외
        if ($this->status === self::STATUS_DELETED) {
            return [];
        }

        return [
            'id' => $this->id,
            'req_id' => $this->req_id,
            'req_at' => $this->req->req_at ? (new Carbon($this->req->req_at))->getTimestampMs() : 0,
            'is_coupang' => $this->req->req_type !== 9 ? 'Y' : 'N',
            'qaid' => $this->qaid,
            'lot' => $this->lot->name ?? '',
            'barcode' => $this->barcode ?? '',
            'name' => $this->name ?? '',
            'status' => $this->status,
            'checked_status' => $this->checked_status,
            'process_grade_code' => $this->palletProducts->first()?->repairGrade?->code ?? '',
            'pallet_status' => $this->palletProducts->first()?->pallet?->status ?? '',
            'pallet_product_id' => $this->palletProducts->first()?->id ?? '',
            'cate4' => $this->cate4_id ?? '',
            'cate5' => $this->cate5_id ?? '',
            'registered_name' => $this->user->name ?? '',
            'is_rg' => $this->rg,
            'created_at' => $this->created_at->getTimestampMs(),
        ];
    }

    public function scopeIsDuplicated($query)
    {
        return $query->where('duplicated', self::IS_DUPLICATED_Y);
    }

    public function req(): BelongsTo
    {
        return $this->belongsTo(Req::class, 'req_id', 'id');
    }

    // Product 모델에 추가할 관계 메서드
    public function productBarcode(): BelongsTo
    {
        return $this->belongsTo(ProductBarcode::class, 'product_barcode_id', 'id');
    }

    public function cate4(): BelongsTo
    {
        return $this->belongsTo(Cate4::class, 'cate4_id', 'id');
    }

    public function cate5(): BelongsTo
    {
        return $this->belongsTo(Cate5::class, 'cate5_id', 'id');
    }

    public function lot(): BelongsTo
    {
        return $this->belongsTo(ProductLot::class, 'product_lot_id', 'id');
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(ProductVendor::class, 'product_vendor_id', 'id');
    }

    public function link(): BelongsTo
    {
        return $this->belongsTo(ProductLink::class, 'product_link_id', 'id');
    }

    public function palletProducts(): HasMany
    {
        return $this->hasMany(PalletProduct::class, 'product_id', 'id');
    }

    public function carryoutProducts(): HasMany
    {
        return $this->hasMany(CarryoutProduct::class, 'product_id', 'id');
    }

    /**
     * 사용법
     *  $products = Product::with('user')->get(); 또는
     *  $products = Product::with(['user', 'checkedUser'])->get();
     *  foreach ($products as $product) {
     *      // 이제 $product->user 는 즉시 사용할 수 있는 유저 정보를 가지고 있습니다.
     *      $user = $product->user;
     *      $checkedUser = $product->checkedUser;
     *  }
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->withTrashed();
    }

    public function checkedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'checked_user_id', 'id')->withTrashed();
    }

    // 상품의 현재 상태
    public function logs(): MorphMany
    {
        return $this->MorphMany(ProductLog::class, 'model');
    }

    // QAID 재발행 목록을 보려면
    public function qaidReprint(): BelongsTo
    {
        return $this->belongsTo(QaidReprint::class, 'qaid', 'qaid');
    }

    public function warehousePalletItems(): HasMany
    {
        return $this->hasMany(WarehousePalletItem::class, 'product_id', 'id');
    }

    public function repairProduct(): HasOne
    {
        return $this->hasOne(RepairProduct::class, 'product_id', 'id');
    }

    /**
     * 반품 사유 bCate
     */
    public function returnReasonB(): BelongsTo
    {
        return $this->belongsTo(ReturnReasonB::class, 'return_reason_b_id', 'id');
    }

    /**
     * 반품 사유 mCate
     */
    public function returnReasonM(): BelongsTo
    {
        return $this->belongsTo(ReturnReasonM::class, 'return_reason_m_id', 'id');
    }

    /**
     * 반품 사유와의 관계 (1:1)
     */
    public function returnReason(): HasOne
    {
        return $this->hasOne(ReturnReason::class, 'product_id', 'id');
    }

    // 관계 추가
    public function lockedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'locked_by', 'id');
    }

    public function monitorSizeLookup(): BelongsTo
    {
        return $this->belongsTo(MonitorSizeLookup::class, 'monitor_size_lookup_id', 'id');
    }

    // 잠금 상태 확인 메서드
    public function isLocked(): bool
    {
        return $this->is_locked;
    }

    // // 자동으로 추가되는 메서드들
    // public function getMonitorSize(): ?object
    // {
    //     $hash = md5($this->name);
    //     return MonitorSizeLookup::select('size', 'unit', 'brand')
    //         ->where('name_hash', $hash)
    //         ->first();
    // }
    //
    // public function getMonitorSizeString(): ?string
    // {
    //     $size = $this->getMonitorSize();
    //     return $size ? "{$size->size} {$size->unit}" : null;
    // }
    //
    // /**
    //  * 모니터 크기를 브랜드와 함께 조회합니다 (더 정확한 조회)
    //  *
    //  * @return object|null 크기 정보 (size, unit, brand) 또는 null
    //  */
    // public function getMonitorSizeWithBrand(): ?object
    // {
    //     $hash = md5($this->name);
    //
    //     // MonitorSizeExtractionService의 getMonitorModel 메서드 사용
    //     $brand = app(MonitorSizeExtractionService::class)->getMonitorModel($this);
    //
    //     return \DB::table('monitor_size_lookup')
    //         ->where('product_name_hash', $hash)
    //         ->where('brand', $brand)
    //         ->select('size', 'unit', 'brand')
    //         ->first();
    // }
}
