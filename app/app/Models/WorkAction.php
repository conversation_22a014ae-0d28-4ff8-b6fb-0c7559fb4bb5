<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class WorkAction extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'category_id',
        'code',
        'name',
        'description',
        'parent_id',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 액션이 속한 카테고리
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(WorkCategory::class, 'category_id');
    }

    /**
     * 부모 액션 (계층 구조)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(WorkAction::class, 'parent_id');
    }

    /**
     * 자식 액션들 (계층 구조)
     */
    public function children(): HasMany
    {
        return $this->hasMany(WorkAction::class, 'parent_id');
    }

    /**
     * 활성 자식 액션들
     */
    public function activeChildren(): HasMany
    {
        return $this->hasMany(WorkAction::class, 'parent_id')
            ->where('is_active', true)
            ->orderBy('sort_order');
    }

    /**
     * 액션에 속한 상태 템플릿들
     */
    public function statusTemplates(): HasMany
    {
        return $this->hasMany(WorkStatusTemplate::class, 'action_id');
    }

    /**
     * 액션을 통해 생성된 WorkStatus들
     */
    public function workStatuses(): HasMany
    {
        return $this->hasMany(WorkStatus::class, 'action_id');
    }

    /**
     * 액션 코드로 조회
     */
    public function scopeByCode($query, string $code)
    {
        return $query->where('code', $code);
    }

    /**
     * 활성 액션만 조회
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 카테고리별 액션 조회
     */
    public function scopeByCategory($query, int $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * 최상위 액션들 조회 (부모가 없는 액션)
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * 정렬 순서대로 조회
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }
}
