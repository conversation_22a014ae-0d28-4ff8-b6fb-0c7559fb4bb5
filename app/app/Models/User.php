<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    const ROLE_SUPER_ADMIN = 'Super-Admin';

    const ROLE_ADMIN = 'Admin';

    const ROLE_RECEIVING_MANAGER = 'Receiving-Manager';

    const ROLE_PALLET_MANAGER = 'Pallet-Manager';

    const ROLE_CARRYOUT_MANAGER = 'Carryout-Manager';

    const ROLE_EMPLOYEE = 'Employee';

    const ROLE_GUEST = 'Guest';

    public static array $ROLE_NAME = [
        self::ROLE_SUPER_ADMIN => '시스템 관리자',
        self::ROLE_ADMIN => '관리자',
        self::ROLE_RECEIVING_MANAGER => '입고 담당',
        self::ROLE_PALLET_MANAGER => '출고 담당',
        self::ROLE_CARRYOUT_MANAGER => '외주 담당',
        self::ROLE_EMPLOYEE => '일반 작업자',
        self::ROLE_GUEST => '게스트',
    ];

    const MEMBER_STATUS_ACTIVE = 1;

    const MEMBER_STATUS_PAUSE = 2;

    const MEMBER_STATUS_UNAVAILABLE = 8;

    const MEMBER_STATUS_DELETED = 9;

    public static array $MEMBER_STATUS_NAME = [
        self::MEMBER_STATUS_ACTIVE => '활성',
        self::MEMBER_STATUS_PAUSE => '일시 정지',
        self::MEMBER_STATUS_UNAVAILABLE => '이용불가',
        self::MEMBER_STATUS_DELETED => '삭제',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'mem_no',
        'company_id', 'role',
        'username', 'caps_id', 'line_number', 'name',
        'part', 'position',
        'email', 'cellphone', 'telephone', 'status',
        'menu',
        'login_at', 'login_ip', 'login_os',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'login_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(UserAttendance::class, 'user_id', 'id');
    }

    /**
     * Get the req requests for the user
     */
    public function registerReqs(): HasMany
    {
        return $this->hasMany(Req::class);
    }

    /**
     * Get the Reqs that the user checked
     */
    public function checkedReqs(): HasMany
    {
        return $this->hasMany(Req::class, 'checked_user_id', 'id');
    }

    public function registeredProducts(): HasMany
    {
        return $this->hasMany(Product::class, 'user_id', 'id');
    }

    public function checkedProducts(): HasMany
    {
        return $this->hasMany(Product::class, 'checked_user_id', 'id');
    }

    public function lockedProducts(): HasMany
    {
        return $this->hasMany(Product::class, 'locked_by', 'id');
    }

    public function registeredPallets(): HasMany
    {
        return $this->hasMany(Pallet::class, 'registered_user_id', 'id');
    }

    public function checkedPallets(): HasMany
    {
        return $this->hasMany(Pallet::class, 'checked_user_id', 'id');
    }

    public function exportedPallets(): HasMany
    {
        return $this->hasMany(Pallet::class, 'exported_user_id', 'id');
    }

    public function registeredPalletProducts(): HasMany
    {
        return $this->hasMany(PalletProduct::class, 'registered_user_id', 'id');
    }

    public function checkedPalletProducts(): HasMany
    {
        return $this->hasMany(PalletProduct::class, 'checked_user_id', 'id');
    }

    public function outCarryouts(): HasMany
    {
        return $this->hasMany(Carryout::class, 'carryout_user_id', 'id');
    }

    public function inCarryouts(): HasMany
    {
        return $this->hasMany(Carryout::class, 'carryin_user_id', 'id');
    }

    public function checkedCarryoutProducts(): HasMany
    {
        return $this->hasMany(CarryoutProduct::class, 'checked_user_id', 'id');
    }

    public function carryinCarryoutProducts(): HasMany
    {
        return $this->hasMany(CarryoutProduct::class, 'carryin_user_id', 'id');
    }

    public function boards(): HasMany
    {
        return $this->hasMany(Board::class);
    }

    public function boardComments(): HasMany
    {
        return $this->hasMany(BoardComment::class);
    }

    public function boardReadStats(): HasMany
    {
        return $this->hasMany(BoardStat::class);
    }

    public function logs(): HasMany
    {
        return $this->hasMany(ProductLog::class);
    }

    public function qaids(): HasMany
    {
        return $this->hasMany(QaidReprint::class);
    }

    public function warehousePalletCreators(): HasMany
    {
        return $this->hasMany(WarehousePallet::class, 'created_by', 'id');
    }

    public function warehousePalletShippedInUsers(): HasMany
    {
        return $this->hasMany(WarehousePallet::class, 'shipped_in_by', 'id');
    }

    public function warehousePalletShippedOutUsers(): HasMany
    {
        return $this->hasMany(WarehousePallet::class, 'shipped_out_by', 'id');
    }

    public function warehousePalletItemChangers(): HasMany
    {
        return $this->hasMany(WarehousePalletItem::class, 'changed_by', 'id');
    }

    public function warehousePalletItemMemoCreators(): HasMany
    {
        return $this->hasMany(WarehousePalletItemMemo::class, 'created_by', 'id');
    }

    public function warehousePalletHistories(): HasMany
    {
        return $this->hasMany(WarehousePalletHistory::class, 'performed_by', 'id');
    }

    public function repairWaitingProducts(): HasMany
    {
        return $this->hasMany(RepairProduct::class, 'waiting_user_id', 'id');
    }

    public function repairCompletedProducts(): HasMany
    {
        return $this->hasMany(RepairProduct::class, 'completed_user_id', 'id');
    }

    public function repairPartsLogs(): HasMany
    {
        return $this->hasMany(RepairPartsLog::class, 'user_id', 'id');
    }

    public function notificationSender(): HasMany
    {
        return $this->hasMany(Notification::class, 'sender_id');
    }

    public function notificationTarget(): HasMany
    {
        return $this->hasMany(Notification::class, 'target_id')
                    ->where('target_type', 'individual');
    }

    public function notificationGroupMembers(): HasMany
    {
        return $this->hasMany(NotificationGroupMember::class, 'user_id');
    }

    public function notificationRecipients(): HasMany
    {
        return $this->hasMany(NotificationRecipient::class, 'user_id');
    }

    public function templates(): HasMany
    {
        return $this->hasMany(NotificationTemplate::class, 'created_by');
    }

    /**
     * 관리자 권한 확인
     */
    public function isAdmin(): bool
    {
        return in_array($this->role, [self::ROLE_SUPER_ADMIN, self::ROLE_ADMIN]);
    }
}
