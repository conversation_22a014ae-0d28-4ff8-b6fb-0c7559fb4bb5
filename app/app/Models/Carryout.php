<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;

class Carryout extends Model
{
    use HasFactory;

    const STATUS_CARRIED_OUT = 10;

    const STATUS_CARRIED_IN = 30;

    const STATUS_CANCELED = 90;

    public static array $STATUS_NAME = [
        self::STATUS_CARRIED_OUT => '반출',
        self::STATUS_CARRIED_IN => '반입',
        self::STATUS_CANCELED => '취소',
    ];

    protected $fillable = [
        'id', 'status',
        'carryout_user_id', 'carryout_at',
        'carryin_user_id', 'carryin_at',
        'token_id', 'memo',
    ];

    protected $hidden = [];

    protected $casts = [
        'carryout_at' => 'date:Y-m-d',
        'carryin_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected $with = [
        'carryoutUser:id,name',
        'carryinUser:id,name',
        'token',
    ];

    public function carryoutUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'carryout_user_id', 'id')->withTrashed();
    }

    public function carryinUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'carryin_user_id', 'id')->withTrashed();
    }

    public function token(): BelongsTo
    {
        return $this->belongsTo(CarryoutToken::class, 'token_id', 'id');
    }

    public function carryoutProducts(): HasMany
    {
        return $this->hasMany(CarryoutProduct::class, 'carryout_id', 'id');
    }

    public function deleteLog(): MorphOne
    {
        return $this->morphOne(DeleteLog::class, 'deletable', 'deletable_type', 'deletable_id');
    }
}
