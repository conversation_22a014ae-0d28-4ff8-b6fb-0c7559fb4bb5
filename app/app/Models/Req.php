<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Laravel\Scout\Searchable;

class Req extends Model
{
    use HasFactory, Searchable;

    /**
     * 요청타입<br>
     * 쿠팡: 1
     * 쿠팡-애플: 2
     * 미등록: 9
     */
    const TYPE_COUPANG = 1; // 쿠팡 요청

    const TYPE_APPLE = 2; // 애플(쿠팡) 요청

    const TYPE_GHOST = 8; // 보관용 상품 요청

    const TYPE_UNLINKED = 9; // 미등록 요청

    public static array $INSPECTION_TYPE_NAME = [
        self::TYPE_COUPANG => '쿠팡',
        self::TYPE_APPLE => '애플',
        self::TYPE_GHOST => '관리',
        self::TYPE_UNLINKED => '미등록',
    ];

    /**
     * 미등록 상품이 등록될 요청서 id
     */
    const UNLINKED_ID = 2;

    const UNLINKED_LOT_ID = 1; // lot_full_name 은 1번 인덱스(-) 로 고정

    const GHOST_ID = 11;

    const GHOST_LOT_ID = 1; // lot_full_name 은 11번 인덱스(-) 로 고정

    /**
     * 등록 상태
     */
    const STATUS_REGISTERED = 10; // 등록 (기존 10)

    const STATUS_CHECKED = 30; // 점검중 (기존 30)

    const STATUS_COMPLETED = 50; // 완료 (기존 30) - 1단계 더 늘림

    const STATUS_DELETED = 90; // 취소, 삭제 (기존 90) - 실제 삭제는 없지만 쿼리할 때 사용됨(나중에 삭제 해야 할 듯)

    public static array $STATUS_NAME = [
        self::STATUS_REGISTERED => '등록',
        self::STATUS_CHECKED => '점검중',
        self::STATUS_COMPLETED => '완료',
        self::STATUS_DELETED => '삭제',
    ];

    protected $fillable = [
        'req_at', 'req_type', 'status',
        'user_id',
        'checked_user_id', 'checked_at',
        'memo', 'total_count',
    ];

    protected $hidden = [];

    protected $casts = [];

    protected $with = [
        'user:id,name',
        'checkedUser:id,name',
        'reqCount',
    ];

    protected static function boot(): void
    {
        parent::boot();

        // static::addGlobalScope('withTrashed', function (Builder $builder) {
        //     $builder->withoutGlobalScope('Illuminate\Database\Eloquent\SoftDeletingScope');
        // });

        static::deleting(function ($req) {
            // 먼저 Scout 인덱스에서 삭제
            $productIds = $req->products()->pluck('id')->toArray();
            if (! empty($productIds)) {
                Product::whereIn('id', $productIds)->unsearchable();
            }

            // 모든 연관 상품들을 한 번에 삭제
            $req->products()->delete();
            $req->reqCount()->delete();
        });
    }

    public function reqCount(): HasOne
    {
        return $this->hasOne(ReqCount::class, 'req_id', 'id');
    }

    // 쿠팡 상품만 가져오기
    public function scopeCoupang($query)
    {
        return $query->where('req_type', self::TYPE_COUPANG);
    }

    // 미등록 상품만 가져오기
    public function scopeUnlinked($query)
    {
        return $query->where('req_type', self::TYPE_UNLINKED);
    }

    // 등록된 상품만 가져오는 쿼리
    public function scopeRegistered($query)
    {
        return $query->where('status', '!=', self::STATUS_REGISTERED);
    }

    // 미등록된 상품만 가져오는 쿼리
    public function scopeUnregistered($query)
    {
        return $query->where('status', self::STATUS_REGISTERED);
    }

    /**
     * Get the user that owns the Req
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    /**
     * Get the user that checked the Req
     */
    public function checkedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'checked_user_id', 'id')->withTrashed();
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'req_id', 'id');
    }

    public function carryoutProducts(): HasMany
    {
        return $this->hasMany(CarryoutProduct::class, 'req_id', 'id');
    }

    /**
     * 상품의 개수
     */
    public function getProductCount(): int
    {
        return $this->products()->count();
    }

    /**
     * 삭제 로그
     */
    public function deleteLog(): MorphOne
    {
        return $this->morphOne(DeleteLog::class, 'deletable');
    }
}
