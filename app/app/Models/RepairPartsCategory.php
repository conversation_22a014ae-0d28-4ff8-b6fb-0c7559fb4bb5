<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RepairPartsCategory extends Model
{
    protected $table = 'repair_parts_categories';

    protected $fillable = [
        'name',
        'parent_id',
        'order_no',
        'level',
    ];

    public function parent(): BelongsTo
    {
        return $this->belongsTo(RepairPartsCategory::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(RepairPartsCategory::class, 'parent_id');
    }

    public function repairParts(): Has<PERSON><PERSON>
    {
        return $this->hasMany(RepairParts::class, 'category_id');
    }

    // 모든 하위 카테고리 조회
    public function getAllChildren()
    {
        return $this->children()->with('children');
    }

    // 모든 상위 카테고리 조회
    public function getAllParents()
    {
        return $this->parent()->with('parent');
    }
}
