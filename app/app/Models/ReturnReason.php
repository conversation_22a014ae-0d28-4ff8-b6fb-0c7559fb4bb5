<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ReturnReason extends Model
{
    public $timestamps = false;

    protected $fillable = [
        'product_id',
        'reason',
    ];

    protected $hidden = [];

    /**
     * 상품과의 관계 (1:1)
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    /**
     * 상세 반품 사유가 있는 상품들만 조회
     */
    public function scopeHasReason($query)
    {
        return $query->whereNotNull('detail_reason')
            ->where('detail_reason', '!=', '');
    }

    /**
     * 특정 키워드가 포함된 반품 사유 조회
     */
    public function scopeByKeyword($query, string $keyword)
    {
        return $query->where('detail_reason', 'like', "%{$keyword}%");
    }
}
