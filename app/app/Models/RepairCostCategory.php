<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RepairCostCategory extends Model
{
    use HasFactory;

    protected $table = 'repair_cost_categories';

    /**
     * 가격 기준 상수
     */
    const PRICING_CRITERIA_SIZE = 'size';      // 크기 기준

    const PRICING_CRITERIA_PRICE = 'price';    // 판매가 기준

    const PRICING_CRITERIA_COMMON = 'common';  // 공통 금액

    protected $fillable = [
        'repair_cost_policy_id',
        'cate4_id',
        'cate5_id',
        'pricing_criteria',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected $hidden = [];

    /**
     * 수리비 정책과의 관계
     */
    public function policy(): BelongsTo
    {
        return $this->belongsTo(RepairCostPolicy::class, 'repair_cost_policy_id', 'id');
    }

    /**
     * 수리비 정책과의 관계 (기존 호환성)
     */
    public function repairCostPolicy(): BelongsTo
    {
        return $this->policy();
    }

    /**
     * 4차 카테고리와의 관계
     */
    public function cate4(): BelongsTo
    {
        return $this->belongsTo(Cate4::class, 'cate4_id', 'id');
    }

    /**
     * 5차 카테고리와의 관계
     */
    public function cate5(): BelongsTo
    {
        return $this->belongsTo(Cate5::class, 'cate5_id', 'id');
    }

    /**
     * 수리비 범위와의 관계
     */
    public function ranges(): HasMany
    {
        return $this->hasMany(RepairCostRange::class, 'repair_cost_category_id', 'id');
    }

    /**
     * 활성화된 카테고리만 조회하는 스코프
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 시스템별 카테고리 조회 스코프
     */
    public function scopeBySystem($query, $systemId)
    {
        return $query->where('repair_cost_policy_id', $systemId);
    }

    /**
     * 카테고리 매핑으로 조회하는 스코프
     */
    public function scopeByMapping($query, int $cate4Id, ?int $cate5Id = null)
    {
        $query->where('cate4_id', $cate4Id);

        if ($cate5Id) {
            $query->where('cate5_id', $cate5Id);
        }

        return $query;
    }

    /**
     * 가격 기준별 조회 스코프
     */
    public function scopeByPricingCriteria($query, string $criteria)
    {
        return $query->where('pricing_criteria', $criteria);
    }
}
