<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class WorkStatusTemplate extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'category_id',
        'action_id',
        'template_code',
        'name',
        'description',
        'conditions',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'conditions' => 'array',
    ];

    protected $hidden = [
        'deleted_at',
    ];

    /**
     * 템플릿이 속한 카테고리
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(WorkCategory::class, 'category_id');
    }

    /**
     * 템플릿이 속한 액션
     */
    public function action(): BelongsTo
    {
        return $this->belongsTo(WorkAction::class, 'action_id');
    }

    /**
     * 템플릿을 생성한 사용자
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 이 템플릿으로 생성된 WorkStatus들
     */
    public function workStatuses(): HasMany
    {
        return $this->hasMany(WorkStatus::class, 'template_id');
    }

    /**
     * 템플릿 코드로 조회
     */
    public function scopeByTemplateCode($query, string $templateCode)
    {
        return $query->where('template_code', $templateCode);
    }

    /**
     * 활성 템플릿만 조회
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 카테고리별 템플릿 조회
     */
    public function scopeByCategory($query, int $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * 액션별 템플릿 조회
     */
    public function scopeByAction($query, int $actionId)
    {
        return $query->where('action_id', $actionId);
    }

    /**
     * 생성자별 템플릿 조회
     */
    public function scopeByCreator($query, int $creatorId)
    {
        return $query->where('created_by', $creatorId);
    }
}
