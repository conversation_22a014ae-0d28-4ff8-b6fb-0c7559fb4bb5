<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RepairSymptom extends Model
{
    protected $fillable = [
        'type',
        'name',
        'code',
        'default_repair_process_id',
        'default_repair_grade_id',
    ];

    public function carryoutProducts(): HasMany
    {
        return $this->hasMany(CarryoutProduct::class, 'repair_symptom_id', 'id');
    }

    public function palletProducts(): HasMany
    {
        return $this->hasMany(PalletProduct::class, 'repair_symptom_id', 'id');
    }

    public function repairProducts(): HasMany
    {
        return $this->hasMany(RepairProduct::class, 'repair_symptom_id', 'id');
    }

    /**
     * 이 증상에 연결된 처리 내용들을 가져옵니다.
     */
    public function repairProcesses(): BelongsToMany
    {
        return $this->belongsToMany(RepairProcess::class, 'repair_process_repair_symptom');
    }

    /**
     * 이 증상에 연결된 등급들을 가져옵니다.
     */
    public function repairGrades(): BelongsToMany
    {
        return $this->belongsToMany(RepairGrade::class, 'repair_grade_repair_symptom');
    }

    /**
     * 이 증상의 기본 처리 내용을 가져옵니다.
     */
    public function defaultRepairProcess(): BelongsTo
    {
        return $this->belongsTo(RepairProcess::class, 'default_repair_process_id');
    }

    /**
     * 이 증상의 기본 수리 등급을 가져옵니다.
     */
    public function defaultRepairGrade(): BelongsTo
    {
        return $this->belongsTo(RepairGrade::class, 'default_repair_grade_id');
    }
}
