<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ProductLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'model_type',
        'model_id',
        'work_status_id',
        'user_id',
        'memo',
    ];

    protected $casts = [
        'created_at' => 'datetime',
    ];

    /**
     * Polymorphic 관계 - 관련 모델 (RepairProduct, PalletProduct 등)
     */
    public function relatedModel(): MorphTo
    {
        return $this->morphTo();
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    public function workStatus(): BelongsTo
    {
        return $this->belongsTo(WorkStatus::class, 'work_status_id');
    }

    public function qaidPrint(): BelongsTo
    {
        return $this->belongsTo(QaidReprint::class);
    }

    /**
     * - **단일화된 접근**: 상품 하나로 모든 관련 이력 한 번의 조회 가능
     * - **확장성 높음**: 향후 새로운 관련 모델 추가가 용이
     * - **유연성**: Polymorphic을 이용하여 관련 모델 추가가 어려움 없이 가능
     * - **높은 유지보수성**: 모든 데이터가 `product_histories` 한 테이블에 모이기 때문에 쿼리 작성도 간단하며, 상태 변경 흐름과 추적성을 확보할 수 있습니다.
     *
     * 현 상태 통계 예제
     * - Pallet에 상품 적재 시
     * ProductHistory::create([
     * 'product_id' => $product->id,
     * 'history_type' => 'PalletProduct',
     * 'related_model_type' => PalletProduct::class,
     * 'related_model_id' => $palletProduct->id,
     * 'status' => $palletProduct->status,
     * 'checked_status' => $palletProduct->checked_status,
     * 'user_id' => auth()->id(),
     * 'memo' => '팔레트 적재됨',
     * 'occurred_at' => now(),
     * ]);
     *
     * - 외주작업(CarryoutProduct) 시
     * ProductHistory::create([
     * 'product_id' => $product->id,
     * 'history_type' => 'CarryoutProduct',
     * 'related_model_type' => CarryoutProduct::class,
     * 'related_model_id' => $carryoutProduct->id,
     * 'status' => $carryoutProduct->status,
     * 'checked_status' => null,
     * 'user_id' => auth()->id(),
     * 'memo' => '외주 작업 시작됨',
     * 'occurred_at' => now(),
     * ]);
     *
     * - ## Product ID 하나로 조회 예시
     * $histories = ProductHistory::with(['user', 'relatedModel'])
     * ->where('product_id', $productId)
     * ->orderBy('occurred_at', 'desc')
     * ->get();
     *
     * // 히스토리 간략 출력 예시
     * foreach ($histories as $history) {
     * echo "[{$history->occurred_at}] {$history->history_type}: {$history->status}, User: {$history->user->name}\n";
     * }
     */
}
