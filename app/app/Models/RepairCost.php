<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 수리비 금액 모델
 *
 * @property int $id
 * @property int $repair_cost_range_id 수리비 범위 인덱스
 * @property int $repair_cost_type_id 수리 유형 인덱스
 * @property int $amount 수리비 금액
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property-read RepairCostRange $range 수리비 범위
 * @property-read RepairCostType $repairType 수리 유형
 */
class RepairCost extends Model
{
    use HasFactory;

    /**
     * 테이블명
     */
    protected $table = 'repair_costs';

    /**
     * 대량 할당 가능한 속성
     */
    protected $fillable = [
        'repair_cost_range_id',
        'repair_cost_type_id',
        'amount',
    ];

    /**
     * 속성 캐스팅
     */
    protected $casts = [
        'repair_cost_range_id' => 'integer',
        'repair_cost_type_id' => 'integer',
        'amount' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 수리비 범위와의 관계 (N:1)
     */
    public function range(): BelongsTo
    {
        return $this->belongsTo(RepairCostRange::class, 'repair_cost_range_id', 'id');
    }

    /**
     * 수리비 범위와의 관계 (별칭)
     */
    public function repairCostRange(): BelongsTo
    {
        return $this->range();
    }

    /**
     * 수리 유형과의 관계 (N:1)
     */
    public function repairType(): BelongsTo
    {
        return $this->belongsTo(RepairCostType::class, 'repair_cost_type_id', 'id');
    }

    /**
     * 수리 유형과의 관계 (별칭)
     */
    public function repairCostType(): BelongsTo
    {
        return $this->repairType();
    }

    /**
     * 수리 유형 코드 반환 (기존 호환성)
     */
    public function getRepairTypeAttribute(): string
    {
        return $this->repairType->code ?? '';
    }

    /**
     * 수리 유형 표시명 반환
     */
    public function getRepairTypeDisplayAttribute(): string
    {
        return $this->repairType->name ?? '';
    }

    /**
     * 금액을 원화 형식으로 포맷팅
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount).'원';
    }

    /**
     * 특정 범위와 수리 유형 ID로 수리비 조회 (새로운 시스템)
     */
    public static function findByRangeAndTypeId(int $rangeId, int $repairTypeId): ?self
    {
        return self::where('repair_cost_range_id', $rangeId)
            ->where('repair_cost_type_id', $repairTypeId)
            ->first();
    }

    /**
     * 특정 범위의 모든 수리비 조회
     */
    public static function getByRange(int $rangeId): \Illuminate\Database\Eloquent\Collection
    {
        return self::with('repairType')
            ->where('repair_cost_range_id', $rangeId)
            ->orderBy('repair_cost_type_id')
            ->get();
    }

    /**
     * 수리 유형별 평균 금액 조회
     */
    public static function getAverageAmountByType(): \Illuminate\Support\Collection
    {
        return self::join('repair_cost_types', 'repair_costs.repair_cost_type_id', '=', 'repair_cost_types.id')
            ->selectRaw('repair_cost_types.code as repair_type, AVG(repair_costs.amount) as average_amount')
            ->groupBy('repair_cost_types.code')
            ->get()
            ->pluck('average_amount', 'repair_type');
    }

    /**
     * 수리비 금액 유효성 검증
     */
    public static function validateAmount(int $amount): bool
    {
        return $amount >= 0 && $amount <= 9999999; // 최대 999만원
    }

    /**
     * ========== 기존 호환성 ==========
     *
     * @todo: 앞으로는 사용 안 할 듯
     */

    /**
     * 특정 범위와 수리 유형으로 수리비 조회 (기존 호환성)
     */
    public static function findByRangeAndType(int $rangeId, string $repairTypeCode): ?self
    {
        return self::whereHas('repairType', function ($query) use ($repairTypeCode) {
            $query->where('code', $repairTypeCode);
        })->where('repair_cost_range_id', $rangeId)->first();
    }

    /**
     * 수리 유형 유효성 검증 (기존 호환성)
     */
    public static function validateRepairType(string $repairTypeCode): bool
    {
        return RepairCostType::validateCode($repairTypeCode);
    }

    /**
     * 수리 유형 목록 반환 (기존 호환성)
     */
    public static function getRepairTypes(): array
    {
        return RepairCostType::getRepairTypes();
    }
}
