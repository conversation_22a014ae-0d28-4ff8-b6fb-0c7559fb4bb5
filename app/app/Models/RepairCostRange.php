<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RepairCostRange extends Model
{
    use HasFactory;

    /**
     * 단위 상수 정의
     */
    const UNIT_INCH = 'inch';

    const UNIT_CM = 'cm';

    const UNIT_WON = 'won';

    const UNIT_COMMON = 'common';

    /**
     * 대량 할당 가능한 속성
     */
    protected $fillable = [
        'repair_cost_category_id',
        'range_name',
        'min_value',
        'max_value',
        'unit',
        'is_active',
    ];

    /**
     * 속성 캐스팅
     */
    protected $casts = [
        'min_value' => 'decimal:2',
        'max_value' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * 수리비 카테고리와의 관계 (N:1)
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(RepairCostCategory::class, 'repair_cost_category_id', 'id');
    }

    /**
     * 수리비 카테고리와의 관계 (별칭)
     */
    public function repairCostCategory(): BelongsTo
    {
        return $this->category();
    }

    /**
     * 수리비 금액과의 관계 (1:N)
     */
    public function costs(): HasMany
    {
        return $this->hasMany(RepairCost::class, 'repair_cost_range_id', 'id');
    }

    /**
     * 활성화된 범위만 조회하는 스코프
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 특정 값이 이 범위에 포함되는지 확인
     */
    public function containsValue($value): bool
    {
        if ($this->unit === self::UNIT_COMMON) {
            return true; // 공통 범위는 모든 값을 포함
        }

        $minValue = $this->min_value ?? 0;
        $maxValue = $this->max_value ?? PHP_FLOAT_MAX;

        return $value >= $minValue && $value < $maxValue;
    }

    /**
     * 범위 표시명 생성
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->unit === self::UNIT_COMMON) {
            return $this->range_name;
        }

        $unitText = match ($this->unit) {
            self::UNIT_INCH => '인치',
            self::UNIT_WON => '원',
            default => ''
        };

        if ($this->min_value && $this->max_value) {
            return "{$this->min_value}-{$this->max_value}{$unitText}";
        } elseif ($this->min_value) {
            return "{$this->min_value}{$unitText} 이상";
        } elseif ($this->max_value) {
            return "{$this->max_value}{$unitText} 미만";
        }

        return $this->range_name;
    }
}
