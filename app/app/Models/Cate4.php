<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Cate4 extends Model
{
    use HasFactory;

    protected $table = 'cate4';

    public $timestamps = false;

    protected $fillable = ['cate4_no', 'name'];

    protected $hidden = [];

    protected $casts = [];

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'cate4_id', 'id');
    }

    public function cate5(): HasMany
    {
        return $this->hasMany(Cate5::class, 'cate4_id', 'id');
    }

    public function partsLogs(): HasMany
    {
        return $this->hasMany(RepairPartsLog::class, 'cate4_id', 'id');
    }

    /**
     * 수리비 카테고리와의 관계
     */
    public function repairCostCategories(): HasMany
    {
        return $this->hasMany(RepairCostCategory::class, 'cate4_id', 'id');
    }

    /**
     * 활성화된 수리비 카테고리만 조회
     */
    public function activeRepairCostCategories(): HasMany
    {
        return $this->repairCostCategories()->where('is_active', true);
    }
}
