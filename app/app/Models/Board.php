<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Board extends Model
{
    use HasFactory, SoftDeletes;

    const TYPE_BOARD = 'board';

    const TYPE_NOTICE = 'notice';

    const TYPE_FAQ = 'faq';

    public static $TYPE_NAME = [
        self::TYPE_BOARD => '게시판',
        self::TYPE_NOTICE => '공지사항',
        self::TYPE_FAQ => 'FAQ',
    ];

    const STATUS_REGISTERED = 1;

    const STATUS_DELETED = 9;

    public static $STATUS_NAME = [
        self::STATUS_REGISTERED => '등록',
        self::STATUS_DELETED => '삭제',
    ];

    const F_SHOW_Y = 'Y';

    const F_SHOW_N = 'N';

    public static $F_SHOW_NAME = [
        self::F_SHOW_Y => '보이기',
        self::F_SHOW_N => '감추기',
    ];

    const F_NOTICE_Y = 'Y';

    const F_NOTICE_N = 'N';

    public static $F_NOTICE_NAME = [
        self::F_NOTICE_Y => '공지',
        self::F_NOTICE_N => '일반',
    ];

    protected $fillable = [
        'type',
        'user_id', 'name',
        'subject', 'content',
        'status',
        'open_at',
        'f_show', 'f_notice',
        'hits',
        'ip', 'user_agent',
    ];

    protected $casts = [
        'open_at' => 'datetime:Y-m-d H:i:s',
        'deleted_at' => 'datetime:Y-m-d H:i:s',
    ];

    protected $hidden = [];

    protected $with = [
        'user:username,name,email',
        'comments',
    ];

    protected static function boot(): void
    {
        parent::boot();

        static::deleting(function ($board) {
            $board->readStats()->delete();
            $board->comments()->delete();
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class)->withTrashed();
    }

    public function comments(): HasMany
    {
        return $this->hasMany(BoardComment::class);
    }

    public function readStats(): HasMany
    {
        return $this->hasMany(BoardStat::class);
    }
}
