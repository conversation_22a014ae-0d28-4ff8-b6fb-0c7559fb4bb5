<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RepairParts extends Model
{
    protected $table = 'repair_parts';

    protected $fillable = [
        'category_id',
        'barcode',
        'name',
        'model_number',
        'price',
        'stock',
        'reorder_stock',
        'acc_count',
        'is_purchasable',
        'location_area',
        'location_zone',
        'location_floor',
        'location_position',
        'memo',
    ];

    /**
     * 캐스팅 설정
     */
    protected $casts = [
        'price' => 'integer',
        'stock' => 'integer',
        'reorder_stock' => 'integer',
        'acc_count' => 'integer',
        'location_floor' => 'integer',
        'location_position' => 'integer',
    ];

    /**
     * 카테고리와의 관계
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(RepairPartsCategory::class, 'category_id');
    }

    /**
     * 로그들과의 관계
     */
    public function logs(): HasMany
    {
        return $this->hasMany(RepairPartsLog::class, 'repair_parts_id', 'id');
    }

    /**
     * 주문들과의 관계
     */
    public function orders(): HasMany
    {
        return $this->hasMany(RepairPartsOrder::class, 'repair_parts_id', 'id');
    }

    /**
     * 제품 구성품들과의 관계
     */
    public function productParts(): HasMany
    {
        return $this->hasMany(RepairProductParts::class, 'repair_parts_id', 'id');
    }

    /**
     * 수리 제품들과의 관계
     */
    public function repairProducts(): BelongsToMany
    {
        return $this->belongsToMany(RepairProduct::class, 'repair_product_parts',
            'repair_parts_id', 'repair_product_id')
            ->withPivot('quantity')
            ->withTimestamps();
    }

    /**
     * 가격 포맷팅
     */
    public function getFormattedPriceAttribute(): string
    {
        return number_format($this->price).'원';
    }

    /**
     * 구매 가능 여부 확인
     */
    public function getIsPurchasableAttribute(): bool
    {
        return $this->attributes['is_purchasable'] === 'Y';
    }

    /**
     * 구매 가능 여부 설정
     */
    public function setIsPurchasableAttribute($value): void
    {
        $this->attributes['is_purchasable'] = $value ? 'Y' : 'N';
    }

    /**
     * 재고 부족 여부 확인
     */
    public function getIsLowStockAttribute(): bool
    {
        return $this->stock <= $this->reorder_stock;
    }

    /**
     * 재고 상태 가져오기
     */
    public function getStockStatusAttribute(): string
    {
        if ($this->stock == 0) {
            return '품절';
        } elseif ($this->is_low_stock) {
            return '재고부족';
        } else {
            return '재고있음';
        }
    }

    /**
     * 위치 정보 가져오기
     */
    public function getLocationAttribute(): string
    {
        $location = [];

        if ($this->location_area) {
            $location[] = $this->location_area;
        }
        if ($this->location_zone) {
            $location[] = $this->location_zone;
        }
        if ($this->location_floor) {
            $location[] = $this->location_floor.'층';
        }
        if ($this->location_position) {
            $location[] = $this->location_position.'번';
        }

        return implode(' ', $location);
    }
}
