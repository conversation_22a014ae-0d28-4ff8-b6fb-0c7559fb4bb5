<?php

namespace Tests\Unit\Models;

use App\Models\NotificationTemplate;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NotificationTemplateTest extends TestCase
{
    use RefreshDatabase;

    /**
     * 유효한 데이터로 검증 테스트
     */
    public function test_validate_data_with_valid_data(): void
    {
        $validData = [
            'name' => '테스트템플릿',
            'title' => '테스트 제목',
            'content' => '테스트 내용입니다.',
            'priority' => 'normal',
        ];

        $errors = NotificationTemplate::validateData($validData);

        $this->assertEmpty($errors);
    }

    /**
     * 필수 필드 누락 시 검증 테스트
     */
    public function test_validate_data_with_missing_required_fields(): void
    {
        $invalidData = [
            'name' => '',
            'title' => '',
            'content' => '',
        ];

        $errors = NotificationTemplate::validateData($invalidData);

        $this->assertContains('템플릿명은 필수입니다.', $errors);
        $this->assertContains('제목은 필수입니다.', $errors);
        $this->assertContains('내용은 필수입니다.', $errors);
    }

    /**
     * 필드 길이 제한 초과 시 검증 테스트
     */
    public function test_validate_data_with_length_exceeded(): void
    {
        $invalidData = [
            'name' => str_repeat('가', 51), // 50자 초과
            'title' => str_repeat('나', 201), // 200자 초과
            'content' => str_repeat('다', 5001), // 5000자 초과
            'priority' => 'invalid_priority',
        ];

        $errors = NotificationTemplate::validateData($invalidData);

        $this->assertContains('템플릿명은 50자를 초과할 수 없습니다.', $errors);
        $this->assertContains('제목은 200자를 초과할 수 없습니다.', $errors);
        $this->assertContains('내용은 5000자를 초과할 수 없습니다.', $errors);
        $this->assertContains('유효하지 않은 우선순위입니다.', $errors);
    }

    /**
     * 인스턴스 검증 메서드 테스트
     */
    public function test_instance_validate_method(): void
    {
        $template = new NotificationTemplate([
            'name' => '',
            'title' => '테스트 제목',
            'content' => '테스트 내용',
            'priority' => 'normal',
        ]);

        $errors = $template->validate();

        $this->assertContains('템플릿명은 필수입니다.', $errors);
    }

    /**
     * 템플릿명 중복 검증 테스트
     */
    public function test_duplicate_name_validation(): void
    {
        // 기존 템플릿 생성
        $user = User::factory()->create();
        NotificationTemplate::create([
            'name' => '중복테스트',
            'title' => '테스트 제목',
            'content' => '테스트 내용',
            'priority' => 'normal',
            'created_by' => $user->id,
        ]);

        // 중복 검증
        $this->assertTrue(NotificationTemplate::isDuplicateName('중복테스트'));
        $this->assertFalse(NotificationTemplate::isDuplicateName('새로운템플릿'));
    }

    /**
     * 생성 전 검증 테스트
     */
    public function test_validate_for_create(): void
    {
        $user = User::factory()->create();

        // 기존 템플릿 생성
        NotificationTemplate::create([
            'name' => '기존템플릿',
            'title' => '테스트 제목',
            'content' => '테스트 내용',
            'priority' => 'normal',
            'created_by' => $user->id,
        ]);

        // 중복된 이름으로 생성 시도
        $duplicateData = [
            'name' => '기존템플릿',
            'title' => '새 제목',
            'content' => '새 내용',
            'priority' => 'high',
            'created_by' => $user->id,
        ];

        $errors = NotificationTemplate::validateForCreate($duplicateData);
        $this->assertContains('이미 존재하는 템플릿명입니다.', $errors);

        // 생성자 정보 누락
        $noCreatorData = [
            'name' => '새템플릿',
            'title' => '새 제목',
            'content' => '새 내용',
            'priority' => 'high',
        ];

        $errors = NotificationTemplate::validateForCreate($noCreatorData);
        $this->assertContains('생성자 정보는 필수입니다.', $errors);
    }

    /**
     * 수정 전 검증 테스트
     */
    public function test_validate_for_update(): void
    {
        $user = User::factory()->create();

        // 기존 템플릿들 생성
        $template1 = NotificationTemplate::create([
            'name' => '템플릿1',
            'title' => '테스트 제목1',
            'content' => '테스트 내용1',
            'priority' => 'normal',
            'created_by' => $user->id,
        ]);

        $template2 = NotificationTemplate::create([
            'name' => '템플릿2',
            'title' => '테스트 제목2',
            'content' => '테스트 내용2',
            'priority' => 'high',
            'created_by' => $user->id,
        ]);

        // 다른 템플릿과 중복된 이름으로 수정 시도
        $duplicateData = [
            'name' => '템플릿2', // template2와 중복
            'title' => '수정된 제목',
            'content' => '수정된 내용',
            'priority' => 'urgent',
        ];

        $errors = NotificationTemplate::validateForUpdate($duplicateData, $template1->id);
        $this->assertContains('이미 존재하는 템플릿명입니다.', $errors);

        // 자신과 같은 이름으로 수정 (허용되어야 함)
        $sameNameData = [
            'name' => '템플릿1', // 자신과 같은 이름
            'title' => '수정된 제목',
            'content' => '수정된 내용',
            'priority' => 'urgent',
        ];

        $errors = NotificationTemplate::validateForUpdate($sameNameData, $template1->id);
        $this->assertNotContains('이미 존재하는 템플릿명입니다.', $errors);
    }
}
