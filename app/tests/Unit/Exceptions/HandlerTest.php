<?php

use App\Exceptions\Handler;
use App\Exceptions\ResourceNotFoundException;
use App\Exceptions\TemplateException;
use App\Exceptions\ValidationException as WmsValidationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

describe('Handler', function () {
    beforeEach(function () {
        $this->handler = app(Handler::class);
    });

    describe('템플릿 예외 처리', function () {
        test('템플릿 not_found 예외를 올바르게 처리한다', function () {
            $exception = TemplateException::notFound(123);
            $request = Request::create('/api/templates/123', 'GET');
            $request->headers->set('Accept', 'application/json');

            $response = $this->handler->render($request, $exception);

            expect($response->getStatusCode())->toBe(404);

            $data = $response->getData(true);
            expect($data)->toHaveKey('success', false);
            expect($data)->toHaveKey('error.code', 'TEMPLATE_NOT_FOUND');
            expect($data)->toHaveKey('error.message');
            expect($data)->toHaveKey('error.module', 'template');
            expect($data)->toHaveKey('error.details.template_id', 123);
        });

        test('템플릿 duplicate_name 예외를 올바르게 처리한다', function () {
            $exception = TemplateException::duplicateName('test-template');
            $request = Request::create('/api/templates', 'POST');
            $request->headers->set('Accept', 'application/json');

            $response = $this->handler->render($request, $exception);

            expect($response->getStatusCode())->toBe(409);

            $data = $response->getData(true);
            expect($data)->toHaveKey('error.code', 'TEMPLATE_DUPLICATE_NAME');
            expect($data)->toHaveKey('error.details.template_name', 'test-template');
        });

        test('템플릿 validation_failed 예외를 올바르게 처리한다', function () {
            $validationErrors = ['name' => ['이름은 필수입니다.']];
            $exception = TemplateException::validationFailed($validationErrors);
            $request = Request::create('/api/templates', 'POST');
            $request->headers->set('Accept', 'application/json');

            $response = $this->handler->render($request, $exception);

            expect($response->getStatusCode())->toBe(422);

            $data = $response->getData(true);
            expect($data)->toHaveKey('error.code', 'TEMPLATE_VALIDATION_FAILED');
            expect($data)->toHaveKey('error.details.validation_errors', $validationErrors);
        });

        test('템플릿 access_denied 예외를 올바르게 처리한다', function () {
            $exception = TemplateException::accessDenied(123, 456);
            $request = Request::create('/api/templates/123', 'PUT');
            $request->headers->set('Accept', 'application/json');

            $response = $this->handler->render($request, $exception);

            expect($response->getStatusCode())->toBe(403);

            $data = $response->getData(true);
            expect($data)->toHaveKey('error.code', 'TEMPLATE_ACCESS_DENIED');
            expect($data)->toHaveKey('error.details.template_id', 123);
            expect($data)->toHaveKey('error.details.user_id', 456);
        });
    });

    describe('Laravel 기본 예외 처리', function () {
        test('AuthenticationException을 올바르게 처리한다', function () {
            $exception = new AuthenticationException('Unauthenticated');
            $request = Request::create('/api/templates', 'GET');
            $request->headers->set('Accept', 'application/json');

            $response = $this->handler->render($request, $exception);

            expect($response->getStatusCode())->toBe(401);

            $data = $response->getData(true);
            expect($data)->toHaveKey('success', false);
            expect($data)->toHaveKey('error.code', 'UNAUTHORIZED');
            expect($data)->toHaveKey('error.message', '인증이 필요합니다.');
        });

        test('AccessDeniedHttpException을 올바르게 처리한다', function () {
            $exception = new AccessDeniedHttpException('Access denied');
            $request = Request::create('/api/templates', 'POST');
            $request->headers->set('Accept', 'application/json');

            $response = $this->handler->render($request, $exception);

            expect($response->getStatusCode())->toBe(403);

            $data = $response->getData(true);
            expect($data)->toHaveKey('error.code', 'FORBIDDEN');
            expect($data)->toHaveKey('error.message', '접근 권한이 없습니다.');
        });

        test('NotFoundHttpException을 올바르게 처리한다', function () {
            $exception = new NotFoundHttpException('Not found');
            $request = Request::create('/api/templates/999', 'GET');
            $request->headers->set('Accept', 'application/json');

            $response = $this->handler->render($request, $exception);

            expect($response->getStatusCode())->toBe(404);

            $data = $response->getData(true);
            expect($data)->toHaveKey('error.code', 'NOT_FOUND');
            expect($data)->toHaveKey('error.message', '요청한 리소스를 찾을 수 없습니다.');
        });

        test('ModelNotFoundException을 올바르게 처리한다', function () {
            $exception = new ModelNotFoundException;
            $exception->setModel('App\\Models\\NotificationTemplate', [123]);

            $request = Request::create('/api/templates/123', 'GET');
            $request->headers->set('Accept', 'application/json');

            $response = $this->handler->render($request, $exception);

            expect($response->getStatusCode())->toBe(404);

            $data = $response->getData(true);
            expect($data)->toHaveKey('error.code', 'NOT_FOUND');
            expect($data)->toHaveKey('error.message', '요청한 데이터를 찾을 수 없습니다.');
            expect($data)->toHaveKey('error.details.model', 'NotificationTemplate');
            expect($data)->toHaveKey('error.details.ids', [123]);
        });

        test('ValidationException을 올바르게 처리한다', function () {
            $exception = ValidationException::withMessages(['name' => ['이름은 필수입니다.']]);

            $request = Request::create('/api/templates', 'POST');
            $request->headers->set('Accept', 'application/json');

            $response = $this->handler->render($request, $exception);

            expect($response->getStatusCode())->toBe(422);

            $data = $response->getData(true);
            expect($data)->toHaveKey('error.code', 'UNPROCESSABLE_ENTITY');
            expect($data)->toHaveKey('error.message', '입력 데이터가 유효하지 않습니다.');
            expect($data)->toHaveKey('error.details.validation_errors');
        });
    });

    describe('기타 예외 처리', function () {
        test('일반 Exception을 올바르게 처리한다', function () {
            $exception = new \Exception('Something went wrong');
            $request = Request::create('/api/templates', 'GET');
            $request->headers->set('Accept', 'application/json');

            $response = $this->handler->render($request, $exception);

            expect($response->getStatusCode())->toBe(500);

            $data = $response->getData(true);
            expect($data)->toHaveKey('success', false);
            expect($data)->toHaveKey('error.code', 'INTERNAL_SERVER_ERROR');
        });

        test('개발 환경에서는 디버그 정보를 포함한다', function () {
            // 개발 환경 시뮬레이션을 위해 Handler를 모킹
            $handler = Mockery::mock(Handler::class)->makePartial();
            $handler->shouldReceive('isDebugMode')->andReturn(true);

            $exception = new \Exception('Debug test');
            $request = Request::create('/api/templates', 'GET');
            $request->headers->set('Accept', 'application/json');

            $response = $handler->render($request, $exception);

            $data = $response->getData(true);
            expect($data)->toHaveKey('error.trace');
            expect($data)->toHaveKey('error.file');
            expect($data)->toHaveKey('error.line');
        });

        test('운영 환경에서는 디버그 정보를 포함하지 않는다', function () {
            // 운영 환경 시뮬레이션을 위해 Handler를 모킹
            $handler = Mockery::mock(Handler::class)->makePartial();
            $handler->shouldReceive('isDebugMode')->andReturn(false);

            $exception = new \Exception('Production test');
            $request = Request::create('/api/templates', 'GET');
            $request->headers->set('Accept', 'application/json');

            $response = $handler->render($request, $exception);

            $data = $response->getData(true);
            expect($data)->not->toHaveKey('error.trace');
            expect($data)->not->toHaveKey('error.file');
            expect($data)->not->toHaveKey('error.line');
            expect($data)->toHaveKey('error.message', '서버 내부 오류가 발생했습니다.');
        });
    });

    describe('WMS 예외 처리', function () {
        test('WMS ValidationException을 올바르게 처리한다', function () {
            $exception = new WmsValidationException('유효성 검증 실패', ['field' => 'value']);
            $request = Request::create('/api/templates', 'POST');
            $request->headers->set('Accept', 'application/json');

            $response = $this->handler->render($request, $exception);

            expect($response->getStatusCode())->toBe(422);

            $data = $response->getData(true);
            expect($data)->toHaveKey('error.code', 'VALIDATION_ERROR');
            expect($data)->toHaveKey('error.details.field', 'value');
        });

        test('ResourceNotFoundException을 올바르게 처리한다', function () {
            $exception = new ResourceNotFoundException('리소스 없음', ['id' => 123]);
            $request = Request::create('/api/templates/123', 'GET');
            $request->headers->set('Accept', 'application/json');

            $response = $this->handler->render($request, $exception);

            expect($response->getStatusCode())->toBe(404);

            $data = $response->getData(true);
            expect($data)->toHaveKey('error.code', 'NOT_FOUND');
            expect($data)->toHaveKey('error.details.id', 123);
        });
    });
});
