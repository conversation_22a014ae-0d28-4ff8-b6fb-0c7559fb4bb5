<?php

namespace Tests\Unit;

use App\Exceptions\BusinessException;
use App\Exceptions\CarryoutException;
use App\Exceptions\ResourceNotFoundException;
use App\Exceptions\ValidationException;
use App\Helpers\ExceptionHelper;
use PHPUnit\Framework\TestCase;

class ExceptionHelperTest extends TestCase
{
    public function test_throw_not_found_creates_resource_not_found_exception()
    {
        $this->expectException(ResourceNotFoundException::class);
        $this->expectExceptionMessage('해당 상품 [TEST123]이 존재하지 않습니다.');

        ExceptionHelper::throwNotFound('상품', 'TEST123');
    }

    public function test_throw_validation_creates_validation_exception()
    {
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('QAID는 필수 입력 항목입니다.');

        ExceptionHelper::throwValidation('qaid', 'QAID는 필수 입력 항목입니다.');
    }

    public function test_throw_duplicate_creates_business_exception()
    {
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('해당 외주 반출 상품 [TEST123]은 이미 처리된 항목입니다.');

        ExceptionHelper::throwDuplicate('외주 반출 상품', 'TEST123');
    }

    public function test_carryout_exception_already_carried_out()
    {
        $this->expectException(CarryoutException::class);
        $this->expectExceptionMessage('해당 상품 [TEST123]은 이미 외주 반출 처리된 상품입니다.');

        throw CarryoutException::alreadyExported('TEST123');
    }

    public function test_exception_context_is_preserved()
    {
        try {
            ExceptionHelper::throwNotFound('상품', 'TEST123');
        } catch (ResourceNotFoundException $e) {
            $context = $e->getContext();
            $this->assertEquals('상품', $context['resource']);
            $this->assertEquals('TEST123', $context['identifier']);
        }
    }

    public function test_exception_type_checking()
    {
        $businessException = new BusinessException('Test message');
        $validationException = new ValidationException('Test validation');

        $this->assertTrue(ExceptionHelper::isBusinessException($businessException));
        $this->assertFalse(ExceptionHelper::isBusinessException($validationException));

        $this->assertTrue(ExceptionHelper::isValidationException($validationException));
        $this->assertFalse(ExceptionHelper::isValidationException($businessException));
    }
}
