<?php

use App\Exceptions\Handler;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Illuminate\Auth\AuthenticationException;

describe('NotificationHistoryErrorHandling', function () {
    beforeEach(function () {
        $this->handler = new Handler(app());
    });

    it('알림 히스토리 API 요청을 올바르게 식별한다', function () {
        $request = Request::create('/api/admin/notifications/history', 'GET');
        $request->headers->set('Accept', 'application/json');
        
        $reflection = new ReflectionClass($this->handler);
        $method = $reflection->getMethod('isNotificationHistoryApiRequest');
        $method->setAccessible(true);
        
        expect($method->invoke($this->handler, $request))->toBeTrue();
    });

    it('일반 API 요청은 알림 히스토리 API로 식별하지 않는다', function () {
        $request = Request::create('/api/products', 'GET');
        $request->headers->set('Accept', 'application/json');
        
        $reflection = new ReflectionClass($this->handler);
        $method = $reflection->getMethod('isNotificationHistoryApiRequest');
        $method->setAccessible(true);
        
        expect($method->invoke($this->handler, $request))->toBeFalse();
    });

    it('ModelNotFoundException을 올바른 JSON 형식으로 처리한다', function () {
        $exception = new ModelNotFoundException();
        
        $reflection = new ReflectionClass($this->handler);
        $method = $reflection->getMethod('renderNotificationHistoryExceptionAsJson');
        $method->setAccessible(true);
        
        $response = $method->invoke($this->handler, $exception);
        $data = json_decode($response->getContent(), true);
        
        expect($response->getStatusCode())->toBe(404);
        expect($data['success'])->toBeFalse();
        expect($data['message'])->toBe('알림을 찾을 수 없습니다.');
    });

    it('ValidationException을 올바른 JSON 형식으로 처리한다', function () {
        // Mock ValidationException 생성
        $exception = new class extends ValidationException {
            public function __construct() {
                // 부모 생성자 호출하지 않음
            }
            
            public function errors() {
                return ['test' => ['test field is required']];
            }
        };
        
        $reflection = new ReflectionClass($this->handler);
        $method = $reflection->getMethod('renderNotificationHistoryExceptionAsJson');
        $method->setAccessible(true);
        
        $response = $method->invoke($this->handler, $exception);
        $data = json_decode($response->getContent(), true);
        
        expect($response->getStatusCode())->toBe(422);
        expect($data['success'])->toBeFalse();
        expect($data['message'])->toBe('입력값이 올바르지 않습니다.');
        expect($data)->toHaveKey('errors');
    });

    it('AuthenticationException을 올바른 JSON 형식으로 처리한다', function () {
        $exception = new AuthenticationException();
        
        $reflection = new ReflectionClass($this->handler);
        $method = $reflection->getMethod('renderNotificationHistoryExceptionAsJson');
        $method->setAccessible(true);
        
        $response = $method->invoke($this->handler, $exception);
        $data = json_decode($response->getContent(), true);
        
        expect($response->getStatusCode())->toBe(401);
        expect($data['success'])->toBeFalse();
        expect($data['message'])->toBe('로그인이 필요합니다.');
    });

    it('AccessDeniedHttpException을 올바른 JSON 형식으로 처리한다', function () {
        $exception = new AccessDeniedHttpException();
        
        $reflection = new ReflectionClass($this->handler);
        $method = $reflection->getMethod('renderNotificationHistoryExceptionAsJson');
        $method->setAccessible(true);
        
        $response = $method->invoke($this->handler, $exception);
        $data = json_decode($response->getContent(), true);
        
        expect($response->getStatusCode())->toBe(403);
        expect($data['success'])->toBeFalse();
        expect($data['message'])->toBe('관리자 권한이 필요합니다.');
    });

    it('NotFoundHttpException을 올바른 JSON 형식으로 처리한다', function () {
        $exception = new NotFoundHttpException();
        
        $reflection = new ReflectionClass($this->handler);
        $method = $reflection->getMethod('renderNotificationHistoryExceptionAsJson');
        $method->setAccessible(true);
        
        $response = $method->invoke($this->handler, $exception);
        $data = json_decode($response->getContent(), true);
        
        expect($response->getStatusCode())->toBe(404);
        expect($data['success'])->toBeFalse();
        expect($data['message'])->toBe('알림을 찾을 수 없습니다.');
    });

    it('일반 Exception을 서버 에러로 처리한다', function () {
        $exception = new Exception('테스트 에러');
        
        $reflection = new ReflectionClass($this->handler);
        $method = $reflection->getMethod('renderNotificationHistoryExceptionAsJson');
        $method->setAccessible(true);
        
        $response = $method->invoke($this->handler, $exception);
        $data = json_decode($response->getContent(), true);
        
        expect($response->getStatusCode())->toBe(500);
        expect($data['success'])->toBeFalse();
        expect($data['message'])->toBe('서버 내부 오류가 발생했습니다.');
    });
});