<?php

namespace Tests\Unit\Services;

use App\Exceptions\ValidationException;
use App\Models\NotificationTemplate;
use App\Services\TemplateValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * TemplateValidationService 고급 기능 단위 테스트
 * 기존 테스트에서 누락된 엣지 케이스와 경계값을 테스트합니다.
 */
class TemplateValidationServiceAdvancedTest extends TestCase
{
    use RefreshDatabase;

    private TemplateValidationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new TemplateValidationService;
    }

    /**
     * 극한 길이 템플릿 이름 검증 테스트
     */
    public function test_극한_길이_템플릿_이름_검증(): void
    {
        // Given: 매우 긴 이름 (255자)
        $longName = str_repeat('a', 255);
        $data = [
            'name' => $longName,
            'title' => '테스트 제목',
            'content' => '테스트 내용',
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'created_by' => 1,
        ];

        // When
        $result = $this->service->validateForCreate($data);

        // Then
        $this->assertEquals($longName, $result['name']);
    }

    /**
     * 빈 문자열과 공백 처리 테스트
     */
    public function test_빈_문자열과_공백_처리(): void
    {
        // Given: 공백만 있는 데이터
        $data = [
            'name' => '   ',
            'title' => "\t\n\r",
            'content' => '     ',
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'created_by' => 1,
        ];

        // When & Then
        $this->expectException(ValidationException::class);
        $this->service->validateForCreate($data);
    }

    /**
     * 특수 문자 포함 템플릿 이름 검증 테스트
     */
    public function test_특수_문자_포함_템플릿_이름_검증(): void
    {
        // Given
        $specialName = '!@#$%^&*()_+-=[]{}|;:,.<>?/~`';
        $data = [
            'name' => $specialName,
            'title' => '특수문자 테스트',
            'content' => '특수문자가 포함된 템플릿',
            'priority' => NotificationTemplate::PRIORITY_HIGH,
            'created_by' => 1,
        ];

        // When
        $result = $this->service->validateForCreate($data);

        // Then
        $this->assertStringContainsString('!@#$%^&amp;*()_+-=[]{}|;:,.&lt;&gt;?/~`', $result['name']);
    }

    /**
     * 유니코드 문자 처리 테스트
     */
    public function test_유니코드_문자_처리(): void
    {
        // Given
        $unicodeName = '한글 템플릿 🚀 テスト العربية';
        $data = [
            'name' => $unicodeName,
            'title' => 'Unicode Test',
            'content' => 'Unicode content test',
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'created_by' => 1,
        ];

        // When
        $result = $this->service->validateForCreate($data);

        // Then
        $this->assertEquals($unicodeName, $result['name']);
    }

    /**
     * HTML 태그 정제 테스트
     */
    public function test_htm_l_태그_정제(): void
    {
        // Given
        $htmlContent = '<script>alert("xss")</script><p>안전한 내용</p><img src="x" onerror="alert(1)">';
        $data = [
            'name' => '테스트 템플릿',
            'title' => '<h1>제목</h1>',
            'content' => $htmlContent,
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'created_by' => 1,
        ];

        // When
        $result = $this->service->validateForCreate($data);

        // Then
        $this->assertStringNotContainsString('<script>', $result['content']);
        $this->assertStringNotContainsString('onerror', $result['content']);
        $this->assertStringContainsString('&lt;h1&gt;제목&lt;/h1&gt;', $result['title']);
    }

    /**
     * 매우 큰 숫자 created_by 처리 테스트
     */
    public function test_매우_큰_숫자_created_by_처리(): void
    {
        // Given
        $data = [
            'name' => '테스트 템플릿',
            'title' => '테스트 제목',
            'content' => '테스트 내용',
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'created_by' => PHP_INT_MAX,
        ];

        // When
        $result = $this->service->validateForCreate($data);

        // Then
        $this->assertEquals(PHP_INT_MAX, $result['created_by']);
    }

    /**
     * 복합 필터 조건 극한 테스트
     */
    public function test_복합_필터_조건_극한_테스트(): void
    {
        // Given
        $filters = [
            'search' => str_repeat('검색', 50), // 100자 검색어
            'priority' => NotificationTemplate::PRIORITY_URGENT,
            'created_by' => PHP_INT_MAX,
            'min_usage' => 0,
            'max_usage' => PHP_INT_MAX,
            'created_from' => '1970-01-01',
            'created_to' => '2099-12-31',
            'sortBy' => 'usage_count',
            'sortDirection' => 'DESC',
            'per_page' => 100,
        ];

        // When
        $result = $this->service->validateFilters($filters);

        // Then
        $this->assertArrayHasKey('search', $result);
        $this->assertEquals(NotificationTemplate::PRIORITY_URGENT, $result['priority']);
        $this->assertEquals(PHP_INT_MAX, $result['created_by']);
        $this->assertEquals(0, $result['min_usage']);
        $this->assertEquals(PHP_INT_MAX, $result['max_usage']);
    }

    /**
     * 날짜 형식 경계값 테스트
     */
    public function test_날짜_형식_경계값_테스트(): void
    {
        // Given: 다양한 날짜 형식
        $validDates = [
            '2024-01-01',
            '2024-12-31',
            '1970-01-01',
            '2099-12-31',
        ];

        foreach ($validDates as $date) {
            $filters = ['created_from' => $date];

            // When
            $result = $this->service->validateFilters($filters);

            // Then
            $this->assertArrayHasKey('created_from', $result);
        }
    }

    /**
     * 잘못된 날짜 형식 테스트
     */
    public function test_잘못된_날짜_형식_테스트(): void
    {
        // Given: 잘못된 날짜 형식들
        $invalidDates = [
            '2024/01/01',
            '01-01-2024',
            '2024.01.01',
            '24-01-01',
            '2024-13-01', // 잘못된 월
            '2024-01-32', // 잘못된 일
            'invalid-date',
        ];

        foreach ($invalidDates as $date) {
            $filters = ['created_from' => $date];

            // When & Then
            $this->expectException(ValidationException::class);
            $this->service->validateFilters($filters);
        }
    }

    /**
     * 사용 횟수 경계값 테스트
     */
    public function test_사용_횟수_경계값_테스트(): void
    {
        // Given: 경계값들
        $boundaryValues = [
            ['min_usage' => 0, 'max_usage' => 0],
            ['min_usage' => 0, 'max_usage' => 1],
            ['min_usage' => PHP_INT_MAX - 1, 'max_usage' => PHP_INT_MAX],
        ];

        foreach ($boundaryValues as $values) {
            $filters = $values;

            // When
            $result = $this->service->validateFilters($filters);

            // Then
            $this->assertEquals($values['min_usage'], $result['min_usage']);
            $this->assertEquals($values['max_usage'], $result['max_usage']);
        }
    }

    /**
     * 정렬 필드 대소문자 구분 테스트
     */
    public function test_정렬_필드_대소문자_구분_테스트(): void
    {
        // Given: 다양한 대소문자 조합
        $sortFields = [
            'name',
            'NAME',
            'Name',
            'usage_count',
            'USAGE_COUNT',
            'Usage_Count',
        ];

        foreach ($sortFields as $field) {
            $filters = ['sortBy' => $field];

            if (in_array(strtolower($field), ['name', 'usage_count', 'created_at', 'priority'])) {
                // When
                $result = $this->service->validateFilters($filters);

                // Then
                $this->assertEquals($field, $result['sortBy']);
            } else {
                // When & Then
                $this->expectException(ValidationException::class);
                $this->service->validateFilters($filters);
            }
        }
    }

    /**
     * 정렬 방향 대소문자 구분 테스트
     */
    public function test_정렬_방향_대소문자_구분_테스트(): void
    {
        // Given: 다양한 대소문자 조합
        $directions = [
            'asc' => true,
            'ASC' => true,
            'desc' => true,
            'DESC' => true,
            'Asc' => true,
            'Desc' => true,
            'invalid' => false,
        ];

        foreach ($directions as $direction => $shouldPass) {
            $filters = ['sortDirection' => $direction];

            if ($shouldPass) {
                // When
                $result = $this->service->validateFilters($filters);

                // Then
                $this->assertEquals($direction, $result['sortDirection']);
            } else {
                // When & Then
                $this->expectException(ValidationException::class);
                $this->service->validateFilters($filters);
            }
        }
    }

    /**
     * 페이지 크기 경계값 테스트
     */
    public function test_페이지_크기_경계값_테스트(): void
    {
        // Given: 경계값들
        $pageSizes = [
            1 => true,    // 최소값
            50 => true,   // 중간값
            100 => true,  // 최대값
            0 => false,   // 최소값 미만
            101 => false,  // 최대값 초과
        ];

        foreach ($pageSizes as $size => $shouldPass) {
            $filters = ['per_page' => $size];

            if ($shouldPass) {
                // When
                $result = $this->service->validateFilters($filters);

                // Then
                $this->assertEquals($size, $result['per_page']);
            } else {
                // When & Then
                $this->expectException(ValidationException::class);
                $this->service->validateFilters($filters);
            }
        }
    }

    /**
     * 필터 정제 시 알 수 없는 필드 제거 테스트
     */
    public function test_필터_정제_시_알_수_없는_필드_제거(): void
    {
        // Given
        $filters = [
            'search' => 'valid',
            'priority' => NotificationTemplate::PRIORITY_HIGH,
            'unknown_field_1' => 'should_be_removed',
            'hack_attempt' => '<script>alert("xss")</script>',
            'sql_injection' => "'; DROP TABLE users; --",
            'created_by' => 1,
        ];

        // When
        $result = $this->service->sanitizeFilters($filters);

        // Then
        $this->assertArrayHasKey('search', $result);
        $this->assertArrayHasKey('priority', $result);
        $this->assertArrayHasKey('created_by', $result);
        $this->assertArrayNotHasKey('unknown_field_1', $result);
        $this->assertArrayNotHasKey('hack_attempt', $result);
        $this->assertArrayNotHasKey('sql_injection', $result);
    }

    /**
     * 중복 템플릿 이름 검증 대소문자 구분 테스트
     */
    public function test_중복_템플릿_이름_검증_대소문자_구분(): void
    {
        // Given: 기존 템플릿 생성
        NotificationTemplate::factory()->create(['name' => 'Test Template']);

        $testCases = [
            'Test Template' => false,  // 정확히 같음
            'test template' => true,   // 소문자
            'TEST TEMPLATE' => true,   // 대문자
            'Test template' => true,   // 혼합
        ];

        foreach ($testCases as $name => $shouldPass) {
            $data = [
                'name' => $name,
                'title' => '테스트 제목',
                'content' => '테스트 내용',
                'priority' => NotificationTemplate::PRIORITY_NORMAL,
                'created_by' => 1,
            ];

            if ($shouldPass) {
                // When
                $result = $this->service->validateForCreate($data);

                // Then
                $this->assertEquals($name, $result['name']);
            } else {
                // When & Then
                $this->expectException(ValidationException::class);
                $this->service->validateForCreate($data);
            }
        }
    }

    /**
     * 템플릿 수정 시 자기 자신과 같은 이름 허용 테스트
     */
    public function test_템플릿_수정_시_자기_자신과_같은_이름_허용(): void
    {
        // Given: 기존 템플릿 생성
        $template = NotificationTemplate::factory()->create(['name' => 'Original Template']);

        $data = [
            'name' => 'Original Template', // 같은 이름
            'title' => '수정된 제목',
            'content' => '수정된 내용',
        ];

        // When
        $result = $this->service->validateForUpdate($data, $template->id);

        // Then
        $this->assertEquals('Original Template', $result['name']);
        $this->assertEquals('수정된 제목', $result['title']);
    }

    /**
     * 빈 배열 필터 처리 테스트
     */
    public function test_빈_배열_필터_처리(): void
    {
        // Given
        $emptyFilters = [];

        // When
        $result = $this->service->validateFilters($emptyFilters);

        // Then
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * null 값 필터 처리 테스트
     */
    public function test_null_값_필터_처리(): void
    {
        // Given
        $nullFilters = [
            'search' => null,
            'priority' => null,
            'created_by' => null,
            'min_usage' => null,
            'max_usage' => null,
        ];

        // When
        $result = $this->service->validateFilters($nullFilters);

        // Then
        $this->assertIsArray($result);
        // null 값들은 제거되어야 함
        $this->assertArrayNotHasKey('search', $result);
        $this->assertArrayNotHasKey('priority', $result);
        $this->assertArrayNotHasKey('created_by', $result);
    }

    /**
     * 매우 긴 검색어 처리 테스트
     */
    public function test_매우_긴_검색어_처리(): void
    {
        // Given: 100자를 초과하는 검색어
        $longSearch = str_repeat('가', 101);
        $filters = ['search' => $longSearch];

        // When & Then
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('검색어는 100자를 초과할 수 없습니다.');
        $this->service->validateFilters($filters);
    }

    /**
     * 정확히 100자 검색어 처리 테스트
     */
    public function test_정확히_100자_검색어_처리(): void
    {
        // Given: 정확히 100자 검색어
        $exactSearch = str_repeat('가', 100);
        $filters = ['search' => $exactSearch];

        // When
        $result = $this->service->validateFilters($filters);

        // Then
        $this->assertEquals($exactSearch, $result['search']);
    }
}
