<?php

namespace Tests\Unit\Services;

use App\Models\RepairCostTypeProcessMapping;
use App\Models\RepairProcess;
use App\Services\RepairCostTypeProcessMappingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class RepairProcessMappingServiceTest extends TestCase
{
    use RefreshDatabase;

    private RepairCostTypeProcessMappingService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new RepairCostTypeProcessMappingService;
    }

    /** @test */
    public function 프로세스_코드로_수리_유형을_조회할_수_있다()
    {
        // Given
        $processCode = 'TEST_001';
        $repairType = RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS;

        RepairCostTypeProcessMapping::create([
            'process_code' => $processCode,
            'repair_type' => $repairType,
            'is_active' => true,
        ]);

        // When
        $result = $this->service->getRepairTypeByCode($processCode);

        // Then
        $this->assertEquals($repairType, $result);
    }

    /** @test */
    public function 매핑되지_않은_프로세스_코드는_null을_반환한다()
    {
        // Given
        $processCode = 'NONEXISTENT_CODE';

        // When
        $result = $this->service->getRepairTypeByCode($processCode);

        // Then
        $this->assertNull($result);
    }

    /** @test */
    public function 매핑되지_않은_프로세스_코드에_기본값을_적용할_수_있다()
    {
        // Given
        $processCode = 'NONEXISTENT_CODE';

        Log::shouldReceive('warning')
            ->once()
            ->with('매핑되지 않은 프로세스 코드에 기본값 적용', [
                'process_code' => $processCode,
                'default_repair_type' => RepairCostTypeProcessMappingService::DEFAULT_REPAIR_TYPE,
            ]);

        // When
        $result = $this->service->getRepairTypeByCodeWithDefault($processCode);

        // Then
        $this->assertEquals(RepairCostTypeProcessMappingService::DEFAULT_REPAIR_TYPE, $result);
    }

    /** @test */
    public function 새로운_매핑을_생성할_수_있다()
    {
        // Given
        $processCode = 'TEST_002';
        $repairType = RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING;

        // RepairProcess 생성
        RepairProcess::create([
            'name' => 'Test Process',
            'code' => $processCode,
        ]);

        // When
        $mapping = $this->service->createMapping($processCode, $repairType);

        // Then
        $this->assertInstanceOf(RepairCostTypeProcessMapping::class, $mapping);
        $this->assertEquals($processCode, $mapping->process_code);
        $this->assertEquals($repairType, $mapping->repair_type);
        $this->assertTrue($mapping->is_active);
    }

    /** @test */
    public function 유효하지_않은_수리_유형으로_매핑_생성_시_예외가_발생한다()
    {
        // Given
        $processCode = 'TEST_003';
        $invalidRepairType = 'invalid_type';

        RepairProcess::create([
            'name' => 'Test Process',
            'code' => $processCode,
        ]);

        // When & Then
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("유효하지 않은 수리 유형입니다: {$invalidRepairType}");

        $this->service->createMapping($processCode, $invalidRepairType);
    }

    /** @test */
    public function 존재하지_않는_프로세스_코드로_매핑_생성_시_예외가_발생한다()
    {
        // Given
        $nonexistentCode = 'NONEXISTENT';
        $repairType = RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS;

        // When & Then
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("존재하지 않는 프로세스 코드입니다: {$nonexistentCode}");

        $this->service->createMapping($nonexistentCode, $repairType);
    }

    /** @test */
    public function 중복_매핑_생성_시_예외가_발생한다()
    {
        // Given
        $processCode = 'TEST_004';
        $repairType1 = RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS;
        $repairType2 = RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING;

        RepairProcess::create([
            'name' => 'Test Process',
            'code' => $processCode,
        ]);

        // 첫 번째 매핑 생성
        $this->service->createMapping($processCode, $repairType1);

        // When & Then
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("프로세스 코드 '{$processCode}'는 이미 '{$repairType1}' 수리 유형에 매핑되어 있습니다.");

        $this->service->createMapping($processCode, $repairType2);
    }

    /** @test */
    public function 기존_매핑을_수정할_수_있다()
    {
        // Given
        $processCode = 'TEST_005';
        $originalType = RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS;
        $newType = RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING;

        RepairProcess::create([
            'name' => 'Test Process',
            'code' => $processCode,
        ]);

        $mapping = $this->service->createMapping($processCode, $originalType);

        // When
        $updatedMapping = $this->service->updateMapping($mapping->id, [
            'repair_type' => $newType,
        ]);

        // Then
        $this->assertEquals($newType, $updatedMapping->repair_type);
        $this->assertEquals($processCode, $updatedMapping->process_code);
    }

    /** @test */
    public function 매핑을_비활성화할_수_있다()
    {
        // Given
        $processCode = 'TEST_006';
        $repairType = RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS;

        RepairProcess::create([
            'name' => 'Test Process',
            'code' => $processCode,
        ]);

        $mapping = $this->service->createMapping($processCode, $repairType);

        // When
        $result = $this->service->deleteMapping($mapping->id);

        // Then
        $this->assertTrue($result);
        $mapping->refresh();
        $this->assertFalse($mapping->is_active);
    }

    /** @test */
    public function 모든_매핑을_조회할_수_있다()
    {
        // Given
        $processCode1 = 'TEST_007';
        $processCode2 = 'TEST_008';

        RepairProcess::create(['name' => 'Test Process 1', 'code' => $processCode1]);
        RepairProcess::create(['name' => 'Test Process 2', 'code' => $processCode2]);

        $this->service->createMapping($processCode1, RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS);
        $this->service->createMapping($processCode2, RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING);

        // When
        $mappings = $this->service->getAllMappings();

        // Then
        $this->assertCount(2, $mappings);
        $this->assertTrue($mappings->contains('process_code', $processCode1));
        $this->assertTrue($mappings->contains('process_code', $processCode2));
    }

    /** @test */
    public function 특정_수리_유형의_매핑을_조회할_수_있다()
    {
        // Given
        $processCode1 = 'TEST_009';
        $processCode2 = 'TEST_010';
        $targetType = RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS;

        RepairProcess::create(['name' => 'Test Process 1', 'code' => $processCode1]);
        RepairProcess::create(['name' => 'Test Process 2', 'code' => $processCode2]);

        $this->service->createMapping($processCode1, $targetType);
        $this->service->createMapping($processCode2, RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING);

        // When
        $mappings = $this->service->getMappingsByRepairType($targetType);

        // Then
        $this->assertCount(1, $mappings);
        $this->assertEquals($processCode1, $mappings->first()->process_code);
        $this->assertEquals($targetType, $mappings->first()->repair_type);
    }

    /** @test */
    public function 매핑되지_않은_프로세스_목록을_조회할_수_있다()
    {
        // Given
        $mappedCode = 'MAPPED_001';
        $unmappedCode = 'UNMAPPED_001';

        RepairProcess::create(['name' => 'Mapped Process', 'code' => $mappedCode]);
        RepairProcess::create(['name' => 'Unmapped Process', 'code' => $unmappedCode]);

        $this->service->createMapping($mappedCode, RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS);

        // When
        $unmappedProcesses = $this->service->getUnmappedProcesses();

        // Then
        $this->assertCount(1, $unmappedProcesses);
        $this->assertEquals($unmappedCode, $unmappedProcesses->first()->code);
    }

    /** @test */
    public function 매핑_통계를_조회할_수_있다()
    {
        // Given
        $processCode1 = 'STAT_001';
        $processCode2 = 'STAT_002';
        $processCode3 = 'STAT_003';

        RepairProcess::create(['name' => 'Process 1', 'code' => $processCode1]);
        RepairProcess::create(['name' => 'Process 2', 'code' => $processCode2]);
        RepairProcess::create(['name' => 'Process 3', 'code' => $processCode3]);

        $this->service->createMapping($processCode1, RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS);
        $this->service->createMapping($processCode2, RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING);
        // processCode3는 매핑하지 않음

        // When
        $stats = $this->service->getMappingStatistics();

        // Then
        $this->assertEquals(3, $stats['total_processes']);
        $this->assertEquals(2, $stats['mapped_processes']);
        $this->assertEquals(1, $stats['unmapped_processes']);
        $this->assertEquals(66.67, $stats['mapping_rate']);
        $this->assertArrayHasKey('repair_type_distribution', $stats);
    }

    /** @test */
    public function 대량_매핑을_처리할_수_있다()
    {
        // Given
        $processCode1 = 'BULK_001';
        $processCode2 = 'BULK_002';

        RepairProcess::create(['name' => 'Bulk Process 1', 'code' => $processCode1]);
        RepairProcess::create(['name' => 'Bulk Process 2', 'code' => $processCode2]);

        $mappings = [
            ['process_code' => $processCode1, 'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS],
            ['process_code' => $processCode2, 'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING],
        ];

        // When
        $results = $this->service->bulkUpdateMappings($mappings);

        // Then
        $this->assertCount(2, $results['success']);
        $this->assertCount(0, $results['failed']);

        // 실제 매핑이 생성되었는지 확인
        $this->assertEquals(RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS,
            $this->service->getRepairTypeByCode($processCode1));
        $this->assertEquals(RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING,
            $this->service->getRepairTypeByCode($processCode2));
    }

    /** @test */
    public function 매핑_상태를_토글할_수_있다()
    {
        // Given
        $processCode = 'TOGGLE_001';
        RepairProcess::create(['name' => 'Toggle Process', 'code' => $processCode]);

        $mapping = $this->service->createMapping($processCode, RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS);
        $this->assertTrue($mapping->is_active);

        // When
        $updatedMapping = $this->service->toggleMappingStatus($mapping->id, false);

        // Then
        $this->assertFalse($updatedMapping->is_active);

        // 다시 활성화
        $reactivatedMapping = $this->service->toggleMappingStatus($mapping->id, true);
        $this->assertTrue($reactivatedMapping->is_active);
    }

    /** @test */
    public function 프로세스_코드_존재_여부를_확인할_수_있다()
    {
        // Given
        $existingCode = 'EXISTS_001';
        $nonexistentCode = 'NONEXISTENT_001';

        RepairProcess::create(['name' => 'Existing Process', 'code' => $existingCode]);

        // When & Then
        $this->assertTrue($this->service->processCodeExists($existingCode));
        $this->assertFalse($this->service->processCodeExists($nonexistentCode));
    }

    /** @test */
    public function 매핑_존재_여부를_확인할_수_있다()
    {
        // Given
        $mappedCode = 'MAPPED_CHECK_001';
        $unmappedCode = 'UNMAPPED_CHECK_001';

        RepairProcess::create(['name' => 'Mapped Process', 'code' => $mappedCode]);
        RepairProcess::create(['name' => 'Unmapped Process', 'code' => $unmappedCode]);

        $this->service->createMapping($mappedCode, RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS);

        // When & Then
        $this->assertTrue($this->service->mappingExists($mappedCode));
        $this->assertFalse($this->service->mappingExists($unmappedCode));
    }
}
