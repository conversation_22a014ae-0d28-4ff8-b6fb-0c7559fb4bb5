<?php

namespace Tests\Unit\Services;

use App\Exceptions\BusinessException;
use App\Exceptions\ValidationException;
use App\Models\NotificationTemplate;
use App\Repositories\Interfaces\NotificationTemplateRepositoryInterface;
use App\Services\TemplateLoggingService;
use App\Services\TemplateSearchService;
use App\Services\TemplateValidationService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator as ConcretePaginator;
use Mockery;
use Tests\TestCase;

/**
 * TemplateSearchService 단위 테스트
 */
class TemplateSearchServiceTest extends TestCase
{
    private TemplateSearchService $service;

    private Mockery\MockInterface $templateRepository;

    private Mockery\MockInterface $validationService;

    private Mockery\MockInterface $loggingService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->templateRepository = Mockery::mock(NotificationTemplateRepositoryInterface::class);
        $this->validationService = Mockery::mock(TemplateValidationService::class);
        $this->loggingService = Mockery::mock(TemplateLoggingService::class);

        $this->service = new TemplateSearchService(
            $this->templateRepository,
            $this->validationService,
            $this->loggingService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @test
     * 템플릿 목록 조회 성공 테스트
     */
    public function get_templates_성공적으로_페이지네이션된_템플릿_목록을_반환한다(): void
    {
        // Given
        $userId = 1;
        $page = 1;
        $filters = ['search' => 'test', 'priority' => 'high'];
        $validatedFilters = ['search' => 'test', 'priority' => 'high'];
        $sanitizedFilters = ['search' => 'test', 'priority' => 'high', 'per_page' => 20];

        $mockPaginator = new ConcretePaginator(
            collect([new NotificationTemplate]),
            1,
            20,
            1
        );

        $this->validationService
            ->shouldReceive('validateFilters')
            ->once()
            ->with($filters)
            ->andReturn($validatedFilters);

        $this->validationService
            ->shouldReceive('sanitizeFilters')
            ->once()
            ->with($validatedFilters)
            ->andReturn($validatedFilters);

        $this->templateRepository
            ->shouldReceive('getPaginatedWithStats')
            ->once()
            ->with(1, 20, $sanitizedFilters)
            ->andReturn($mockPaginator);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once()
            ->with('템플릿 목록 조회 완료', Mockery::type('array'));

        // When
        $result = $this->service->getTemplates($userId, $page, $filters);

        // Then
        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
        $this->assertEquals(1, $result->total());
    }

    /**
     * @test
     * 템플릿 목록 조회 시 유효성 검증 실패 테스트
     */
    public function get_templates_유효성_검증_실패_시_validation_exception을_던진다(): void
    {
        // Given
        $userId = 1;
        $page = 1;
        $filters = ['invalid' => 'data'];

        $this->validationService
            ->shouldReceive('validateFilters')
            ->once()
            ->with($filters)
            ->andThrow(new ValidationException('유효성 검증 실패'));

        // When & Then
        $this->expectException(ValidationException::class);
        $this->service->getTemplates($userId, $page, $filters);
    }

    /**
     * @test
     * 템플릿 목록 조회 시 예외 발생 테스트
     */
    public function get_templates_예외_발생_시_business_exception을_던진다(): void
    {
        // Given
        $userId = 1;
        $page = 1;
        $filters = [];

        $this->validationService
            ->shouldReceive('validateFilters')
            ->once()
            ->andReturn([]);

        $this->validationService
            ->shouldReceive('sanitizeFilters')
            ->once()
            ->andReturn([]);

        $this->templateRepository
            ->shouldReceive('getPaginatedWithStats')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->loggingService
            ->shouldReceive('logError')
            ->once()
            ->with('템플릿 목록 조회 중 오류 발생', Mockery::type('array'));

        // When & Then
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('템플릿 목록 조회 중 오류가 발생했습니다.');
        $this->service->getTemplates($userId, $page, $filters);
    }

    /**
     * @test
     * 템플릿 검색 성공 테스트
     */
    public function search_templates_성공적으로_검색된_템플릿_목록을_반환한다(): void
    {
        // Given
        $userId = 1;
        $search = 'test search';
        $filters = ['priority' => 'high'];

        $mockCollection = Mockery::mock(Collection::class);
        $mockCollection->shouldReceive('count')->andReturn(1);

        $this->templateRepository
            ->shouldReceive('search')
            ->once()
            ->with($search, $filters)
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once()
            ->with('템플릿 검색 완료', Mockery::type('array'));

        // When
        $result = $this->service->searchTemplates($userId, $search, $filters);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * @test
     * 우선순위별 템플릿 조회 성공 테스트
     */
    public function get_templates_by_priority_성공적으로_우선순위별_템플릿을_반환한다(): void
    {
        // Given
        $userId = 1;
        $priority = 'high';

        $mockCollection = Mockery::mock(Collection::class);
        $mockCollection->shouldReceive('count')->andReturn(1);

        $this->templateRepository
            ->shouldReceive('findByPriority')
            ->once()
            ->with($priority)
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once()
            ->with('우선순위별 템플릿 조회 완료', Mockery::type('array'));

        // When
        $result = $this->service->getTemplatesByPriority($userId, $priority);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * @test
     * 사용 횟수 기준 정렬된 템플릿 조회 성공 테스트
     */
    public function get_templates_ordered_by_usage_성공적으로_사용_횟수_기준_정렬된_템플릿을_반환한다(): void
    {
        // Given
        $userId = 1;
        $direction = 'desc';

        $mockCollection = Mockery::mock(Collection::class);
        $mockCollection->shouldReceive('count')->andReturn(1);

        $this->templateRepository
            ->shouldReceive('getOrderedByUsage')
            ->once()
            ->with($direction)
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once()
            ->with('사용 횟수 기준 템플릿 조회 완료', Mockery::type('array'));

        // When
        $result = $this->service->getTemplatesOrderedByUsage($userId, $direction);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * @test
     * 생성일 기준 정렬된 템플릿 조회 성공 테스트
     */
    public function get_templates_ordered_by_created_성공적으로_생성일_기준_정렬된_템플릿을_반환한다(): void
    {
        // Given
        $userId = 1;
        $direction = 'asc';

        $mockCollection = Mockery::mock(Collection::class);
        $mockCollection->shouldReceive('count')->andReturn(1);

        $this->templateRepository
            ->shouldReceive('getOrderedByCreated')
            ->once()
            ->with($direction)
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once()
            ->with('생성일 기준 템플릿 조회 완료', Mockery::type('array'));

        // When
        $result = $this->service->getTemplatesOrderedByCreated($userId, $direction);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * @test
     * 인기 템플릿 조회 성공 테스트
     */
    public function get_popular_templates_성공적으로_인기_템플릿을_반환한다(): void
    {
        // Given
        $userId = 1;
        $limit = 5;

        $mockCollection = Mockery::mock(Collection::class);
        $mockCollection->shouldReceive('count')->andReturn(1);

        $this->templateRepository
            ->shouldReceive('getPopularTemplates')
            ->once()
            ->with($limit)
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once()
            ->with('인기 템플릿 조회 완료', Mockery::type('array'));

        // When
        $result = $this->service->getPopularTemplates($userId, $limit);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * @test
     * 최근 템플릿 조회 성공 테스트
     */
    public function get_recent_templates_성공적으로_최근_템플릿을_반환한다(): void
    {
        // Given
        $userId = 1;
        $limit = 10;

        $mockCollection = Mockery::mock(Collection::class);
        $mockCollection->shouldReceive('count')->andReturn(1);

        $this->templateRepository
            ->shouldReceive('getRecentTemplates')
            ->once()
            ->with($limit)
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once()
            ->with('최근 템플릿 조회 완료', Mockery::type('array'));

        // When
        $result = $this->service->getRecentTemplates($userId, $limit);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * @test
     * 고급 필터링 템플릿 조회 성공 테스트
     */
    public function get_templates_with_advanced_filters_성공적으로_고급_필터링된_템플릿을_반환한다(): void
    {
        // Given
        $userId = 1;
        $filters = ['priority' => 'high', 'min_usage' => 5];
        $validatedFilters = ['priority' => 'high', 'min_usage' => 5];

        $mockCollection = Mockery::mock(Collection::class);
        $mockCollection->shouldReceive('count')->andReturn(1);

        $this->validationService
            ->shouldReceive('validateFilters')
            ->once()
            ->with($filters)
            ->andReturn($validatedFilters);

        $this->templateRepository
            ->shouldReceive('search')
            ->once()
            ->with('', $validatedFilters)
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once()
            ->with('고급 필터링 템플릿 조회 완료', Mockery::type('array'));

        // When
        $result = $this->service->getTemplatesWithAdvancedFilters($userId, $filters);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * @test
     * 사용되지 않은 템플릿 조회 성공 테스트
     */
    public function get_unused_templates_성공적으로_사용되지_않은_템플릿을_반환한다(): void
    {
        // Given
        $userId = 1;

        $mockCollection = Mockery::mock(Collection::class);
        $mockCollection->shouldReceive('count')->andReturn(1);

        $this->templateRepository
            ->shouldReceive('getUnusedTemplates')
            ->once()
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once()
            ->with('사용되지 않은 템플릿 조회 완료', Mockery::type('array'));

        // When
        $result = $this->service->getUnusedTemplates($userId);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * @test
     * 사용 횟수 범위별 템플릿 조회 성공 테스트
     */
    public function get_templates_by_usage_range_성공적으로_사용_횟수_범위별_템플릿을_반환한다(): void
    {
        // Given
        $userId = 1;
        $minUsage = 5;
        $maxUsage = 20;

        $mockCollection = Mockery::mock(Collection::class);
        $mockCollection->shouldReceive('count')->andReturn(1);

        $this->templateRepository
            ->shouldReceive('getTemplatesByUsageRange')
            ->once()
            ->with($minUsage, $maxUsage)
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once()
            ->with('사용 횟수 범위별 템플릿 조회 완료', Mockery::type('array'));

        // When
        $result = $this->service->getTemplatesByUsageRange($userId, $minUsage, $maxUsage);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * @test
     * 사용 횟수 범위별 템플릿 조회 시 최소값 유효성 검증 실패 테스트
     */
    public function get_templates_by_usage_range_최소_사용_횟수가_음수일_때_validation_exception을_던진다(): void
    {
        // Given
        $userId = 1;
        $minUsage = -1;

        // When & Then
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('최소 사용 횟수는 0 이상이어야 합니다.');
        $this->service->getTemplatesByUsageRange($userId, $minUsage);
    }

    /**
     * @test
     * 사용 횟수 범위별 템플릿 조회 시 최대값 유효성 검증 실패 테스트
     */
    public function get_templates_by_usage_range_최대_사용_횟수가_음수일_때_validation_exception을_던진다(): void
    {
        // Given
        $userId = 1;
        $minUsage = 0;
        $maxUsage = -1;

        // When & Then
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('최대 사용 횟수는 0 이상이어야 합니다.');
        $this->service->getTemplatesByUsageRange($userId, $minUsage, $maxUsage);
    }

    /**
     * @test
     * 사용 횟수 범위별 템플릿 조회 시 범위 논리 검증 실패 테스트
     */
    public function get_templates_by_usage_range_최소값이_최대값보다_클_때_validation_exception을_던진다(): void
    {
        // Given
        $userId = 1;
        $minUsage = 10;
        $maxUsage = 5;

        // When & Then
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('최소 사용 횟수는 최대 사용 횟수보다 클 수 없습니다.');
        $this->service->getTemplatesByUsageRange($userId, $minUsage, $maxUsage);
    }

    /**
     * @test
     * 검색 메서드들의 예외 처리 테스트
     */
    public function search_templates_예외_발생_시_business_exception을_던진다(): void
    {
        // Given
        $userId = 1;
        $search = 'test';
        $filters = [];

        $this->templateRepository
            ->shouldReceive('search')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->loggingService
            ->shouldReceive('logError')
            ->once()
            ->with('템플릿 검색 중 오류 발생', Mockery::type('array'));

        // When & Then
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('템플릿 검색 중 오류가 발생했습니다.');
        $this->service->searchTemplates($userId, $search, $filters);
    }
}
