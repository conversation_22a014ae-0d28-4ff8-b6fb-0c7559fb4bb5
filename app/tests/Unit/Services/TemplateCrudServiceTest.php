<?php

namespace Tests\Unit\Services;

use App\Exceptions\BusinessException;
use App\Exceptions\TemplateException;
use App\Exceptions\ValidationException;
use App\Models\NotificationTemplate;
use App\Repositories\Interfaces\NotificationTemplateRepositoryInterface;
use App\Services\TemplateCrudService;
use App\Services\TemplateLoggingService;
use App\Services\TemplatePermissionService;
use App\Services\TemplateValidationService;
use Illuminate\Database\Eloquent\Collection;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\TestCase;

class TemplateCrudServiceTest extends TestCase
{
    private TemplateCrudService $service;

    private MockObject $templateRepository;

    private MockObject $validationService;

    private MockObject $permissionService;

    private MockObject $loggingService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->templateRepository = $this->createMock(NotificationTemplateRepositoryInterface::class);
        $this->validationService = $this->createMock(TemplateValidationService::class);
        $this->permissionService = $this->createMock(TemplatePermissionService::class);
        $this->loggingService = $this->createMock(TemplateLoggingService::class);

        $this->service = new TemplateCrudService(
            $this->templateRepository,
            $this->validationService,
            $this->permissionService,
            $this->loggingService
        );
    }

    public function test_create_성공적으로_템플릿을_생성한다(): void
    {
        // Given
        $userId = 1;
        $templateData = [
            'name' => '테스트 템플릿',
            'title' => '테스트 제목',
            'content' => '테스트 내용',
        ];
        $expectedData = array_merge($templateData, ['created_by' => $userId]);
        $validatedData = $expectedData;

        $template = new NotificationTemplate;
        $template->id = 1;
        $template->name = '테스트 템플릿';

        // When
        $this->validationService
            ->expects($this->once())
            ->method('validateForCreate')
            ->with($expectedData)
            ->willReturn($validatedData);

        $this->templateRepository
            ->expects($this->once())
            ->method('create')
            ->with($validatedData)
            ->willReturn($template);

        $this->loggingService
            ->expects($this->once())
            ->method('logTemplateAction')
            ->with('create', 1, $userId, ['template_name' => '테스트 템플릿']);

        // Then
        $result = $this->service->create($userId, $templateData);

        $this->assertSame($template, $result);
    }

    public function test_create_유효성_검증_실패_시_validation_exception을_던진다(): void
    {
        // Given
        $userId = 1;
        $templateData = ['invalid' => 'data'];
        $expectedData = array_merge($templateData, ['created_by' => $userId]);

        // When
        $this->validationService
            ->expects($this->once())
            ->method('validateForCreate')
            ->with($expectedData)
            ->willThrowException(new ValidationException('유효성 검증 실패'));

        // Then
        $this->expectException(ValidationException::class);
        $this->service->create($userId, $templateData);
    }

    public function test_create_예외_발생_시_business_exception을_던진다(): void
    {
        // Given
        $userId = 1;
        $templateData = ['name' => '테스트'];
        $expectedData = array_merge($templateData, ['created_by' => $userId]);

        // When
        $this->validationService
            ->expects($this->once())
            ->method('validateForCreate')
            ->with($expectedData)
            ->willReturn($expectedData);

        $this->templateRepository
            ->expects($this->once())
            ->method('create')
            ->willThrowException(new \Exception('DB 오류'));

        $this->loggingService
            ->expects($this->once())
            ->method('logError');

        // Then
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('템플릿 생성 중 오류가 발생했습니다.');

        $this->service->create($userId, $templateData);
    }

    public function test_find_by_id_성공적으로_템플릿을_조회한다(): void
    {
        // Given
        $templateId = 1;
        $template = new NotificationTemplate;
        $template->id = $templateId;

        // When
        $this->templateRepository
            ->expects($this->once())
            ->method('findById')
            ->with($templateId)
            ->willReturn($template);

        // Then
        $result = $this->service->findById($templateId);
        $this->assertSame($template, $result);
    }

    public function test_find_by_id_템플릿이_없으면_template_exception을_던진다(): void
    {
        // Given
        $templateId = 999;

        // When
        $this->templateRepository
            ->expects($this->once())
            ->method('findById')
            ->with($templateId)
            ->willReturn(null);

        // Then
        $this->expectException(TemplateException::class);
        $this->service->findById($templateId);
    }

    public function test_find_by_id_예외_발생_시_business_exception을_던진다(): void
    {
        // Given
        $templateId = 1;

        // When
        $this->templateRepository
            ->expects($this->once())
            ->method('findById')
            ->willThrowException(new \Exception('DB 오류'));

        $this->loggingService
            ->expects($this->once())
            ->method('logError');

        // Then
        $this->expectException(BusinessException::class);
        $this->service->findById($templateId);
    }

    public function test_update_성공적으로_템플릿을_수정한다(): void
    {
        // Given
        $userId = 1;
        $templateId = 1;
        $templateData = ['name' => '수정된 템플릿'];

        $existingTemplate = new NotificationTemplate;
        $existingTemplate->id = $templateId;

        $updatedTemplate = new NotificationTemplate;
        $updatedTemplate->id = $templateId;
        $updatedTemplate->name = '수정된 템플릿';

        // When
        $this->templateRepository
            ->expects($this->once())
            ->method('findById')
            ->with($templateId)
            ->willReturn($existingTemplate);

        $this->permissionService
            ->expects($this->once())
            ->method('validatePermission')
            ->with($userId, $existingTemplate, 'update');

        $this->validationService
            ->expects($this->once())
            ->method('validateForUpdate')
            ->with($templateData, $templateId)
            ->willReturn($templateData);

        $this->templateRepository
            ->expects($this->once())
            ->method('update')
            ->with($templateId, $templateData)
            ->willReturn($updatedTemplate);

        $this->loggingService
            ->expects($this->once())
            ->method('logTemplateAction')
            ->with('update', $templateId, $userId, [
                'template_name' => '수정된 템플릿',
                'updated_fields' => ['name'],
            ]);

        // Then
        $result = $this->service->update($userId, $templateId, $templateData);
        $this->assertSame($updatedTemplate, $result);
    }

    public function test_update_권한이_없으면_business_exception을_던진다(): void
    {
        // Given
        $userId = 1;
        $templateId = 1;
        $templateData = ['name' => '수정된 템플릿'];

        $existingTemplate = new NotificationTemplate;
        $existingTemplate->id = $templateId;

        // When
        $this->templateRepository
            ->expects($this->once())
            ->method('findById')
            ->willReturn($existingTemplate);

        $this->permissionService
            ->expects($this->once())
            ->method('validatePermission')
            ->willThrowException(new BusinessException('권한이 없습니다.'));

        // Then
        $this->expectException(BusinessException::class);
        $this->service->update($userId, $templateId, $templateData);
    }

    public function test_delete_성공적으로_템플릿을_삭제한다(): void
    {
        // Given
        $userId = 1;
        $templateId = 1;

        $template = new NotificationTemplate;
        $template->id = $templateId;
        $template->name = '삭제할 템플릿';

        // When
        $this->templateRepository
            ->expects($this->once())
            ->method('findById')
            ->with($templateId)
            ->willReturn($template);

        $this->permissionService
            ->expects($this->once())
            ->method('validatePermission')
            ->with($userId, $template, 'delete');

        $this->templateRepository
            ->expects($this->once())
            ->method('delete')
            ->with($templateId)
            ->willReturn(true);

        $this->loggingService
            ->expects($this->once())
            ->method('logTemplateAction')
            ->with('delete', $templateId, $userId, ['template_name' => '삭제할 템플릿']);

        // Then
        $result = $this->service->delete($userId, $templateId);
        $this->assertTrue($result);
    }

    public function test_delete_삭제_실패_시_template_exception을_던진다(): void
    {
        // Given
        $userId = 1;
        $templateId = 1;

        $template = new NotificationTemplate;
        $template->id = $templateId;

        // When
        $this->templateRepository
            ->expects($this->once())
            ->method('findById')
            ->willReturn($template);

        $this->permissionService
            ->expects($this->once())
            ->method('validatePermission');

        $this->templateRepository
            ->expects($this->once())
            ->method('delete')
            ->willReturn(false);

        // Then
        $this->expectException(TemplateException::class);
        $this->service->delete($userId, $templateId);
    }

    public function test_get_all_성공적으로_모든_템플릿을_조회한다(): void
    {
        // Given
        $templates = new Collection([
            new NotificationTemplate,
            new NotificationTemplate,
        ]);

        // When
        $this->templateRepository
            ->expects($this->once())
            ->method('getAllWithRelations')
            ->willReturn($templates);

        $this->loggingService
            ->expects($this->once())
            ->method('logInfo')
            ->with('전체 템플릿 목록 조회 완료', ['total_count' => 2]);

        // Then
        $result = $this->service->getAll();
        $this->assertSame($templates, $result);
    }

    public function test_get_all_예외_발생_시_business_exception을_던진다(): void
    {
        // When
        $this->templateRepository
            ->expects($this->once())
            ->method('getAllWithRelations')
            ->willThrowException(new \Exception('DB 오류'));

        $this->loggingService
            ->expects($this->once())
            ->method('logError');

        // Then
        $this->expectException(BusinessException::class);
        $this->service->getAll();
    }
}
