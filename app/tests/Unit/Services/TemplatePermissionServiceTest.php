<?php

namespace Tests\Unit\Services;

use App\Exceptions\BusinessException;
use App\Models\NotificationTemplate;
use App\Models\User;
use App\Services\TemplateLoggingService;
use App\Services\TemplatePermissionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * TemplatePermissionService 단위 테스트
 */
class TemplatePermissionServiceTest extends TestCase
{
    use RefreshDatabase;

    private TemplatePermissionService $service;

    private TemplateLoggingService $loggingService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->loggingService = $this->createMock(TemplateLoggingService::class);
        $this->service = new TemplatePermissionService($this->loggingService);
    }

    /**
     * 템플릿 생성 권한 테스트
     */
    public function test_can_create_returns_true_for_authenticated_user(): void
    {
        // Given: 인증된 사용자
        $user = User::factory()->create([
            'role' => User::ROLE_EMPLOYEE,
        ]);

        // When: 생성 권한 확인
        $result = $this->service->canCreate($user->id);

        // Then: 권한이 있어야 함
        $this->assertTrue($result);
    }

    public function test_can_create_returns_false_for_nonexistent_user(): void
    {
        // Given: 존재하지 않는 사용자 ID
        $nonExistentUserId = 99999;

        // When: 생성 권한 확인
        $result = $this->service->canCreate($nonExistentUserId);

        // Then: 권한이 없어야 함
        $this->assertFalse($result);
    }

    /**
     * 템플릿 읽기 권한 테스트
     */
    public function test_can_read_returns_true_for_authenticated_user(): void
    {
        // Given: 인증된 사용자와 템플릿
        $user = User::factory()->create();
        $template = NotificationTemplate::factory()->create();

        // When: 읽기 권한 확인
        $result = $this->service->canRead($user->id, $template->id);

        // Then: 권한이 있어야 함
        $this->assertTrue($result);
    }

    public function test_can_read_returns_false_for_nonexistent_user(): void
    {
        // Given: 존재하지 않는 사용자 ID와 템플릿
        $nonExistentUserId = 99999;
        $template = NotificationTemplate::factory()->create();

        // When: 읽기 권한 확인
        $result = $this->service->canRead($nonExistentUserId, $template->id);

        // Then: 권한이 없어야 함
        $this->assertFalse($result);
    }

    /**
     * 템플릿 수정 권한 테스트
     */
    public function test_can_update_returns_true_for_admin(): void
    {
        // Given: 관리자 사용자와 템플릿
        $admin = User::factory()->create([
            'role' => User::ROLE_ADMIN,
        ]);
        $template = NotificationTemplate::factory()->create();

        // When: 수정 권한 확인
        $result = $this->service->canUpdate($admin->id, $template->id);

        // Then: 권한이 있어야 함
        $this->assertTrue($result);
    }

    public function test_can_update_returns_true_for_super_admin(): void
    {
        // Given: 슈퍼 관리자 사용자와 템플릿
        $superAdmin = User::factory()->create([
            'role' => User::ROLE_SUPER_ADMIN,
        ]);
        $template = NotificationTemplate::factory()->create();

        // When: 수정 권한 확인
        $result = $this->service->canUpdate($superAdmin->id, $template->id);

        // Then: 권한이 있어야 함
        $this->assertTrue($result);
    }

    public function test_can_update_returns_true_for_template_creator(): void
    {
        // Given: 템플릿 생성자
        $creator = User::factory()->create([
            'role' => User::ROLE_EMPLOYEE,
        ]);
        $template = NotificationTemplate::factory()->create([
            'created_by' => $creator->id,
        ]);

        // When: 수정 권한 확인
        $result = $this->service->canUpdate($creator->id, $template->id);

        // Then: 권한이 있어야 함
        $this->assertTrue($result);
    }

    public function test_can_update_returns_false_for_other_user(): void
    {
        // Given: 다른 사용자와 템플릿
        $creator = User::factory()->create();
        $otherUser = User::factory()->create([
            'role' => User::ROLE_EMPLOYEE,
        ]);
        $template = NotificationTemplate::factory()->create([
            'created_by' => $creator->id,
        ]);

        // When: 수정 권한 확인
        $result = $this->service->canUpdate($otherUser->id, $template->id);

        // Then: 권한이 없어야 함
        $this->assertFalse($result);
    }

    public function test_can_update_returns_false_for_nonexistent_user(): void
    {
        // Given: 존재하지 않는 사용자 ID와 템플릿
        $nonExistentUserId = 99999;
        $template = NotificationTemplate::factory()->create();

        // When: 수정 권한 확인
        $result = $this->service->canUpdate($nonExistentUserId, $template->id);

        // Then: 권한이 없어야 함
        $this->assertFalse($result);
    }

    public function test_can_update_returns_false_for_nonexistent_template(): void
    {
        // Given: 사용자와 존재하지 않는 템플릿 ID
        $user = User::factory()->create();
        $nonExistentTemplateId = 99999;

        // When: 수정 권한 확인
        $result = $this->service->canUpdate($user->id, $nonExistentTemplateId);

        // Then: 권한이 없어야 함
        $this->assertFalse($result);
    }

    /**
     * 템플릿 삭제 권한 테스트
     */
    public function test_can_delete_returns_true_for_admin(): void
    {
        // Given: 관리자 사용자와 템플릿
        $admin = User::factory()->create([
            'role' => User::ROLE_ADMIN,
        ]);
        $template = NotificationTemplate::factory()->create();

        // When: 삭제 권한 확인
        $result = $this->service->canDelete($admin->id, $template->id);

        // Then: 권한이 있어야 함
        $this->assertTrue($result);
    }

    public function test_can_delete_returns_true_for_template_creator(): void
    {
        // Given: 템플릿 생성자
        $creator = User::factory()->create([
            'role' => User::ROLE_EMPLOYEE,
        ]);
        $template = NotificationTemplate::factory()->create([
            'created_by' => $creator->id,
        ]);

        // When: 삭제 권한 확인
        $result = $this->service->canDelete($creator->id, $template->id);

        // Then: 권한이 있어야 함
        $this->assertTrue($result);
    }

    public function test_can_delete_returns_false_for_other_user(): void
    {
        // Given: 다른 사용자와 템플릿
        $creator = User::factory()->create();
        $otherUser = User::factory()->create([
            'role' => User::ROLE_EMPLOYEE,
        ]);
        $template = NotificationTemplate::factory()->create([
            'created_by' => $creator->id,
        ]);

        // When: 삭제 권한 확인
        $result = $this->service->canDelete($otherUser->id, $template->id);

        // Then: 권한이 없어야 함
        $this->assertFalse($result);
    }

    /**
     * 권한 검증 메서드 테스트
     */
    public function test_validate_permission_allows_read_for_all_users(): void
    {
        // Given: 일반 사용자와 다른 사용자의 템플릿
        $creator = User::factory()->create();
        $otherUser = User::factory()->create([
            'role' => User::ROLE_EMPLOYEE,
        ]);
        $template = NotificationTemplate::factory()->create([
            'created_by' => $creator->id,
        ]);

        // When & Then: 읽기 권한은 예외가 발생하지 않아야 함
        $this->service->validatePermission($otherUser->id, $template, 'read');
        $this->assertTrue(true); // 예외가 발생하지 않으면 성공
    }

    public function test_validate_permission_allows_update_for_admin(): void
    {
        // Given: 관리자와 템플릿
        $admin = User::factory()->create([
            'role' => User::ROLE_ADMIN,
        ]);
        $template = NotificationTemplate::factory()->create();

        // When & Then: 관리자는 수정 권한이 있어야 함
        $this->service->validatePermission($admin->id, $template, 'update');
        $this->assertTrue(true); // 예외가 발생하지 않으면 성공
    }

    public function test_validate_permission_allows_update_for_creator(): void
    {
        // Given: 템플릿 생성자
        $creator = User::factory()->create([
            'role' => User::ROLE_EMPLOYEE,
        ]);
        $template = NotificationTemplate::factory()->create([
            'created_by' => $creator->id,
        ]);

        // When & Then: 생성자는 수정 권한이 있어야 함
        $this->service->validatePermission($creator->id, $template, 'update');
        $this->assertTrue(true); // 예외가 발생하지 않으면 성공
    }

    public function test_validate_permission_throws_exception_for_unauthorized_update(): void
    {
        // Given: 다른 사용자와 템플릿
        $creator = User::factory()->create();
        $otherUser = User::factory()->create([
            'role' => User::ROLE_EMPLOYEE,
        ]);
        $template = NotificationTemplate::factory()->create([
            'created_by' => $creator->id,
        ]);

        // When & Then: 권한이 없는 사용자는 예외가 발생해야 함
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('템플릿을 update할 권한이 없습니다.');

        $this->service->validatePermission($otherUser->id, $template, 'update');
    }

    public function test_validate_permission_throws_exception_for_nonexistent_user(): void
    {
        // Given: 존재하지 않는 사용자 ID와 템플릿
        $nonExistentUserId = 99999;
        $template = NotificationTemplate::factory()->create();

        // When & Then: 존재하지 않는 사용자는 예외가 발생해야 함
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('사용자를 찾을 수 없습니다.');

        $this->service->validatePermission($nonExistentUserId, $template, 'update');
    }

    /**
     * 관리자 권한 확인 테스트
     */
    public function test_is_admin_returns_true_for_super_admin(): void
    {
        // Given: 슈퍼 관리자
        $superAdmin = User::factory()->create([
            'role' => User::ROLE_SUPER_ADMIN,
        ]);

        // When: 관리자 권한 확인
        $result = $this->service->isAdmin($superAdmin);

        // Then: 관리자여야 함
        $this->assertTrue($result);
    }

    public function test_is_admin_returns_true_for_admin(): void
    {
        // Given: 관리자
        $admin = User::factory()->create([
            'role' => User::ROLE_ADMIN,
        ]);

        // When: 관리자 권한 확인
        $result = $this->service->isAdmin($admin);

        // Then: 관리자여야 함
        $this->assertTrue($result);
    }

    public function test_is_admin_returns_false_for_regular_user(): void
    {
        // Given: 일반 사용자
        $user = User::factory()->create([
            'role' => User::ROLE_EMPLOYEE,
        ]);

        // When: 관리자 권한 확인
        $result = $this->service->isAdmin($user);

        // Then: 관리자가 아니어야 함
        $this->assertFalse($result);
    }

    /**
     * hasPermissionForTemplate 메서드 테스트
     */
    public function test_has_permission_for_template_returns_true_for_read(): void
    {
        // Given: 사용자와 템플릿
        $user = User::factory()->create();
        $template = NotificationTemplate::factory()->create();

        // When: 읽기 권한 확인
        $result = $this->service->hasPermissionForTemplate($user->id, $template->id, 'read');

        // Then: 권한이 있어야 함
        $this->assertTrue($result);
    }

    public function test_has_permission_for_template_returns_true_for_creator_update(): void
    {
        // Given: 템플릿 생성자
        $creator = User::factory()->create();
        $template = NotificationTemplate::factory()->create([
            'created_by' => $creator->id,
        ]);

        // When: 수정 권한 확인
        $result = $this->service->hasPermissionForTemplate($creator->id, $template->id, 'update');

        // Then: 권한이 있어야 함
        $this->assertTrue($result);
    }

    public function test_has_permission_for_template_returns_false_for_other_user_update(): void
    {
        // Given: 다른 사용자와 템플릿
        $creator = User::factory()->create();
        $otherUser = User::factory()->create([
            'role' => User::ROLE_EMPLOYEE,
        ]);
        $template = NotificationTemplate::factory()->create([
            'created_by' => $creator->id,
        ]);

        // When: 수정 권한 확인
        $result = $this->service->hasPermissionForTemplate($otherUser->id, $template->id, 'update');

        // Then: 권한이 없어야 함
        $this->assertFalse($result);
    }

    public function test_has_permission_for_template_returns_false_for_nonexistent_template(): void
    {
        // Given: 사용자와 존재하지 않는 템플릿 ID
        $user = User::factory()->create();
        $nonExistentTemplateId = 99999;

        // When: 권한 확인
        $result = $this->service->hasPermissionForTemplate($user->id, $nonExistentTemplateId, 'read');

        // Then: 권한이 없어야 함
        $this->assertFalse($result);
    }

    public function test_has_permission_for_template_returns_false_for_unknown_action(): void
    {
        // Given: 사용자와 템플릿, 알 수 없는 작업
        $user = User::factory()->create();
        $template = NotificationTemplate::factory()->create();

        // When: 알 수 없는 작업에 대한 권한 확인
        $result = $this->service->hasPermissionForTemplate($user->id, $template->id, 'unknown_action');

        // Then: 권한이 없어야 함
        $this->assertFalse($result);
    }
}
