<?php

namespace Tests\Unit\Services;

use App\Models\Product;
use App\Models\RepairCost;
use App\Models\RepairCostCategory;
use App\Models\RepairCostPolicy;
use App\Models\RepairCostRange;
use App\Models\RepairCostType;
use App\Models\RepairCostTypeProcessMapping;
use App\Services\RepairCostService;
use App\Services\TelegramService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RepairCostServiceTest extends TestCase
{
    use RefreshDatabase;

    protected RepairCostService $repairCostService;

    protected TelegramService $telegramService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->telegramService = $this->createMock(TelegramService::class);
        $this->repairCostService = new RepairCostService($this->telegramService);
    }

    /** @test */
    public function 기본_정책이_활성화되어_있는지_확인할_수_있다()
    {
        // 기본 정책이 없는 경우
        $this->assertFalse($this->repairCostService->hasDefaultPolicy());

        // 기본 정책 생성
        $defaultPolicy = RepairCostPolicy::factory()->create([
            'code' => RepairCostPolicy::POLICY_DEFAULT,
            'name' => '기본 수리비 정책',
            'is_active' => true,
        ]);

        $this->assertTrue($this->repairCostService->hasDefaultPolicy());
    }

    /** @test */
    public function 기본_정책_정보를_조회할_수_있다()
    {
        // 기본 정책 생성
        $defaultPolicy = RepairCostPolicy::factory()->create([
            'code' => RepairCostPolicy::POLICY_DEFAULT,
            'name' => '기본 수리비 정책',
            'is_active' => true,
        ]);

        $defaultCategory = RepairCostCategory::factory()->create([
            'repair_cost_policy_id' => $defaultPolicy->id,
            'name' => '기본 카테고리',
            'is_active' => true,
        ]);

        $defaultRange = RepairCostRange::factory()->create([
            'repair_cost_category_id' => $defaultCategory->id,
            'range_name' => '기본 범위',
            'is_active' => true,
        ]);

        $policyInfo = $this->repairCostService->getDefaultPolicyInfo();

        $this->assertNotNull($policyInfo);
        $this->assertEquals($defaultPolicy->id, $policyInfo['policy']['id']);
        $this->assertEquals($defaultCategory->id, $policyInfo['category']['id']);
        $this->assertEquals($defaultRange->id, $policyInfo['range']['id']);
    }

    /** @test */
    public function 기본_정책으로_수리비를_계산할_수_있다()
    {
        // 기본 정책 설정
        $defaultPolicy = RepairCostPolicy::factory()->create([
            'code' => RepairCostPolicy::POLICY_DEFAULT,
            'name' => '기본 수리비 정책',
            'is_active' => true,
        ]);

        $defaultCategory = RepairCostCategory::factory()->create([
            'repair_cost_policy_id' => $defaultPolicy->id,
            'name' => '기본 카테고리',
            'is_active' => true,
        ]);

        $defaultRange = RepairCostRange::factory()->create([
            'repair_cost_category_id' => $defaultCategory->id,
            'range_name' => '기본 범위',
            'is_active' => true,
        ]);

        $repairType = RepairCostType::factory()->create([
            'name' => '일반 수리',
            'is_active' => true,
        ]);

        $repairCost = RepairCost::factory()->create([
            'repair_cost_range_id' => $defaultRange->id,
            'repair_cost_type_id' => $repairType->id,
            'amount' => 30000,
        ]);

        $processMapping = RepairCostTypeProcessMapping::factory()->create([
            'repair_process_id' => 1,
            'repair_cost_type_id' => $repairType->id,
            'is_active' => true,
        ]);

        $product = Product::factory()->create([
            'name' => '테스트 제품',
        ]);

        $result = $this->repairCostService->calculateWithDefaultPolicy($product, 1);

        $this->assertNotNull($result);
        $this->assertEquals(30000, $result['amount']);
        $this->assertTrue($result['details']['is_default_policy']);
    }

    /** @test */
    public function 수리비_계산_실패_시_기본_정책을_사용한다()
    {
        // 기본 정책 설정
        $defaultPolicy = RepairCostPolicy::factory()->create([
            'code' => RepairCostPolicy::POLICY_DEFAULT,
            'name' => '기본 수리비 정책',
            'is_active' => true,
        ]);

        $defaultCategory = RepairCostCategory::factory()->create([
            'repair_cost_policy_id' => $defaultPolicy->id,
            'name' => '기본 카테고리',
            'is_active' => true,
        ]);

        $defaultRange = RepairCostRange::factory()->create([
            'repair_cost_category_id' => $defaultCategory->id,
            'range_name' => '기본 범위',
            'is_active' => true,
        ]);

        $repairType = RepairCostType::factory()->create([
            'name' => '일반 수리',
            'is_active' => true,
        ]);

        $repairCost = RepairCost::factory()->create([
            'repair_cost_range_id' => $defaultRange->id,
            'repair_cost_type_id' => $repairType->id,
            'amount' => 25000,
        ]);

        $processMapping = RepairCostTypeProcessMapping::factory()->create([
            'repair_process_id' => 1,
            'repair_cost_type_id' => $repairType->id,
            'is_active' => true,
        ]);

        // 매핑되지 않은 제품 생성
        $product = Product::factory()->create([
            'name' => '매핑되지 않은 제품',
            'cate4_id' => 99999, // 존재하지 않는 카테고리
            'cate5_id' => null,
        ]);

        $result = $this->repairCostService->calculateRepairCost($product, 1);

        $this->assertEquals(25000, $result['amount']);
        $this->assertStringContainsString('기본값 적용', $result['basis']);
        $this->assertTrue($result['details']['is_default_policy']);
    }

    /** @test */
    public function 기본_정책이_없는_경우_최종_폴백을_사용한다()
    {
        // 기본 정책이 없는 상태에서 매핑되지 않은 제품
        $product = Product::factory()->create([
            'name' => '매핑되지 않은 제품',
            'cate4_id' => 99999,
            'cate5_id' => null,
        ]);

        $result = $this->repairCostService->calculateRepairCost($product, 1);

        $this->assertEquals(RepairCostService::DEFAULT_REPAIR_COST, $result['amount']);
        $this->assertStringContainsString('최종 기본값 적용', $result['basis']);
        $this->assertTrue($result['details']['is_fallback']);
    }

    /** @test */
    public function 기본_카테고리_조회_시_default_정책을_우선적으로_사용한다()
    {
        // 일반 정책 생성
        $generalPolicy = RepairCostPolicy::factory()->create([
            'code' => RepairCostPolicy::POLICY_GENERAL_PRICE,
            'name' => '일반 수리비 정책',
            'is_active' => true,
        ]);

        $generalCategory = RepairCostCategory::factory()->create([
            'repair_cost_policy_id' => $generalPolicy->id,
            'name' => '일반 카테고리',
            'is_active' => true,
        ]);

        // 기본 정책 생성
        $defaultPolicy = RepairCostPolicy::factory()->create([
            'code' => RepairCostPolicy::POLICY_DEFAULT,
            'name' => '기본 수리비 정책',
            'is_active' => true,
        ]);

        $defaultCategory = RepairCostCategory::factory()->create([
            'repair_cost_policy_id' => $defaultPolicy->id,
            'name' => '기본 카테고리',
            'is_active' => true,
        ]);

        // 일반 정책으로 요청했지만 default 정책이 우선적으로 반환되어야 함
        $result = $this->repairCostService->getDefaultCategory(RepairCostPolicy::POLICY_GENERAL_PRICE);

        $this->assertNotNull($result);
        $this->assertEquals($defaultCategory->id, $result->id);
        $this->assertEquals($defaultPolicy->id, $result->repair_cost_policy_id);
    }
}
