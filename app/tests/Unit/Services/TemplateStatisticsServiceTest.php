<?php

namespace Tests\Unit\Services;

use App\Exceptions\BusinessException;
use App\Exceptions\TemplateException;
use App\Exceptions\ValidationException;
use App\Models\NotificationTemplate;
use App\Repositories\Interfaces\NotificationTemplateRepositoryInterface;
use App\Services\TemplateLoggingService;
use App\Services\TemplateStatisticsService;
use Illuminate\Database\Eloquent\Collection;
use Mockery;
use Tests\TestCase;

/**
 * TemplateStatisticsService 단위 테스트
 */
class TemplateStatisticsServiceTest extends TestCase
{
    private TemplateStatisticsService $service;

    private NotificationTemplateRepositoryInterface $mockRepository;

    private TemplateLoggingService $mockLoggingService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockRepository = Mockery::mock(NotificationTemplateRepositoryInterface::class);
        $this->mockLoggingService = Mockery::mock(TemplateLoggingService::class);

        $this->service = new TemplateStatisticsService(
            $this->mockRepository,
            $this->mockLoggingService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 템플릿 사용 횟수 증가 성공 테스트
     */
    public function test_increment_usage_success(): void
    {
        // Given
        $templateId = 1;
        $template = new NotificationTemplate([
            'id' => $templateId,
            'name' => 'Test Template',
            'usage_count' => 5,
        ]);

        $this->mockRepository
            ->shouldReceive('findById')
            ->with($templateId)
            ->once()
            ->andReturn($template);

        $this->mockRepository
            ->shouldReceive('incrementUsage')
            ->with($templateId)
            ->once()
            ->andReturn(true);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->incrementUsage($templateId);

        // Then
        $this->assertTrue($result);
    }

    /**
     * 존재하지 않는 템플릿 사용 횟수 증가 실패 테스트
     */
    public function test_increment_usage_template_not_found(): void
    {
        // Given
        $templateId = 999;

        $this->mockRepository
            ->shouldReceive('findById')
            ->with($templateId)
            ->once()
            ->andReturn(null);

        // When & Then
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('템플릿을 찾을 수 없습니다.');

        $this->service->incrementUsage($templateId);
    }

    /**
     * 사용 횟수 증가 실패 테스트
     */
    public function test_increment_usage_repository_failure(): void
    {
        // Given
        $templateId = 1;
        $template = new NotificationTemplate([
            'id' => $templateId,
            'name' => 'Test Template',
            'usage_count' => 5,
        ]);

        $this->mockRepository
            ->shouldReceive('findById')
            ->with($templateId)
            ->once()
            ->andReturn($template);

        $this->mockRepository
            ->shouldReceive('incrementUsage')
            ->with($templateId)
            ->once()
            ->andReturn(false);

        // When & Then
        $this->expectException(TemplateException::class);

        $this->service->incrementUsage($templateId);
    }

    /**
     * 사용 통계 조회 성공 테스트
     */
    public function test_get_usage_statistics_success(): void
    {
        // Given
        $expectedStats = [
            'total_templates' => 10,
            'total_usage' => 150,
            'average_usage' => 15.0,
        ];

        $this->mockRepository
            ->shouldReceive('getUsageStatistics')
            ->once()
            ->andReturn($expectedStats);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getUsageStatistics();

        // Then
        $this->assertEquals($expectedStats, $result);
    }

    /**
     * 사용 통계 조회 실패 테스트
     */
    public function test_get_usage_statistics_failure(): void
    {
        // Given
        $this->mockRepository
            ->shouldReceive('getUsageStatistics')
            ->once()
            ->andThrow(new \Exception('Database error'));

        $this->mockLoggingService
            ->shouldReceive('logError')
            ->once();

        // When & Then
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('템플릿 사용 통계 조회 중 오류가 발생했습니다.');

        $this->service->getUsageStatistics();
    }

    /**
     * 우선순위별 개수 조회 성공 테스트
     */
    public function test_get_count_by_priority_success(): void
    {
        // Given
        $expectedCounts = [
            'high' => 5,
            'medium' => 8,
            'low' => 2,
        ];

        $this->mockRepository
            ->shouldReceive('getCountByPriority')
            ->once()
            ->andReturn($expectedCounts);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getCountByPriority();

        // Then
        $this->assertEquals($expectedCounts, $result);
    }

    /**
     * 사용 분포 조회 성공 테스트
     */
    public function test_get_usage_distribution_success(): void
    {
        // Given
        $expectedDistribution = [
            '0-10' => 5,
            '11-50' => 3,
            '51-100' => 2,
        ];

        $this->mockRepository
            ->shouldReceive('getUsageDistribution')
            ->once()
            ->andReturn($expectedDistribution);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getUsageDistribution();

        // Then
        $this->assertEquals($expectedDistribution, $result);
    }

    /**
     * 사용되지 않은 템플릿 조회 성공 테스트
     */
    public function test_get_unused_templates_success(): void
    {
        // Given
        $templates = new Collection([
            new NotificationTemplate(['id' => 1, 'name' => 'Unused 1', 'usage_count' => 0]),
            new NotificationTemplate(['id' => 2, 'name' => 'Unused 2', 'usage_count' => 0]),
        ]);

        $this->mockRepository
            ->shouldReceive('getUnusedTemplates')
            ->once()
            ->andReturn($templates);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getUnusedTemplates();

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /**
     * 사용 횟수 범위별 템플릿 조회 성공 테스트
     */
    public function test_get_templates_by_usage_range_success(): void
    {
        // Given
        $minUsage = 5;
        $maxUsage = 20;
        $templates = new Collection([
            new NotificationTemplate(['id' => 1, 'name' => 'Template 1', 'usage_count' => 10]),
            new NotificationTemplate(['id' => 2, 'name' => 'Template 2', 'usage_count' => 15]),
        ]);

        $this->mockRepository
            ->shouldReceive('getTemplatesByUsageRange')
            ->with($minUsage, $maxUsage)
            ->once()
            ->andReturn($templates);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getTemplatesByUsageRange($minUsage, $maxUsage);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /**
     * 잘못된 사용 횟수 범위 검증 테스트
     */
    public function test_get_templates_by_usage_range_invalid_range(): void
    {
        // Given
        $minUsage = -1;
        $maxUsage = 10;

        // When & Then
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('최소 사용 횟수는 0 이상이어야 합니다.');

        $this->service->getTemplatesByUsageRange($minUsage, $maxUsage);
    }

    /**
     * 최소값이 최대값보다 큰 경우 검증 테스트
     */
    public function test_get_templates_by_usage_range_min_greater_than_max(): void
    {
        // Given
        $minUsage = 20;
        $maxUsage = 10;

        // When & Then
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('최소 사용 횟수는 최대 사용 횟수보다 클 수 없습니다.');

        $this->service->getTemplatesByUsageRange($minUsage, $maxUsage);
    }

    /**
     * 인기 템플릿 조회 성공 테스트
     */
    public function test_get_popular_templates_success(): void
    {
        // Given
        $limit = 5;
        $templates = new Collection([
            new NotificationTemplate(['id' => 1, 'name' => 'Popular 1', 'usage_count' => 100]),
            new NotificationTemplate(['id' => 2, 'name' => 'Popular 2', 'usage_count' => 80]),
        ]);

        $this->mockRepository
            ->shouldReceive('getPopularTemplates')
            ->with($limit)
            ->once()
            ->andReturn($templates);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getPopularTemplates($limit);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /**
     * 잘못된 limit 값으로 인기 템플릿 조회 실패 테스트
     */
    public function test_get_popular_templates_invalid_limit(): void
    {
        // Given
        $limit = 0;

        // When & Then
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('조회 개수는 1~100 사이여야 합니다.');

        $this->service->getPopularTemplates($limit);
    }

    /**
     * 최근 템플릿 조회 성공 테스트
     */
    public function test_get_recent_templates_success(): void
    {
        // Given
        $limit = 5;
        $templates = new Collection([
            new NotificationTemplate(['id' => 1, 'name' => 'Recent 1']),
            new NotificationTemplate(['id' => 2, 'name' => 'Recent 2']),
        ]);

        $this->mockRepository
            ->shouldReceive('getRecentTemplates')
            ->with($limit)
            ->once()
            ->andReturn($templates);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getRecentTemplates($limit);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /**
     * 통계 요약 정보 조회 성공 테스트
     */
    public function test_get_statistics_summary_success(): void
    {
        // Given
        $basicStats = ['total_templates' => 10, 'total_usage' => 150];
        $priorityCounts = ['high' => 5, 'medium' => 3, 'low' => 2];
        $usageDistribution = ['0-10' => 5, '11-50' => 3, '51-100' => 2];
        $unusedTemplates = new Collection([
            new NotificationTemplate(['id' => 1, 'usage_count' => 0]),
        ]);

        $this->mockRepository
            ->shouldReceive('getUsageStatistics')
            ->once()
            ->andReturn($basicStats);

        $this->mockRepository
            ->shouldReceive('getCountByPriority')
            ->once()
            ->andReturn($priorityCounts);

        $this->mockRepository
            ->shouldReceive('getUsageDistribution')
            ->once()
            ->andReturn($usageDistribution);

        $this->mockRepository
            ->shouldReceive('getUnusedTemplates')
            ->once()
            ->andReturn($unusedTemplates);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->times(5); // 각 메서드 호출 + 요약 정보 로그

        // When
        $result = $this->service->getStatisticsSummary();

        // Then
        $this->assertArrayHasKey('basic_statistics', $result);
        $this->assertArrayHasKey('priority_distribution', $result);
        $this->assertArrayHasKey('usage_distribution', $result);
        $this->assertArrayHasKey('unused_templates_count', $result);
        $this->assertArrayHasKey('generated_at', $result);

        $this->assertEquals($basicStats, $result['basic_statistics']);
        $this->assertEquals($priorityCounts, $result['priority_distribution']);
        $this->assertEquals($usageDistribution, $result['usage_distribution']);
        $this->assertEquals(1, $result['unused_templates_count']);
    }

    /**
     * 사용 트렌드 분석 성공 테스트
     */
    public function test_get_usage_trends_success(): void
    {
        // Given
        $days = 30;
        $currentStats = ['total_templates' => 10, 'total_usage' => 150];

        $popularTemplates = new Collection([
            (object) [
                'id' => 1,
                'name' => 'Popular Template',
                'usage_count' => 100,
                'priority' => 'high',
            ],
        ]);

        $recentTemplates = new Collection([
            (object) [
                'id' => 2,
                'name' => 'Recent Template',
                'usage_count' => 5,
                'created_at' => now(),
            ],
        ]);

        $this->mockRepository
            ->shouldReceive('getUsageStatistics')
            ->once()
            ->andReturn($currentStats);

        $this->mockRepository
            ->shouldReceive('getPopularTemplates')
            ->with(5)
            ->once()
            ->andReturn($popularTemplates);

        $this->mockRepository
            ->shouldReceive('getRecentTemplates')
            ->with(5)
            ->once()
            ->andReturn($recentTemplates);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->times(4); // 각 메서드 호출 + 트렌드 분석 로그

        // When
        $result = $this->service->getUsageTrends($days);

        // Then
        $this->assertArrayHasKey('analysis_period_days', $result);
        $this->assertArrayHasKey('current_statistics', $result);
        $this->assertArrayHasKey('top_popular_templates', $result);
        $this->assertArrayHasKey('recent_templates', $result);
        $this->assertArrayHasKey('generated_at', $result);

        $this->assertEquals($days, $result['analysis_period_days']);
        $this->assertEquals($currentStats, $result['current_statistics']);
        $this->assertCount(1, $result['top_popular_templates']);
        $this->assertCount(1, $result['recent_templates']);
    }

    /**
     * 잘못된 분석 기간으로 사용 트렌드 분석 실패 테스트
     */
    public function test_get_usage_trends_invalid_days(): void
    {
        // Given
        $days = 0;

        // When & Then
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('분석 기간은 1~365일 사이여야 합니다.');

        $this->service->getUsageTrends($days);
    }
}
