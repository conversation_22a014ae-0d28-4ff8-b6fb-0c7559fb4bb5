<?php

namespace Tests\Unit\Services;

use App\Exceptions\BusinessException;
use App\Exceptions\TemplateException;
use App\Exceptions\ValidationException;
use App\Models\NotificationTemplate;
use App\Services\TemplateCrudService;
use App\Services\TemplatePermissionService;
use App\Services\TemplateSearchService;
use App\Services\TemplateService;
use App\Services\TemplateStatisticsService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Pagination\LengthAwarePaginator as ConcretePaginator;
use Mockery;
use Tests\TestCase;

/**
 * TemplateService 단위 테스트
 */
class TemplateServiceTest extends TestCase
{
    use RefreshDatabase;

    protected TemplateService $templateService;

    protected TemplateCrudService $mockCrudService;

    protected TemplateSearchService $mockSearchService;

    protected TemplateStatisticsService $mockStatisticsService;

    protected TemplatePermissionService $mockPermissionService;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock 서비스들 생성
        $this->mockCrudService = Mockery::mock(TemplateCrudService::class);
        $this->mockSearchService = Mockery::mock(TemplateSearchService::class);
        $this->mockStatisticsService = Mockery::mock(TemplateStatisticsService::class);
        $this->mockPermissionService = Mockery::mock(TemplatePermissionService::class);

        // TemplateService 인스턴스 생성
        $this->templateService = new TemplateService(
            $this->mockCrudService,
            $this->mockSearchService,
            $this->mockStatisticsService,
            $this->mockPermissionService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 서비스 인스턴스 생성 테스트
     */
    public function test_서비스_인스턴스_생성_성공(): void
    {
        $this->assertInstanceOf(TemplateService::class, $this->templateService);
    }

    /**
     * 템플릿 생성 성공 테스트
     */
    public function test_템플릿_생성_성공(): void
    {
        // Given
        $userId = 1;
        $templateData = [
            'name' => '테스트 템플릿',
            'title' => '테스트 제목',
            'content' => '테스트 내용',
        ];
        $mockTemplate = new NotificationTemplate($templateData);
        $mockTemplate->id = 1;

        $this->mockCrudService
            ->shouldReceive('create')
            ->with($userId, $templateData)
            ->once()
            ->andReturn($mockTemplate);

        // When
        $result = $this->templateService->createTemplate($userId, $templateData);

        // Then
        $this->assertInstanceOf(NotificationTemplate::class, $result);
        $this->assertEquals(1, $result->id);
    }

    /**
     * 템플릿 목록 조회 성공 테스트
     */
    public function test_템플릿_목록_조회_성공(): void
    {
        // Given
        $userId = 1;
        $filters = ['search' => 'test'];
        $mockPaginator = new ConcretePaginator(
            collect([new NotificationTemplate]),
            1,
            20,
            1
        );

        $this->mockSearchService
            ->shouldReceive('getTemplates')
            ->with($userId, 1, $filters)
            ->once()
            ->andReturn($mockPaginator);

        // When
        $result = $this->templateService->getTemplates($userId, $filters);

        // Then
        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
    }

    /**
     * 템플릿 조회 성공 테스트
     */
    public function test_템플릿_조회_성공(): void
    {
        // Given
        $templateId = 1;
        $userId = 1;
        $mockTemplate = new NotificationTemplate([
            'name' => '테스트 템플릿',
            'title' => '테스트 제목',
            'content' => '테스트 내용',
            'priority' => 'normal',
        ]);
        $mockTemplate->id = $templateId;

        $this->mockCrudService
            ->shouldReceive('findById')
            ->with($templateId)
            ->once()
            ->andReturn($mockTemplate);

        // When
        $result = $this->templateService->getTemplateById($userId, $templateId);

        // Then
        $this->assertInstanceOf(NotificationTemplate::class, $result);
        $this->assertEquals($templateId, $result->id);
        $this->assertEquals('테스트 템플릿', $result->name);
    }

    /**
     * 존재하지 않는 템플릿 조회 시 예외 발생 테스트
     */
    public function test_존재하지_않는_템플릿_조회_시_예외_발생(): void
    {
        // Given
        $templateId = 999;
        $userId = 1;

        $this->mockCrudService
            ->shouldReceive('findById')
            ->with($templateId)
            ->once()
            ->andThrow(new TemplateException('템플릿을 찾을 수 없습니다.'));

        // When & Then
        $this->expectException(TemplateException::class);
        $this->templateService->getTemplateById($userId, $templateId);
    }

    /**
     * 템플릿 수정 성공 테스트
     */
    public function test_템플릿_수정_성공(): void
    {
        // Given
        $userId = 1;
        $templateId = 1;
        $templateData = ['name' => '수정된 템플릿'];
        $updatedTemplate = new NotificationTemplate($templateData);
        $updatedTemplate->id = $templateId;

        $this->mockCrudService
            ->shouldReceive('update')
            ->with($userId, $templateId, $templateData)
            ->once()
            ->andReturn($updatedTemplate);

        // When
        $result = $this->templateService->updateTemplate($userId, $templateId, $templateData);

        // Then
        $this->assertInstanceOf(NotificationTemplate::class, $result);
        $this->assertEquals($templateId, $result->id);
    }

    /**
     * 템플릿 삭제 성공 테스트
     */
    public function test_템플릿_삭제_성공(): void
    {
        // Given
        $userId = 1;
        $templateId = 1;

        $this->mockCrudService
            ->shouldReceive('delete')
            ->with($userId, $templateId)
            ->once()
            ->andReturn(true);

        // When
        $result = $this->templateService->deleteTemplate($userId, $templateId);

        // Then
        $this->assertTrue($result);
    }

    /**
     * 전체 템플릿 목록 조회 테스트
     */
    public function test_전체_템플릿_목록_조회_성공(): void
    {
        // Given
        $userId = 1;
        $template1 = new NotificationTemplate(['name' => '템플릿1']);
        $template1->id = 1;
        $template2 = new NotificationTemplate(['name' => '템플릿2']);
        $template2->id = 2;

        $mockCollection = new Collection([$template1, $template2]);

        $this->mockSearchService
            ->shouldReceive('getAllTemplates')
            ->with($userId)
            ->once()
            ->andReturn($mockCollection);

        // When
        $result = $this->templateService->getAllTemplates($userId);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /**
     * 사용 횟수 증가 성공 테스트
     */
    public function test_사용_횟수_증가_성공(): void
    {
        // Given
        $templateId = 1;
        $userId = 1;
        $mockTemplate = new NotificationTemplate(['name' => '테스트 템플릿']);
        $mockTemplate->id = $templateId;
        $mockStatistics = ['total_usage' => 100];

        $this->mockPermissionService
            ->shouldReceive('canUse')
            ->with($userId, $templateId)
            ->once()
            ->andReturn(true);

        $this->mockStatisticsService
            ->shouldReceive('incrementUsage')
            ->with($templateId)
            ->once()
            ->andReturn(true);

        $this->mockCrudService
            ->shouldReceive('findById')
            ->with($templateId)
            ->once()
            ->andReturn($mockTemplate);

        $this->mockStatisticsService
            ->shouldReceive('getUsageStatistics')
            ->once()
            ->andReturn($mockStatistics);

        // When
        $result = $this->templateService->incrementUsage($userId, $templateId);

        // Then
        $this->assertIsArray($result);
        $this->assertArrayHasKey('template', $result);
        $this->assertArrayHasKey('statistics', $result);
        $this->assertEquals($mockTemplate, $result['template']);
        $this->assertEquals($mockStatistics, $result['statistics']);
    }

    /**
     * 사용 횟수 증가 실패 테스트
     */
    public function test_사용_횟수_증가_실패_시_예외_발생(): void
    {
        // Given
        $templateId = 1;
        $userId = 1;

        $this->mockPermissionService
            ->shouldReceive('canUse')
            ->with($userId, $templateId)
            ->once()
            ->andReturn(true);

        $this->mockStatisticsService
            ->shouldReceive('incrementUsage')
            ->with($templateId)
            ->once()
            ->andReturn(false);

        // When & Then
        $this->expectException(BusinessException::class);
        $this->templateService->incrementUsage($userId, $templateId);
    }

    /**
     * 템플릿 검색 성공 테스트
     */
    public function test_템플릿_검색_성공(): void
    {
        // Given
        $userId = 1;
        $search = '테스트';
        $filters = ['priority' => 'high'];

        $template1 = new NotificationTemplate(['name' => '테스트 템플릿1']);
        $template1->id = 1;
        $template2 = new NotificationTemplate(['name' => '테스트 템플릿2']);
        $template2->id = 2;

        $mockCollection = new Collection([$template1, $template2]);

        $this->mockSearchService
            ->shouldReceive('search')
            ->with($userId, $search, $filters)
            ->once()
            ->andReturn($mockCollection);

        // When
        $result = $this->templateService->searchTemplates($userId, $search, $filters);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /**
     * 우선순위별 템플릿 조회 성공 테스트
     */
    public function test_우선순위별_템플릿_조회_성공(): void
    {
        // Given
        $userId = 1;
        $priority = 'urgent';

        $template1 = new NotificationTemplate(['name' => '긴급 템플릿1', 'priority' => 'urgent']);
        $template1->id = 1;
        $template2 = new NotificationTemplate(['name' => '긴급 템플릿2', 'priority' => 'urgent']);
        $template2->id = 2;

        $mockCollection = new Collection([$template1, $template2]);

        $this->mockSearchService
            ->shouldReceive('getByPriority')
            ->with($userId, $priority)
            ->once()
            ->andReturn($mockCollection);

        // When
        $result = $this->templateService->getTemplatesByPriority($userId, $priority);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /**
     * 사용 횟수 기준 정렬된 템플릿 조회 성공 테스트
     */
    public function test_사용_횟수_기준_정렬된_템플릿_조회_성공(): void
    {
        // Given
        $userId = 1;
        $direction = 'desc';

        $template1 = new NotificationTemplate(['name' => '인기 템플릿1', 'usage_count' => 100]);
        $template1->id = 1;
        $template2 = new NotificationTemplate(['name' => '인기 템플릿2', 'usage_count' => 50]);
        $template2->id = 2;

        $mockCollection = new Collection([$template1, $template2]);

        $this->mockSearchService
            ->shouldReceive('getOrderedByUsage')
            ->with($userId, $direction)
            ->once()
            ->andReturn($mockCollection);

        // When
        $result = $this->templateService->getTemplatesOrderedByUsage($userId, $direction);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /**
     * 인기 템플릿 조회 성공 테스트
     */
    public function test_인기_템플릿_조회_성공(): void
    {
        // Given
        $userId = 1;
        $limit = 5;

        $template1 = new NotificationTemplate(['name' => '인기 템플릿1', 'usage_count' => 100]);
        $template1->id = 1;
        $template2 = new NotificationTemplate(['name' => '인기 템플릿2', 'usage_count' => 90]);
        $template2->id = 2;

        $mockCollection = new Collection([$template1, $template2]);

        $this->mockSearchService
            ->shouldReceive('getPopular')
            ->with($userId, $limit)
            ->once()
            ->andReturn($mockCollection);

        // When
        $result = $this->templateService->getPopularTemplates($userId, $limit);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /**
     * 최근 템플릿 조회 성공 테스트
     */
    public function test_최근_템플릿_조회_성공(): void
    {
        // Given
        $userId = 1;
        $limit = 5;

        $template1 = new NotificationTemplate(['name' => '최근 템플릿1']);
        $template1->id = 1;
        $template2 = new NotificationTemplate(['name' => '최근 템플릿2']);
        $template2->id = 2;

        $mockCollection = new Collection([$template1, $template2]);

        $this->mockSearchService
            ->shouldReceive('getRecent')
            ->with($userId, $limit)
            ->once()
            ->andReturn($mockCollection);

        // When
        $result = $this->templateService->getRecentTemplates($userId, $limit);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /**
     * 사용되지 않은 템플릿 조회 성공 테스트
     */
    public function test_사용되지_않은_템플릿_조회_성공(): void
    {
        // Given
        $userId = 1;

        $template1 = new NotificationTemplate(['name' => '미사용 템플릿1', 'usage_count' => 0]);
        $template1->id = 1;
        $template2 = new NotificationTemplate(['name' => '미사용 템플릿2', 'usage_count' => 0]);
        $template2->id = 2;

        $mockCollection = new Collection([$template1, $template2]);

        $this->mockSearchService
            ->shouldReceive('getUnused')
            ->with($userId)
            ->once()
            ->andReturn($mockCollection);

        // When
        $result = $this->templateService->getUnusedTemplates($userId);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /**
     * 사용 통계 조회 성공 테스트
     */
    public function test_사용_통계_조회_성공(): void
    {
        // Given
        $userId = 1;
        $mockStatistics = [
            'total_templates' => 10,
            'total_usage' => 150,
            'average_usage' => 15.0,
            'most_used_template' => [
                'id' => 1,
                'name' => '인기 템플릿',
                'usage_count' => 50,
            ],
            'unused_templates_count' => 2,
            'usage_rate' => 80.0,
        ];

        $this->mockStatisticsService
            ->shouldReceive('getUsageStatistics')
            ->once()
            ->andReturn($mockStatistics);

        // When
        $result = $this->templateService->getUsageStatistics($userId);

        // Then
        $this->assertIsArray($result);
        $this->assertEquals(10, $result['total_templates']);
        $this->assertEquals(150, $result['total_usage']);
        $this->assertEquals(15.0, $result['average_usage']);
    }

    /**
     * 사용 횟수 범위별 템플릿 조회 성공 테스트
     */
    public function test_사용_횟수_범위별_템플릿_조회_성공(): void
    {
        // Given
        $userId = 1;
        $minUsage = 10;
        $maxUsage = 50;

        $template1 = new NotificationTemplate(['name' => '중간 사용 템플릿1', 'usage_count' => 25]);
        $template1->id = 1;
        $template2 = new NotificationTemplate(['name' => '중간 사용 템플릿2', 'usage_count' => 35]);
        $template2->id = 2;

        $mockCollection = new Collection([$template1, $template2]);

        $this->mockSearchService
            ->shouldReceive('getByUsageRange')
            ->with($userId, $minUsage, $maxUsage)
            ->once()
            ->andReturn($mockCollection);

        // When
        $result = $this->templateService->getTemplatesByUsageRange($userId, $minUsage, $maxUsage);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /**
     * 사용 횟수 범위 검증 실패 테스트
     */
    public function test_사용_횟수_범위_검증_실패(): void
    {
        // Given
        $userId = 1;
        $minUsage = 50;
        $maxUsage = 10; // 최소값이 최대값보다 큰 경우

        // When & Then
        $this->expectException(ValidationException::class);
        $this->templateService->getTemplatesByUsageRange($userId, $minUsage, $maxUsage);
    }

    /**
     * 템플릿 권한 확인 테스트 (읽기 권한)
     */
    public function test_템플릿_권한_확인_읽기_권한(): void
    {
        // Given
        $templateId = 1;
        $userId = 1;

        $this->mockPermissionService
            ->shouldReceive('canRead')
            ->with($userId, $templateId)
            ->once()
            ->andReturn(true);

        // When
        $result = $this->templateService->hasPermissionForTemplate($userId, $templateId, 'read');

        // Then
        $this->assertTrue($result);
    }

    /**
     * 템플릿 권한 확인 테스트 (수정 권한 없음)
     */
    public function test_템플릿_권한_확인_수정_권한_없음(): void
    {
        // Given
        $templateId = 1;
        $userId = 1;

        $this->mockPermissionService
            ->shouldReceive('canUpdate')
            ->with($userId, $templateId)
            ->once()
            ->andReturn(false);

        // When
        $result = $this->templateService->hasPermissionForTemplate($userId, $templateId, 'update');

        // Then
        $this->assertFalse($result);
    }

    /**
     * 템플릿 권한 확인 테스트 (삭제 권한)
     */
    public function test_템플릿_권한_확인_삭제_권한(): void
    {
        // Given
        $templateId = 1;
        $userId = 1;

        $this->mockPermissionService
            ->shouldReceive('canDelete')
            ->with($userId, $templateId)
            ->once()
            ->andReturn(true);

        // When
        $result = $this->templateService->hasPermissionForTemplate($userId, $templateId, 'delete');

        // Then
        $this->assertTrue($result);
    }

    /**
     * 알 수 없는 액션에 대한 권한 확인 테스트
     */
    public function test_알_수_없는_액션_권한_확인(): void
    {
        // Given
        $templateId = 1;
        $userId = 1;

        // When
        $result = $this->templateService->hasPermissionForTemplate($userId, $templateId, 'unknown_action');

        // Then
        $this->assertFalse($result);
    }
}
