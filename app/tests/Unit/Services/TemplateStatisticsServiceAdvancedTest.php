<?php

namespace Tests\Unit\Services;

use App\Exceptions\BusinessException;
use App\Exceptions\ValidationException;
use App\Models\NotificationTemplate;
use App\Repositories\Interfaces\NotificationTemplateRepositoryInterface;
use App\Services\TemplateLoggingService;
use App\Services\TemplateStatisticsService;
use Illuminate\Database\Eloquent\Collection;
use Mockery;
use Tests\TestCase;

/**
 * TemplateStatisticsService 고급 기능 단위 테스트
 * 기존 테스트에서 누락된 엣지 케이스와 에러 처리를 테스트합니다.
 */
class TemplateStatisticsServiceAdvancedTest extends TestCase
{
    private TemplateStatisticsService $service;

    private NotificationTemplateRepositoryInterface $mockRepository;

    private TemplateLoggingService $mockLoggingService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockRepository = Mockery::mock(NotificationTemplateRepositoryInterface::class);
        $this->mockLoggingService = Mockery::mock(TemplateLoggingService::class);

        $this->service = new TemplateStatisticsService(
            $this->mockRepository,
            $this->mockLoggingService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 동시 사용 횟수 증가 처리 테스트
     */
    public function test_동시_사용_횟수_증가_처리(): void
    {
        // Given
        $templateId = 1;
        $template = new NotificationTemplate([
            'id' => $templateId,
            'name' => 'Concurrent Template',
            'usage_count' => 10,
        ]);

        // 동시 요청 시뮬레이션
        $this->mockRepository
            ->shouldReceive('findById')
            ->times(3)
            ->with($templateId)
            ->andReturn($template);

        $this->mockRepository
            ->shouldReceive('incrementUsage')
            ->times(3)
            ->with($templateId)
            ->andReturn(true);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->times(3);

        // When
        $result1 = $this->service->incrementUsage($templateId);
        $result2 = $this->service->incrementUsage($templateId);
        $result3 = $this->service->incrementUsage($templateId);

        // Then
        $this->assertTrue($result1);
        $this->assertTrue($result2);
        $this->assertTrue($result3);
    }

    /**
     * 대용량 템플릿 통계 처리 테스트
     */
    public function test_대용량_템플릿_통계_처리(): void
    {
        // Given
        $largeStats = [
            'total_templates' => 100000,
            'total_usage' => 5000000,
            'average_usage' => 50.0,
            'max_usage' => 10000,
            'min_usage' => 0,
        ];

        $this->mockRepository
            ->shouldReceive('getUsageStatistics')
            ->once()
            ->andReturn($largeStats);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getUsageStatistics();

        // Then
        $this->assertEquals(100000, $result['total_templates']);
        $this->assertEquals(5000000, $result['total_usage']);
        $this->assertEquals(50.0, $result['average_usage']);
    }

    /**
     * 빈 통계 데이터 처리 테스트
     */
    public function test_빈_통계_데이터_처리(): void
    {
        // Given
        $emptyStats = [
            'total_templates' => 0,
            'total_usage' => 0,
            'average_usage' => 0.0,
        ];

        $this->mockRepository
            ->shouldReceive('getUsageStatistics')
            ->once()
            ->andReturn($emptyStats);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getUsageStatistics();

        // Then
        $this->assertEquals(0, $result['total_templates']);
        $this->assertEquals(0, $result['total_usage']);
        $this->assertEquals(0.0, $result['average_usage']);
    }

    /**
     * 우선순위별 개수 불균형 처리 테스트
     */
    public function test_우선순위별_개수_불균형_처리(): void
    {
        // Given
        $unbalancedCounts = [
            'urgent' => 1,
            'high' => 5,
            'normal' => 1000,
            'low' => 2,
        ];

        $this->mockRepository
            ->shouldReceive('getCountByPriority')
            ->once()
            ->andReturn($unbalancedCounts);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getCountByPriority();

        // Then
        $this->assertEquals(1, $result['urgent']);
        $this->assertEquals(1000, $result['normal']);
        $this->assertEquals(2, $result['low']);
    }

    /**
     * 사용 분포 극값 처리 테스트
     */
    public function test_사용_분포_극값_처리(): void
    {
        // Given
        $extremeDistribution = [
            '0' => 500,        // 사용되지 않은 템플릿이 많음
            '1-10' => 300,
            '11-100' => 150,
            '101-1000' => 45,
            '1001+' => 5,       // 매우 많이 사용된 템플릿은 적음
        ];

        $this->mockRepository
            ->shouldReceive('getUsageDistribution')
            ->once()
            ->andReturn($extremeDistribution);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getUsageDistribution();

        // Then
        $this->assertEquals(500, $result['0']);
        $this->assertEquals(5, $result['1001+']);
    }

    /**
     * 사용되지 않은 템플릿 대량 처리 테스트
     */
    public function test_사용되지_않은_템플릿_대량_처리(): void
    {
        // Given
        $unusedTemplates = new Collection;
        for ($i = 1; $i <= 1000; $i++) {
            $template = new NotificationTemplate([
                'id' => $i,
                'name' => "Unused Template $i",
                'usage_count' => 0,
            ]);
            $unusedTemplates->push($template);
        }

        $this->mockRepository
            ->shouldReceive('getUnusedTemplates')
            ->once()
            ->andReturn($unusedTemplates);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getUnusedTemplates();

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(1000, $result);
    }

    /**
     * 사용 횟수 범위 경계값 테스트
     */
    public function test_사용_횟수_범위_경계값_테스트(): void
    {
        // Given
        $minUsage = 0;
        $maxUsage = 0; // 경계값: 같은 값

        $templates = new Collection([
            new NotificationTemplate(['id' => 1, 'usage_count' => 0]),
            new NotificationTemplate(['id' => 2, 'usage_count' => 0]),
        ]);

        $this->mockRepository
            ->shouldReceive('getTemplatesByUsageRange')
            ->with($minUsage, $maxUsage)
            ->once()
            ->andReturn($templates);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getTemplatesByUsageRange($minUsage, $maxUsage);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    /**
     * 인기 템플릿 제한값 경계 테스트
     */
    public function test_인기_템플릿_제한값_경계_테스트(): void
    {
        // Given
        $limit = 1; // 최소 제한값

        $template = new NotificationTemplate([
            'id' => 1,
            'name' => 'Most Popular',
            'usage_count' => 1000,
        ]);

        $templates = new Collection([$template]);

        $this->mockRepository
            ->shouldReceive('getPopularTemplates')
            ->with($limit)
            ->once()
            ->andReturn($templates);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getPopularTemplates($limit);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(1, $result);
    }

    /**
     * 최대 제한값 테스트
     */
    public function test_최대_제한값_테스트(): void
    {
        // Given
        $limit = 100; // 최대 제한값

        $templates = new Collection;
        for ($i = 1; $i <= $limit; $i++) {
            $template = new NotificationTemplate([
                'id' => $i,
                'name' => "Popular Template $i",
                'usage_count' => 1000 - $i,
            ]);
            $templates->push($template);
        }

        $this->mockRepository
            ->shouldReceive('getPopularTemplates')
            ->with($limit)
            ->once()
            ->andReturn($templates);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getPopularTemplates($limit);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(100, $result);
    }

    /**
     * 제한값 초과 테스트
     */
    public function test_제한값_초과_테스트(): void
    {
        // Given
        $limit = 101; // 최대값 초과

        // When & Then
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('조회 개수는 1~100 사이여야 합니다.');

        $this->service->getPopularTemplates($limit);
    }

    /**
     * 음수 제한값 테스트
     */
    public function test_음수_제한값_테스트(): void
    {
        // Given
        $limit = -1; // 음수 제한값

        // When & Then
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('조회 개수는 1~100 사이여야 합니다.');

        $this->service->getPopularTemplates($limit);
    }

    /**
     * 최근 템플릿 빈 결과 처리 테스트
     */
    public function test_최근_템플릿_빈_결과_처리(): void
    {
        // Given
        $limit = 10;
        $emptyCollection = new Collection([]);

        $this->mockRepository
            ->shouldReceive('getRecentTemplates')
            ->with($limit)
            ->once()
            ->andReturn($emptyCollection);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getRecentTemplates($limit);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(0, $result);
    }

    /**
     * 통계 요약 정보 부분 실패 처리 테스트
     */
    public function test_통계_요약_정보_부분_실패_처리(): void
    {
        // Given
        $basicStats = ['total_templates' => 10, 'total_usage' => 150];
        $priorityCounts = ['high' => 5, 'medium' => 3, 'low' => 2];

        $this->mockRepository
            ->shouldReceive('getUsageStatistics')
            ->once()
            ->andReturn($basicStats);

        $this->mockRepository
            ->shouldReceive('getCountByPriority')
            ->once()
            ->andReturn($priorityCounts);

        $this->mockRepository
            ->shouldReceive('getUsageDistribution')
            ->once()
            ->andThrow(new \Exception('Distribution calculation failed'));

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->times(2); // 성공한 호출들만

        $this->mockLoggingService
            ->shouldReceive('logError')
            ->once(); // 실패한 호출

        // When & Then
        $this->expectException(BusinessException::class);
        $this->service->getStatisticsSummary();
    }

    /**
     * 사용 트렌드 분석 기간 경계값 테스트
     */
    public function test_사용_트렌드_분석_기간_경계값(): void
    {
        // Given
        $days = 1; // 최소 기간

        $currentStats = ['total_templates' => 5, 'total_usage' => 25];
        $popularTemplates = new Collection([
            (object) ['id' => 1, 'name' => 'Popular', 'usage_count' => 20],
        ]);
        $recentTemplates = new Collection([
            (object) ['id' => 2, 'name' => 'Recent', 'usage_count' => 5],
        ]);

        $this->mockRepository
            ->shouldReceive('getUsageStatistics')
            ->once()
            ->andReturn($currentStats);

        $this->mockRepository
            ->shouldReceive('getPopularTemplates')
            ->with(5)
            ->once()
            ->andReturn($popularTemplates);

        $this->mockRepository
            ->shouldReceive('getRecentTemplates')
            ->with(5)
            ->once()
            ->andReturn($recentTemplates);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->times(4);

        // When
        $result = $this->service->getUsageTrends($days);

        // Then
        $this->assertEquals(1, $result['analysis_period_days']);
        $this->assertArrayHasKey('current_statistics', $result);
        $this->assertArrayHasKey('top_popular_templates', $result);
        $this->assertArrayHasKey('recent_templates', $result);
    }

    /**
     * 최대 분석 기간 테스트
     */
    public function test_최대_분석_기간_테스트(): void
    {
        // Given
        $days = 365; // 최대 기간

        $currentStats = ['total_templates' => 1000, 'total_usage' => 50000];
        $popularTemplates = new Collection([]);
        $recentTemplates = new Collection([]);

        $this->mockRepository
            ->shouldReceive('getUsageStatistics')
            ->once()
            ->andReturn($currentStats);

        $this->mockRepository
            ->shouldReceive('getPopularTemplates')
            ->with(5)
            ->once()
            ->andReturn($popularTemplates);

        $this->mockRepository
            ->shouldReceive('getRecentTemplates')
            ->with(5)
            ->once()
            ->andReturn($recentTemplates);

        $this->mockLoggingService
            ->shouldReceive('logInfo')
            ->times(4);

        // When
        $result = $this->service->getUsageTrends($days);

        // Then
        $this->assertEquals(365, $result['analysis_period_days']);
    }

    /**
     * 분석 기간 초과 테스트
     */
    public function test_분석_기간_초과_테스트(): void
    {
        // Given
        $days = 366; // 최대값 초과

        // When & Then
        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('분석 기간은 1~365일 사이여야 합니다.');

        $this->service->getUsageTrends($days);
    }

    /**
     * 템플릿 사용 횟수 증가 트랜잭션 실패 시뮬레이션
     */
    public function test_사용_횟수_증가_트랜잭션_실패(): void
    {
        // Given
        $templateId = 1;
        $template = new NotificationTemplate([
            'id' => $templateId,
            'name' => 'Test Template',
            'usage_count' => 5,
        ]);

        $this->mockRepository
            ->shouldReceive('findById')
            ->with($templateId)
            ->once()
            ->andReturn($template);

        $this->mockRepository
            ->shouldReceive('incrementUsage')
            ->with($templateId)
            ->once()
            ->andThrow(new \Exception('Database transaction failed'));

        // When & Then
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('템플릿 사용 횟수 증가 중 오류가 발생했습니다.');

        $this->service->incrementUsage($templateId);
    }

    /**
     * 메모리 부족 상황 시뮬레이션
     */
    public function test_메모리_부족_상황_시뮬레이션(): void
    {
        // Given
        $this->mockRepository
            ->shouldReceive('getUsageStatistics')
            ->once()
            ->andThrow(new \Exception('Allowed memory size exhausted'));

        $this->mockLoggingService
            ->shouldReceive('logError')
            ->once();

        // When & Then
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('템플릿 사용 통계 조회 중 오류가 발생했습니다.');

        $this->service->getUsageStatistics();
    }
}
