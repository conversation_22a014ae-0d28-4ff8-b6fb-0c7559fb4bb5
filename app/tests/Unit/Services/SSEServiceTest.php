<?php

namespace Tests\Unit\Services;

use App\Models\Notification;
use App\Models\NotificationRecipient;
use App\Models\User;
use App\Services\SSEService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SSEServiceTest extends TestCase
{
    use RefreshDatabase;

    private SSEService $sseService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->sseService = new SSEService();
    }

    public function test_미읽음_알림_조회_성공()
    {
        // Arrange
        $user = User::factory()->create();
        $sender = User::factory()->create();
        
        // 읽지 않은 알림 생성
        $notification1 = Notification::factory()->sent()->create([
            'title' => '첫 번째 알림',
            'content' => '첫 번째 내용',
            'sender_id' => $sender->id,
        ]);
        
        $notification2 = Notification::factory()->sent()->create([
            'title' => '두 번째 알림',
            'content' => '두 번째 내용',
            'sender_id' => $sender->id,
        ]);

        // 이미 전달된 알림 생성 (결과에 포함되지 않아야 함)
        $deliveredNotification = Notification::factory()->sent()->create([
            'title' => '전달된 알림',
            'content' => '전달된 내용',
            'sender_id' => $sender->id,
        ]);

        // 수신자 정보 생성
        NotificationRecipient::factory()->create([
            'notification_id' => $notification1->id,
            'user_id' => $user->id,
            'delivered_at' => null // 미전달
        ]);

        NotificationRecipient::factory()->create([
            'notification_id' => $notification2->id,
            'user_id' => $user->id,
            'delivered_at' => null // 미전달
        ]);

        NotificationRecipient::factory()->create([
            'notification_id' => $deliveredNotification->id,
            'user_id' => $user->id,
            'delivered_at' => now() // 이미 전달됨
        ]);

        // Act
        $result = $this->sseService->getPendingNotifications($user->id);

        // Assert
        $this->assertCount(2, $result);
        
        // 첫 번째 알림 확인
        $this->assertEquals($notification1->id, $result[0]['id']);
        $this->assertEquals('첫 번째 알림', $result[0]['title']);
        $this->assertEquals('첫 번째 내용', $result[0]['content']);
        $this->assertArrayHasKey('sent_at', $result[0]);

        // 두 번째 알림 확인
        $this->assertEquals($notification2->id, $result[1]['id']);
        $this->assertEquals('두 번째 알림', $result[1]['title']);
        $this->assertEquals('두 번째 내용', $result[1]['content']);
        $this->assertArrayHasKey('sent_at', $result[1]);
    }

    public function test_미읽음_알림이_없으면_빈_배열_반환()
    {
        // Arrange
        $user = User::factory()->create();

        // Act
        $result = $this->sseService->getPendingNotifications($user->id);

        // Assert
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    public function test_다른_사용자의_알림은_조회되지_않음()
    {
        // Arrange
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $sender = User::factory()->create();
        
        $notification = Notification::factory()->sent()->create([
            'title' => '사용자2 전용 알림',
            'content' => '사용자2만 받을 알림',
            'sender_id' => $sender->id,
        ]);

        // user2에게만 알림 전송
        NotificationRecipient::factory()->create([
            'notification_id' => $notification->id,
            'user_id' => $user2->id,
            'delivered_at' => null
        ]);

        // Act
        $result1 = $this->sseService->getPendingNotifications($user1->id);
        $result2 = $this->sseService->getPendingNotifications($user2->id);

        // Assert
        $this->assertEmpty($result1); // user1은 알림이 없어야 함
        $this->assertCount(1, $result2); // user2는 알림이 1개 있어야 함
        $this->assertEquals($notification->id, $result2[0]['id']);
    }

    public function test_알림_전달_완료_처리_성공()
    {
        // Arrange
        $user = User::factory()->create();
        $sender = User::factory()->create();
        
        $notification = Notification::factory()->sent()->create([
            'title' => '테스트 알림',
            'content' => '테스트 내용',
            'sender_id' => $sender->id,
        ]);

        $recipient = NotificationRecipient::factory()->create([
            'notification_id' => $notification->id,
            'user_id' => $user->id,
            'delivered_at' => null
        ]);

        // Act
        $result = $this->sseService->markAsDelivered($user->id, $notification->id);

        // Assert
        $this->assertTrue($result);
        $recipient->refresh();
        $this->assertNotNull($recipient->delivered_at);
    }

    public function test_존재하지_않는_알림_전달_완료_처리시_아무_일도_일어나지_않음()
    {
        // Arrange
        $user = User::factory()->create();
        $nonExistentNotificationId = 'non-existent-id';

        // Act & Assert - 예외가 발생하지 않아야 함
        $this->sseService->markAsDelivered($user->id, $nonExistentNotificationId);
        
        // 데이터베이스에 변화가 없는지 확인
        $this->assertEquals(0, NotificationRecipient::count());
    }

    public function test_다른_사용자의_알림_전달_완료_처리시_영향_없음()
    {
        // Arrange
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $sender = User::factory()->create();
        
        $notification = Notification::factory()->sent()->create([
            'title' => '테스트 알림',
            'content' => '테스트 내용',
            'sender_id' => $sender->id,
        ]);

        $recipient1 = NotificationRecipient::factory()->create([
            'notification_id' => $notification->id,
            'user_id' => $user1->id,
            'delivered_at' => null
        ]);

        $recipient2 = NotificationRecipient::factory()->create([
            'notification_id' => $notification->id,
            'user_id' => $user2->id,
            'delivered_at' => null
        ]);

        // Act - user1의 알림만 전달 완료 처리
        $result = $this->sseService->markAsDelivered($user1->id, $notification->id);

        // Assert
        $this->assertTrue($result);
        $recipient1->refresh();
        $recipient2->refresh();
        
        $this->assertNotNull($recipient1->delivered_at); // user1의 알림은 전달 완료
        $this->assertNull($recipient2->delivered_at); // user2의 알림은 여전히 미전달
    }

    public function test_미전달_알림_전달_완료_처리_성공()
    {
        // Arrange
        $user = User::factory()->create();
        $sender = User::factory()->create();
        
        $notification = Notification::factory()->sent()->create([
            'title' => '테스트 알림',
            'content' => '테스트 내용',
            'sender_id' => $sender->id,
        ]);

        $recipient = NotificationRecipient::factory()->create([
            'notification_id' => $notification->id,
            'user_id' => $user->id,
            'delivered_at' => null // 아직 전달되지 않음
        ]);

        // Act
        $result = $this->sseService->markAsDelivered($user->id, $notification->id);

        // Assert
        $this->assertTrue($result);
        $recipient->refresh();
        $this->assertNotNull($recipient->delivered_at);
    }
}