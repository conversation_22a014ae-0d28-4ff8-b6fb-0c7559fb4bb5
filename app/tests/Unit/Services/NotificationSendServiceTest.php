<?php

namespace Tests\Unit\Services;

use App\Models\Notification;
use App\Models\NotificationGroup;
use App\Models\NotificationGroupMember;
use App\Models\NotificationRecipient;
use App\Models\User;
use App\Services\NotificationSendService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class NotificationSendServiceTest extends TestCase
{
    use RefreshDatabase;

    private NotificationSendService $notificationSendService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->notificationSendService = new NotificationSendService();
    }

    public function test_알림_전송_성공_모든_사용자_대상()
    {
        // Arrange
        $sender = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $users = User::factory()->count(3)->create(['status' => User::MEMBER_STATUS_ACTIVE]);

        $data = [
            'title' => '테스트 알림',
            'content' => '테스트 내용',
            'target_type' => 'all',
            'target_ids' => [],
            'sender_id' => $sender->id
        ];

        // Act
        $result = $this->notificationSendService->sendNotification($data);

        // Assert
        $this->assertArrayHasKey('notification_id', $result);
        $this->assertArrayHasKey('recipients_count', $result);
        $this->assertEquals(3, $result['recipients_count']);

        // 알림이 생성되었는지 확인
        $notification = Notification::find($result['notification_id']);
        $this->assertNotNull($notification);
        $this->assertEquals('테스트 알림', $notification->title);
        $this->assertEquals('테스트 내용', $notification->content);
        $this->assertEquals('all', $notification->target_type);
        $this->assertEquals('sent', $notification->status);
        $this->assertEquals($sender->id, $notification->sender_id);

        // 수신자가 생성되었는지 확인
        $recipients = NotificationRecipient::where('notification_id', $result['notification_id'])->get();
        $this->assertCount(3, $recipients);
    }

    public function test_알림_전송_성공_그룹_대상()
    {
        // Arrange
        $sender = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $group = NotificationGroup::factory()->create();
        $users = User::factory()->count(2)->create(['status' => User::MEMBER_STATUS_ACTIVE]);
        
        // 그룹 멤버 추가
        foreach ($users as $user) {
            NotificationGroupMember::factory()->create([
                'group_id' => $group->id,
                'user_id' => $user->id
            ]);
        }

        $data = [
            'title' => '그룹 알림',
            'content' => '그룹 내용',
            'target_type' => 'group',
            'target_ids' => [$group->id],
            'sender_id' => $sender->id
        ];

        // Act
        $result = $this->notificationSendService->sendNotification($data);

        // Assert
        $this->assertEquals(2, $result['recipients_count']);

        // 수신자가 올바르게 생성되었는지 확인
        $recipients = NotificationRecipient::where('notification_id', $result['notification_id'])->get();
        $this->assertCount(2, $recipients);
        
        $recipientUserIds = $recipients->pluck('user_id')->toArray();
        $expectedUserIds = $users->pluck('id')->toArray();
        $this->assertEquals(sort($expectedUserIds), sort($recipientUserIds));
    }

    public function test_알림_전송_성공_개별_사용자_대상()
    {
        // Arrange
        $sender = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $targetUsers = User::factory()->count(2)->create(['status' => User::MEMBER_STATUS_ACTIVE]);
        $targetIds = $targetUsers->pluck('id')->toArray();

        $data = [
            'title' => '개별 알림',
            'content' => '개별 내용',
            'target_type' => 'individual',
            'target_ids' => $targetIds,
            'sender_id' => $sender->id
        ];

        // Act
        $result = $this->notificationSendService->sendNotification($data);

        // Assert
        $this->assertEquals(2, $result['recipients_count']);

        // 수신자가 올바르게 생성되었는지 확인
        $recipients = NotificationRecipient::where('notification_id', $result['notification_id'])->get();
        $this->assertCount(2, $recipients);
        
        $recipientUserIds = $recipients->pluck('user_id')->toArray();
        $this->assertEquals(sort($targetIds), sort($recipientUserIds));
    }

    public function test_비활성_사용자는_수신자에서_제외됨()
    {
        // Arrange
        $sender = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $activeUser = User::factory()->create(['status' => User::MEMBER_STATUS_ACTIVE]);
        $inactiveUser = User::factory()->create(['status' => 0]);

        $data = [
            'title' => '테스트 알림',
            'content' => '테스트 내용',
            'target_type' => 'individual',
            'target_ids' => [$activeUser->id, $inactiveUser->id],
            'sender_id' => $sender->id
        ];

        // Act
        $result = $this->notificationSendService->sendNotification($data);

        // Assert
        $this->assertEquals(1, $result['recipients_count']);

        // 활성 사용자만 수신자로 등록되었는지 확인
        $recipients = NotificationRecipient::where('notification_id', $result['notification_id'])->get();
        $this->assertCount(1, $recipients);
        $this->assertEquals($activeUser->id, $recipients->first()->user_id);
    }

    public function test_존재하지_않는_그룹은_예외를_발생시킴()
    {
        // Arrange
        $sender = User::factory()->create(['role' => User::ROLE_ADMIN]);

        $data = [
            'title' => '그룹 알림',
            'content' => '그룹 내용',
            'target_type' => 'group',
            'target_ids' => [999], // 999는 존재하지 않는 그룹
            'sender_id' => $sender->id
        ];

        // Act & Assert
        $this->expectException(\App\Exceptions\ResourceNotFoundException::class);
        $this->expectExceptionMessage('그룹을 찾을 수 없습니다. (ID: 999)');
        
        $this->notificationSendService->sendNotification($data);
    }

    public function test_트랜잭션_롤백_테스트()
    {
        // Arrange
        $sender = User::factory()->create(['role' => User::ROLE_ADMIN]);
        
        $data = [
            'title' => '테스트 알림',
            'content' => '테스트 내용',
            'target_type' => 'all',
            'target_ids' => [],
            'sender_id' => $sender->id
        ];

        // DB 에러를 강제로 발생시키기 위해 모킹
        DB::shouldReceive('transaction')->andThrow(new \Exception('Database error'));

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');
        
        $this->notificationSendService->sendNotification($data);

        // 알림이 생성되지 않았는지 확인
        $this->assertEquals(0, Notification::count());
        $this->assertEquals(0, NotificationRecipient::count());
    }

    public function test_중복_사용자_제거_테스트()
    {
        // Arrange
        $sender = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $user = User::factory()->create(['status' => User::MEMBER_STATUS_ACTIVE]);

        $data = [
            'title' => '중복 테스트',
            'content' => '중복 내용',
            'target_type' => 'individual',
            'target_ids' => [$user->id, $user->id, $user->id], // 같은 사용자 ID 중복
            'sender_id' => $sender->id
        ];

        // Act
        $result = $this->notificationSendService->sendNotification($data);

        // Assert
        $this->assertEquals(1, $result['recipients_count']);

        // 수신자가 중복 없이 하나만 생성되었는지 확인
        $recipients = NotificationRecipient::where('notification_id', $result['notification_id'])->get();
        $this->assertCount(1, $recipients);
        $this->assertEquals($user->id, $recipients->first()->user_id);
    }
}