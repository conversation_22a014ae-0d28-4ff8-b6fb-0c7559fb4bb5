<?php

namespace Tests\Unit\Services;

use App\Exceptions\ValidationException;
use App\Models\NotificationTemplate;
use App\Services\TemplateValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TemplateValidationServiceTest extends TestCase
{
    use RefreshDatabase;

    private TemplateValidationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new TemplateValidationService;
    }

    /**
     * 템플릿 생성 데이터 유효성 검증 테스트
     */
    public function test_validate_for_create_with_valid_data(): void
    {
        $data = [
            'name' => '테스트 템플릿',
            'title' => '테스트 제목',
            'content' => '테스트 내용',
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'created_by' => 1,
        ];

        $result = $this->service->validateForCreate($data);

        $this->assertIsArray($result);
        $this->assertEquals('테스트 템플릿', $result['name']);
        $this->assertEquals('테스트 제목', $result['title']);
        $this->assertEquals('테스트 내용', $result['content']);
        $this->assertEquals(NotificationTemplate::PRIORITY_NORMAL, $result['priority']);
        $this->assertEquals(1, $result['created_by']);
    }

    public function test_validate_for_create_with_missing_required_fields(): void
    {
        $data = [
            'name' => '',
            'title' => '',
            'content' => '',
        ];

        $this->expectException(ValidationException::class);
        $this->expectExceptionMessage('템플릿 생성 데이터 유효성 검증에 실패했습니다.');

        $this->service->validateForCreate($data);
    }

    public function test_validate_for_create_with_duplicate_name(): void
    {
        // 기존 템플릿 생성
        NotificationTemplate::factory()->create(['name' => '중복 템플릿']);

        $data = [
            'name' => '중복 템플릿',
            'title' => '테스트 제목',
            'content' => '테스트 내용',
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'created_by' => 1,
        ];

        $this->expectException(ValidationException::class);
        $this->service->validateForCreate($data);
    }

    /**
     * 템플릿 수정 데이터 유효성 검증 테스트
     */
    public function test_validate_for_update_with_valid_data(): void
    {
        $template = NotificationTemplate::factory()->create();

        $data = [
            'name' => '수정된 템플릿',
            'title' => '수정된 제목',
            'content' => '수정된 내용',
            'priority' => NotificationTemplate::PRIORITY_HIGH,
        ];

        $result = $this->service->validateForUpdate($data, $template->id);

        $this->assertIsArray($result);
        $this->assertEquals('수정된 템플릿', $result['name']);
        $this->assertEquals('수정된 제목', $result['title']);
        $this->assertEquals('수정된 내용', $result['content']);
        $this->assertEquals(NotificationTemplate::PRIORITY_HIGH, $result['priority']);
    }

    public function test_validate_for_update_with_same_name_should_pass(): void
    {
        $template = NotificationTemplate::factory()->create(['name' => '기존 템플릿']);

        $data = [
            'name' => '기존 템플릿', // 같은 이름은 허용
            'title' => '수정된 제목',
            'content' => '수정된 내용',
        ];

        $result = $this->service->validateForUpdate($data, $template->id);
        $this->assertEquals('기존 템플릿', $result['name']);
    }

    /**
     * 필터 조건 유효성 검증 테스트
     */
    public function test_validate_filters_with_valid_data(): void
    {
        $filters = [
            'search' => '테스트',
            'priority' => NotificationTemplate::PRIORITY_HIGH,
            'created_by' => 1,
            'min_usage' => 0,
            'max_usage' => 10,
            'created_from' => '2024-01-01',
            'created_to' => '2024-12-31',
            'sortBy' => 'usage_count',
            'sortDirection' => 'desc',
            'per_page' => 20,
        ];

        $result = $this->service->validateFilters($filters);

        $this->assertEquals('테스트', $result['search']);
        $this->assertEquals(NotificationTemplate::PRIORITY_HIGH, $result['priority']);
        $this->assertEquals(1, $result['created_by']);
        $this->assertEquals(0, $result['min_usage']);
        $this->assertEquals(10, $result['max_usage']);
        $this->assertStringContainsString('2024-01-01', $result['created_from']);
        $this->assertStringContainsString('2024-12-31', $result['created_to']);
        $this->assertEquals('usage_count', $result['sortBy']);
        $this->assertEquals('desc', $result['sortDirection']);
    }

    public function test_validate_filters_with_empty_search(): void
    {
        $filters = ['search' => '   '];

        $result = $this->service->validateFilters($filters);

        $this->assertArrayNotHasKey('search', $result);
    }

    public function test_validate_filters_with_invalid_priority(): void
    {
        $filters = ['priority' => 'invalid_priority'];

        $this->expectException(ValidationException::class);
        $this->service->validateFilters($filters);
    }

    public function test_validate_filters_with_invalid_created_by(): void
    {
        $filters = ['created_by' => 'invalid_id'];

        $this->expectException(ValidationException::class);
        $this->service->validateFilters($filters);
    }

    public function test_validate_filters_with_invalid_usage_range(): void
    {
        $filters = [
            'min_usage' => 10,
            'max_usage' => 5, // min > max
        ];

        $this->expectException(ValidationException::class);
        $this->service->validateFilters($filters);
    }

    public function test_validate_filters_with_negative_usage(): void
    {
        $filters = ['min_usage' => -1];

        $this->expectException(ValidationException::class);
        $this->service->validateFilters($filters);
    }

    public function test_validate_filters_with_invalid_date_format(): void
    {
        $filters = ['created_from' => 'invalid_date'];

        $this->expectException(ValidationException::class);
        $this->service->validateFilters($filters);
    }

    public function test_validate_filters_with_invalid_sort_field(): void
    {
        $filters = ['sortBy' => 'invalid_field'];

        $this->expectException(ValidationException::class);
        $this->service->validateFilters($filters);
    }

    public function test_validate_filters_with_invalid_sort_direction(): void
    {
        $filters = ['sortDirection' => 'invalid_direction'];

        $this->expectException(ValidationException::class);
        $this->service->validateFilters($filters);
    }

    public function test_validate_filters_with_invalid_per_page(): void
    {
        $filters = ['per_page' => 150]; // > 100

        $this->expectException(ValidationException::class);
        $this->service->validateFilters($filters);
    }

    /**
     * 필터 정제 테스트
     */
    public function test_sanitize_filters(): void
    {
        $filters = [
            'search' => '  <script>alert("test")</script>  ',
            'priority' => NotificationTemplate::PRIORITY_HIGH,
            'created_by' => '1',
            'min_usage' => '5',
            'max_usage' => '10',
            'sortBy' => 'usage_count',
            'sortDirection' => 'DESC',
            'unknown_field' => 'should_be_removed',
        ];

        $result = $this->service->sanitizeFilters($filters);

        $this->assertEquals('&lt;script&gt;alert(&quot;test&quot;)&lt;/script&gt;', $result['search']);
        $this->assertEquals(NotificationTemplate::PRIORITY_HIGH, $result['priority']);
        $this->assertEquals(1, $result['created_by']);
        $this->assertEquals(5, $result['min_usage']);
        $this->assertEquals(10, $result['max_usage']);
        $this->assertEquals('usage_count', $result['sortBy']);
        $this->assertEquals('DESC', $result['sortDirection']);
        $this->assertArrayNotHasKey('unknown_field', $result);
    }

    /**
     * 검색어 유효성 검증 테스트
     */
    public function test_validate_search_term_with_valid_string(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('validateSearchTerm');
        $method->setAccessible(true);

        $result = $method->invoke($this->service, '테스트 검색어');

        $this->assertEquals('테스트 검색어', $result['value']);
        $this->assertNull($result['error']);
    }

    public function test_validate_search_term_with_empty_string(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('validateSearchTerm');
        $method->setAccessible(true);

        $result = $method->invoke($this->service, '   ');

        $this->assertNull($result['value']);
        $this->assertNull($result['error']);
    }

    public function test_validate_search_term_with_too_long_string(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('validateSearchTerm');
        $method->setAccessible(true);

        $longString = str_repeat('a', 101);
        $result = $method->invoke($this->service, $longString);

        $this->assertNull($result['value']);
        $this->assertStringContainsString('100자를 초과할 수 없습니다', $result['error']);
    }

    public function test_validate_search_term_with_non_string(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('validateSearchTerm');
        $method->setAccessible(true);

        $result = $method->invoke($this->service, 123);

        // 숫자는 문자열로 변환되어 처리됨
        $this->assertEquals('123', $result['value']);
        $this->assertNull($result['error']);
    }

    /**
     * 우선순위 유효성 검증 테스트
     */
    public function test_validate_priority_with_valid_priority(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('validatePriority');
        $method->setAccessible(true);

        $result = $method->invoke($this->service, NotificationTemplate::PRIORITY_URGENT);

        $this->assertEquals(NotificationTemplate::PRIORITY_URGENT, $result['value']);
        $this->assertNull($result['error']);
    }

    public function test_validate_priority_with_invalid_priority(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('validatePriority');
        $method->setAccessible(true);

        $result = $method->invoke($this->service, 'invalid');

        $this->assertNull($result['value']);
        $this->assertStringContainsString('유효하지 않은 우선순위', $result['error']);
    }

    /**
     * 사용 횟수 범위 유효성 검증 테스트
     */
    public function test_validate_usage_range_with_valid_range(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('validateUsageRange');
        $method->setAccessible(true);

        $filters = ['min_usage' => 5, 'max_usage' => 10];
        $result = $method->invoke($this->service, $filters);

        $this->assertEquals(5, $result['min_usage']);
        $this->assertEquals(10, $result['max_usage']);
        $this->assertEmpty($result['errors']);
    }

    public function test_validate_usage_range_with_invalid_range(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('validateUsageRange');
        $method->setAccessible(true);

        $filters = ['min_usage' => 10, 'max_usage' => 5];
        $result = $method->invoke($this->service, $filters);

        $this->assertNotEmpty($result['errors']);
        $this->assertStringContainsString('최소 사용 횟수는 최대 사용 횟수보다 클 수 없습니다', $result['errors'][0]);
    }

    /**
     * 페이지 크기 유효성 검증 테스트
     */
    public function test_validate_per_page_with_valid_size(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('validatePerPage');
        $method->setAccessible(true);

        $result = $method->invoke($this->service, 20);

        $this->assertEquals(20, $result['value']);
        $this->assertNull($result['error']);
    }

    public function test_validate_per_page_with_invalid_size(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('validatePerPage');
        $method->setAccessible(true);

        $result = $method->invoke($this->service, 150);

        $this->assertNull($result['value']);
        $this->assertStringContainsString('1~100 사이여야 합니다', $result['error']);
    }

    /**
     * 템플릿 데이터 정제 테스트
     */
    public function test_sanitize_template_data(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('sanitizeTemplateData');
        $method->setAccessible(true);

        $data = [
            'name' => '  <script>alert("test")</script>  ',
            'title' => '  테스트 제목  ',
            'content' => '  테스트 내용  ',
            'priority' => NotificationTemplate::PRIORITY_HIGH,
            'created_by' => '1',
            'unknown_field' => 'should_be_removed',
        ];

        $result = $method->invoke($this->service, $data);

        $this->assertEquals('&lt;script&gt;alert(&quot;test&quot;)&lt;/script&gt;', $result['name']);
        $this->assertEquals('테스트 제목', $result['title']);
        $this->assertEquals('테스트 내용', $result['content']);
        $this->assertEquals(NotificationTemplate::PRIORITY_HIGH, $result['priority']);
        $this->assertEquals(1, $result['created_by']);
        $this->assertArrayNotHasKey('unknown_field', $result);
    }
}
