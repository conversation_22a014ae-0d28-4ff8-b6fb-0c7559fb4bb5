<?php

namespace Tests\Unit\Services;

use App\Services\TemplateLoggingService;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

/**
 * TemplateLoggingService 단위 테스트
 */
class TemplateLoggingServiceTest extends TestCase
{
    use RefreshDatabase;

    private TemplateLoggingService $loggingService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->loggingService = new TemplateLoggingService;
    }

    /**
     * 정보 로그 기록 테스트
     */
    public function test_log_info_기본_메시지_로깅(): void
    {
        // Given
        Log::shouldReceive('channel')
            ->with('single')
            ->once()
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->with(
                '[TemplateService] 테스트 메시지',
                $this->callback(function ($context) {
                    return isset($context['timestamp']) &&
                           isset($context['ip_address']);
                })
            )
            ->once();

        // When
        $this->loggingService->logInfo('테스트 메시지');

        // Then - Mock 검증으로 충분
    }

    /**
     * 컨텍스트와 함께 정보 로그 기록 테스트
     */
    public function test_log_info_컨텍스트와_함께_로깅(): void
    {
        // Given
        $context = [
            'user_id' => 1,
            'template_id' => 123,
        ];

        Log::shouldReceive('channel')
            ->with('single')
            ->once()
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->with(
                '[TemplateService] 템플릿 생성 완료',
                $this->callback(function ($loggedContext) use ($context) {
                    return $loggedContext['user_id'] === $context['user_id'] &&
                           $loggedContext['template_id'] === $context['template_id'] &&
                           isset($loggedContext['timestamp']) &&
                           isset($loggedContext['ip_address']);
                })
            )
            ->once();

        // When
        $this->loggingService->logInfo('템플릿 생성 완료', $context);

        // Then - Mock 검증으로 충분
    }

    /**
     * 에러 로그 기록 테스트
     */
    public function test_log_error_에러_메시지_로깅(): void
    {
        // Given
        $context = ['error_code' => 'TEMPLATE_001'];

        Log::shouldReceive('channel')
            ->with('single')
            ->once()
            ->andReturnSelf();

        Log::shouldReceive('error')
            ->with(
                '[TemplateService] 템플릿 생성 실패',
                $this->callback(function ($loggedContext) use ($context) {
                    return $loggedContext['error_code'] === $context['error_code'] &&
                           isset($loggedContext['timestamp']);
                })
            )
            ->once();

        // When
        $this->loggingService->logError('템플릿 생성 실패', $context);

        // Then - Mock 검증으로 충분
    }

    /**
     * 경고 로그 기록 테스트
     */
    public function test_log_warning_경고_메시지_로깅(): void
    {
        // Given
        Log::shouldReceive('channel')
            ->with('single')
            ->once()
            ->andReturnSelf();

        Log::shouldReceive('warning')
            ->with(
                '[TemplateService] 권한 부족 경고',
                $this->callback(function ($context) {
                    return isset($context['timestamp']);
                })
            )
            ->once();

        // When
        $this->loggingService->logWarning('권한 부족 경고');

        // Then - Mock 검증으로 충분
    }

    /**
     * 디버그 로그 기록 테스트
     */
    public function test_log_debug_디버그_메시지_로깅(): void
    {
        // Given
        Log::shouldReceive('channel')
            ->with('single')
            ->once()
            ->andReturnSelf();

        Log::shouldReceive('debug')
            ->with(
                '[TemplateService] 디버그 정보',
                $this->callback(function ($context) {
                    return isset($context['timestamp']);
                })
            )
            ->once();

        // When
        $this->loggingService->logDebug('디버그 정보');

        // Then - Mock 검증으로 충분
    }

    /**
     * 템플릿 액션 로그 기록 테스트
     */
    public function test_log_template_action_템플릿_액션_로깅(): void
    {
        // Given
        $action = 'create';
        $templateId = 123;
        $userId = 456;
        $additionalContext = ['template_name' => '테스트 템플릿'];

        Log::shouldReceive('channel')
            ->with('single')
            ->once()
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->with(
                '[TemplateService] 템플릿 액션 수행: create',
                $this->callback(function ($context) use ($action, $templateId, $userId, $additionalContext) {
                    return $context['action'] === $action &&
                           $context['template_id'] === $templateId &&
                           $context['user_id'] === $userId &&
                           $context['template_name'] === $additionalContext['template_name'] &&
                           isset($context['timestamp']);
                })
            )
            ->once();

        // When
        $this->loggingService->logTemplateAction($action, $templateId, $userId, $additionalContext);

        // Then - Mock 검증으로 충분
    }

    /**
     * 성능 메트릭 로그 기록 테스트
     */
    public function test_log_performance_성능_메트릭_로깅(): void
    {
        // Given
        $operation = 'template_search';
        $executionTime = 0.125; // 125ms
        $additionalContext = ['result_count' => 50];

        Log::shouldReceive('channel')
            ->with('single')
            ->once()
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->with(
                '[TemplateService] 성능 메트릭: template_search',
                $this->callback(function ($context) use ($operation, $executionTime, $additionalContext) {
                    return $context['operation'] === $operation &&
                           $context['execution_time_seconds'] === $executionTime &&
                           $context['execution_time_ms'] === 125.0 &&
                           $context['result_count'] === $additionalContext['result_count'] &&
                           isset($context['timestamp']);
                })
            )
            ->once();

        // When
        $this->loggingService->logPerformance($operation, $executionTime, $additionalContext);

        // Then - Mock 검증으로 충분
    }

    /**
     * 비즈니스 이벤트 로그 기록 테스트
     */
    public function test_log_business_event_비즈니스_이벤트_로깅(): void
    {
        // Given
        $event = 'template_usage_threshold_reached';
        $context = [
            'template_id' => 123,
            'usage_count' => 1000,
            'threshold' => 1000,
        ];

        Log::shouldReceive('channel')
            ->with('single')
            ->once()
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->with(
                '[TemplateService] 비즈니스 이벤트: template_usage_threshold_reached',
                $this->callback(function ($loggedContext) use ($event, $context) {
                    return $loggedContext['event'] === $event &&
                           $loggedContext['template_id'] === $context['template_id'] &&
                           $loggedContext['usage_count'] === $context['usage_count'] &&
                           $loggedContext['threshold'] === $context['threshold'] &&
                           isset($loggedContext['timestamp']);
                })
            )
            ->once();

        // When
        $this->loggingService->logBusinessEvent($event, $context);

        // Then - Mock 검증으로 충분
    }

    /**
     * 예외 로그 기록 테스트
     */
    public function test_log_exception_예외_정보_로깅(): void
    {
        // Given
        $message = '템플릿 처리 중 예외 발생';
        $exception = new Exception('테스트 예외', 500);
        $additionalContext = ['template_id' => 123];

        Log::shouldReceive('channel')
            ->with('single')
            ->once()
            ->andReturnSelf();

        Log::shouldReceive('error')
            ->with(
                '[TemplateService] 템플릿 처리 중 예외 발생',
                $this->callback(function ($context) use ($additionalContext) {
                    return $context['exception_class'] === Exception::class &&
                           $context['exception_message'] === '테스트 예외' &&
                           $context['exception_code'] === 500 &&
                           $context['template_id'] === $additionalContext['template_id'] &&
                           isset($context['exception_file']) &&
                           isset($context['exception_line']) &&
                           isset($context['stack_trace']) &&
                           isset($context['timestamp']);
                })
            )
            ->once();

        // When
        $this->loggingService->logException($message, $exception, $additionalContext);

        // Then - Mock 검증으로 충분
    }

    /**
     * 검증 실패 로그 기록 테스트
     */
    public function test_log_validation_failure_검증_실패_로깅(): void
    {
        // Given
        $validationType = 'template_creation';
        $validationErrors = [
            'name' => ['이름은 필수입니다.'],
            'priority' => ['유효하지 않은 우선순위입니다.'],
        ];
        $inputData = [
            'name' => '',
            'priority' => 'invalid',
            'content' => '테스트 내용',
        ];

        Log::shouldReceive('channel')
            ->with('single')
            ->once()
            ->andReturnSelf();

        Log::shouldReceive('warning')
            ->with(
                '[TemplateService] 검증 실패: template_creation',
                $this->callback(function ($context) use ($validationType, $validationErrors, $inputData) {
                    return $context['validation_type'] === $validationType &&
                           $context['validation_errors'] === $validationErrors &&
                           $context['input_data_keys'] === array_keys($inputData) &&
                           $context['error_count'] === 2 &&
                           isset($context['timestamp']);
                })
            )
            ->once();

        // When
        $this->loggingService->logValidationFailure($validationType, $validationErrors, $inputData);

        // Then - Mock 검증으로 충분
    }

    /**
     * 민감한 정보 마스킹 테스트
     */
    public function test_민감한_정보_마스킹_처리(): void
    {
        // Given
        $context = [
            'user_id' => 123,
            'password' => 'secret123',
            'token' => 'abc123token',
            'template_name' => '테스트 템플릿',
            'nested' => [
                'api_key' => 'secret_key',
                'normal_data' => '일반 데이터',
            ],
        ];

        Log::shouldReceive('channel')
            ->with('single')
            ->once()
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->with(
                '[TemplateService] 민감 정보 테스트',
                $this->callback(function ($loggedContext) {
                    return $loggedContext['user_id'] === 123 &&
                           $loggedContext['password'] === '***MASKED***' &&
                           $loggedContext['token'] === '***MASKED***' &&
                           $loggedContext['template_name'] === '테스트 템플릿' &&
                           $loggedContext['nested']['api_key'] === '***MASKED***' &&
                           $loggedContext['nested']['normal_data'] === '일반 데이터';
                })
            )
            ->once();

        // When
        $this->loggingService->logInfo('민감 정보 테스트', $context);

        // Then - Mock 검증으로 충분
    }

    /**
     * 요청 헤더 정보 포함 테스트
     */
    public function test_요청_헤더_정보_포함(): void
    {
        // Given
        $request = Request::create('/test', 'GET');
        $request->headers->set('X-Request-ID', 'test-request-123');
        $request->headers->set('User-Agent', 'TestAgent/1.0');
        $this->app->instance('request', $request);

        Log::shouldReceive('channel')
            ->with('single')
            ->once()
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->with(
                '[TemplateService] 헤더 테스트',
                $this->callback(function ($context) {
                    return $context['request_id'] === 'test-request-123' &&
                           $context['user_agent'] === 'TestAgent/1.0' &&
                           isset($context['ip_address']) &&
                           isset($context['timestamp']);
                })
            )
            ->once();

        // When
        $this->loggingService->logInfo('헤더 테스트');

        // Then - Mock 검증으로 충분
    }

    /**
     * 빈 컨텍스트로 로깅 테스트
     */
    public function test_빈_컨텍스트로_로깅(): void
    {
        // Given
        Log::shouldReceive('channel')
            ->with('single')
            ->once()
            ->andReturnSelf();

        Log::shouldReceive('info')
            ->with(
                '[TemplateService] 빈 컨텍스트 테스트',
                $this->callback(function ($context) {
                    return isset($context['timestamp']) &&
                           isset($context['ip_address']) &&
                           count($context) >= 2; // 최소한 timestamp와 ip_address는 있어야 함
                })
            )
            ->once();

        // When
        $this->loggingService->logInfo('빈 컨텍스트 테스트', []);

        // Then - Mock 검증으로 충분
    }
}
