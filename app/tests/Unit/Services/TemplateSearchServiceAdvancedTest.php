<?php

namespace Tests\Unit\Services;

use App\Exceptions\BusinessException;
use App\Models\NotificationTemplate;
use App\Repositories\Interfaces\NotificationTemplateRepositoryInterface;
use App\Services\TemplateLoggingService;
use App\Services\TemplateSearchService;
use App\Services\TemplateValidationService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator as ConcretePaginator;
use Mockery;
use Tests\TestCase;

/**
 * TemplateSearchService 고급 기능 단위 테스트
 * 기존 테스트에서 누락된 엣지 케이스와 에러 처리를 테스트합니다.
 */
class TemplateSearchServiceAdvancedTest extends TestCase
{
    private TemplateSearchService $service;

    private Mockery\MockInterface $templateRepository;

    private Mockery\MockInterface $validationService;

    private Mockery\MockInterface $loggingService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->templateRepository = Mockery::mock(NotificationTemplateRepositoryInterface::class);
        $this->validationService = Mockery::mock(TemplateValidationService::class);
        $this->loggingService = Mockery::mock(TemplateLoggingService::class);

        $this->service = new TemplateSearchService(
            $this->templateRepository,
            $this->validationService,
            $this->loggingService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 빈 검색 결과 처리 테스트
     */
    public function test_빈_검색_결과_처리(): void
    {
        // Given
        $userId = 1;
        $search = 'nonexistent';
        $filters = [];

        $emptyCollection = new Collection([]);

        $this->templateRepository
            ->shouldReceive('search')
            ->once()
            ->with($search, $filters)
            ->andReturn($emptyCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once()
            ->with('템플릿 검색 완료', Mockery::type('array'));

        // When
        $result = $this->service->searchTemplates($userId, $search, $filters);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(0, $result);
    }

    /**
     * 페이지네이션 경계값 테스트
     */
    public function test_페이지네이션_경계값_처리(): void
    {
        // Given
        $userId = 1;
        $page = 0; // 경계값: 0 페이지
        $filters = [];

        $mockPaginator = new ConcretePaginator(
            collect([]),
            0,
            20,
            1 // 실제로는 1페이지로 처리됨
        );

        $this->validationService
            ->shouldReceive('validateFilters')
            ->once()
            ->andReturn([]);

        $this->validationService
            ->shouldReceive('sanitizeFilters')
            ->once()
            ->andReturn([]);

        $this->templateRepository
            ->shouldReceive('getPaginatedWithStats')
            ->once()
            ->with(1, 20, ['per_page' => 20]) // page는 1로 보정됨, per_page 추가됨
            ->andReturn($mockPaginator);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once();

        $this->loggingService
            ->shouldReceive('logError')
            ->never();

        // When
        $result = $this->service->getTemplates($userId, $page, $filters);

        // Then
        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
    }

    /**
     * 대용량 페이지 요청 테스트
     */
    public function test_대용량_페이지_요청_처리(): void
    {
        // Given
        $userId = 1;
        $page = 9999; // 매우 큰 페이지 번호
        $filters = [];

        $mockPaginator = new ConcretePaginator(
            collect([]),
            0,
            20,
            $page
        );

        $this->validationService
            ->shouldReceive('validateFilters')
            ->once()
            ->andReturn([]);

        $this->validationService
            ->shouldReceive('sanitizeFilters')
            ->once()
            ->andReturn([]);

        $this->templateRepository
            ->shouldReceive('getPaginatedWithStats')
            ->once()
            ->with($page, 20, ['per_page' => 20])
            ->andReturn($mockPaginator);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once();

        $this->loggingService
            ->shouldReceive('logError')
            ->never();

        // When
        $result = $this->service->getTemplates($userId, $page, $filters);

        // Then
        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
    }

    /**
     * 복합 필터 조건 테스트
     */
    public function test_복합_필터_조건_처리(): void
    {
        // Given
        $userId = 1;
        $complexFilters = [
            'search' => 'test',
            'priority' => 'high',
            'created_by' => 1,
            'min_usage' => 5,
            'max_usage' => 50,
            'created_from' => '2024-01-01',
            'created_to' => '2024-12-31',
            'sortBy' => 'usage_count',
            'sortDirection' => 'desc',
        ];

        $mockCollection = new Collection([new NotificationTemplate]);

        $this->validationService
            ->shouldReceive('validateFilters')
            ->once()
            ->with($complexFilters)
            ->andReturn($complexFilters);

        $this->templateRepository
            ->shouldReceive('search')
            ->once()
            ->with('', $complexFilters)
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getTemplatesWithAdvancedFilters($userId, $complexFilters);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * 특수 문자 검색어 처리 테스트
     */
    public function test_특수_문자_검색어_처리(): void
    {
        // Given
        $userId = 1;
        $specialSearch = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        $filters = [];

        $mockCollection = new Collection([]);

        $this->templateRepository
            ->shouldReceive('search')
            ->once()
            ->with($specialSearch, $filters)
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->searchTemplates($userId, $specialSearch, $filters);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * 유니코드 검색어 처리 테스트
     */
    public function test_유니코드_검색어_처리(): void
    {
        // Given
        $userId = 1;
        $unicodeSearch = '한글 검색어 🔍 テスト';
        $filters = [];

        $mockCollection = new Collection([new NotificationTemplate]);

        $this->templateRepository
            ->shouldReceive('search')
            ->once()
            ->with($unicodeSearch, $filters)
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->searchTemplates($userId, $unicodeSearch, $filters);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * 동시성 처리 시뮬레이션 테스트
     */
    public function test_동시성_처리_시뮬레이션(): void
    {
        // Given
        $userId1 = 1;
        $userId2 = 2;
        $search = 'concurrent';

        $mockCollection1 = new Collection([new NotificationTemplate]);
        $mockCollection2 = new Collection([new NotificationTemplate, new NotificationTemplate]);

        $this->templateRepository
            ->shouldReceive('search')
            ->twice()
            ->with($search, [])
            ->andReturn($mockCollection1, $mockCollection2);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->twice();

        // When
        $result1 = $this->service->searchTemplates($userId1, $search);
        $result2 = $this->service->searchTemplates($userId2, $search);

        // Then
        $this->assertCount(1, $result1);
        $this->assertCount(2, $result2);
    }

    /**
     * 메모리 효율성 테스트 (대용량 결과 처리)
     */
    public function test_대용량_결과_처리(): void
    {
        // Given
        $userId = 1;
        $limit = 1000; // 큰 limit 값

        // 대용량 컬렉션 시뮬레이션
        $largeCollection = new Collection;
        for ($i = 0; $i < $limit; $i++) {
            $template = new NotificationTemplate(['name' => "Template $i"]);
            $template->id = $i + 1;
            $largeCollection->push($template);
        }

        $this->templateRepository
            ->shouldReceive('getPopularTemplates')
            ->once()
            ->with($limit)
            ->andReturn($largeCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getPopularTemplates($userId, $limit);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount($limit, $result);
    }

    /**
     * 캐시 무효화 시나리오 테스트
     */
    public function test_캐시_무효화_시나리오(): void
    {
        // Given
        $userId = 1;

        // 첫 번째 호출
        $firstCollection = new Collection([new NotificationTemplate]);
        // 두 번째 호출 (캐시 무효화 후)
        $secondCollection = new Collection([new NotificationTemplate, new NotificationTemplate]);

        $this->templateRepository
            ->shouldReceive('getPopularTemplates')
            ->twice()
            ->with(10)
            ->andReturn($firstCollection, $secondCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->twice();

        // When
        $result1 = $this->service->getPopularTemplates($userId, 10);
        $result2 = $this->service->getPopularTemplates($userId, 10);

        // Then
        $this->assertCount(1, $result1);
        $this->assertCount(2, $result2);
    }

    /**
     * 네트워크 타임아웃 시뮬레이션 테스트
     */
    public function test_네트워크_타임아웃_시뮬레이션(): void
    {
        // Given
        $userId = 1;
        $search = 'timeout_test';

        $this->templateRepository
            ->shouldReceive('search')
            ->once()
            ->with($search, [])
            ->andThrow(new \Exception('Connection timeout'));

        $this->loggingService
            ->shouldReceive('logError')
            ->once()
            ->with('템플릿 검색 중 오류 발생', Mockery::type('array'));

        // When & Then
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('템플릿 검색 중 오류가 발생했습니다.');
        $this->service->searchTemplates($userId, $search);
    }

    /**
     * 데이터베이스 연결 실패 시뮬레이션 테스트
     */
    public function test_데이터베이스_연결_실패_시뮬레이션(): void
    {
        // Given
        $userId = 1;

        $this->templateRepository
            ->shouldReceive('getPopularTemplates')
            ->once()
            ->with(10)
            ->andThrow(new \Exception('Database connection failed'));

        $this->loggingService
            ->shouldReceive('logError')
            ->once()
            ->with('인기 템플릿 조회 중 오류 발생', Mockery::type('array'));

        // When & Then
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('인기 템플릿 조회 중 오류가 발생했습니다.');
        $this->service->getPopularTemplates($userId, 10);
    }

    /**
     * 잘못된 정렬 방향 처리 테스트
     */
    public function test_잘못된_정렬_방향_처리(): void
    {
        // Given
        $userId = 1;
        $invalidDirection = 'invalid_direction';

        $mockCollection = new Collection([]);

        $this->templateRepository
            ->shouldReceive('getOrderedByUsage')
            ->once()
            ->with($invalidDirection)
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getTemplatesOrderedByUsage($userId, $invalidDirection);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * 존재하지 않는 우선순위 처리 테스트
     */
    public function test_존재하지_않는_우선순위_처리(): void
    {
        // Given
        $userId = 1;
        $invalidPriority = 'nonexistent_priority';

        $emptyCollection = new Collection([]);

        $this->templateRepository
            ->shouldReceive('findByPriority')
            ->once()
            ->with($invalidPriority)
            ->andReturn($emptyCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getTemplatesByPriority($userId, $invalidPriority);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(0, $result);
    }

    /**
     * 극한 사용 횟수 범위 테스트
     */
    public function test_극한_사용_횟수_범위_테스트(): void
    {
        // Given
        $userId = 1;
        $minUsage = 0;
        $maxUsage = PHP_INT_MAX;

        $mockCollection = new Collection([]);

        $this->templateRepository
            ->shouldReceive('getTemplatesByUsageRange')
            ->once()
            ->with($minUsage, $maxUsage)
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getTemplatesByUsageRange($userId, $minUsage, $maxUsage);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }

    /**
     * 부동소수점 사용 횟수 처리 테스트
     */
    public function test_부동소수점_사용_횟수_처리(): void
    {
        // Given
        $userId = 1;
        $minUsage = 5.5; // 부동소수점 -> int로 변환되어 5가 됨
        $maxUsage = 10.9; // 부동소수점 -> int로 변환되어 10이 됨

        $mockCollection = new Collection([]);

        $this->templateRepository
            ->shouldReceive('getTemplatesByUsageRange')
            ->once()
            ->with(5, 10) // 정수로 변환된 값
            ->andReturn($mockCollection);

        $this->loggingService
            ->shouldReceive('logInfo')
            ->once();

        // When
        $result = $this->service->getTemplatesByUsageRange($userId, $minUsage, $maxUsage);

        // Then
        $this->assertInstanceOf(Collection::class, $result);
    }
}
