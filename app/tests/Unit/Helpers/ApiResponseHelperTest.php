<?php

use App\Helpers\ApiResponseHelper;
use Illuminate\Pagination\LengthAwarePaginator;

describe('ApiResponseHelper', function () {
    test('성공 응답을 올바르게 생성한다', function () {
        $data = ['id' => 1, 'name' => 'test'];
        $message = '테스트 성공';

        $response = ApiResponseHelper::success($data, $message);

        expect($response->getStatusCode())->toBe(200);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', true);
        expect($responseData)->toHaveKey('message', $message);
        expect($responseData)->toHaveKey('data', $data);
    });

    test('데이터 없이 성공 응답을 생성한다', function () {
        $response = ApiResponseHelper::success();

        expect($response->getStatusCode())->toBe(200);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', true);
        expect($responseData)->toHaveKey('message');
        expect($responseData)->not->toHaveKey('data');
    });

    test('페이지네이션된 성공 응답을 올바르게 생성한다', function () {
        $items = collect([
            ['id' => 1, 'name' => 'item1'],
            ['id' => 2, 'name' => 'item2'],
        ]);

        $paginator = new LengthAwarePaginator(
            $items,
            10,
            2,
            1,
            ['path' => 'http://localhost/api/templates']
        );

        $response = ApiResponseHelper::successWithPagination($paginator);

        expect($response->getStatusCode())->toBe(200);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', true);
        expect($responseData)->toHaveKey('data.items');
        expect($responseData)->toHaveKey('data.pagination');
        expect($responseData['data']['pagination'])->toHaveKey('current_page', 1);
        expect($responseData['data']['pagination'])->toHaveKey('per_page', 2);
        expect($responseData['data']['pagination'])->toHaveKey('total', 10);
    });

    test('생성 성공 응답을 올바르게 생성한다', function () {
        $data = ['id' => 1, 'name' => 'created'];

        $response = ApiResponseHelper::created($data);

        expect($response->getStatusCode())->toBe(201);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', true);
        expect($responseData)->toHaveKey('data', $data);
    });

    test('수정 성공 응답을 올바르게 생성한다', function () {
        $data = ['id' => 1, 'name' => 'updated'];

        $response = ApiResponseHelper::updated($data);

        expect($response->getStatusCode())->toBe(200);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', true);
        expect($responseData)->toHaveKey('data', $data);
    });

    test('삭제 성공 응답을 올바르게 생성한다', function () {
        $response = ApiResponseHelper::deleted();

        expect($response->getStatusCode())->toBe(200);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', true);
        expect($responseData)->not->toHaveKey('data');
    });

    test('에러 응답을 올바르게 생성한다', function () {
        $code = 'TEST_ERROR';
        $message = '테스트 에러';
        $details = ['field' => 'value'];

        $response = ApiResponseHelper::error($code, $message, $details, 400);

        expect($response->getStatusCode())->toBe(400);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', false);
        expect($responseData)->toHaveKey('error.code', $code);
        expect($responseData)->toHaveKey('error.message', $message);
        expect($responseData)->toHaveKey('error.details', $details);
    });

    test('유효성 검증 에러 응답을 올바르게 생성한다', function () {
        $errors = ['name' => ['이름은 필수입니다.']];

        $response = ApiResponseHelper::validationError($errors);

        expect($response->getStatusCode())->toBe(422);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', false);
        expect($responseData)->toHaveKey('error.code', 'VALIDATION_ERROR');
        expect($responseData)->toHaveKey('error.details.validation_errors', $errors);
    });

    test('인증 에러 응답을 올바르게 생성한다', function () {
        $response = ApiResponseHelper::unauthorized();

        expect($response->getStatusCode())->toBe(401);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', false);
        expect($responseData)->toHaveKey('error.code', 'UNAUTHORIZED');
    });

    test('권한 에러 응답을 올바르게 생성한다', function () {
        $response = ApiResponseHelper::forbidden();

        expect($response->getStatusCode())->toBe(403);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', false);
        expect($responseData)->toHaveKey('error.code', 'FORBIDDEN');
    });

    test('리소스 없음 에러 응답을 올바르게 생성한다', function () {
        $details = ['id' => 123];

        $response = ApiResponseHelper::notFound('템플릿을 찾을 수 없습니다.', $details);

        expect($response->getStatusCode())->toBe(404);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', false);
        expect($responseData)->toHaveKey('error.code', 'NOT_FOUND');
        expect($responseData)->toHaveKey('error.details', $details);
    });

    test('중복 리소스 에러 응답을 올바르게 생성한다', function () {
        $response = ApiResponseHelper::conflict();

        expect($response->getStatusCode())->toBe(409);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', false);
        expect($responseData)->toHaveKey('error.code', 'CONFLICT');
    });

    test('서버 내부 에러 응답을 올바르게 생성한다', function () {
        $response = ApiResponseHelper::internalServerError();

        expect($response->getStatusCode())->toBe(500);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', false);
        expect($responseData)->toHaveKey('error.code', 'INTERNAL_SERVER_ERROR');
    });

    test('템플릿 에러 응답을 올바르게 생성한다', function () {
        $message = '템플릿을 찾을 수 없습니다.';
        $details = ['template_id' => 123];

        $response = ApiResponseHelper::templateError('not_found', $message, $details);

        expect($response->getStatusCode())->toBe(404);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('success', false);
        expect($responseData)->toHaveKey('error.code', 'TEMPLATE_NOT_FOUND');
        expect($responseData)->toHaveKey('error.message', $message);
        expect($responseData)->toHaveKey('error.details', $details);
    });

    test('알 수 없는 템플릿 에러 타입에 대해 기본 에러를 반환한다', function () {
        $response = ApiResponseHelper::templateError('unknown_type', '알 수 없는 에러');

        expect($response->getStatusCode())->toBe(500);

        $responseData = $response->getData(true);
        expect($responseData)->toHaveKey('error.code', 'TEMPLATE_ERROR');
    });
});
