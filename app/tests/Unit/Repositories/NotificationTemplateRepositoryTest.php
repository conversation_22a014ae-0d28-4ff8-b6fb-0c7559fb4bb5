<?php

namespace Tests\Unit\Repositories;

use App\Models\NotificationTemplate;
use App\Models\User;
use App\Repositories\NotificationTemplateRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NotificationTemplateRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private NotificationTemplateRepository $repository;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new NotificationTemplateRepository;
        $this->user = User::factory()->create();
    }

    /** @test */
    public function it_can_create_a_template()
    {
        $data = [
            'name' => '테스트템플릿',
            'title' => '테스트 제목',
            'content' => '테스트 내용입니다.',
            'priority' => 'normal',
            'created_by' => $this->user->id,
        ];

        $template = $this->repository->create($data);

        $this->assertInstanceOf(NotificationTemplate::class, $template);
        $this->assertEquals('테스트템플릿', $template->name);
        $this->assertEquals('테스트 제목', $template->title);
        $this->assertEquals('테스트 내용입니다.', $template->content);
        $this->assertEquals('normal', $template->priority);
        $this->assertEquals($this->user->id, $template->created_by);
    }

    /** @test */
    public function it_can_find_template_by_id()
    {
        $template = NotificationTemplate::factory()->create([
            'created_by' => $this->user->id,
        ]);

        $found = $this->repository->findById($template->id);

        $this->assertInstanceOf(NotificationTemplate::class, $found);
        $this->assertEquals($template->id, $found->id);
    }

    /** @test */
    public function it_returns_null_when_template_not_found()
    {
        $found = $this->repository->findById(999);

        $this->assertNull($found);
    }

    /** @test */
    public function it_can_find_all_templates()
    {
        NotificationTemplate::factory()->count(3)->create([
            'created_by' => $this->user->id,
        ]);

        $templates = $this->repository->findAll();

        $this->assertCount(3, $templates);
    }

    /** @test */
    public function it_can_find_template_by_name()
    {
        $template = NotificationTemplate::factory()->create([
            'name' => '고유템플릿명',
            'created_by' => $this->user->id,
        ]);

        $found = $this->repository->findByName('고유템플릿명');

        $this->assertInstanceOf(NotificationTemplate::class, $found);
        $this->assertEquals($template->id, $found->id);
    }

    /** @test */
    public function it_can_find_templates_by_priority()
    {
        NotificationTemplate::factory()->create([
            'priority' => 'high',
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'priority' => 'normal',
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'priority' => 'high',
            'created_by' => $this->user->id,
        ]);

        $highPriorityTemplates = $this->repository->findByPriority('high');

        $this->assertCount(2, $highPriorityTemplates);
        $highPriorityTemplates->each(function ($template) {
            $this->assertEquals('high', $template->priority);
        });
    }

    /** @test */
    public function it_can_update_template()
    {
        $template = NotificationTemplate::factory()->create([
            'created_by' => $this->user->id,
        ]);

        $updateData = [
            'name' => '수정된템플릿명',
            'title' => '수정된 제목',
            'priority' => 'urgent',
        ];

        $updated = $this->repository->update($template->id, $updateData);

        $this->assertEquals('수정된템플릿명', $updated->name);
        $this->assertEquals('수정된 제목', $updated->title);
        $this->assertEquals('urgent', $updated->priority);
    }

    /** @test */
    public function it_can_delete_template()
    {
        $template = NotificationTemplate::factory()->create([
            'created_by' => $this->user->id,
        ]);

        $result = $this->repository->delete($template->id);

        $this->assertTrue($result);
        $this->assertDatabaseMissing('notification_templates', ['id' => $template->id]);
    }

    /** @test */
    public function it_can_get_paginated_templates()
    {
        NotificationTemplate::factory()->count(25)->create([
            'created_by' => $this->user->id,
        ]);

        $paginated = $this->repository->getPaginated(1, 10);

        $this->assertEquals(10, $paginated->count());
        $this->assertEquals(25, $paginated->total());
        $this->assertEquals(3, $paginated->lastPage());
    }

    /** @test */
    public function it_can_search_templates()
    {
        NotificationTemplate::factory()->create([
            'name' => '검색테스트',
            'title' => '검색 제목',
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'name' => '다른템플릿',
            'title' => '다른 제목',
            'created_by' => $this->user->id,
        ]);

        $results = $this->repository->search('검색');

        $this->assertCount(1, $results);
        $this->assertEquals('검색테스트', $results->first()->name);
    }

    /** @test */
    public function it_can_increment_usage_count()
    {
        $template = NotificationTemplate::factory()->create([
            'usage_count' => 5,
            'created_by' => $this->user->id,
        ]);

        $result = $this->repository->incrementUsage($template->id);

        $this->assertTrue($result);
        $template->refresh();
        $this->assertEquals(6, $template->usage_count);
    }

    /** @test */
    public function it_can_check_duplicate_name()
    {
        NotificationTemplate::factory()->create([
            'name' => '중복테스트',
            'created_by' => $this->user->id,
        ]);

        $isDuplicate = $this->repository->isDuplicateName('중복테스트');
        $isNotDuplicate = $this->repository->isDuplicateName('고유한이름');

        $this->assertTrue($isDuplicate);
        $this->assertFalse($isNotDuplicate);
    }

    /** @test */
    public function it_can_get_templates_ordered_by_usage()
    {
        NotificationTemplate::factory()->create([
            'usage_count' => 10,
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'usage_count' => 5,
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'usage_count' => 15,
            'created_by' => $this->user->id,
        ]);

        $templates = $this->repository->getOrderedByUsage('desc');

        $this->assertEquals(15, $templates->first()->usage_count);
        $this->assertEquals(5, $templates->last()->usage_count);
    }

    /** @test */
    public function it_can_get_templates_with_relations()
    {
        NotificationTemplate::factory()->create([
            'created_by' => $this->user->id,
        ]);

        $templates = $this->repository->getAllWithRelations();

        $this->assertCount(1, $templates);
        $this->assertTrue($templates->first()->relationLoaded('creator'));
    }

    /** @test */
    public function it_can_get_popular_templates()
    {
        NotificationTemplate::factory()->create([
            'usage_count' => 100,
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'usage_count' => 50,
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'usage_count' => 200,
            'created_by' => $this->user->id,
        ]);

        $popular = $this->repository->getPopularTemplates(2);

        $this->assertCount(2, $popular);
        $this->assertEquals(200, $popular->first()->usage_count);
        $this->assertEquals(100, $popular->last()->usage_count);
    }

    /** @test */
    public function it_can_get_unused_templates()
    {
        NotificationTemplate::factory()->create([
            'usage_count' => 0,
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'usage_count' => 5,
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'usage_count' => 0,
            'created_by' => $this->user->id,
        ]);

        $unused = $this->repository->getUnusedTemplates();

        $this->assertCount(2, $unused);
        $unused->each(function ($template) {
            $this->assertEquals(0, $template->usage_count);
        });
    }

    /** @test */
    public function it_can_get_usage_statistics()
    {
        NotificationTemplate::factory()->create([
            'usage_count' => 10,
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'usage_count' => 0,
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'usage_count' => 20,
            'created_by' => $this->user->id,
        ]);

        $stats = $this->repository->getUsageStatistics();

        $this->assertEquals(3, $stats['total_templates']);
        $this->assertEquals(30, $stats['total_usage']);
        $this->assertEquals(10, $stats['average_usage']);
        $this->assertEquals(1, $stats['unused_templates_count']);
        $this->assertEquals(66.67, $stats['usage_rate']);
        $this->assertNotNull($stats['most_used_template']);
        $this->assertEquals(20, $stats['most_used_template']['usage_count']);
    }

    /** @test */
    public function it_can_get_usage_distribution()
    {
        // 사용되지 않은 템플릿 (0회)
        NotificationTemplate::factory()->create([
            'usage_count' => 0,
            'created_by' => $this->user->id,
        ]);

        // 낮은 사용 (1-10회)
        NotificationTemplate::factory()->create([
            'usage_count' => 5,
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'usage_count' => 8,
            'created_by' => $this->user->id,
        ]);

        // 중간 사용 (11-50회)
        NotificationTemplate::factory()->create([
            'usage_count' => 25,
            'created_by' => $this->user->id,
        ]);

        // 높은 사용 (51-100회)
        NotificationTemplate::factory()->create([
            'usage_count' => 75,
            'created_by' => $this->user->id,
        ]);

        // 매우 높은 사용 (100회 초과)
        NotificationTemplate::factory()->create([
            'usage_count' => 150,
            'created_by' => $this->user->id,
        ]);

        $distribution = $this->repository->getUsageDistribution();

        $this->assertEquals(1, $distribution['unused']);
        $this->assertEquals(2, $distribution['low']);
        $this->assertEquals(1, $distribution['medium']);
        $this->assertEquals(1, $distribution['high']);
        $this->assertEquals(1, $distribution['very_high']);
    }

    /** @test */
    public function it_can_get_templates_by_usage_range()
    {
        NotificationTemplate::factory()->create([
            'usage_count' => 5,
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'usage_count' => 15,
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'usage_count' => 25,
            'created_by' => $this->user->id,
        ]);

        // 10회 이상 사용된 템플릿
        $highUsage = $this->repository->getTemplatesByUsageRange(10);
        $this->assertCount(2, $highUsage);

        // 10-20회 사용된 템플릿
        $mediumUsage = $this->repository->getTemplatesByUsageRange(10, 20);
        $this->assertCount(1, $mediumUsage);
        $this->assertEquals(15, $mediumUsage->first()->usage_count);
    }

    /** @test */
    public function it_can_get_count_by_priority()
    {
        NotificationTemplate::factory()->create([
            'priority' => 'high',
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'priority' => 'high',
            'created_by' => $this->user->id,
        ]);
        NotificationTemplate::factory()->create([
            'priority' => 'normal',
            'created_by' => $this->user->id,
        ]);

        $counts = $this->repository->getCountByPriority();

        $this->assertEquals(2, $counts['high']);
        $this->assertEquals(1, $counts['normal']);
        $this->assertEquals(0, $counts['low']);
        $this->assertEquals(0, $counts['urgent']);
    }
}
