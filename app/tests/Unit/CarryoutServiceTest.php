<?php

namespace Tests\Unit;

use App\Exceptions\CarryoutException;
use App\Models\CarryoutProduct;
use App\Models\Product;
use App\Models\User;
use App\Services\CarryoutService;
use App\Services\WorkStatusService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CarryoutServiceTest extends TestCase
{
    use RefreshDatabase;

    private CarryoutService $carryoutService;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $workStatusService = $this->createMock(WorkStatusService::class);
        $workStatusService->method('getIds')->willReturn([]);

        $this->carryoutService = new CarryoutService($workStatusService);
        $this->user = User::factory()->create();
    }

    public function test_find_product_throws_exception_when_product_not_found()
    {
        $this->expectException(\App\Exceptions\ResourceNotFoundException::class);
        $this->expectExceptionMessage('해당 상품 [NONEXISTENT]이 존재하지 않습니다.');

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->carryoutService);
        $method = $reflection->getMethod('findProduct');
        $method->setAccessible(true);

        $method->invoke($this->carryoutService, 'NONEXISTENT');
    }

    public function test_find_product_returns_product_when_found()
    {
        $product = Product::factory()->create(['qaid' => 'TEST123']);

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->carryoutService);
        $method = $reflection->getMethod('findProduct');
        $method->setAccessible(true);

        $result = $method->invoke($this->carryoutService, 'test123'); // Test case insensitive

        $this->assertEquals($product->id, $result->id);
        $this->assertEquals('TEST123', $result->qaid);
    }

    public function test_check_carryout_duplicate_throws_exception_when_already_carried_out()
    {
        $product = Product::factory()->create(['qaid' => 'TEST123']);
        CarryoutProduct::factory()->create(['product_id' => $product->id]);

        $this->expectException(CarryoutException::class);
        $this->expectExceptionMessage('해당 상품 [TEST123]은 이미 외주 반출 처리된 상품입니다.');

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->carryoutService);
        $method = $reflection->getMethod('checkCarryoutDuplicate');
        $method->setAccessible(true);

        $method->invoke($this->carryoutService, $product);
    }

    public function test_check_carryout_duplicate_passes_when_not_carried_out()
    {
        $product = Product::factory()->create(['qaid' => 'TEST123']);

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->carryoutService);
        $method = $reflection->getMethod('checkCarryoutDuplicate');
        $method->setAccessible(true);

        // Should not throw exception
        $method->invoke($this->carryoutService, $product);

        $this->assertTrue(true); // Test passes if no exception is thrown
    }

    public function test_validate_product_for_export_throws_exception_when_invalid_status()
    {
        $product = Product::factory()->create([
            'qaid' => 'TEST123',
            'status' => Product::STATUS_CARRIED_OUT, // Invalid status for export
        ]);

        $this->expectException(CarryoutException::class);
        $this->expectExceptionMessage('해당 상품 [TEST123]이 없거나 이미 점검완료(출고대기)된 상품입니다.');

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->carryoutService);
        $method = $reflection->getMethod('validateProductForExport');
        $method->setAccessible(true);

        $method->invoke($this->carryoutService, $product, 'TEST123');
    }

    public function test_validate_product_for_export_passes_when_valid_status()
    {
        $product = Product::factory()->create([
            'qaid' => 'TEST123',
            'status' => Product::STATUS_REGISTERED, // Valid status for export
        ]);

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->carryoutService);
        $method = $reflection->getMethod('validateProductForExport');
        $method->setAccessible(true);

        // Should not throw exception
        $method->invoke($this->carryoutService, $product, 'TEST123');

        $this->assertTrue(true); // Test passes if no exception is thrown
    }

    public function test_find_product_by_status_throws_exception_when_not_found()
    {
        $this->expectException(CarryoutException::class);

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->carryoutService);
        $method = $reflection->getMethod('findProductByStatus');
        $method->setAccessible(true);

        $method->invoke($this->carryoutService, 'NONEXISTENT', Product::STATUS_CARRIED_OUT);
    }

    public function test_find_product_by_status_returns_product_when_found()
    {
        $product = Product::factory()->create([
            'qaid' => 'TEST123',
            'status' => Product::STATUS_CARRIED_OUT,
        ]);

        // Use reflection to access private method
        $reflection = new \ReflectionClass($this->carryoutService);
        $method = $reflection->getMethod('findProductByStatus');
        $method->setAccessible(true);

        $result = $method->invoke($this->carryoutService, 'TEST123', Product::STATUS_CARRIED_OUT);

        $this->assertEquals($product->id, $result->id);
        $this->assertEquals('TEST123', $result->qaid);
    }
}
