<?php

namespace Tests\Unit\Http\Requests\NotificationTemplate;

use App\Http\Requests\NotificationTemplate\StoreRequest;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

/**
 * 알림 템플릿 생성 요청 유효성 검증 테스트
 */
class StoreRequestTest extends TestCase
{
    use RefreshDatabase;

    private StoreRequest $request;

    private User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->request = new StoreRequest;

        // 관리자 사용자 생성
        $this->adminUser = User::factory()->create([
            'role' => User::ROLE_ADMIN,
        ]);
    }

    /**
     * 유효한 데이터로 검증 성공 테스트
     */
    public function test_valid_data_passes_validation(): void
    {
        $data = [
            'name' => '테스트템플릿',
            'title' => '테스트 제목',
            'content' => '테스트 내용입니다.',
            'priority' => 'normal',
        ];

        $validator = Validator::make($data, $this->request->rules());

        $this->assertTrue($validator->passes());
    }

    /**
     * 필수 필드 누락 시 검증 실패 테스트
     */
    public function test_required_fields_validation(): void
    {
        $testCases = [
            ['value' => null, 'expectedError' => 'name'],
            ['value' => null, 'expectedError' => 'title'],
            ['value' => null, 'expectedError' => 'content'],
        ];

        foreach ($testCases as $testCase) {
            $data = [
                'name' => '테스트템플릿',
                'title' => '테스트 제목',
                'content' => '테스트 내용',
                'priority' => 'normal',
            ];

            $data[$testCase['expectedError']] = $testCase['value'];

            $validator = Validator::make($data, $this->request->rules());

            $this->assertTrue($validator->fails());
            $this->assertTrue($validator->errors()->has($testCase['expectedError']));
        }
    }

    /**
     * 문자열 길이 제한 테스트
     */
    public function test_string_length_validation(): void
    {
        $testCases = [
            [
                'field' => 'name',
                'value' => str_repeat('a', 51), // 50자 초과
                'shouldFail' => true,
            ],
            [
                'field' => 'title',
                'value' => str_repeat('a', 201), // 200자 초과
                'shouldFail' => true,
            ],
            [
                'field' => 'content',
                'value' => str_repeat('a', 5001), // 5000자 초과
                'shouldFail' => true,
            ],
            [
                'field' => 'name',
                'value' => str_repeat('a', 50), // 정확히 50자
                'shouldFail' => false,
            ],
        ];

        foreach ($testCases as $testCase) {
            $data = [
                'name' => '테스트템플릿',
                'title' => '테스트 제목',
                'content' => '테스트 내용',
                'priority' => 'normal',
            ];

            $data[$testCase['field']] = $testCase['value'];

            $validator = Validator::make($data, $this->request->rules());

            if ($testCase['shouldFail']) {
                $this->assertTrue($validator->fails());
                $this->assertTrue($validator->errors()->has($testCase['field']));
            } else {
                $this->assertFalse($validator->errors()->has($testCase['field']));
            }
        }
    }

    /**
     * 우선순위 값 검증 테스트
     */
    public function test_priority_validation(): void
    {
        $validPriorities = ['low', 'normal', 'high', 'urgent'];
        $invalidPriorities = ['invalid', 'test', '123', ''];

        // 유효한 우선순위 테스트
        foreach ($validPriorities as $priority) {
            $data = [
                'name' => '테스트템플릿',
                'title' => '테스트 제목',
                'content' => '테스트 내용',
                'priority' => $priority,
            ];

            $validator = Validator::make($data, $this->request->rules());
            $this->assertFalse($validator->errors()->has('priority'));
        }

        // 무효한 우선순위 테스트
        foreach ($invalidPriorities as $priority) {
            $data = [
                'name' => '테스트템플릿',
                'title' => '테스트 제목',
                'content' => '테스트 내용',
                'priority' => $priority,
            ];

            $validator = Validator::make($data, $this->request->rules());

            if ($priority !== '') { // 빈 문자열은 sometimes 규칙으로 인해 통과
                $this->assertTrue($validator->fails());
                $this->assertTrue($validator->errors()->has('priority'));
            }
        }
    }

    /**
     * 템플릿명 정규식 검증 테스트
     */
    public function test_template_name_regex_validation(): void
    {
        $validNames = [
            '테스트템플릿',
            'TestTemplate',
            '테스트_템플릿',
            '테스트-템플릿',
            'Test Template 123',
            '긴급알림템플릿_v1',
        ];

        $invalidNames = [
            'test@template',
            'test#template',
            'test$template',
            'test%template',
            'test&template',
            'test*template',
            'test+template',
            'test=template',
            'test|template',
        ];

        // 유효한 템플릿명 테스트
        foreach ($validNames as $name) {
            $data = [
                'name' => $name,
                'title' => '테스트 제목',
                'content' => '테스트 내용',
                'priority' => 'normal',
            ];

            $validator = Validator::make($data, $this->request->rules());
            $this->assertFalse($validator->errors()->has('name'), "Valid name '{$name}' should pass validation");
        }

        // 무효한 템플릿명 테스트
        foreach ($invalidNames as $name) {
            $data = [
                'name' => $name,
                'title' => '테스트 제목',
                'content' => '테스트 내용',
                'priority' => 'normal',
            ];

            $validator = Validator::make($data, $this->request->rules());
            $this->assertTrue($validator->fails());
            $this->assertTrue($validator->errors()->has('name'), "Invalid name '{$name}' should fail validation");
        }
    }

    /**
     * 권한 검증 테스트
     */
    public function test_authorization(): void
    {
        // 관리자 권한 테스트
        $this->actingAs($this->adminUser);
        $this->assertTrue($this->request->authorize());

        // Super-Admin 권한 테스트
        $superAdminUser = User::factory()->create(['role' => 'Super-Admin']);
        $this->actingAs($superAdminUser);
        $this->assertTrue($this->request->authorize());

        // 일반 사용자 권한 테스트
        $normalUser = User::factory()->create(['role' => User::ROLE_EMPLOYEE]);
        $this->actingAs($normalUser);
        $this->assertFalse($this->request->authorize());

        // 인증되지 않은 사용자 테스트
        auth()->logout();
        $this->assertFalse($this->request->authorize());
    }
}
