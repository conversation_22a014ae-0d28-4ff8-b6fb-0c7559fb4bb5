<?php

use App\Http\Middleware\ErrorHandlingMiddleware;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

describe('ErrorHandlingMiddleware', function () {
    beforeEach(function () {
        $this->middleware = new ErrorHandlingMiddleware;
    });

    test('JSON 요청이 아닌 경우 응답을 그대로 반환한다', function () {
        $request = Request::create('/test', 'GET');
        $request->headers->set('Accept', 'text/html');

        $response = new Response('HTML Content');

        $next = function () use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        expect($result)->toBe($response);
    });

    test('이미 표준화된 JSON 응답은 그대로 반환한다', function () {
        $request = Request::create('/test', 'GET');
        $request->headers->set('Accept', 'application/json');

        $responseData = [
            'success' => true,
            'message' => '이미 표준화됨',
            'data' => ['id' => 1],
        ];

        $response = new JsonResponse($responseData);

        $next = function () use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        expect($result)->toBe($response);
        expect($result->getData(true))->toBe($responseData);
    });

    test('성공 응답을 표준화된 형식으로 변환한다', function () {
        $request = Request::create('/test', 'GET');
        $request->headers->set('Accept', 'application/json');

        $originalData = ['id' => 1, 'name' => 'test'];
        $response = new JsonResponse($originalData, 200);

        $next = function () use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        expect($result)->toBeInstanceOf(JsonResponse::class);
        expect($result->getStatusCode())->toBe(200);

        $resultData = $result->getData(true);
        expect($resultData)->toHaveKey('success', true);
        expect($resultData)->toHaveKey('message', '요청이 성공적으로 처리되었습니다.');
        expect($resultData)->toHaveKey('data', $originalData);
    });

    test('201 상태 코드에 대해 적절한 메시지를 설정한다', function () {
        $request = Request::create('/test', 'POST');
        $request->headers->set('Accept', 'application/json');

        $originalData = ['id' => 1, 'name' => 'created'];
        $response = new JsonResponse($originalData, 201);

        $next = function () use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        $resultData = $result->getData(true);
        expect($resultData)->toHaveKey('message', '리소스가 성공적으로 생성되었습니다.');
    });

    test('202 상태 코드에 대해 적절한 메시지를 설정한다', function () {
        $request = Request::create('/test', 'POST');
        $request->headers->set('Accept', 'application/json');

        $originalData = ['status' => 'accepted'];
        $response = new JsonResponse($originalData, 202);

        $next = function () use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        $resultData = $result->getData(true);
        expect($resultData)->toHaveKey('message', '요청이 접수되었습니다.');
    });

    test('204 상태 코드에 대해 적절한 메시지를 설정한다', function () {
        $request = Request::create('/test', 'DELETE');
        $request->headers->set('Accept', 'application/json');

        $response = new JsonResponse(null, 204);

        $next = function () use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        $resultData = $result->getData(true);
        expect($resultData)->toHaveKey('message', '요청이 성공적으로 처리되었습니다.');
    });

    test('예외가 발생하면 다시 던진다', function () {
        $request = Request::create('/test', 'GET');
        $request->headers->set('Accept', 'application/json');

        $exception = new \Exception('Test exception');

        $next = function () use ($exception) {
            throw $exception;
        };

        expect(fn () => $this->middleware->handle($request, $next))
            ->toThrow(\Exception::class, 'Test exception');
    });

    test('에러 응답은 변환하지 않는다', function () {
        $request = Request::create('/test', 'GET');
        $request->headers->set('Accept', 'application/json');

        $errorData = [
            'success' => false,
            'error' => [
                'code' => 'TEST_ERROR',
                'message' => 'Test error',
            ],
        ];

        $response = new JsonResponse($errorData, 400);

        $next = function () use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        expect($result)->toBe($response);
        expect($result->getData(true))->toBe($errorData);
    });
});
