<?php

namespace Tests\Unit\Rules\NotificationTemplate;

use App\Rules\NotificationTemplate\ValidTemplateContentRule;
use Tests\TestCase;

/**
 * 템플릿 내용 유효성 검증 규칙 테스트
 */
class ValidTemplateContentRuleTest extends TestCase
{
    private ValidTemplateContentRule $rule;

    protected function setUp(): void
    {
        parent::setUp();
        $this->rule = new ValidTemplateContentRule;
    }

    /**
     * 유효한 템플릿 내용 테스트
     */
    public function test_valid_template_content(): void
    {
        $validContents = [
            '일반 텍스트 내용입니다.',
            '<p>단락 태그가 포함된 내용</p>',
            '<div><span>중첩된 태그</span></div>',
            '<strong>강조</strong>된 <em>텍스트</em>',
            '<ul><li>목록 항목</li></ul>',
            '<a href="https://example.com">링크</a>',
            '<img src="image.jpg" alt="이미지">',
            '특수문자: !@#$%^&*()+=[]{}|;:,.<>?',
        ];

        foreach ($validContents as $content) {
            $failed = false;
            $this->rule->validate('content', $content, function ($message) use (&$failed) {
                $failed = true;
            });

            $this->assertFalse($failed, "Valid content should pass validation: {$content}");
        }
    }

    /**
     * 금지된 HTML 태그 테스트
     */
    public function test_forbidden_html_tags(): void
    {
        $forbiddenTags = [
            'script' => '<script>alert("xss")</script>',
            'iframe' => '<iframe src="malicious.html"></iframe>',
            'object' => '<object data="malicious.swf"></object>',
            'embed' => '<embed src="malicious.swf">',
            'form' => '<form action="/submit"></form>',
            'input' => '<input type="text" name="test">',
            'button' => '<button>클릭</button>',
            'select' => '<select><option>옵션</option></select>',
            'textarea' => '<textarea>텍스트</textarea>',
            'link' => '<link rel="stylesheet" href="style.css">',
            'meta' => '<meta charset="utf-8">',
            'style' => '<style>body{color:red;}</style>',
        ];

        foreach ($forbiddenTags as $tagName => $content) {
            $failed = false;
            $errorMessage = '';

            $this->rule->validate('content', $content, function ($message) use (&$failed, &$errorMessage) {
                $failed = true;
                $errorMessage = $message;
            });

            $this->assertTrue($failed, "Forbidden tag '{$tagName}' should fail validation");
            $this->assertStringContainsString($tagName, $errorMessage);
            $this->assertStringContainsString('허용되지 않는 HTML 태그', $errorMessage);
        }
    }

    /**
     * 대소문자 구분 없는 태그 검사 테스트
     */
    public function test_case_insensitive_tag_detection(): void
    {
        $caseVariations = [
            '<SCRIPT>alert("xss")</SCRIPT>',
            '<Script>alert("xss")</Script>',
            '<sCrIpT>alert("xss")</ScRiPt>',
            '<iframe SRC="test.html"></iframe>',
            '<IFRAME src="test.html"></IFRAME>',
        ];

        foreach ($caseVariations as $content) {
            $failed = false;

            $this->rule->validate('content', $content, function ($message) use (&$failed) {
                $failed = true;
            });

            $this->assertTrue($failed, "Case variation should be detected: {$content}");
        }
    }

    /**
     * JavaScript 이벤트 핸들러 테스트
     */
    public function test_javascript_event_handlers(): void
    {
        $eventHandlers = [
            'onclick' => '<div onclick="alert()">클릭</div>',
            'onload' => '<img onload="malicious()" src="image.jpg">',
            'onmouseover' => '<span onmouseover="hack()">마우스오버</span>',
            'onmouseout' => '<div onmouseout="evil()">마우스아웃</div>',
            'onfocus' => '<input onfocus="steal()">',
            'onblur' => '<input onblur="phish()">',
            'onchange' => '<select onchange="malware()"></select>',
            'onsubmit' => '<form onsubmit="virus()"></form>',
            'onkeydown' => '<input onkeydown="keylogger()">',
            'onkeyup' => '<input onkeyup="spyware()">',
            'onkeypress' => '<input onkeypress="trojan()">',
        ];

        foreach ($eventHandlers as $eventName => $content) {
            $failed = false;
            $errorMessage = '';

            $this->rule->validate('content', $content, function ($message) use (&$failed, &$errorMessage) {
                $failed = true;
                $errorMessage = $message;
            });

            $this->assertTrue($failed, "Event handler '{$eventName}' should fail validation");
            $this->assertStringContainsString($eventName, $errorMessage);
            $this->assertStringContainsString('JavaScript 이벤트 핸들러', $errorMessage);
        }
    }

    /**
     * JavaScript 프로토콜 테스트
     */
    public function test_javascript_protocol(): void
    {
        $javascriptProtocols = [
            '<a href="javascript:alert()">링크</a>',
            '<img src="javascript:malicious()">',
            '<div style="background: url(javascript:hack())">',
            'javascript:void(0)',
            'JAVASCRIPT:alert("XSS")',
            'JavaScript:malicious()',
            'java script:alert()', // 공백 포함
            "java\tscript:alert()", // 탭 포함
        ];

        foreach ($javascriptProtocols as $content) {
            $failed = false;
            $errorMessage = '';

            $this->rule->validate('content', $content, function ($message) use (&$failed, &$errorMessage) {
                $failed = true;
                $errorMessage = $message;
            });

            $this->assertTrue($failed, "JavaScript protocol should fail validation: {$content}");
            $this->assertStringContainsString('JavaScript 코드', $errorMessage);
        }
    }

    /**
     * 과도한 HTML 엔티티 테스트
     */
    public function test_excessive_html_entities(): void
    {
        // 51개의 HTML 엔티티 생성 (제한: 50개)
        $excessiveEntities = str_repeat('&#65;', 51); // 'A' 문자를 51번 반복

        $failed = false;
        $errorMessage = '';

        $this->rule->validate('content', $excessiveEntities, function ($message) use (&$failed, &$errorMessage) {
            $failed = true;
            $errorMessage = $message;
        });

        $this->assertTrue($failed, 'Excessive HTML entities should fail validation');
        $this->assertStringContainsString('과도한 HTML 엔티티', $errorMessage);

        // 정상 범위의 엔티티는 통과해야 함
        $normalEntities = str_repeat('&#65;', 50); // 정확히 50개

        $failed = false;
        $this->rule->validate('content', $normalEntities, function ($message) use (&$failed) {
            $failed = true;
        });

        $this->assertFalse($failed, 'Normal amount of HTML entities should pass validation');
    }

    /**
     * 공백이 포함된 태그 테스트
     */
    public function test_tags_with_whitespace(): void
    {
        $tagsWithWhitespace = [
            '< script>alert("xss")</script>',
            '<script >alert("xss")</script>',
            '< script >alert("xss")</script>',
            "<\tscript>alert(\"xss\")</script>", // 실제 탭 문자
            "<\nscript>alert(\"xss\")</script>", // 실제 개행 문자
        ];

        foreach ($tagsWithWhitespace as $content) {
            $failed = false;

            $this->rule->validate('content', $content, function ($message) use (&$failed) {
                $failed = true;
            });

            $this->assertTrue($failed, "Tag with whitespace should be detected: {$content}");
        }
    }

    /**
     * 비문자열 입력 테스트
     */
    public function test_non_string_input(): void
    {
        $nonStringInputs = [
            123,
            null,
            [],
            new \stdClass,
            true,
            false,
        ];

        foreach ($nonStringInputs as $input) {
            $failed = false;

            $this->rule->validate('content', $input, function ($message) use (&$failed) {
                $failed = true;
            });

            // 비문자열 입력은 규칙에서 early return되어 통과해야 함
            $this->assertFalse($failed, 'Non-string input should be ignored by the rule');
        }
    }
}
