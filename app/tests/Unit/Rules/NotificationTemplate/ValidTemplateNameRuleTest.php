<?php

namespace Tests\Unit\Rules\NotificationTemplate;

use App\Rules\NotificationTemplate\ValidTemplateNameRule;
use Tests\TestCase;

/**
 * 템플릿명 유효성 검증 규칙 테스트
 */
class ValidTemplateNameRuleTest extends TestCase
{
    private ValidTemplateNameRule $rule;

    protected function setUp(): void
    {
        parent::setUp();
        $this->rule = new ValidTemplateNameRule;
    }

    /**
     * 유효한 템플릿명 테스트
     */
    public function test_valid_template_names(): void
    {
        $validNames = [
            '일반템플릿',
            'NormalTemplate',
            '템플릿_v1',
            '템플릿-버전1',
            'Template 123',
            '긴급알림',
            'Emergency_Alert',
        ];

        foreach ($validNames as $name) {
            $failed = false;
            $this->rule->validate('name', $name, function ($message) use (&$failed) {
                $failed = true;
            });

            $this->assertFalse($failed, "Valid name '{$name}' should pass validation");
        }
    }

    /**
     * 시스템 예약어 테스트
     */
    public function test_reserved_words(): void
    {
        $reservedWords = [
            'system',
            'admin',
            'root',
            'default',
            'template',
            'notification',
            'SYSTEM', // 대소문자 구분 없음
            'Admin',
            'ROOT',
        ];

        foreach ($reservedWords as $word) {
            $failed = false;
            $errorMessage = '';

            $this->rule->validate('name', $word, function ($message) use (&$failed, &$errorMessage) {
                $failed = true;
                $errorMessage = $message;
            });

            $this->assertTrue($failed, "Reserved word '{$word}' should fail validation");
            $this->assertStringContainsString('시스템 예약어', $errorMessage);
        }
    }

    /**
     * 빈 문자열 및 공백 테스트
     */
    public function test_empty_and_whitespace(): void
    {
        $invalidNames = [
            '',
            '   ',
            "\t",
            "\n",
        ];

        foreach ($invalidNames as $name) {
            $failed = false;
            $errorMessage = '';

            $this->rule->validate('name', $name, function ($message) use (&$failed, &$errorMessage) {
                $failed = true;
                $errorMessage = $message;
            });

            $this->assertTrue($failed, 'Empty/whitespace name should fail validation');
            $this->assertStringContainsString('공백만으로 구성될 수 없습니다', $errorMessage, "Actual error message: {$errorMessage}");
        }
    }

    /**
     * 복합 공백 문자 테스트
     */
    public function test_complex_whitespace(): void
    {
        $complexWhitespaceNames = [
            '  \t  \n  ', // 공백, 탭, 개행의 조합
            "\r\n\t   ", // 캐리지 리턴, 개행, 탭, 공백
        ];

        foreach ($complexWhitespaceNames as $name) {
            $failed = false;
            $errorMessage = '';

            $this->rule->validate('name', $name, function ($message) use (&$failed, &$errorMessage) {
                $failed = true;
                $errorMessage = $message;
            });

            $this->assertTrue($failed, 'Complex whitespace name should fail validation');
            // 복합 공백의 경우 어떤 에러든 상관없음 (공백만 구성 또는 앞뒤 공백)
            $this->assertTrue(
                str_contains($errorMessage, '공백만으로 구성될 수 없습니다') ||
                str_contains($errorMessage, '앞뒤에 공백은 사용할 수 없습니다'),
                "Should fail with whitespace-related error, got: {$errorMessage}"
            );
        }
    }

    /**
     * 연속된 공백 테스트
     */
    public function test_consecutive_spaces(): void
    {
        $invalidNames = [
            '템플릿  이름',
            'Template   Name',
            '테스트    템플릿',
        ];

        foreach ($invalidNames as $name) {
            $failed = false;
            $errorMessage = '';

            $this->rule->validate('name', $name, function ($message) use (&$failed, &$errorMessage) {
                $failed = true;
                $errorMessage = $message;
            });

            $this->assertTrue($failed, 'Name with consecutive spaces should fail validation');
            $this->assertStringContainsString('연속된 공백', $errorMessage);
        }
    }

    /**
     * 앞뒤 공백 테스트
     */
    public function test_leading_trailing_spaces(): void
    {
        $invalidNames = [
            ' 템플릿',
            '템플릿 ',
            ' 템플릿 ',
            '  템플릿이름  ',
        ];

        foreach ($invalidNames as $name) {
            $failed = false;
            $errorMessage = '';

            $this->rule->validate('name', $name, function ($message) use (&$failed, &$errorMessage) {
                $failed = true;
                $errorMessage = $message;
            });

            $this->assertTrue($failed, 'Name with leading/trailing spaces should fail validation');
            $this->assertStringContainsString('앞뒤에 공백', $errorMessage);
        }
    }

    /**
     * 연속된 특수문자 테스트
     */
    public function test_consecutive_special_characters(): void
    {
        $invalidNames = [
            '템플릿--이름',
            'Template__Name',
            '테스트---템플릿',
            '이름____버전',
        ];

        foreach ($invalidNames as $name) {
            $failed = false;
            $errorMessage = '';

            $this->rule->validate('name', $name, function ($message) use (&$failed, &$errorMessage) {
                $failed = true;
                $errorMessage = $message;
            });

            $this->assertTrue($failed, 'Name with consecutive special characters should fail validation');
            $this->assertStringContainsString('연속된 특수문자', $errorMessage);
        }
    }

    /**
     * 숫자로만 구성된 이름 테스트
     */
    public function test_numeric_only_names(): void
    {
        $invalidNames = [
            '123',
            '456789',
            '0',
            '999999',
        ];

        foreach ($invalidNames as $name) {
            $failed = false;
            $errorMessage = '';

            $this->rule->validate('name', $name, function ($message) use (&$failed, &$errorMessage) {
                $failed = true;
                $errorMessage = $message;
            });

            $this->assertTrue($failed, 'Numeric-only name should fail validation');
            $this->assertStringContainsString('숫자로만 구성될 수 없습니다', $errorMessage, "Actual error message: {$errorMessage}");
        }
    }

    /**
     * 비문자열 입력 테스트
     */
    public function test_non_string_input(): void
    {
        $nonStringInputs = [
            123,
            null,
            [],
            new \stdClass,
            true,
            false,
        ];

        foreach ($nonStringInputs as $input) {
            $failed = false;

            $this->rule->validate('name', $input, function ($message) use (&$failed) {
                $failed = true;
            });

            // 비문자열 입력은 규칙에서 early return되어 통과해야 함
            $this->assertFalse($failed, 'Non-string input should be ignored by the rule');
        }
    }
}
