<?php

use App\Models\Location;
use App\Models\Pallet;
use Illuminate\Foundation\Testing\RefreshDatabase;

// 테스트마다 DB를 초기화합니다.
// uses(RefreshDatabase::class);

it('Location과 Pallet의 관계가 정상적으로 동작한다', function () {
    // 테스트용 Location 생성
    $location = Location::factory()->create();

    // 해당 Location에 Pallet 2개 생성
    $pallet1 = Pallet::factory()->create(['location_id' => $location->id]);
    $pallet2 = Pallet::factory()->create(['location_id' => $location->id]);

    // 관계 확인: Location에서 pallets()로 접근
    expect($location->pallets)->toHaveCount(2);
    expect($location->pallets->first()->id)->toBe($pallet1->id);
});
