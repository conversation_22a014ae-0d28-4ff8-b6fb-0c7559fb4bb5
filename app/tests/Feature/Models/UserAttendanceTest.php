<?php

use App\Models\User;
use App\Models\UserAttendance;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

it('근태 생성 및 사용자 관계 확인', function () {
    $attendance = UserAttendance::factory()->create();
    expect($attendance->user)->not->toBeNull();
    expect($attendance->user)->toBeInstanceOf(User::class);
});

it('여러 근태 생성 시 user_id별로 조회 가능', function () {
    $user = User::factory()->create();
    UserAttendance::factory()->count(3)->create(['user_id' => $user->id]);
    expect($user->attendances)->toHaveCount(3);
});
