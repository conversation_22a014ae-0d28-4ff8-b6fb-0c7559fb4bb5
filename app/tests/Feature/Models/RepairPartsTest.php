<?php

use App\Models\RepairParts;
use App\Models\RepairPartsCategory;
use App\Models\RepairPartsLog;
use App\Models\RepairPartsOrder;

beforeEach(function () {
    $this->category = RepairPartsCategory::factory()->create();
});

describe('RepairParts 모델 테스트', function () {
    it('구성품을 생성할 수 있다', function () {
        $part = RepairParts::factory()->create([
            'category_id' => $this->category->id,
            'barcode' => '1234567890123',
            'name' => '테스트 구성품',
            'model_number' => 'TEST001',
            'price' => 50000,
            'stock' => 10,
            'reorder_stock' => 5,
            'acc_count' => 100,
            'is_purchasable' => 'Y',
        ]);

        expect($part)->toBeInstanceOf(RepairParts::class);
        expect($part->category_id)->toBe($this->category->id);
        expect($part->barcode)->toBe('1234567890123');
        expect($part->name)->toBe('테스트 구성품');
        expect($part->price)->toBe(50000);
        expect($part->stock)->toBe(10);
    });

    it('카테고리와의 관계가 올바르게 작동한다', function () {
        $part = RepairParts::factory()->create([
            'category_id' => $this->category->id,
        ]);

        expect($part->category)->toBeInstanceOf(RepairPartsCategory::class);
        expect($part->category->id)->toBe($this->category->id);
    });

    it('로그들과의 관계가 올바르게 작동한다', function () {
        $part = RepairParts::factory()->create();

        $logs = RepairPartsLog::factory()->count(3)->create([
            'repair_parts_id' => $part->id,
        ]);

        expect($part->logs)->toHaveCount(3);
        expect($part->logs->first())->toBeInstanceOf(RepairPartsLog::class);
    });

    it('주문들과의 관계가 올바르게 작동한다', function () {
        $part = RepairParts::factory()->create();

        $orders = RepairPartsOrder::factory()->count(2)->create([
            'repair_parts_id' => $part->id,
        ]);

        expect($part->orders)->toHaveCount(2);
        expect($part->orders->first())->toBeInstanceOf(RepairPartsOrder::class);
    });

    it('액세서가 올바르게 작동한다', function () {
        $part = RepairParts::factory()->create([
            'price' => 50000,
            'is_purchasable' => 'Y',
            'stock' => 5,
            'reorder_stock' => 10,
            'location_area' => 'A동',
            'location_zone' => 'A구역',
            'location_floor' => 2,
            'location_position' => 15,
        ]);

        // 가격 포맷팅
        expect($part->formatted_price)->toBe('50,000원');

        // 구매 가능 여부
        expect($part->is_purchasable)->toBeTrue();

        // 재고 부족 여부
        expect($part->is_low_stock)->toBeTrue();

        // 재고 상태
        expect($part->stock_status)->toBe('재고부족');

        // 위치 정보
        expect($part->location)->toBe('A동 A구역 2층 15번');
    });

    it('재고 상태가 올바르게 판단된다', function () {
        // 품절 상태
        $outOfStock = RepairParts::factory()->outOfStock()->create();
        expect($outOfStock->stock_status)->toBe('품절');
        expect($outOfStock->is_low_stock)->toBeTrue();

        // 재고 부족 상태
        $lowStock = RepairParts::factory()->lowStock()->create();
        expect($lowStock->stock_status)->toBe('재고부족');
        expect($lowStock->is_low_stock)->toBeTrue();

        // 재고 있음 상태
        $inStock = RepairParts::factory()->create([
            'stock' => 20,
            'reorder_stock' => 10,
        ]);
        expect($inStock->stock_status)->toBe('재고있음');
        expect($inStock->is_low_stock)->toBeFalse();
    });

    it('뮤테이터가 올바르게 작동한다', function () {
        $part = RepairParts::factory()->create();

        // 구매 가능으로 설정
        $part->is_purchasable = true;
        expect($part->attributes['is_purchasable'])->toBe('Y');

        // 구매 불가능으로 설정
        $part->is_purchasable = false;
        expect($part->attributes['is_purchasable'])->toBe('N');
    });

    it('팩토리 상태가 올바르게 작동한다', function () {
        // 구매 가능한 구성품
        $purchasable = RepairParts::factory()->purchasable()->create();
        expect($purchasable->is_purchasable)->toBeTrue();

        // 구매 불가능한 구성품
        $notPurchasable = RepairParts::factory()->notPurchasable()->create();
        expect($notPurchasable->is_purchasable)->toBeFalse();

        // 재고 부족
        $lowStock = RepairParts::factory()->lowStock()->create();
        expect($lowStock->is_low_stock)->toBeTrue();

        // 품절
        $outOfStock = RepairParts::factory()->outOfStock()->create();
        expect($outOfStock->stock)->toBe(0);
    });

    it('캐스팅이 올바르게 작동한다', function () {
        $part = RepairParts::factory()->create([
            'price' => '50000',
            'stock' => '10',
            'reorder_stock' => '5',
            'acc_count' => '100',
            'location_floor' => '2',
            'location_position' => '15',
        ]);

        expect($part->price)->toBe(50000);
        expect($part->stock)->toBe(10);
        expect($part->reorder_stock)->toBe(5);
        expect($part->acc_count)->toBe(100);
        expect($part->location_floor)->toBe(2);
        expect($part->location_position)->toBe(15);

        expect(is_int($part->price))->toBeTrue();
        expect(is_int($part->stock))->toBeTrue();
        expect(is_int($part->reorder_stock))->toBeTrue();
        expect(is_int($part->acc_count))->toBeTrue();
        expect(is_int($part->location_floor))->toBeTrue();
        expect(is_int($part->location_position))->toBeTrue();
    });

    it('제약조건이 올바르게 적용된다', function () {
        // 가격이 음수일 수 없음
        expect(function () {
            RepairParts::factory()->create(['price' => -1000]);
        })->toThrow(Exception::class);

        // 재고가 음수일 수 없음
        expect(function () {
            RepairParts::factory()->create(['stock' => -5]);
        })->toThrow(Exception::class);

        // 재주문 재고가 음수일 수 없음
        expect(function () {
            RepairParts::factory()->create(['reorder_stock' => -1]);
        })->toThrow(Exception::class);

        // 누적 사용 개수가 음수일 수 없음
        expect(function () {
            RepairParts::factory()->create(['acc_count' => -10]);
        })->toThrow(Exception::class);

        // 층이 1 미만일 수 없음
        expect(function () {
            RepairParts::factory()->create(['location_floor' => 0]);
        })->toThrow(Exception::class);

        // 위치가 1 미만일 수 없음
        expect(function () {
            RepairParts::factory()->create(['location_position' => 0]);
        })->toThrow(Exception::class);
    });

    it('바코드가 유니크해야 한다', function () {
        $barcode = '1234567890123';

        RepairParts::factory()->create(['barcode' => $barcode]);

        expect(function () {
            RepairParts::factory()->create(['barcode' => $barcode]);
        })->toThrow(Exception::class);
    });

    it('위치 정보가 부분적으로 있을 때도 올바르게 표시된다', function () {
        // 위치 정보가 없는 경우
        $noLocation = RepairParts::factory()->create([
            'location_area' => null,
            'location_zone' => null,
            'location_floor' => 1,
            'location_position' => 1,
        ]);
        expect($noLocation->location)->toBe('1층 1번');

        // 일부 위치 정보만 있는 경우
        $partialLocation = RepairParts::factory()->create([
            'location_area' => 'A동',
            'location_zone' => null,
            'location_floor' => 2,
            'location_position' => 10,
        ]);
        expect($partialLocation->location)->toBe('A동 2층 10번');
    });
});
