<?php

use App\Models\Cate4;
use App\Models\Cate5;
use App\Models\DeleteLog;
use App\Models\Product;
use App\Models\ProductBarcode;
use App\Models\ProductLink;
use App\Models\ProductLot;
use App\Models\ProductVendor;
use App\Models\Req;
use App\Models\ReqCount;
use App\Models\User;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

// Req와 ReqCount 관계 테스트
it('Req는 ReqCount와 1:1 관계를 가진다', function () {
    $req = Req::factory()->create();
    $reqCount = ReqCount::factory()->create(['req_id' => $req->id]);

    expect($req->reqCount)->not->toBeNull();
    expect($req->reqCount->id)->toBe($reqCount->id);
    expect($reqCount->req->id)->toBe($req->id);
});

// Cate4와 Cate5 관계 테스트
it('Cate4는 Cate5와 1:N 관계를 가진다', function () {
    $cate4 = Cate4::factory()->create();
    $cate5 = Cate5::factory()->create(['cate4_id' => $cate4->id]);

    expect($cate4->cate5)->toHaveCount(1);
    expect($cate4->cate5->first()->id)->toBe($cate5->id);
    expect($cate5->cate4->id)->toBe($cate4->id);
});

// ProductVendor와 Product 관계 테스트
it('ProductVendor는 Product와 1:N 관계를 가진다', function () {
    $vendor = ProductVendor::factory()->create(['name' => '삼성전자']);
    $product = Product::factory()->for($vendor, 'vendor')->create();

    expect($vendor->products)->toHaveCount(1);
    expect($vendor->products->first()->id)->toBe($product->id);
    expect($product->vendor->id)->toBe($vendor->id);
});

// ProductLot와 Product 관계 테스트
it('ProductLot는 Product와 1:N 관계를 가진다', function () {
    $lot = ProductLot::factory()->create(['name' => 'LOT-001']);
    $product = Product::factory()->for($lot, 'lot')->create();

    expect($lot->products)->toHaveCount(1);
    expect($lot->products->first()->id)->toBe($product->id);
    expect($product->lot->id)->toBe($lot->id);
});

// ProductLink와 Product 관계 테스트
it('ProductLink는 Product와 1:N 관계를 가진다', function () {
    $link = ProductLink::factory()->create(['vendor_item_id' => 'VENDOR-001']);
    $product = Product::factory()->for($link, 'link')->create();

    expect($link->products)->toHaveCount(1);
    expect($link->products->first()->id)->toBe($product->id);
    expect($product->link->id)->toBe($link->id);
});

// ProductBarcode 관계 테스트 (ProductBarcode는 Product와 직접적 관계 없음, 생성/조회만 검증)
it('ProductBarcode는 생성/조회가 가능하다', function () {
    $barcode = ProductBarcode::factory()->create();
    $found = ProductBarcode::find($barcode->id);
    expect($found)->not->toBeNull();
    expect($found->barcode)->toBe($barcode->barcode);
});

// Product의 user, checkedUser, req 관계 테스트
it('Product는 user, checkedUser, req와 belongsTo 관계를 가진다', function () {
    $user = User::factory()->create();
    $checkedUser = User::factory()->create();
    $req = Req::factory()->create();
    $product = Product::factory()->create([
        'user_id' => $user->id,
        'checked_user_id' => $checkedUser->id,
        'req_id' => $req->id,
    ]);
    expect($product->user->id)->toBe($user->id);
    expect($product->checkedUser->id)->toBe($checkedUser->id);
    expect($product->req->id)->toBe($req->id);
});

// Cate4, Cate5, Product 관계 테스트
it('Product는 Cate4, Cate5와 각각 belongsTo 관계를 가진다', function () {
    $cate4 = Cate4::factory()->create();
    $cate5 = Cate5::factory()->create(['cate4_id' => $cate4->id]);
    $product = Product::factory()->create(['cate4_id' => $cate4->id, 'cate5_id' => $cate5->id]);

    expect($product->cate4->id)->toBe($cate4->id);
    expect($product->cate5->id)->toBe($cate5->id);
});

// DeleteLog morphTo 관계 테스트
it('DeleteLog는 morphTo 관계를 가진다', function () {
    $req = Req::factory()->create();
    $log = DeleteLog::factory()->create([
        'deletable_type' => Req::class,
        'deletable_id' => $req->id,
    ]);

    expect($log->deletable)->not->toBeNull();
    expect($log->deletable->id)->toBe($req->id);
});

// unique 제약조건 테스트 (ProductVendor name 중복)
it('ProductVendor name은 unique 제약조건을 가진다', function () {
    $name = '벤더A';
    ProductVendor::factory()->create(['name' => $name]);
    expect(fn () => ProductVendor::factory()->create(['name' => $name]))
        ->toThrow(QueryException::class);
});

// 외래키 제약조건 테스트 (Cate5 cate4_id 없는 값)
it('Cate5 cate4_id는 cate4 존재하지 않으면 생성 불가', function () {
    expect(fn () => Cate5::factory()->create(['cate4_id' => 999999]))
        ->toThrow(QueryException::class);
});
