<?php

use App\Models\Product;
use App\Models\RepairGrade;
use App\Models\RepairParts;
use App\Models\RepairProcess;
use App\Models\RepairProduct;
use App\Models\RepairProductParts;
use App\Models\RepairSymptom;
use App\Models\User;

beforeEach(function () {
    $this->product = Product::factory()->create();
    $this->symptom = RepairSymptom::factory()->create();
    $this->process = RepairProcess::factory()->create();
    $this->grade = RepairGrade::factory()->create();
    $this->user = User::factory()->create();
});

describe('RepairProduct 모델 테스트', function () {
    it('수리 제품을 생성할 수 있다', function () {
        $repairProduct = RepairProduct::factory()->create([
            'product_id' => $this->product->id,
            'status' => RepairProduct::STATUS_WAITING,
            'waiting_user_id' => $this->user->id,
            'amount' => 500000,
            'repair_symptom_id' => $this->symptom->id,
            'repair_process_id' => $this->process->id,
            'repair_grade_id' => $this->grade->id,
            'invoice1' => 50000,
            'invoice2' => 10000,
            'invoice3' => 5000,
        ]);

        expect($repairProduct)->toBeInstanceOf(RepairProduct::class);
        expect($repairProduct->product_id)->toBe($this->product->id);
        expect($repairProduct->status)->toBe(RepairProduct::STATUS_WAITING);
        expect($repairProduct->amount)->toBe(500000);
    });

    it('상품과의 관계가 올바르게 작동한다', function () {
        $repairProduct = RepairProduct::factory()->create([
            'product_id' => $this->product->id,
        ]);

        expect($repairProduct->product)->toBeInstanceOf(Product::class);
        expect($repairProduct->product->id)->toBe($this->product->id);
    });

    it('수리 증상과의 관계가 올바르게 작동한다', function () {
        $repairProduct = RepairProduct::factory()->create([
            'repair_symptom_id' => $this->symptom->id,
        ]);

        expect($repairProduct->repairSymptom)->toBeInstanceOf(RepairSymptom::class);
        expect($repairProduct->repairSymptom->id)->toBe($this->symptom->id);
    });

    it('수리 처리와의 관계가 올바르게 작동한다', function () {
        $repairProduct = RepairProduct::factory()->create([
            'repair_process_id' => $this->process->id,
        ]);

        expect($repairProduct->repairProcess)->toBeInstanceOf(RepairProcess::class);
        expect($repairProduct->repairProcess->id)->toBe($this->process->id);
    });

    it('수리 등급과의 관계가 올바르게 작동한다', function () {
        $repairProduct = RepairProduct::factory()->create([
            'repair_grade_id' => $this->grade->id,
        ]);

        expect($repairProduct->repairGrade)->toBeInstanceOf(RepairGrade::class);
        expect($repairProduct->repairGrade->id)->toBe($this->grade->id);
    });

    it('사용자들과의 관계가 올바르게 작동한다', function () {
        $repairProduct = RepairProduct::factory()->create([
            'waiting_user_id' => $this->user->id,
            'completed_user_id' => $this->user->id,
        ]);

        expect($repairProduct->waitingUser)->toBeInstanceOf(User::class);
        expect($repairProduct->completedUser)->toBeInstanceOf(User::class);
        expect($repairProduct->waitingUser->id)->toBe($this->user->id);
        expect($repairProduct->completedUser->id)->toBe($this->user->id);
    });

    it('액세서가 올바르게 작동한다', function () {
        $repairProduct = RepairProduct::factory()->create([
            'status' => RepairProduct::STATUS_WAITING,
            'amount' => 500000,
            'invoice1' => 50000,
            'invoice2' => 10000,
            'invoice3' => 5000,
        ]);

        // 상태 이름
        expect($repairProduct->status_name)->toBe('구성품 신청');

        // 판매가 포맷팅
        expect($repairProduct->formatted_amount)->toBe('500,000원');

        // 총 청구금액
        expect($repairProduct->total_invoice)->toBe(65000);
        expect($repairProduct->formatted_total_invoice)->toBe('65,000원');

        // 상태 확인
        expect($repairProduct->is_waiting)->toBeTrue();
        expect($repairProduct->is_completed)->toBeFalse();
        expect($repairProduct->is_deleted)->toBeFalse();
    });

    it('상태별 액세서가 올바르게 작동한다', function () {
        // 대기 상태
        $waiting = RepairProduct::factory()->create(['status' => RepairProduct::STATUS_WAITING]);
        expect($waiting->is_waiting)->toBeTrue();
        expect($waiting->is_completed)->toBeFalse();
        expect($waiting->is_deleted)->toBeFalse();

        // 완료 상태
        $completed = RepairProduct::factory()->create(['status' => RepairProduct::STATUS_REPAIRED]);
        expect($completed->is_waiting)->toBeFalse();
        expect($completed->is_completed)->toBeTrue();
        expect($completed->is_deleted)->toBeFalse();

        // 삭제 상태
        $deleted = RepairProduct::factory()->create(['status' => RepairProduct::STATUS_DELETED]);
        expect($deleted->is_waiting)->toBeFalse();
        expect($deleted->is_completed)->toBeFalse();
        expect($deleted->is_deleted)->toBeTrue();
    });

    it('구성품들과의 관계가 올바르게 작동한다', function () {
        $repairProduct = RepairProduct::factory()->create();
        $part = RepairParts::factory()->create();

        // 수리 제품 구성품 생성
        $productPart = RepairProductParts::factory()->create([
            'repair_product_id' => $repairProduct->id,
            'repair_parts_id' => $part->id,
            'quantity' => 2,
            'price' => 25000,
        ]);

        expect($repairProduct->repairProductParts)->toHaveCount(1);
        expect($repairProduct->repairProductParts->first())->toBeInstanceOf(RepairProductParts::class);

        // 구성품 총 비용 계산
        expect($repairProduct->parts_total_cost)->toBe(50000); // 2 * 25000
        expect($repairProduct->formatted_parts_total_cost)->toBe('50,000원');
    });

    it('캐스팅이 올바르게 작동한다', function () {
        $repairProduct = RepairProduct::factory()->create([
            'status' => '30',
            'amount' => '500000',
            'invoice1' => '50000',
            'invoice2' => '10000',
            'invoice3' => '5000',
            'waiting_at' => '2024-01-01 10:00:00',
            'completed_at' => '2024-01-02 15:30:00',
        ]);

        expect($repairProduct->status)->toBe(30);
        expect($repairProduct->amount)->toBe(500000);
        expect($repairProduct->invoice1)->toBe(50000);
        expect($repairProduct->invoice2)->toBe(10000);
        expect($repairProduct->invoice3)->toBe(5000);

        expect(is_int($repairProduct->status))->toBeTrue();
        expect(is_int($repairProduct->amount))->toBeTrue();
        expect(is_int($repairProduct->invoice1))->toBeTrue();
        expect(is_int($repairProduct->invoice2))->toBeTrue();
        expect(is_int($repairProduct->invoice3))->toBeTrue();

        expect($repairProduct->waiting_at)->toBeInstanceOf(\Carbon\Carbon::class);
        expect($repairProduct->completed_at)->toBeInstanceOf(\Carbon\Carbon::class);
    });

    it('제약조건이 올바르게 적용된다', function () {
        // 상태가 유효한 값이어야 함
        expect(function () {
            RepairProduct::factory()->create(['status' => 999]);
        })->toThrow(Exception::class);

        // 금액이 0 이상이어야 함
        expect(function () {
            RepairProduct::factory()->create(['amount' => -1000]);
        })->toThrow(Exception::class);

        // 청구금액들이 0 이상이어야 함
        expect(function () {
            RepairProduct::factory()->create(['invoice1' => -5000]);
        })->toThrow(Exception::class);

        expect(function () {
            RepairProduct::factory()->create(['invoice2' => -1000]);
        })->toThrow(Exception::class);

        expect(function () {
            RepairProduct::factory()->create(['invoice3' => -500]);
        })->toThrow(Exception::class);
    });

    it('소프트 삭제가 올바르게 작동한다', function () {
        $repairProduct = RepairProduct::factory()->create();

        expect(RepairProduct::count())->toBe(1);

        $repairProduct->delete();

        expect(RepairProduct::count())->toBe(0);
        expect(RepairProduct::withTrashed()->count())->toBe(1);
        expect($repairProduct->trashed())->toBeTrue();
    });

    it('총 청구금액 계산이 올바르게 작동한다', function () {
        $repairProduct = RepairProduct::factory()->create([
            'invoice1' => 50000,
            'invoice2' => 10000,
            'invoice3' => 5000,
        ]);

        expect($repairProduct->total_invoice)->toBe(65000);
    });

    it('구성품이 없을 때 총 비용이 0이다', function () {
        $repairProduct = RepairProduct::factory()->create();

        expect($repairProduct->parts_total_cost)->toBe(0);
        expect($repairProduct->formatted_parts_total_cost)->toBe('0원');
    });

    it('여러 구성품의 총 비용이 올바르게 계산된다', function () {
        $repairProduct = RepairProduct::factory()->create();
        $part1 = RepairParts::factory()->create();
        $part2 = RepairParts::factory()->create();

        // 첫 번째 구성품: 2개 * 25000원 = 50000원
        RepairProductParts::factory()->create([
            'repair_product_id' => $repairProduct->id,
            'repair_parts_id' => $part1->id,
            'quantity' => 2,
            'price' => 25000,
        ]);

        // 두 번째 구성품: 1개 * 30000원 = 30000원
        RepairProductParts::factory()->create([
            'repair_product_id' => $repairProduct->id,
            'repair_parts_id' => $part2->id,
            'quantity' => 1,
            'price' => 30000,
        ]);

        expect($repairProduct->parts_total_cost)->toBe(80000); // 50000 + 30000
        expect($repairProduct->formatted_parts_total_cost)->toBe('80,000원');
    });
});
