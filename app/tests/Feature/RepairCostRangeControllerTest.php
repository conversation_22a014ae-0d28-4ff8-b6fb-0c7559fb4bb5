<?php

namespace Tests\Feature;

use App\Models\Cate4;
use App\Models\RepairCost;
use App\Models\RepairCostCategory;
use App\Models\RepairCostPolicy;
use App\Models\RepairCostRange;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

/**
 * 수리비 범위 관리 컨트롤러 테스트
 */
class RepairCostRangeControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private RepairCostPolicy $system;

    private RepairCostCategory $category;

    private Cate4 $cate4;

    protected function setUp(): void
    {
        parent::setUp();

        // 테스트용 사용자 생성 및 인증
        $this->user = User::factory()->create();
        Sanctum::actingAs($this->user);

        // 테스트 데이터 설정
        $this->cate4 = Cate4::factory()->create([
            'name' => '테스트 카테고리',
        ]);

        $this->system = RepairCostPolicy::factory()->create([
            'name' => 'monitor',
            'display_name' => '모니터',
            'pricing_type' => 'size',
            'is_active' => true,
        ]);

        $this->category = RepairCostCategory::factory()->create([
            'repair_cost_policy_id' => $this->system->id,
            'cate4_id' => $this->cate4->id,
            'pricing_criteria' => 'size',
            'is_active' => true,
        ]);
    }

    /**
     * 수리비 범위 목록 조회 테스트
     */
    public function test_can_get_repair_cost_ranges_list(): void
    {
        // Given: 테스트 범위 데이터 생성
        $range1 = RepairCostRange::factory()->create([
            'repair_cost_category_id' => $this->category->id,
            'range_name' => '24인치 미만',
            'min_value' => 0,
            'max_value' => 24,
            'unit' => 'inch',
            'is_active' => true,
        ]);

        $range2 = RepairCostRange::factory()->create([
            'repair_cost_category_id' => $this->category->id,
            'range_name' => '24-32인치',
            'min_value' => 24,
            'max_value' => 32,
            'unit' => 'inch',
            'is_active' => true,
        ]);

        // 각 범위에 수리비 추가
        RepairCost::factory()->create([
            'repair_cost_range_id' => $range1->id,
            'repair_type' => 'repair_parts',
            'amount' => 50000,
        ]);

        RepairCost::factory()->create([
            'repair_cost_range_id' => $range2->id,
            'repair_type' => 'repair_parts',
            'amount' => 70000,
        ]);

        // When: API 호출
        $response = $this->getJson("/wms/settings/repairs/cost-ranges?category_id={$this->category->id}");

        // Then: 응답 검증
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'category' => [
                        'id',
                        'policy_name',
                        'pricing_criteria',
                    ],
                    'ranges' => [
                        'data' => [
                            '*' => [
                                'id',
                                'range_name',
                                'min_value',
                                'max_value',
                                'unit',
                                'is_active',
                                'costs_summary',
                                'display_name',
                            ],
                        ],
                    ],
                ],
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'category' => [
                        'id' => $this->category->id,
                        'pricing_criteria' => 'size',
                    ],
                ],
            ]);

        // 범위가 min_value 기준으로 정렬되었는지 확인
        $ranges = $response->json('data.ranges.data');
        $this->assertEquals(0, $ranges[0]['min_value']);
        $this->assertEquals(24, $ranges[1]['min_value']);
    }

    /**
     * 수리비 범위 생성 테스트
     */
    public function test_can_create_repair_cost_range(): void
    {
        // Given: 생성할 범위 데이터
        $rangeData = [
            'repair_cost_category_id' => $this->category->id,
            'range_name' => '32-43인치',
            'min_value' => 32,
            'max_value' => 43,
            'unit' => 'inch',
            'is_active' => true,
            'repair_costs' => [
                [
                    'repair_type' => 'repair_parts',
                    'amount' => 80000,
                ],
                [
                    'repair_type' => 'inspection',
                    'amount' => 30000,
                ],
            ],
        ];

        // When: API 호출
        $response = $this->postJson('/wms/settings/repairs/cost-ranges', $rangeData);

        // Then: 응답 검증
        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'range_name',
                    'min_value',
                    'max_value',
                    'unit',
                    'is_active',
                    'costs_summary',
                    'display_name',
                ],
            ])
            ->assertJson([
                'success' => true,
                'data' => [
                    'range_name' => '32-43인치',
                    'min_value' => 32,
                    'max_value' => 43,
                    'unit' => 'inch',
                ],
            ]);

        // 데이터베이스에 저장되었는지 확인
        $this->assertDatabaseHas('repair_cost_ranges', [
            'repair_cost_category_id' => $this->category->id,
            'range_name' => '32-43인치',
            'min_value' => 32,
            'max_value' => 43,
            'unit' => 'inch',
        ]);

        // 수리비도 함께 생성되었는지 확인
        $range = RepairCostRange::where('range_name', '32-43인치')->first();
        $this->assertDatabaseHas('repair_costs', [
            'repair_cost_range_id' => $range->id,
            'repair_type' => 'repair_parts',
            'amount' => 80000,
        ]);
    }

    /**
     * 범위 중복 검증 테스트
     */
    public function test_cannot_create_overlapping_range(): void
    {
        // Given: 기존 범위 생성
        RepairCostRange::factory()->create([
            'repair_cost_category_id' => $this->category->id,
            'range_name' => '24-32인치',
            'min_value' => 24,
            'max_value' => 32,
            'unit' => 'inch',
            'is_active' => true,
        ]);

        // When: 중복되는 범위 생성 시도
        $overlappingData = [
            'repair_cost_category_id' => $this->category->id,
            'range_name' => '30-40인치',
            'min_value' => 30,
            'max_value' => 40,
            'unit' => 'inch',
            'is_active' => true,
        ];

        $response = $this->postJson('/wms/settings/repairs/cost-ranges', $overlappingData);

        // Then: 에러 응답 확인
        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'error_code' => 'RANGE_OVERLAP',
            ]);
    }

    /**
     * 수리비 범위 수정 테스트
     */
    public function test_can_update_repair_cost_range(): void
    {
        // Given: 기존 범위 생성
        $range = RepairCostRange::factory()->create([
            'repair_cost_category_id' => $this->category->id,
            'range_name' => '24-32인치',
            'min_value' => 24,
            'max_value' => 32,
            'unit' => 'inch',
            'is_active' => true,
        ]);

        // When: 범위 수정
        $updateData = [
            'range_name' => '24-35인치',
            'max_value' => 35,
        ];

        $response = $this->putJson("/wms/settings/repairs/cost-ranges/{$range->id}", $updateData);

        // Then: 응답 검증
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'range_name' => '24-35인치',
                    'max_value' => 35,
                ],
            ]);

        // 데이터베이스 업데이트 확인
        $this->assertDatabaseHas('repair_cost_ranges', [
            'id' => $range->id,
            'range_name' => '24-35인치',
            'max_value' => 35,
        ]);
    }

    /**
     * 수리비 범위 삭제 테스트
     */
    public function test_can_delete_repair_cost_range_without_costs(): void
    {
        // Given: 수리비가 없는 범위 생성
        $range = RepairCostRange::factory()->create([
            'repair_cost_category_id' => $this->category->id,
            'range_name' => '24-32인치',
            'min_value' => 24,
            'max_value' => 32,
            'unit' => 'inch',
            'is_active' => true,
        ]);

        // When: 범위 삭제
        $response = $this->deleteJson("/wms/settings/repairs/cost-ranges/{$range->id}");

        // Then: 응답 검증
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
            ]);

        // 데이터베이스에서 삭제되었는지 확인
        $this->assertDatabaseMissing('repair_cost_ranges', [
            'id' => $range->id,
        ]);
    }

    /**
     * 수리비가 있는 범위 삭제 방지 테스트
     */
    public function test_cannot_delete_range_with_costs(): void
    {
        // Given: 수리비가 있는 범위 생성
        $range = RepairCostRange::factory()->create([
            'repair_cost_category_id' => $this->category->id,
            'range_name' => '24-32인치',
            'min_value' => 24,
            'max_value' => 32,
            'unit' => 'inch',
            'is_active' => true,
        ]);

        RepairCost::factory()->create([
            'repair_cost_range_id' => $range->id,
            'repair_type' => 'repair_parts',
            'amount' => 50000,
        ]);

        // When: 범위 삭제 시도
        $response = $this->deleteJson("/wms/settings/repairs/cost-ranges/{$range->id}");

        // Then: 에러 응답 확인
        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'error_code' => 'RANGE_HAS_COSTS',
            ]);

        // 데이터베이스에 여전히 존재하는지 확인
        $this->assertDatabaseHas('repair_cost_ranges', [
            'id' => $range->id,
        ]);
    }

    /**
     * 수리비 금액 목록 조회 테스트
     */
    public function test_can_get_repair_costs(): void
    {
        // Given: 범위와 수리비 생성
        $range = RepairCostRange::factory()->create([
            'repair_cost_category_id' => $this->category->id,
            'range_name' => '24-32인치',
            'min_value' => 24,
            'max_value' => 32,
            'unit' => 'inch',
            'is_active' => true,
        ]);

        RepairCost::factory()->create([
            'repair_cost_range_id' => $range->id,
            'repair_type' => 'repair_parts',
            'amount' => 50000,
        ]);

        RepairCost::factory()->create([
            'repair_cost_range_id' => $range->id,
            'repair_type' => 'inspection',
            'amount' => 30000,
        ]);

        // When: API 호출
        $response = $this->getJson("/wms/settings/repairs/cost-ranges/{$range->id}/costs");

        // Then: 응답 검증
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'range' => [
                        'id',
                        'range_name',
                        'display_name',
                        'category',
                    ],
                    'costs' => [
                        '*' => [
                            'repair_type',
                            'repair_type_display',
                            'amount',
                            'cost_id',
                            'formatted_amount',
                        ],
                    ],
                ],
            ])
            ->assertJson([
                'success' => true,
            ]);

        // 모든 수리 유형이 포함되었는지 확인
        $costs = $response->json('data.costs');
        $this->assertCount(5, $costs); // 5가지 수리 유형
    }

    /**
     * 수리비 금액 일괄 수정 테스트
     */
    public function test_can_update_repair_costs(): void
    {
        // Given: 범위 생성
        $range = RepairCostRange::factory()->create([
            'repair_cost_category_id' => $this->category->id,
            'range_name' => '24-32인치',
            'min_value' => 24,
            'max_value' => 32,
            'unit' => 'inch',
            'is_active' => true,
        ]);

        // When: 수리비 금액 수정
        $costsData = [
            'costs' => [
                [
                    'repair_type' => 'repair_parts',
                    'amount' => 60000,
                ],
                [
                    'repair_type' => 'inspection',
                    'amount' => 35000,
                ],
                [
                    'repair_type' => 'repair_cleaning',
                    'amount' => 40000,
                ],
            ],
            'track_changes' => true,
        ];

        $response = $this->putJson("/wms/settings/repairs/cost-ranges/{$range->id}/costs", $costsData);

        // Then: 응답 검증
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'range',
                    'costs',
                    'changes',
                    'changes_count',
                ],
            ])
            ->assertJson([
                'success' => true,
            ]);

        // 데이터베이스에 저장되었는지 확인
        $this->assertDatabaseHas('repair_costs', [
            'repair_cost_range_id' => $range->id,
            'repair_type' => 'repair_parts',
            'amount' => 60000,
        ]);

        $this->assertDatabaseHas('repair_costs', [
            'repair_cost_range_id' => $range->id,
            'repair_type' => 'inspection',
            'amount' => 35000,
        ]);
    }

    /**
     * 수리비 금액 복사 테스트
     */
    public function test_can_copy_repair_costs(): void
    {
        // Given: 소스 범위와 수리비 생성
        $sourceRange = RepairCostRange::factory()->create([
            'repair_cost_category_id' => $this->category->id,
            'range_name' => '소스 범위',
            'min_value' => 24,
            'max_value' => 32,
            'unit' => 'inch',
            'is_active' => true,
        ]);

        RepairCost::factory()->create([
            'repair_cost_range_id' => $sourceRange->id,
            'repair_type' => 'repair_parts',
            'amount' => 50000,
        ]);

        RepairCost::factory()->create([
            'repair_cost_range_id' => $sourceRange->id,
            'repair_type' => 'inspection',
            'amount' => 30000,
        ]);

        // 대상 범위 생성
        $targetRange = RepairCostRange::factory()->create([
            'repair_cost_category_id' => $this->category->id,
            'range_name' => '대상 범위',
            'min_value' => 32,
            'max_value' => 43,
            'unit' => 'inch',
            'is_active' => true,
        ]);

        // When: 수리비 복사
        $copyData = [
            'target_range_ids' => [$targetRange->id],
            'overwrite_existing' => true,
        ];

        $response = $this->postJson("/wms/settings/repairs/cost-ranges/{$sourceRange->id}/copy-costs", $copyData);

        // Then: 응답 검증
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'source_range',
                    'results',
                    'summary',
                ],
            ])
            ->assertJson([
                'success' => true,
            ]);

        // 대상 범위에 수리비가 복사되었는지 확인
        $this->assertDatabaseHas('repair_costs', [
            'repair_cost_range_id' => $targetRange->id,
            'repair_type' => 'repair_parts',
            'amount' => 50000,
        ]);

        $this->assertDatabaseHas('repair_costs', [
            'repair_cost_range_id' => $targetRange->id,
            'repair_type' => 'inspection',
            'amount' => 30000,
        ]);
    }

    /**
     * 유효성 검사 테스트
     */
    public function test_validates_input_data(): void
    {
        // Given: 잘못된 데이터
        $invalidData = [
            'repair_cost_category_id' => 999999, // 존재하지 않는 카테고리
            'range_name' => '', // 빈 이름
            'min_value' => -1, // 음수 값
            'unit' => 'invalid_unit', // 잘못된 단위
        ];

        // When: API 호출
        $response = $this->postJson('/wms/settings/repairs/cost-ranges', $invalidData);

        // Then: 유효성 검사 에러 확인
        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'error_code' => 'VALIDATION_ERROR',
            ])
            ->assertJsonValidationErrors([
                'repair_cost_category_id',
                'range_name',
                'min_value',
                'unit',
            ]);
    }
}
