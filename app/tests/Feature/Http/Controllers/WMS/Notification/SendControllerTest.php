<?php

namespace Tests\Feature\Http\Controllers\WMS\Notification;

use App\Models\User;
use App\Models\NotificationGroup;
use App\Models\NotificationGroupMember;
use App\Services\NotificationSendService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Mockery;

class SendControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;
    private User $regularUser;

    protected function setUp(): void
    {
        parent::setUp();

        // 관리자 사용자 생성
        $this->admin = User::factory()->create([
            'role' => User::ROLE_ADMIN,
            'status' => User::MEMBER_STATUS_ACTIVE
        ]);

        // 일반 사용자 생성
        $this->regularUser = User::factory()->create([
            'role' => 'Member',
            'status' => User::MEMBER_STATUS_ACTIVE
        ]);
    }

    /** @test */
    public function 관리자는_알림을_전송할_수_있다()
    {
        // Given: 관리자로 로그인
        $this->actingAs($this->admin, 'sanctum');

        // When: 알림 전송 요청
        $response = $this->postJson('/wms/notifications/send', [
            'title' => '테스트 알림',
            'content' => '테스트 알림 내용입니다.',
            'target_type' => 'all'
        ]);

        // Then: 성공 응답
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => '알림이 성공적으로 전송되었습니다.'
                ])
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'notification_id',
                        'recipients_count',
                        'target_type',
                        'sent_at'
                    ]
                ]);
    }

    /** @test */
    public function 일반_사용자는_알림을_전송할_수_없다()
    {
        // Given: 일반 사용자로 로그인
        $this->actingAs($this->regularUser, 'sanctum');

        // When: 알림 전송 요청
        $response = $this->postJson('/wms/notifications/send', [
            'title' => '테스트 알림',
            'content' => '테스트 알림 내용입니다.',
            'target_type' => 'all'
        ]);

        // Then: 500 Internal Server Error 응답 (ErrorHandlingMiddleware에 의해 처리됨)
        $response->assertStatus(500)
                ->assertJson([
                    'success' => false,
                    'error' => [
                        'message' => 'Access denied'
                    ]
                ]);
    }

    /** @test */
    public function 인증되지_않은_사용자는_알림을_전송할_수_없다()
    {
        // When: 인증 없이 알림 전송 요청
        $response = $this->postJson('/wms/notifications/send', [
            'title' => '테스트 알림',
            'content' => '테스트 알림 내용입니다.',
            'target_type' => 'all'
        ]);

        // Then: 401 Unauthorized 응답
        $response->assertStatus(401);
    }

    /** @test */
    public function 필수_필드가_누락되면_유효성_검증_오류가_발생한다()
    {
        // Given: 관리자로 로그인
        $this->actingAs($this->admin, 'sanctum');

        // When: 필수 필드 누락으로 알림 전송 요청
        $response = $this->postJson('/wms/notifications/send', [
            // title 누락
            'content' => '테스트 알림 내용입니다.',
            'target_type' => 'all'
        ]);

        // Then: 422 Unprocessable Entity 응답
        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'error' => [
                        'code' => 'UNPROCESSABLE_ENTITY',
                        'message' => '입력 데이터가 유효하지 않습니다.',
                        'details' => [
                            'validation_errors' => [
                                'title' => ['알림 제목은 필수 입력 항목입니다.']
                            ]
                        ]
                    ]
                ]);
    }

    /** @test */
    public function 잘못된_대상_타입으로_요청하면_유효성_검증_오류가_발생한다()
    {
        // Given: 관리자로 로그인
        $this->actingAs($this->admin, 'sanctum');

        // When: 잘못된 대상 타입으로 알림 전송 요청
        $response = $this->postJson('/wms/notifications/send', [
            'title' => '테스트 알림',
            'content' => '테스트 알림 내용입니다.',
            'target_type' => 'invalid_type'
        ]);

        // Then: 422 Unprocessable Entity 응답
        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'error' => [
                        'code' => 'UNPROCESSABLE_ENTITY',
                        'details' => [
                            'validation_errors' => [
                                'target_type' => ['대상 타입은 all, group, individual 중 하나여야 합니다.']
                            ]
                        ]
                    ]
                ]);
    }

    /** @test */
    public function 그룹_대상_선택시_target_ids가_없으면_유효성_검증_오류가_발생한다()
    {
        // Given: 관리자로 로그인
        $this->actingAs($this->admin, 'sanctum');

        // When: 그룹 대상이지만 target_ids 없이 알림 전송 요청
        $response = $this->postJson('/wms/notifications/send', [
            'title' => '테스트 알림',
            'content' => '테스트 알림 내용입니다.',
            'target_type' => 'group'
            // target_ids 누락
        ]);

        // Then: 422 Unprocessable Entity 응답
        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'error' => [
                        'code' => 'UNPROCESSABLE_ENTITY',
                        'details' => [
                            'validation_errors' => [
                                'target_ids' => ['그룹 또는 개별 대상 선택 시 대상 ID는 필수입니다.']
                            ]
                        ]
                    ]
                ]);
    }

    /** @test */
    public function 개별_대상으로_알림을_전송할_수_있다()
    {
        // Given: 관리자로 로그인하고 대상 사용자 생성
        $this->actingAs($this->admin, 'sanctum');
        $targetUser = User::factory()->create(['status' => User::MEMBER_STATUS_ACTIVE]);

        // When: 개별 대상으로 알림 전송 요청
        $response = $this->postJson('/wms/notifications/send', [
            'title' => '개별 알림',
            'content' => '개별 알림 내용입니다.',
            'target_type' => 'individual',
            'target_ids' => [$targetUser->id]
        ]);

        // Then: 성공 응답
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => '알림이 성공적으로 전송되었습니다.'
                ]);
    }

    /** @test */
    public function 그룹_대상으로_알림을_전송할_수_있다()
    {
        // Given: 관리자로 로그인하고 알림 그룹 생성
        $this->actingAs($this->admin, 'sanctum');
        
        $group = NotificationGroup::factory()->create([
            'is_active' => true
        ]);
        
        $member = User::factory()->create(['status' => User::MEMBER_STATUS_ACTIVE]);
        NotificationGroupMember::create([
            'group_id' => $group->id,
            'user_id' => $member->id
        ]);

        // When: 그룹 대상으로 알림 전송 요청
        $response = $this->postJson('/wms/notifications/send', [
            'title' => '그룹 알림',
            'content' => '그룹 알림 내용입니다.',
            'target_type' => 'group',
            'target_ids' => [$group->id]
        ]);

        // Then: 성공 응답
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => '알림이 성공적으로 전송되었습니다.'
                ]);
    }

    /** @test */
    public function 알림_통계를_조회할_수_있다()
    {
        // Given: 관리자로 로그인
        $this->actingAs($this->admin, 'sanctum');

        // Mock NotificationSendService
        $mockService = Mockery::mock(NotificationSendService::class);
        $mockService->shouldReceive('getNotificationStats')
                   ->with('test-notification-id')
                   ->andReturn([
                       'notification_id' => 'test-notification-id',
                       'title' => '테스트 알림',
                       'total_recipients' => 5,
                       'delivered_count' => 3,
                       'read_count' => 1,
                       'delivery_rate' => 60.0,
                       'read_rate' => 20.0
                   ]);

        $this->app->instance(NotificationSendService::class, $mockService);

        // When: 알림 통계 조회 요청
        $response = $this->getJson('/wms/notifications/stats/test-notification-id');

        // Then: 성공 응답
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'notification_id',
                        'title',
                        'total_recipients',
                        'delivered_count',
                        'read_count',
                        'delivery_rate',
                        'read_rate'
                    ]
                ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}