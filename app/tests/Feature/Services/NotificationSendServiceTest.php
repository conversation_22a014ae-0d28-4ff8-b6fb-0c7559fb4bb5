<?php

use App\Services\NotificationSendService;
use App\Models\Notification;
use App\Models\NotificationRecipient;
use App\Models\NotificationGroup;
use App\Models\NotificationGroupMember;
use App\Models\User;
use App\Exceptions\ValidationException;
use App\Exceptions\ResourceNotFoundException;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

beforeEach(function () {
    $this->service = new NotificationSendService();
});

describe('NotificationSendService', function () {
    
    describe('sendNotification', function () {
        
        it('모든 사용자에게 알림을 전송할 수 있다', function () {
            // Given: 활성 사용자들이 존재
            $sender = User::factory()->create(['status' => User::MEMBER_STATUS_ACTIVE]);
            $users = User::factory()->count(3)->create(['status' => User::MEMBER_STATUS_ACTIVE]);
            
            $data = [
                'title' => '테스트 알림',
                'content' => '테스트 내용',
                'sender_id' => $sender->id,
                'target_type' => 'all'
            ];

            // When: 알림을 전송
            $result = $this->service->sendNotification($data);

            // Then: 알림이 생성되고 모든 활성 사용자에게 전송됨
            expect($result)->toHaveKeys(['notification_id', 'recipients_count', 'target_type', 'sent_at']);
            expect($result['recipients_count'])->toBe(4); // sender + 3 users
            expect($result['target_type'])->toBe('all');

            // 알림이 데이터베이스에 저장되었는지 확인
            $this->assertDatabaseHas('notifications', [
                'id' => $result['notification_id'],
                'title' => '테스트 알림',
                'content' => '테스트 내용',
                'sender_id' => $sender->id,
                'target_type' => 'all',
                'status' => 'sent'
            ]);

            // 수신자 정보가 저장되었는지 확인
            expect(NotificationRecipient::where('notification_id', $result['notification_id'])->count())
                ->toBe(4);
        });

        it('그룹 멤버에게 알림을 전송할 수 있다', function () {
            // Given: 그룹과 멤버들이 존재
            $sender = User::factory()->create(['status' => User::MEMBER_STATUS_ACTIVE]);
            $group = NotificationGroup::factory()->create(['is_active' => true]);
            $members = User::factory()->count(2)->create(['status' => User::MEMBER_STATUS_ACTIVE]);
            
            foreach ($members as $member) {
                NotificationGroupMember::create([
                    'group_id' => $group->id,
                    'user_id' => $member->id
                ]);
            }

            $data = [
                'title' => '그룹 알림',
                'content' => '그룹 내용',
                'sender_id' => $sender->id,
                'target_type' => 'group',
                'target_ids' => [$group->id]
            ];

            // When: 그룹에 알림을 전송
            $result = $this->service->sendNotification($data);

            // Then: 그룹 멤버들에게 알림이 전송됨
            expect($result['recipients_count'])->toBe(2);
            expect($result['target_type'])->toBe('group');

            // 수신자 정보 확인
            $recipientUserIds = NotificationRecipient::where('notification_id', $result['notification_id'])
                ->pluck('user_id')
                ->toArray();
            
            expect($recipientUserIds)->toContain($members[0]->id);
            expect($recipientUserIds)->toContain($members[1]->id);
        });

        it('개별 사용자에게 알림을 전송할 수 있다', function () {
            // Given: 개별 사용자들이 존재
            $sender = User::factory()->create(['status' => User::MEMBER_STATUS_ACTIVE]);
            $users = User::factory()->count(2)->create(['status' => User::MEMBER_STATUS_ACTIVE]);

            $data = [
                'title' => '개별 알림',
                'content' => '개별 내용',
                'sender_id' => $sender->id,
                'target_type' => 'individual',
                'target_ids' => [$users[0]->id, $users[1]->id]
            ];

            // When: 개별 사용자에게 알림을 전송
            $result = $this->service->sendNotification($data);

            // Then: 선택된 사용자들에게 알림이 전송됨
            expect($result['recipients_count'])->toBe(2);
            expect($result['target_type'])->toBe('individual');

            // 수신자 정보 확인
            $recipientUserIds = NotificationRecipient::where('notification_id', $result['notification_id'])
                ->pluck('user_id')
                ->toArray();
            
            expect($recipientUserIds)->toContain($users[0]->id);
            expect($recipientUserIds)->toContain($users[1]->id);
        });

        it('제목이 비어있으면 ValidationException을 발생시킨다', function () {
            $sender = User::factory()->create();
            
            $data = [
                'title' => '',
                'content' => '테스트 내용',
                'sender_id' => $sender->id,
                'target_type' => 'all'
            ];

            expect(fn() => $this->service->sendNotification($data))
                ->toThrow(ValidationException::class, '알림 제목은 필수입니다.');
        });

        it('내용이 비어있으면 ValidationException을 발생시킨다', function () {
            $sender = User::factory()->create();
            
            $data = [
                'title' => '테스트 알림',
                'content' => '',
                'sender_id' => $sender->id,
                'target_type' => 'all'
            ];

            expect(fn() => $this->service->sendNotification($data))
                ->toThrow(ValidationException::class, '알림 내용은 필수입니다.');
        });

        it('존재하지 않는 그룹 ID로 전송하면 ResourceNotFoundException을 발생시킨다', function () {
            $sender = User::factory()->create();
            
            $data = [
                'title' => '테스트 알림',
                'content' => '테스트 내용',
                'sender_id' => $sender->id,
                'target_type' => 'group',
                'target_ids' => [999] // 존재하지 않는 그룹 ID
            ];

            expect(fn() => $this->service->sendNotification($data))
                ->toThrow(ResourceNotFoundException::class);
        });

        it('존재하지 않는 사용자 ID로 전송하면 ResourceNotFoundException을 발생시킨다', function () {
            $sender = User::factory()->create();
            
            // 존재하지 않는 사용자 ID 생성 (현재 최대 ID + 1000)
            $maxUserId = User::max('id') ?? 0;
            $nonExistentUserId = $maxUserId + 1000;
            
            $data = [
                'title' => '테스트 알림',
                'content' => '테스트 내용',
                'sender_id' => $sender->id,
                'target_type' => 'individual',
                'target_ids' => [$nonExistentUserId]
            ];

            expect(fn() => $this->service->sendNotification($data))
                ->toThrow(ResourceNotFoundException::class);
        });

        it('비활성 사용자는 수신자 목록에서 제외된다', function () {
            // Given: 활성 사용자와 비활성 사용자가 존재
            $sender = User::factory()->create(['status' => User::MEMBER_STATUS_ACTIVE]);
            $activeUser = User::factory()->create(['status' => User::MEMBER_STATUS_ACTIVE]);
            $inactiveUser = User::factory()->create(['status' => User::MEMBER_STATUS_PAUSE]);

            $data = [
                'title' => '테스트 알림',
                'content' => '테스트 내용',
                'sender_id' => $sender->id,
                'target_type' => 'all'
            ];

            // When: 모든 사용자에게 알림을 전송
            $result = $this->service->sendNotification($data);

            // Then: 활성 사용자만 수신자 목록에 포함됨
            expect($result['recipients_count'])->toBe(2); // sender + activeUser만

            $recipientUserIds = NotificationRecipient::where('notification_id', $result['notification_id'])
                ->pluck('user_id')
                ->toArray();
            
            expect($recipientUserIds)->toContain($sender->id);
            expect($recipientUserIds)->toContain($activeUser->id);
            expect($recipientUserIds)->not->toContain($inactiveUser->id);
        });
    });

    describe('getNotificationStats', function () {
        
        it('알림 전송 통계를 조회할 수 있다', function () {
            // Given: 알림 전송을 통해 데이터 생성
            $sender = User::factory()->create(['status' => User::MEMBER_STATUS_ACTIVE]);
            $users = User::factory()->count(3)->create(['status' => User::MEMBER_STATUS_ACTIVE]);
            
            // 알림 전송
            $data = [
                'title' => '통계 테스트 알림',
                'content' => '통계 테스트 내용',
                'sender_id' => $sender->id,
                'target_type' => 'individual',
                'target_ids' => $users->pluck('id')->toArray()
            ];
            
            $result = $this->service->sendNotification($data);
            $notificationId = $result['notification_id'];

            // 일부 수신자의 전달 및 읽음 상태 업데이트
            $recipients = NotificationRecipient::where('notification_id', $notificationId)->get();
            
            // 첫 번째 수신자: 전달됨 + 읽음
            $recipients[0]->update([
                'delivered_at' => now(),
                'read_at' => now()
            ]);
            
            // 두 번째 수신자: 전달됨만
            $recipients[1]->update([
                'delivered_at' => now()
            ]);
            
            // 세 번째 수신자: 전달되지 않음 (기본 상태 유지)

            // When: 통계를 조회
            $stats = $this->service->getNotificationStats($notificationId);

            // Then: 올바른 통계가 반환됨
            expect($stats)->toHaveKeys([
                'notification_id', 'title', 'sent_at', 'total_recipients',
                'delivered_count', 'read_count', 'delivery_rate', 'read_rate'
            ]);
            
            expect($stats['total_recipients'])->toBe(3);
            expect($stats['delivered_count'])->toBe(2);
            expect($stats['read_count'])->toBe(1);
            expect($stats['delivery_rate'])->toBe(66.67);
            expect($stats['read_rate'])->toBe(33.33);
        });

        it('존재하지 않는 알림 ID로 조회하면 ResourceNotFoundException을 발생시킨다', function () {
            expect(fn() => $this->service->getNotificationStats('non-existent-id'))
                ->toThrow(ResourceNotFoundException::class, '알림을 찾을 수 없습니다.');
        });
    });
});