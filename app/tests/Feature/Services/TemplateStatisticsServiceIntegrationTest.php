<?php

namespace Tests\Feature\Services;

use App\Models\NotificationTemplate;
use App\Models\User;
use App\Services\TemplateStatisticsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * TemplateStatisticsService 통합 테스트
 */
class TemplateStatisticsServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private TemplateStatisticsService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(TemplateStatisticsService::class);
    }

    /**
     * 사용 통계 서비스 통합 테스트
     */
    public function test_statistics_service_integration(): void
    {
        // Given: 테스트 데이터 생성
        $user = User::factory()->create();

        // 다양한 사용 횟수를 가진 템플릿들 생성
        $template1 = NotificationTemplate::factory()->create([
            'name' => 'High Usage Template',
            'usage_count' => 100,
            'priority' => NotificationTemplate::PRIORITY_HIGH,
            'created_by' => $user->id,
        ]);

        $template2 = NotificationTemplate::factory()->create([
            'name' => 'Medium Usage Template',
            'usage_count' => 50,
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'created_by' => $user->id,
        ]);

        $template3 = NotificationTemplate::factory()->create([
            'name' => 'Low Usage Template',
            'usage_count' => 5,
            'priority' => NotificationTemplate::PRIORITY_LOW,
            'created_by' => $user->id,
        ]);

        $template4 = NotificationTemplate::factory()->create([
            'name' => 'Unused Template',
            'usage_count' => 0,
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'created_by' => $user->id,
        ]);

        // When & Then: 사용 횟수 증가 테스트
        $result = $this->service->incrementUsage($template4->id);
        $this->assertTrue($result);

        // 데이터베이스에서 실제로 증가했는지 확인
        $template4->refresh();
        $this->assertEquals(1, $template4->usage_count);

        // When & Then: 사용 통계 조회 테스트
        $statistics = $this->service->getUsageStatistics();

        $this->assertArrayHasKey('total_templates', $statistics);
        $this->assertArrayHasKey('total_usage', $statistics);
        $this->assertArrayHasKey('average_usage', $statistics);
        $this->assertEquals(4, $statistics['total_templates']);
        $this->assertEquals(156, $statistics['total_usage']); // 100 + 50 + 5 + 1

        // When & Then: 우선순위별 개수 조회 테스트
        $priorityCounts = $this->service->getCountByPriority();

        $this->assertArrayHasKey(NotificationTemplate::PRIORITY_HIGH, $priorityCounts);
        $this->assertArrayHasKey(NotificationTemplate::PRIORITY_NORMAL, $priorityCounts);
        $this->assertArrayHasKey(NotificationTemplate::PRIORITY_LOW, $priorityCounts);
        $this->assertEquals(1, $priorityCounts[NotificationTemplate::PRIORITY_HIGH]);
        $this->assertEquals(2, $priorityCounts[NotificationTemplate::PRIORITY_NORMAL]);
        $this->assertEquals(1, $priorityCounts[NotificationTemplate::PRIORITY_LOW]);

        // When & Then: 사용 분포 조회 테스트
        $distribution = $this->service->getUsageDistribution();

        $this->assertArrayHasKey('unused', $distribution);
        $this->assertArrayHasKey('low', $distribution);
        $this->assertArrayHasKey('medium', $distribution);
        $this->assertArrayHasKey('high', $distribution);
        $this->assertEquals(0, $distribution['unused']); // template4가 1회 사용됨
        $this->assertEquals(2, $distribution['low']); // template3(5회), template4(1회)
        $this->assertEquals(1, $distribution['medium']); // template2(50회)
        $this->assertEquals(1, $distribution['high']); // template1(100회)

        // When & Then: 사용되지 않은 템플릿 조회 테스트
        $unusedTemplates = $this->service->getUnusedTemplates();
        $this->assertCount(0, $unusedTemplates); // 모든 템플릿이 1회 이상 사용됨

        // When & Then: 사용 횟수 범위별 템플릿 조회 테스트
        $rangeTemplates = $this->service->getTemplatesByUsageRange(10, 100);
        $this->assertCount(2, $rangeTemplates); // template1(100회), template2(50회)

        // When & Then: 인기 템플릿 조회 테스트
        $popularTemplates = $this->service->getPopularTemplates(2);
        $this->assertCount(2, $popularTemplates);
        $this->assertEquals($template1->id, $popularTemplates->first()->id); // 가장 많이 사용된 템플릿

        // When & Then: 최근 템플릿 조회 테스트
        $recentTemplates = $this->service->getRecentTemplates(2);
        $this->assertCount(2, $recentTemplates);

        // When & Then: 통계 요약 정보 조회 테스트
        $summary = $this->service->getStatisticsSummary();

        $this->assertArrayHasKey('basic_statistics', $summary);
        $this->assertArrayHasKey('priority_distribution', $summary);
        $this->assertArrayHasKey('usage_distribution', $summary);
        $this->assertArrayHasKey('unused_templates_count', $summary);
        $this->assertArrayHasKey('generated_at', $summary);
        $this->assertEquals(0, $summary['unused_templates_count']);

        // When & Then: 사용 트렌드 분석 테스트
        $trends = $this->service->getUsageTrends(30);

        $this->assertArrayHasKey('analysis_period_days', $trends);
        $this->assertArrayHasKey('current_statistics', $trends);
        $this->assertArrayHasKey('top_popular_templates', $trends);
        $this->assertArrayHasKey('recent_templates', $trends);
        $this->assertArrayHasKey('generated_at', $trends);
        $this->assertEquals(30, $trends['analysis_period_days']);
    }

    /**
     * 예외 상황 통합 테스트
     */
    public function test_exception_scenarios(): void
    {
        // Given: 존재하지 않는 템플릿 ID
        $nonExistentId = 99999;

        // When & Then: 존재하지 않는 템플릿 사용 횟수 증가 시도
        $this->expectException(\App\Exceptions\BusinessException::class);
        $this->expectExceptionMessage('템플릿을 찾을 수 없습니다.');

        $this->service->incrementUsage($nonExistentId);
    }

    /**
     * 검증 실패 통합 테스트
     */
    public function test_validation_failures(): void
    {
        // When & Then: 잘못된 사용 횟수 범위
        $this->expectException(\App\Exceptions\ValidationException::class);
        $this->expectExceptionMessage('최소 사용 횟수는 0 이상이어야 합니다.');

        $this->service->getTemplatesByUsageRange(-1, 10);
    }
}
