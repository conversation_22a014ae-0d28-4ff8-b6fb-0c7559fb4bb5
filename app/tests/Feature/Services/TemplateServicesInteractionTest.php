<?php

namespace Tests\Feature\Services;

use App\Exceptions\BusinessException;
use App\Exceptions\ValidationException;
use App\Models\NotificationTemplate;
use App\Models\User;
use App\Services\TemplateCrudService;
use App\Services\TemplateLoggingService;
use App\Services\TemplatePermissionService;
use App\Services\TemplateSearchService;
use App\Services\TemplateStatisticsService;
use App\Services\TemplateValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * 템플릿 서비스 간 상호작용 테스트
 *
 * 리팩토링된 각 서비스들이 올바르게 상호작용하는지 검증합니다.
 */
class TemplateServicesInteractionTest extends TestCase
{
    use RefreshDatabase;

    private TemplateCrudService $crudService;

    private TemplateSearchService $searchService;

    private TemplateStatisticsService $statisticsService;

    private TemplatePermissionService $permissionService;

    private TemplateValidationService $validationService;

    private TemplateLoggingService $loggingService;

    private User $adminUser;

    private User $normalUser;

    protected function setUp(): void
    {
        parent::setUp();

        // 각 서비스 인스턴스 생성
        $this->crudService = app(TemplateCrudService::class);
        $this->searchService = app(TemplateSearchService::class);
        $this->statisticsService = app(TemplateStatisticsService::class);
        $this->permissionService = app(TemplatePermissionService::class);
        $this->validationService = app(TemplateValidationService::class);
        $this->loggingService = app(TemplateLoggingService::class);

        // 테스트 사용자 생성
        $this->adminUser = User::factory()->create(['role' => 'Super-Admin']);
        $this->normalUser = User::factory()->create(['role' => User::ROLE_EMPLOYEE]);
    }

    /**
     * CRUD와 Validation 서비스 상호작용 테스트
     */
    public function test_crud_and_validation_service_interaction(): void
    {
        // 유효하지 않은 데이터로 생성 시도
        $invalidData = [
            'name' => '', // 빈 이름
            'title' => 'a', // 너무 짧은 제목
            'content' => '', // 빈 내용
            'priority' => 'invalid_priority', // 잘못된 우선순위
        ];

        // ValidationService를 통한 검증 실패 확인
        $this->expectException(ValidationException::class);
        $validatedData = $this->validationService->validateForCreate($invalidData);

        // CrudService는 검증된 데이터만 받아야 함
        $this->crudService->create($this->adminUser->id, $validatedData);
    }

    /**
     * CRUD와 Permission 서비스 상호작용 테스트
     */
    public function test_crud_and_permission_service_interaction(): void
    {
        // 관리자가 템플릿 생성
        $templateData = [
            'name' => '권한테스트템플릿',
            'title' => '권한 테스트 템플릿',
            'content' => '권한 테스트 내용',
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'is_active' => true,
        ];

        $template = $this->crudService->create($this->adminUser->id, $templateData);

        // 일반 사용자의 조회 권한 확인
        $canRead = $this->permissionService->canRead($this->normalUser->id, $template->id);
        $this->assertTrue($canRead);

        // 일반 사용자의 수정 권한 확인
        $canUpdate = $this->permissionService->canUpdate($this->normalUser->id, $template->id);
        $this->assertFalse($canUpdate);

        // 권한이 없는 사용자의 수정 시도
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('템플릿을 update할 권한이 없습니다.');

        $this->permissionService->validatePermission(
            $this->normalUser->id,
            $template,
            'update'
        );
    }

    /**
     * Search와 Statistics 서비스 상호작용 테스트
     */
    public function test_search_and_statistics_service_interaction(): void
    {
        // 테스트 템플릿들 생성
        $templates = [];
        for ($i = 1; $i <= 5; $i++) {
            $template = $this->crudService->create($this->adminUser->id, [
                'name' => "검색테스트템플릿{$i}",
                'title' => "검색 테스트 템플릿 {$i}",
                'content' => "검색 테스트 내용 {$i}",
                'priority' => $i % 2 === 0 ? NotificationTemplate::PRIORITY_HIGH : NotificationTemplate::PRIORITY_NORMAL,
                'is_active' => true,
            ]);
            $templates[] = $template;

            // 사용 횟수 차등 적용
            for ($j = 0; $j < $i * 2; $j++) {
                $this->statisticsService->incrementUsage($template->id);
            }
        }

        // SearchService를 통한 인기 템플릿 조회
        $popularTemplates = $this->searchService->getOrderedByUsage($this->adminUser->id, 'desc');
        $this->assertCount(5, $popularTemplates);
        $this->assertEquals(10, $popularTemplates->first()->usage_count); // 템플릿5가 가장 많이 사용됨

        // StatisticsService를 통한 인기 템플릿 조회와 비교
        $statisticsPopular = $this->statisticsService->getPopularTemplates(3);
        $this->assertCount(3, $statisticsPopular);
        $this->assertEquals($popularTemplates->first()->id, $statisticsPopular->first()->id);

        // SearchService를 통한 우선순위별 조회
        $highPriorityTemplates = $this->searchService->getByPriority($this->adminUser->id, NotificationTemplate::PRIORITY_HIGH);

        // StatisticsService를 통한 우선순위별 개수와 비교
        $priorityCounts = $this->statisticsService->getCountByPriority();
        $this->assertEquals($highPriorityTemplates->count(), $priorityCounts[NotificationTemplate::PRIORITY_HIGH]);
    }

    /**
     * 모든 서비스 통합 워크플로우 테스트
     */
    public function test_all_services_integration_workflow(): void
    {
        // 1. Validation -> CRUD -> Logging 워크플로우
        $templateData = [
            'name' => '통합워크플로우테스트',
            'title' => '통합 워크플로우 테스트 템플릿',
            'content' => '모든 서비스가 함께 동작하는 테스트',
            'priority' => NotificationTemplate::PRIORITY_HIGH,
            'is_active' => true,
        ];

        // 데이터 검증 (created_by 추가)
        $templateDataWithCreator = array_merge($templateData, ['created_by' => $this->adminUser->id]);
        $validatedData = $this->validationService->validateForCreate($templateDataWithCreator);
        $this->assertArrayHasKey('name', $validatedData);
        $this->assertArrayHasKey('title', $validatedData);

        // 권한 확인
        $canCreate = $this->permissionService->canCreate($this->adminUser->id);
        $this->assertTrue($canCreate);

        // 템플릿 생성
        $template = $this->crudService->create($this->adminUser->id, $validatedData);
        $this->assertInstanceOf(NotificationTemplate::class, $template);

        // 2. Search -> Statistics 워크플로우
        // 검색을 통한 템플릿 조회
        $searchResults = $this->searchService->search($this->adminUser->id, '통합워크플로우', []);
        $this->assertCount(1, $searchResults);
        $foundTemplate = $searchResults->first();
        $this->assertEquals($template->id, $foundTemplate->id);

        // 사용 횟수 증가
        $incrementResult = $this->statisticsService->incrementUsage($template->id);
        $this->assertTrue($incrementResult);

        // 통계 확인
        $statistics = $this->statisticsService->getUsageStatistics();
        $this->assertEquals(1, $statistics['total_templates']);
        $this->assertEquals(1, $statistics['total_usage']);

        // 3. Permission -> CRUD -> Logging 워크플로우 (수정)
        $updateData = [
            'title' => '수정된 제목',
            'content' => '수정된 내용',
        ];

        // 수정 권한 확인
        $canUpdate = $this->permissionService->canUpdate($this->adminUser->id, $template->id);
        $this->assertTrue($canUpdate);

        // 수정 데이터 검증
        $updateDataWithRequired = array_merge($updateData, [
            'name' => '통합워크플로우테스트',
            'content' => '수정된 내용',
        ]);
        $validatedUpdateData = $this->validationService->validateForUpdate($updateDataWithRequired, $template->id);

        // 템플릿 수정
        $updatedTemplate = $this->crudService->update($this->adminUser->id, $template->id, $validatedUpdateData);
        $this->assertEquals($updateData['title'], $updatedTemplate->title);
        $this->assertEquals($updateData['content'], $updatedTemplate->content);

        // 4. 최종 검증 - 모든 서비스를 통한 데이터 일관성 확인
        // Search를 통한 수정된 템플릿 조회
        $searchAfterUpdate = $this->searchService->search($this->adminUser->id, '수정된', []);
        $this->assertCount(1, $searchAfterUpdate);

        // Statistics를 통한 사용 통계 확인
        $finalStatistics = $this->statisticsService->getUsageStatistics();
        $this->assertEquals(1, $finalStatistics['total_templates']);
        $this->assertEquals(1, $finalStatistics['total_usage']);

        // CRUD를 통한 최종 조회
        $finalTemplate = $this->crudService->findById($template->id);
        $this->assertEquals($updateData['title'], $finalTemplate->title);
        $this->assertEquals(1, $finalTemplate->usage_count);
    }

    /**
     * 에러 전파 및 처리 테스트
     */
    public function test_error_propagation_between_services(): void
    {
        // 존재하지 않는 템플릿에 대한 작업 시도
        $nonExistentId = 99999;

        // CRUD 서비스에서 발생한 예외가 올바르게 전파되는지 확인
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessageMatches('/템플릿.*을 찾을 수 없습니다/');

        $this->crudService->findById($nonExistentId);
    }

    /**
     * 트랜잭션 롤백 테스트
     */
    public function test_transaction_rollback_between_services(): void
    {
        $templateData = [
            'name' => '트랜잭션테스트',
            'title' => '트랜잭션 테스트 템플릿',
            'content' => '트랜잭션 테스트 내용',
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'is_active' => true,
        ];

        // 정상적인 템플릿 생성
        $template = $this->crudService->create($this->adminUser->id, $templateData);
        $this->assertInstanceOf(NotificationTemplate::class, $template);

        // 초기 템플릿 개수 확인
        $initialCount = NotificationTemplate::count();

        try {
            // 의도적으로 실패하는 작업 수행
            $this->crudService->update($this->adminUser->id, $template->id, [
                'name' => '트랜잭션테스트',
                'title' => '트랜잭션 테스트 템플릿',
                'content' => '트랜잭션 테스트 내용',
                'priority' => 'invalid_priority', // 잘못된 우선순위
            ]);
        } catch (ValidationException $e) {
            // 예외 발생 후 데이터 일관성 확인
            $currentCount = NotificationTemplate::count();
            $this->assertEquals($initialCount, $currentCount);

            // 원본 템플릿이 변경되지 않았는지 확인
            $unchangedTemplate = $this->crudService->findById($template->id);
            $this->assertEquals($templateData['priority'], $unchangedTemplate->priority);
        }
    }

    /**
     * 캐시 일관성 테스트
     */
    public function test_cache_consistency_between_services(): void
    {
        // 템플릿 생성
        $template = $this->crudService->create($this->adminUser->id, [
            'name' => '캐시테스트템플릿',
            'title' => '캐시 테스트 템플릿',
            'content' => '캐시 테스트 내용',
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'is_active' => true,
        ]);

        // 첫 번째 통계 조회 (캐시 생성)
        $firstStatistics = $this->statisticsService->getUsageStatistics();

        // 사용 횟수 증가
        $this->statisticsService->incrementUsage($template->id);

        // 두 번째 통계 조회 (캐시 갱신 확인)
        $secondStatistics = $this->statisticsService->getUsageStatistics();

        // 통계가 올바르게 갱신되었는지 확인
        $this->assertEquals($firstStatistics['total_usage'] + 1, $secondStatistics['total_usage']);
    }

    /**
     * 동시성 처리 테스트
     */
    public function test_concurrent_operations_between_services(): void
    {
        $template = $this->crudService->create($this->adminUser->id, [
            'name' => '동시성테스트템플릿',
            'title' => '동시성 테스트 템플릿',
            'content' => '동시성 테스트 내용',
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'is_active' => true,
        ]);

        // 동시에 여러 서비스에서 같은 템플릿에 접근
        $operations = [];

        // 사용 횟수 증가 (Statistics Service)
        for ($i = 0; $i < 5; $i++) {
            $operations[] = function () use ($template) {
                return $this->statisticsService->incrementUsage($template->id);
            };
        }

        // 템플릿 조회 (CRUD Service)
        for ($i = 0; $i < 3; $i++) {
            $operations[] = function () use ($template) {
                return $this->crudService->findById($template->id);
            };
        }

        // 검색 (Search Service)
        for ($i = 0; $i < 2; $i++) {
            $operations[] = function () {
                return $this->searchService->search($this->adminUser->id, '동시성테스트', []);
            };
        }

        // 모든 작업 실행
        $results = [];
        foreach ($operations as $operation) {
            $results[] = $operation();
        }

        // 결과 검증
        $this->assertCount(10, $results);

        // 최종 사용 횟수 확인
        $finalTemplate = $this->crudService->findById($template->id);
        $this->assertEquals(5, $finalTemplate->usage_count);
    }
}
