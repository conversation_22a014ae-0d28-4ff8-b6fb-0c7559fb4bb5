<?php

namespace Tests\Feature\Services;

use App\Models\RepairCostTypeProcessMapping;
use App\Models\RepairProcess;
use App\Services\RepairCostTypeProcessMappingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RepairProcessMappingServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private RepairCostTypeProcessMappingService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(RepairCostTypeProcessMappingService::class);
    }

    /** @test */
    public function 실제_데이터베이스와_연동하여_매핑_생성_및_조회가_가능하다()
    {
        // Given: 실제 RepairProcess 데이터 생성
        $repairProcess = RepairProcess::create([
            'name' => '액정 교체',
            'code' => 'LCD_REPLACE_001',
        ]);

        $repairType = RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS;

        // When: 매핑 생성
        $mapping = $this->service->createMapping($repairProcess->code, $repairType);

        // Then: 데이터베이스에 저장되고 조회 가능
        $this->assertDatabaseHas('repair_process_mappings', [
            'process_code' => $repairProcess->code,
            'repair_type' => $repairType,
            'is_active' => true,
        ]);

        // 서비스를 통한 조회 확인
        $retrievedType = $this->service->getRepairTypeByCode($repairProcess->code);
        $this->assertEquals($repairType, $retrievedType);

        // 관계 조회 확인
        $mappingWithProcess = RepairCostTypeProcessMapping::with('repairProcess')
            ->where('id', $mapping->id)
            ->first();

        $this->assertNotNull($mappingWithProcess->repairProcess);
        $this->assertEquals($repairProcess->name, $mappingWithProcess->repairProcess->name);
    }

    /** @test */
    public function 복수의_프로세스에_대한_매핑_관리가_가능하다()
    {
        // Given: 여러 RepairProcess 생성
        $processes = [
            ['name' => '액정 교체', 'code' => 'LCD_001', 'type' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS],
            ['name' => '청소 작업', 'code' => 'CLEAN_001', 'type' => RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING],
            ['name' => '소프트웨어 설치', 'code' => 'SW_001', 'type' => RepairCostTypeProcessMapping::REPAIR_TYPE_SOFTWARE],
            ['name' => '기타 수리', 'code' => 'OTHER_001', 'type' => RepairCostTypeProcessMapping::REPAIR_TYPE_OTHER],
            ['name' => '검수 작업', 'code' => 'INSPECT_001', 'type' => RepairCostTypeProcessMapping::REPAIR_TYPE_INSPECTION],
        ];

        foreach ($processes as $processData) {
            RepairProcess::create([
                'name' => $processData['name'],
                'code' => $processData['code'],
            ]);
        }

        // When: 대량 매핑 생성
        $mappingData = array_map(function ($process) {
            return [
                'process_code' => $process['code'],
                'repair_type' => $process['type'],
            ];
        }, $processes);

        $results = $this->service->bulkUpdateMappings($mappingData);

        // Then: 모든 매핑이 성공적으로 생성됨
        $this->assertCount(5, $results['success']);
        $this->assertCount(0, $results['failed']);

        // 각 매핑이 올바르게 조회됨
        foreach ($processes as $processData) {
            $retrievedType = $this->service->getRepairTypeByCode($processData['code']);
            $this->assertEquals($processData['type'], $retrievedType);
        }

        // 통계 확인
        $stats = $this->service->getMappingStatistics();
        $this->assertEquals(5, $stats['total_processes']);
        $this->assertEquals(5, $stats['mapped_processes']);
        $this->assertEquals(0, $stats['unmapped_processes']);
        $this->assertEquals(100, $stats['mapping_rate']);
    }

    /** @test */
    public function 매핑_수정_및_상태_변경이_트랜잭션으로_처리된다()
    {
        // Given
        $repairProcess = RepairProcess::create([
            'name' => '테스트 프로세스',
            'code' => 'TRANS_TEST_001',
        ]);

        $originalType = RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS;
        $newType = RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING;

        $mapping = $this->service->createMapping($repairProcess->code, $originalType);

        // When: 매핑 수정
        $updatedMapping = $this->service->updateMapping($mapping->id, [
            'repair_type' => $newType,
        ]);

        // Then: 변경사항이 데이터베이스에 반영됨
        $this->assertDatabaseHas('repair_process_mappings', [
            'id' => $mapping->id,
            'process_code' => $repairProcess->code,
            'repair_type' => $newType,
            'is_active' => true,
        ]);

        $this->assertEquals($newType, $updatedMapping->repair_type);

        // When: 매핑 비활성화
        $this->service->deleteMapping($mapping->id);

        // Then: 상태가 변경됨
        $this->assertDatabaseHas('repair_process_mappings', [
            'id' => $mapping->id,
            'is_active' => false,
        ]);

        // 비활성화된 매핑은 조회되지 않음
        $retrievedType = $this->service->getRepairTypeByCode($repairProcess->code);
        $this->assertNull($retrievedType);
    }

    /** @test */
    public function 매핑되지_않은_프로세스_조회_시_기본값_처리가_올바르게_작동한다()
    {
        // Given: 매핑되지 않은 프로세스
        $unmappedProcess = RepairProcess::create([
            'name' => '매핑되지 않은 프로세스',
            'code' => 'UNMAPPED_001',
        ]);

        // When: 기본값 없이 조회
        $typeWithoutDefault = $this->service->getRepairTypeByCode($unmappedProcess->code);

        // Then: null 반환
        $this->assertNull($typeWithoutDefault);

        // When: 기본값과 함께 조회
        $typeWithDefault = $this->service->getRepairTypeByCodeWithDefault($unmappedProcess->code);

        // Then: 기본값 반환
        $this->assertEquals(RepairCostTypeProcessMappingService::DEFAULT_REPAIR_TYPE, $typeWithDefault);
    }

    /** @test */
    public function 서비스_인스턴스가_laravel_컨테이너에서_올바르게_해결된다()
    {
        // Given & When: 컨테이너에서 서비스 해결
        $serviceFromContainer = app(RepairCostTypeProcessMappingService::class);
        $anotherInstance = resolve(RepairCostTypeProcessMappingService::class);

        // Then: 인스턴스가 올바르게 생성됨
        $this->assertInstanceOf(RepairCostTypeProcessMappingService::class, $serviceFromContainer);
        $this->assertInstanceOf(RepairCostTypeProcessMappingService::class, $anotherInstance);

        // 싱글톤이 아니므로 다른 인스턴스
        $this->assertNotSame($serviceFromContainer, $anotherInstance);
    }

    /** @test */
    public function 실제_수리_프로세스_데이터와_연동하여_매핑이_작동한다()
    {
        // Given: 실제 수리 프로세스와 유사한 데이터 생성
        $realProcesses = [
            ['name' => 'LCD 패널 교체', 'code' => 'RP_LCD_REPLACE'],
            ['name' => '키보드 청소', 'code' => 'RP_KB_CLEAN'],
            ['name' => 'OS 재설치', 'code' => 'RP_OS_INSTALL'],
            ['name' => '하드웨어 점검', 'code' => 'RP_HW_CHECK'],
            ['name' => '기타 수리', 'code' => 'RP_MISC_REPAIR'],
        ];

        $expectedMappings = [
            'RP_LCD_REPLACE' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS,
            'RP_KB_CLEAN' => RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING,
            'RP_OS_INSTALL' => RepairCostTypeProcessMapping::REPAIR_TYPE_SOFTWARE,
            'RP_HW_CHECK' => RepairCostTypeProcessMapping::REPAIR_TYPE_INSPECTION,
            'RP_MISC_REPAIR' => RepairCostTypeProcessMapping::REPAIR_TYPE_OTHER,
        ];

        // RepairProcess 생성
        foreach ($realProcesses as $processData) {
            RepairProcess::create($processData);
        }

        // When: 각 프로세스에 대한 매핑 생성
        foreach ($expectedMappings as $code => $type) {
            $this->service->createMapping($code, $type);
        }

        // Then: 모든 매핑이 올바르게 작동
        foreach ($expectedMappings as $code => $expectedType) {
            $actualType = $this->service->getRepairTypeByCode($code);
            $this->assertEquals($expectedType, $actualType,
                "프로세스 코드 {$code}의 매핑이 예상과 다릅니다.");
        }

        // 통계 확인
        $stats = $this->service->getMappingStatistics();
        $this->assertEquals(5, $stats['mapped_processes']);
        $this->assertEquals(100, $stats['mapping_rate']);

        // 수리 유형별 분포 확인
        $distribution = $stats['repair_type_distribution'];
        $this->assertEquals(1, $distribution[RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS]);
        $this->assertEquals(1, $distribution[RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING]);
        $this->assertEquals(1, $distribution[RepairCostTypeProcessMapping::REPAIR_TYPE_SOFTWARE]);
        $this->assertEquals(1, $distribution[RepairCostTypeProcessMapping::REPAIR_TYPE_INSPECTION]);
        $this->assertEquals(1, $distribution[RepairCostTypeProcessMapping::REPAIR_TYPE_OTHER]);
    }
}
