<?php

namespace Tests\Feature\Services;

use App\Exceptions\BusinessException;
use App\Exceptions\ValidationException;
use App\Models\NotificationTemplate;
use App\Models\User;
use App\Services\TemplateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * TemplateService 통합 테스트
 *
 * 리팩토링된 서비스 간 상호작용을 검증하고
 * 전체 워크플로우가 올바르게 동작하는지 확인합니다.
 */
class TemplateServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private TemplateService $templateService;

    private User $adminUser;

    private User $normalUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->templateService = app(TemplateService::class);

        // 테스트 사용자 생성
        $this->adminUser = User::factory()->create([
            'role' => 'Super-Admin',
        ]);

        $this->normalUser = User::factory()->create([
            'role' => User::ROLE_EMPLOYEE,
        ]);
    }

    /**
     * 전체 템플릿 생명주기 워크플로우 테스트
     * 생성 -> 조회 -> 수정 -> 사용 -> 통계 -> 삭제
     */
    public function test_complete_template_lifecycle_workflow(): void
    {
        // 1. 템플릿 생성 (CRUD + Validation + Permission + Logging)
        $templateData = [
            'name' => '통합테스트템플릿',
            'title' => '통합 테스트용 템플릿',
            'content' => '이것은 통합 테스트를 위한 템플릿입니다.',
            'priority' => NotificationTemplate::PRIORITY_HIGH,
            'is_active' => true,
        ];

        $createdTemplate = $this->templateService->createTemplate(
            $this->adminUser->id,
            $templateData
        );

        $this->assertInstanceOf(NotificationTemplate::class, $createdTemplate);
        $this->assertEquals($templateData['name'], $createdTemplate->name);
        $this->assertEquals($this->adminUser->id, $createdTemplate->created_by);
        $this->assertEquals(0, $createdTemplate->usage_count);

        // 2. 템플릿 조회 (CRUD + Permission)
        $retrievedTemplate = $this->templateService->getTemplateById(
            $this->adminUser->id,
            $createdTemplate->id
        );

        $this->assertEquals($createdTemplate->id, $retrievedTemplate->id);
        $this->assertEquals($createdTemplate->name, $retrievedTemplate->name);

        // 3. 템플릿 수정 (CRUD + Validation + Permission + Logging)
        $updateData = [
            'name' => '통합테스트템플릿',
            'title' => '수정된 템플릿 제목',
            'content' => '수정된 템플릿 내용',
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
        ];

        $updatedTemplate = $this->templateService->updateTemplate(
            $this->adminUser->id,
            $createdTemplate->id,
            $updateData
        );

        $this->assertEquals($updateData['title'], $updatedTemplate->title);
        $this->assertEquals($updateData['content'], $updatedTemplate->content);
        $this->assertEquals($updateData['priority'], $updatedTemplate->priority);

        // 4. 템플릿 사용 (Statistics + Logging)
        $usageResult = $this->templateService->incrementUsage($this->adminUser->id, $createdTemplate->id);
        $this->assertIsArray($usageResult);
        $this->assertArrayHasKey('template', $usageResult);
        $this->assertArrayHasKey('statistics', $usageResult);

        // 사용 횟수 증가 확인
        $templateAfterUsage = $this->templateService->getTemplateById(
            $this->adminUser->id,
            $createdTemplate->id
        );
        $this->assertEquals(1, $templateAfterUsage->usage_count);

        // 5. 검색 기능 테스트 (Search + Permission)
        $searchResults = $this->templateService->searchTemplates(
            $this->adminUser->id,
            '통합테스트',
            []
        );

        $this->assertCount(1, $searchResults);
        $this->assertEquals($createdTemplate->id, $searchResults->first()->id);

        // 6. 통계 조회 (Statistics)
        $statistics = $this->templateService->getUsageStatistics($this->adminUser->id);

        $this->assertArrayHasKey('total_templates', $statistics);
        $this->assertArrayHasKey('total_usage', $statistics);
        $this->assertEquals(1, $statistics['total_templates']);
        $this->assertEquals(1, $statistics['total_usage']);

        // 7. 우선순위별 조회 (Search + Statistics)
        $highPriorityTemplates = $this->templateService->getTemplatesByPriority(
            $this->adminUser->id,
            NotificationTemplate::PRIORITY_NORMAL
        );

        $this->assertCount(1, $highPriorityTemplates);
        $this->assertEquals($createdTemplate->id, $highPriorityTemplates->first()->id);

        // 8. 템플릿 삭제 (CRUD + Permission + Logging)
        $deleteResult = $this->templateService->deleteTemplate(
            $this->adminUser->id,
            $createdTemplate->id
        );

        $this->assertTrue($deleteResult);

        // 삭제 확인
        $this->expectException(BusinessException::class);
        $this->templateService->getTemplateById(
            $this->adminUser->id,
            $createdTemplate->id
        );
    }

    /**
     * 권한 기반 접근 제어 통합 테스트
     */
    public function test_permission_based_access_control_integration(): void
    {
        // 관리자가 템플릿 생성
        $templateData = [
            'name' => '권한테스트템플릿',
            'title' => '권한 테스트용 템플릿',
            'content' => '권한 테스트 내용',
            'priority' => NotificationTemplate::PRIORITY_NORMAL,
            'is_active' => true,
        ];

        $template = $this->templateService->createTemplate(
            $this->adminUser->id,
            $templateData
        );

        // 일반 사용자 조회 권한 테스트
        $retrievedByNormalUser = $this->templateService->getTemplateById(
            $this->normalUser->id,
            $template->id
        );
        $this->assertEquals($template->id, $retrievedByNormalUser->id);

        // 일반 사용자 수정 권한 테스트 (실패해야 함)
        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('템플릿을 update할 권한이 없습니다.');

        $this->templateService->updateTemplate(
            $this->normalUser->id,
            $template->id,
            [
                'name' => '권한테스트템플릿',
                'title' => '수정 시도',
                'content' => '권한 테스트 내용',
                'priority' => NotificationTemplate::PRIORITY_NORMAL,
            ]
        );
    }

    /**
     * 검색 및 필터링 통합 테스트
     */
    public function test_search_and_filtering_integration(): void
    {
        // 다양한 템플릿 생성
        $templates = [
            [
                'name' => '긴급알림템플릿',
                'title' => '긴급 상황 알림',
                'content' => '긴급 상황이 발생했습니다.',
                'priority' => NotificationTemplate::PRIORITY_URGENT,
                'is_active' => true,
            ],
            [
                'name' => '일반알림템플릿',
                'title' => '일반 공지사항',
                'content' => '일반적인 공지사항입니다.',
                'priority' => NotificationTemplate::PRIORITY_NORMAL,
                'is_active' => true,
            ],
            [
                'name' => '시스템점검알림',
                'title' => '시스템 점검 안내',
                'content' => '시스템 점검이 예정되어 있습니다.',
                'priority' => NotificationTemplate::PRIORITY_HIGH,
                'is_active' => false,
            ],
        ];

        $createdTemplates = [];
        foreach ($templates as $templateData) {
            $createdTemplates[] = $this->templateService->createTemplate(
                $this->adminUser->id,
                $templateData
            );
        }

        // 일부 템플릿 사용
        $this->templateService->incrementUsage($this->adminUser->id, $createdTemplates[0]->id);
        $this->templateService->incrementUsage($this->adminUser->id, $createdTemplates[0]->id);
        $this->templateService->incrementUsage($this->adminUser->id, $createdTemplates[1]->id);

        // 키워드 검색 테스트
        $searchResults = $this->templateService->searchTemplates(
            $this->adminUser->id,
            '알림',
            []
        );
        $this->assertCount(3, $searchResults);

        // 우선순위 필터링 테스트
        $urgentTemplates = $this->templateService->getTemplatesByPriority(
            $this->adminUser->id,
            NotificationTemplate::PRIORITY_URGENT
        );
        $this->assertCount(1, $urgentTemplates);
        $this->assertEquals('긴급알림템플릿', $urgentTemplates->first()->name);

        // 사용 횟수 기준 정렬 테스트
        $templatesByUsage = $this->templateService->getTemplatesOrderedByUsage(
            $this->adminUser->id,
            'desc'
        );
        $this->assertEquals(2, $templatesByUsage->first()->usage_count);

        // 페이지네이션 테스트
        $paginatedTemplates = $this->templateService->getTemplates(
            $this->adminUser->id,
            [
                'page' => 1,
                'per_page' => 2,
            ]
        );

        $this->assertEquals(2, $paginatedTemplates->perPage());
        $this->assertEquals(3, $paginatedTemplates->total());
        $this->assertEquals(2, $paginatedTemplates->lastPage());
    }

    /**
     * 통계 서비스 통합 테스트
     */
    public function test_statistics_service_integration(): void
    {
        // 테스트 데이터 생성
        $templates = [];
        $priorities = [
            NotificationTemplate::PRIORITY_URGENT,
            NotificationTemplate::PRIORITY_HIGH,
            NotificationTemplate::PRIORITY_NORMAL,
            NotificationTemplate::PRIORITY_LOW,
        ];

        foreach ($priorities as $index => $priority) {
            $template = $this->templateService->createTemplate(
                $this->adminUser->id,
                [
                    'name' => "템플릿{$index}",
                    'title' => "제목{$index}",
                    'content' => "내용{$index}",
                    'priority' => $priority,
                    'is_active' => true,
                ]
            );
            $templates[] = $template;

            // 사용 횟수 차등 적용
            for ($i = 0; $i < ($index + 1) * 5; $i++) {
                $this->templateService->incrementUsage($this->adminUser->id, $template->id);
            }
        }

        // 전체 통계 조회
        $statistics = $this->templateService->getUsageStatistics($this->adminUser->id);
        $this->assertEquals(4, $statistics['total_templates']);
        $this->assertEquals(50, $statistics['total_usage']); // 5+10+15+20

        // 우선순위별 개수 조회
        $priorityCounts = $this->templateService->getCountByPriority($this->adminUser->id);
        foreach ($priorities as $priority) {
            $this->assertEquals(1, $priorityCounts[$priority]);
        }

        // 사용 분포 조회
        $distribution = $this->templateService->getUsageDistribution($this->adminUser->id);
        $this->assertArrayHasKey('low', $distribution);
        $this->assertArrayHasKey('medium', $distribution);
        $this->assertArrayHasKey('high', $distribution);

        // 인기 템플릿 조회
        $popularTemplates = $this->templateService->getPopularTemplates($this->adminUser->id, 2);
        $this->assertCount(2, $popularTemplates);
        $this->assertEquals(20, $popularTemplates->first()->usage_count);

        // 사용되지 않은 템플릿 조회
        $unusedTemplates = $this->templateService->getUnusedTemplates($this->adminUser->id);
        $this->assertCount(0, $unusedTemplates);
    }

    /**
     * 예외 처리 통합 테스트
     */
    public function test_exception_handling_integration(): void
    {
        // 유효성 검증 실패 테스트
        $this->expectException(ValidationException::class);
        $this->templateService->createTemplate(
            $this->adminUser->id,
            [] // 필수 필드 누락
        );
    }

    /**
     * 동시성 처리 테스트
     */
    public function test_concurrent_usage_increment(): void
    {
        $template = $this->templateService->createTemplate(
            $this->adminUser->id,
            [
                'name' => '동시성테스트템플릿',
                'title' => '동시성 테스트',
                'content' => '동시성 테스트 내용',
                'priority' => NotificationTemplate::PRIORITY_NORMAL,
                'is_active' => true,
            ]
        );

        // 동시에 여러 번 사용 횟수 증가
        $results = [];
        for ($i = 0; $i < 10; $i++) {
            $results[] = $this->templateService->incrementUsage($this->adminUser->id, $template->id);
        }

        // 모든 증가 작업이 성공했는지 확인
        foreach ($results as $result) {
            $this->assertIsArray($result);
            $this->assertArrayHasKey('template', $result);
            $this->assertArrayHasKey('statistics', $result);
        }

        // 최종 사용 횟수 확인
        $finalTemplate = $this->templateService->getTemplateById(
            $this->adminUser->id,
            $template->id
        );
        $this->assertEquals(10, $finalTemplate->usage_count);
    }
}
