<?php

namespace Tests\Feature\Services;

use App\Models\NotificationTemplate;
use App\Models\User;
use App\Services\TemplateService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * TemplateService 성능 테스트
 *
 * 리팩토링 후 성능 저하가 없는지 확인하고
 * 대용량 데이터 처리 성능을 검증합니다.
 */
class TemplateServicePerformanceTest extends TestCase
{
    use RefreshDatabase;

    private TemplateService $templateService;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->templateService = app(TemplateService::class);
        $this->user = User::factory()->create(['role' => 'Super-Admin']);
    }

    /**
     * 대량 템플릿 생성 성능 테스트
     */
    public function test_bulk_template_creation_performance(): void
    {
        $templateCount = 100;
        $startTime = microtime(true);

        // 100개 템플릿 생성
        for ($i = 1; $i <= $templateCount; $i++) {
            $this->templateService->createTemplate(
                $this->user->id,
                [
                    'name' => "성능테스트템플릿{$i}",
                    'title' => "성능 테스트 템플릿 {$i}",
                    'content' => "성능 테스트를 위한 템플릿 내용 {$i}",
                    'priority' => $this->getRandomPriority(),
                    'is_active' => true,
                ]
            );
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // 성능 기준: 100개 템플릿 생성이 10초 이내에 완료되어야 함
        $this->assertLessThan(10.0, $executionTime,
            "템플릿 {$templateCount}개 생성이 {$executionTime}초 소요됨 (기준: 10초 이내)");

        // 실제로 생성되었는지 확인
        $totalTemplates = NotificationTemplate::count();
        $this->assertEquals($templateCount, $totalTemplates);

        echo "\n성능 테스트 결과: {$templateCount}개 템플릿 생성 시간: ".
             number_format($executionTime, 3)."초\n";
    }

    /**
     * 대량 검색 성능 테스트
     */
    public function test_bulk_search_performance(): void
    {
        // 테스트 데이터 준비 (500개 템플릿)
        $this->createTestTemplates(500);

        $searchQueries = [
            '성능',
            '테스트',
            '템플릿',
            '알림',
            '시스템',
        ];

        $totalSearchTime = 0;
        $searchCount = count($searchQueries);

        foreach ($searchQueries as $query) {
            $startTime = microtime(true);

            $results = $this->templateService->searchTemplates(
                $this->user->id,
                $query,
                []
            );

            $endTime = microtime(true);
            $searchTime = $endTime - $startTime;
            $totalSearchTime += $searchTime;

            // 각 검색이 1초 이내에 완료되어야 함
            $this->assertLessThan(1.0, $searchTime,
                "검색어 '{$query}' 검색이 {$searchTime}초 소요됨 (기준: 1초 이내)");
        }

        $averageSearchTime = $totalSearchTime / $searchCount;

        echo "\n검색 성능 테스트 결과:\n";
        echo "- 총 검색 횟수: {$searchCount}회\n";
        echo '- 총 검색 시간: '.number_format($totalSearchTime, 3)."초\n";
        echo '- 평균 검색 시간: '.number_format($averageSearchTime, 3)."초\n";
    }

    /**
     * 페이지네이션 성능 테스트
     */
    public function test_pagination_performance(): void
    {
        // 테스트 데이터 준비 (1000개 템플릿)
        $this->createTestTemplates(1000);

        $pageSize = 20;
        $totalPages = 10; // 200개 템플릿 조회
        $totalPaginationTime = 0;

        for ($page = 1; $page <= $totalPages; $page++) {
            $startTime = microtime(true);

            $paginatedResults = $this->templateService->getTemplates(
                $this->user->id,
                [
                    'page' => $page,
                    'per_page' => $pageSize,
                ]
            );

            $endTime = microtime(true);
            $paginationTime = $endTime - $startTime;
            $totalPaginationTime += $paginationTime;

            // 각 페이지 조회가 0.5초 이내에 완료되어야 함
            $this->assertLessThan(0.5, $paginationTime,
                "페이지 {$page} 조회가 {$paginationTime}초 소요됨 (기준: 0.5초 이내)");

            // 페이지네이션 결과 검증
            $this->assertEquals($pageSize, $paginatedResults->perPage());
            $this->assertEquals($page, $paginatedResults->currentPage());
        }

        $averagePaginationTime = $totalPaginationTime / $totalPages;

        echo "\n페이지네이션 성능 테스트 결과:\n";
        echo "- 총 페이지 조회 횟수: {$totalPages}회\n";
        echo "- 페이지당 항목 수: {$pageSize}개\n";
        echo '- 총 조회 시간: '.number_format($totalPaginationTime, 3)."초\n";
        echo '- 평균 페이지 조회 시간: '.number_format($averagePaginationTime, 3)."초\n";
    }

    /**
     * 통계 계산 성능 테스트
     */
    public function test_statistics_calculation_performance(): void
    {
        // 테스트 데이터 준비 (50개 템플릿만 생성하여 빠른 테스트)
        $templates = $this->createTestTemplates(50);

        // 일부 템플릿에만 사용 횟수 증가
        for ($i = 0; $i < 10; $i++) {
            $template = $templates[$i];
            for ($j = 0; $j < 5; $j++) {
                $this->templateService->incrementUsage($this->user->id, $template->id);
            }
        }

        $statisticsOperations = [
            'getUsageStatistics' => '전체 사용 통계',
            'getCountByPriority' => '우선순위별 개수',
            'getUsageDistribution' => '사용 분포',
        ];

        foreach ($statisticsOperations as $method => $description) {
            $startTime = microtime(true);

            $result = $this->templateService->$method($this->user->id);

            $endTime = microtime(true);
            $operationTime = $endTime - $startTime;

            // 각 통계 계산이 1초 이내에 완료되어야 함
            $this->assertLessThan(1.0, $operationTime,
                "{$description} 계산이 {$operationTime}초 소요됨 (기준: 1초 이내)");

            echo "\n{$description} 계산 시간: ".number_format($operationTime, 3).'초';
        }
    }

    /**
     * 동시 사용 횟수 증가 성능 테스트
     */
    public function test_concurrent_usage_increment_performance(): void
    {
        $template = $this->templateService->createTemplate(
            $this->user->id,
            [
                'name' => '동시성성능테스트',
                'title' => '동시성 성능 테스트',
                'content' => '동시성 성능 테스트 내용',
                'priority' => NotificationTemplate::PRIORITY_NORMAL,
                'is_active' => true,
            ]
        );

        $incrementCount = 100; // 테스트 시간 단축을 위해 100회로 감소
        $startTime = microtime(true);

        // 100번 사용 횟수 증가
        for ($i = 0; $i < $incrementCount; $i++) {
            $this->templateService->incrementUsage($this->user->id, $template->id);
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // 100번 증가가 5초 이내에 완료되어야 함
        $this->assertLessThan(5.0, $executionTime,
            "사용 횟수 {$incrementCount}회 증가가 {$executionTime}초 소요됨 (기준: 5초 이내)");

        // 최종 사용 횟수 확인
        $finalTemplate = $this->templateService->getTemplateById(
            $this->user->id,
            $template->id
        );
        $this->assertEquals($incrementCount, $finalTemplate->usage_count);

        echo "\n사용 횟수 증가 성능 테스트 결과:\n";
        echo "- 증가 횟수: {$incrementCount}회\n";
        echo '- 총 실행 시간: '.number_format($executionTime, 3)."초\n";
        echo '- 초당 처리량: '.number_format($incrementCount / $executionTime, 0)."회/초\n";
    }

    /**
     * 메모리 사용량 테스트
     */
    public function test_memory_usage(): void
    {
        $initialMemory = memory_get_usage(true);

        // 100개 템플릿 생성 및 조회
        $templates = $this->createTestTemplates(100);

        // 모든 템플릿 조회
        foreach ($templates as $template) {
            $this->templateService->getTemplateById($this->user->id, $template->id);
        }

        $finalMemory = memory_get_usage(true);
        $memoryUsed = $finalMemory - $initialMemory;

        // 메모리 사용량이 50MB를 초과하지 않아야 함
        $maxMemoryMB = 50 * 1024 * 1024; // 50MB
        $this->assertLessThan($maxMemoryMB, $memoryUsed,
            '메모리 사용량이 '.number_format($memoryUsed / 1024 / 1024, 2).'MB (기준: 50MB 이내)');

        echo "\n메모리 사용량 테스트 결과:\n";
        echo '- 초기 메모리: '.number_format($initialMemory / 1024 / 1024, 2)."MB\n";
        echo '- 최종 메모리: '.number_format($finalMemory / 1024 / 1024, 2)."MB\n";
        echo '- 사용된 메모리: '.number_format($memoryUsed / 1024 / 1024, 2)."MB\n";
    }

    /**
     * 테스트용 템플릿 생성 헬퍼 메서드
     */
    private function createTestTemplates(int $count): array
    {
        $templates = [];
        $priorities = [
            NotificationTemplate::PRIORITY_URGENT,
            NotificationTemplate::PRIORITY_HIGH,
            NotificationTemplate::PRIORITY_NORMAL,
            NotificationTemplate::PRIORITY_LOW,
        ];

        for ($i = 1; $i <= $count; $i++) {
            $template = $this->templateService->createTemplate(
                $this->user->id,
                [
                    'name' => "성능테스트템플릿{$i}",
                    'title' => "성능 테스트 템플릿 {$i}",
                    'content' => "성능 테스트를 위한 템플릿 내용 {$i}. 검색 키워드: 성능, 테스트, 템플릿, 알림, 시스템",
                    'priority' => $priorities[$i % count($priorities)],
                    'is_active' => true,
                ]
            );
            $templates[] = $template;
        }

        return $templates;
    }

    /**
     * 랜덤 우선순위 반환 헬퍼 메서드
     */
    private function getRandomPriority(): string
    {
        $priorities = [
            NotificationTemplate::PRIORITY_URGENT,
            NotificationTemplate::PRIORITY_HIGH,
            NotificationTemplate::PRIORITY_NORMAL,
            NotificationTemplate::PRIORITY_LOW,
        ];

        return $priorities[array_rand($priorities)];
    }
}
