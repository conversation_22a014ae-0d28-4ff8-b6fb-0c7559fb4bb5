<?php

use App\Models\MonitorRule;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // 테스트용 기본 데이터 생성
    MonitorRule::create([
        'rule_type' => MonitorRule::RULE_TYPE_BRAND,
        'pattern' => '삼성',
        'description' => '삼성 브랜드 키워드',
        'priority' => 1,
        'is_active' => true,
    ]);

    MonitorRule::create([
        'rule_type' => MonitorRule::RULE_TYPE_EXCLUDE,
        'pattern' => '스마트폰',
        'description' => '스마트폰 제외',
        'priority' => 1,
        'is_active' => true,
    ]);

    MonitorRule::create([
        'rule_type' => MonitorRule::RULE_TYPE_SIZE_PATTERN,
        'pattern' => '/(\d+(?:\.\d+)?)\s*[인치inch"]/i',
        'description' => '인치 단위 크기 추출',
        'priority' => 1,
        'is_active' => true,
    ]);

    MonitorRule::create([
        'rule_type' => MonitorRule::RULE_TYPE_BRAND,
        'pattern' => 'LG',
        'description' => 'LG 브랜드 키워드',
        'priority' => 2,
        'is_active' => false, // 비활성화
    ]);
});

test('모니터 규칙 상수가 올바르게 정의되어 있다', function () {
    expect(MonitorRule::RULE_TYPE_BRAND)->toBe('brand');
    expect(MonitorRule::RULE_TYPE_EXCLUDE)->toBe('exclude');
    expect(MonitorRule::RULE_TYPE_SIZE_PATTERN)->toBe('size_pattern');
});

test('활성화된 브랜드 규칙만 조회된다', function () {
    $brandRules = MonitorRule::getBrandRules();

    expect($brandRules)->toHaveCount(1);
    expect($brandRules->first()->pattern)->toBe('삼성');
    expect($brandRules->first()->is_active)->toBeTrue();
});

test('활성화된 제외 규칙만 조회된다', function () {
    $excludeRules = MonitorRule::getExcludeRules();

    expect($excludeRules)->toHaveCount(1);
    expect($excludeRules->first()->pattern)->toBe('스마트폰');
});

test('활성화된 크기 패턴 규칙만 조회된다', function () {
    $sizeRules = MonitorRule::getSizePatternRules();

    expect($sizeRules)->toHaveCount(1);
    expect($sizeRules->first()->rule_type)->toBe(MonitorRule::RULE_TYPE_SIZE_PATTERN);
});

test('브랜드 키워드 패턴 검증이 올바르게 작동한다', function () {
    $rule = MonitorRule::where('pattern', '삼성')->first();

    expect($rule->validatePattern('삼성 24인치 모니터'))->toBeTrue();
    expect($rule->validatePattern('LG 27인치 모니터'))->toBeFalse();
    expect($rule->validatePattern('SAMSUNG 모니터'))->toBeFalse(); // 대소문자 구분 없음 확인
});

test('제외 키워드 패턴 검증이 올바르게 작동한다', function () {
    $rule = MonitorRule::where('pattern', '스마트폰')->first();

    expect($rule->validatePattern('갤럭시 스마트폰'))->toBeTrue();
    expect($rule->validatePattern('24인치 모니터'))->toBeFalse();
});

test('크기 추출 정규식 패턴이 올바르게 작동한다', function () {
    $rule = MonitorRule::where('rule_type', MonitorRule::RULE_TYPE_SIZE_PATTERN)->first();

    expect($rule->validatePattern('24인치 모니터'))->toBeTrue();
    expect($rule->validatePattern('27inch 모니터'))->toBeTrue();
    expect($rule->validatePattern('32" 모니터'))->toBeTrue();
    expect($rule->validatePattern('모니터 스탠드'))->toBeFalse();
});

test('규칙 유형 한글명이 올바르게 반환된다', function () {
    $brandRule = MonitorRule::where('rule_type', MonitorRule::RULE_TYPE_BRAND)->first();
    $excludeRule = MonitorRule::where('rule_type', MonitorRule::RULE_TYPE_EXCLUDE)->first();
    $sizeRule = MonitorRule::where('rule_type', MonitorRule::RULE_TYPE_SIZE_PATTERN)->first();

    expect($brandRule->rule_type_name)->toBe('브랜드 키워드');
    expect($excludeRule->rule_type_name)->toBe('제외 키워드');
    expect($sizeRule->rule_type_name)->toBe('크기 추출 패턴');
});

test('우선순위 재정렬이 올바르게 작동한다', function () {
    $rule1 = MonitorRule::create([
        'rule_type' => MonitorRule::RULE_TYPE_BRAND,
        'pattern' => '테스트1',
        'description' => '테스트 규칙 1',
        'priority' => 1,
        'is_active' => true,
    ]);

    $rule2 = MonitorRule::create([
        'rule_type' => MonitorRule::RULE_TYPE_BRAND,
        'pattern' => '테스트2',
        'description' => '테스트 규칙 2',
        'priority' => 2,
        'is_active' => true,
    ]);

    // 순서 바꾸기
    $result = MonitorRule::reorderPriorities([$rule2->id, $rule1->id]);

    expect($result)->toBeTrue();

    $rule1->refresh();
    $rule2->refresh();

    expect($rule2->priority)->toBe(1);
    expect($rule1->priority)->toBe(2);
});

test('스코프가 올바르게 작동한다', function () {
    // active 스코프 테스트
    $activeRules = MonitorRule::active()->get();
    expect($activeRules)->toHaveCount(3); // 비활성화된 LG 규칙 제외

    // byType 스코프 테스트
    $brandRules = MonitorRule::byType(MonitorRule::RULE_TYPE_BRAND)->get();
    expect($brandRules)->toHaveCount(2); // 삼성, LG (비활성화 포함)

    // orderByPriority 스코프 테스트
    $orderedRules = MonitorRule::byType(MonitorRule::RULE_TYPE_BRAND)->orderByPriority()->get();
    expect($orderedRules->first()->priority)->toBe(1);
    expect($orderedRules->last()->priority)->toBe(2);
});

test('잘못된 정규식 패턴에 대해 false를 반환한다', function () {
    $rule = MonitorRule::create([
        'rule_type' => MonitorRule::RULE_TYPE_SIZE_PATTERN,
        'pattern' => '/[/', // 잘못된 정규식
        'description' => '잘못된 패턴',
        'priority' => 1,
        'is_active' => true,
    ]);

    expect($rule->validatePattern('테스트 문자열'))->toBeFalse();
});

test('실제 상품명에서 크기 추출이 올바르게 작동한다', function () {
    // 다양한 크기 패턴 규칙 추가
    MonitorRule::create([
        'rule_type' => MonitorRule::RULE_TYPE_SIZE_PATTERN,
        'pattern' => '/(\d+(?:\.\d+)?)\s*[cm센치]/i',
        'description' => 'cm 단위 크기 추출',
        'priority' => 2,
        'is_active' => true,
    ]);

    MonitorRule::create([
        'rule_type' => MonitorRule::RULE_TYPE_SIZE_PATTERN,
        'pattern' => '/(\d{2,3})[A-Z]\d+/i',
        'description' => '모델명에서 크기 추출',
        'priority' => 3,
        'is_active' => true,
    ]);

    $testProducts = [
        '삼성 24인치 모니터 S24F350' => '24',
        'LG 27inch 울트라와이드 27WL500' => '27',
        '32" 게이밍 모니터' => '32',
        '60cm 모니터 스탠드' => '60',
        '24G2460 게이밍 모니터' => '24',
        '27U850 4K 모니터' => '27',
    ];

    $sizeRules = MonitorRule::getSizePatternRules();

    foreach ($testProducts as $productName => $expectedSize) {
        $extracted = null;

        foreach ($sizeRules as $rule) {
            if ($rule->validatePattern($productName)) {
                preg_match($rule->pattern, $productName, $matches);
                if (isset($matches[1])) {
                    $extracted = $matches[1];
                    break;
                }
            }
        }

        expect($extracted)->toBe($expectedSize, "상품명 '{$productName}'에서 크기 '{$expectedSize}' 추출 실패");
    }
});
