<?php

namespace Tests\Feature;

use App\Models\MonitorRule;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class MonitorRuleControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // 테스트용 사용자 생성 및 인증
        $user = User::factory()->create();
        Sanctum::actingAs($user);
    }

    /** @test */
    public function 모니터_규칙_목록을_조회할_수_있다()
    {
        // Given: 모니터 규칙들이 생성되어 있음
        MonitorRule::factory()->count(3)->create();

        // When: 규칙 목록 조회
        $response = $this->getJson('/wms/settings/repairs/monitor-rules');

        // Then: 성공적으로 조회됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'rules',
                    'grouped_rules',
                    'rule_types',
                ],
            ]);
    }

    /** @test */
    public function 새로운_모니터_규칙을_생성할_수_있다()
    {
        // Given: 새로운 규칙 데이터
        $ruleData = [
            'rule_type' => MonitorRule::RULE_TYPE_BRAND,
            'pattern' => '테스트브랜드',
            'description' => '테스트 브랜드 규칙',
            'is_active' => true,
        ];

        // When: 규칙 생성
        $response = $this->postJson('/wms/settings/repairs/monitor-rules', $ruleData);

        // Then: 성공적으로 생성됨
        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'rule_type',
                    'pattern',
                    'description',
                    'priority',
                    'is_active',
                ],
            ]);

        $this->assertDatabaseHas('monitor_rules', [
            'pattern' => '테스트브랜드',
            'rule_type' => MonitorRule::RULE_TYPE_BRAND,
        ]);
    }

    /** @test */
    public function 모니터_규칙을_수정할_수_있다()
    {
        // Given: 기존 브랜드 규칙이 있음
        $rule = MonitorRule::factory()->brand()->create();

        // When: 규칙 수정
        $updateData = [
            'pattern' => '수정된패턴',
            'description' => '수정된 설명',
        ];

        $response = $this->putJson("/wms/settings/repairs/monitor-rules/{$rule->id}", $updateData);

        // Then: 성공적으로 수정됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ]);

        $this->assertDatabaseHas('monitor_rules', [
            'id' => $rule->id,
            'pattern' => '수정된패턴',
        ]);
    }

    /** @test */
    public function 모니터_규칙을_삭제할_수_있다()
    {
        // Given: 기존 규칙이 있음
        $rule = MonitorRule::factory()->create();

        // When: 규칙 삭제
        $response = $this->deleteJson("/wms/settings/repairs/monitor-rules/{$rule->id}");

        // Then: 성공적으로 삭제됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
            ]);

        $this->assertDatabaseMissing('monitor_rules', [
            'id' => $rule->id,
        ]);
    }

    /** @test */
    public function 규칙_우선순위를_재정렬할_수_있다()
    {
        // Given: 여러 규칙들이 있음
        $rule1 = MonitorRule::factory()->create(['priority' => 1]);
        $rule2 = MonitorRule::factory()->create(['priority' => 2]);
        $rule3 = MonitorRule::factory()->create(['priority' => 3]);

        // When: 우선순위 재정렬
        $response = $this->postJson('/wms/settings/repairs/monitor-rules/reorder', [
            'rule_ids' => [$rule3->id, $rule1->id, $rule2->id],
        ]);

        // Then: 성공적으로 재정렬됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data',
            ]);

        // 우선순위가 올바르게 변경되었는지 확인
        $this->assertEquals(1, $rule3->fresh()->priority);
        $this->assertEquals(2, $rule1->fresh()->priority);
        $this->assertEquals(3, $rule2->fresh()->priority);
    }

    /** @test */
    public function 모니터_크기_추출_테스트를_수행할_수_있다()
    {
        // Given: 테스트할 상품명
        $productName = '삼성 32인치 모니터';

        // When: 크기 추출 테스트
        $response = $this->postJson('/wms/settings/repairs/monitor-rules/test-extraction', [
            'product_name' => $productName,
        ]);

        // Then: 성공적으로 테스트됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'product_name',
                    'extracted_size',
                    'applied_rules',
                    'analysis' => [
                        'is_monitor_product',
                        'applied_brand_rules',
                        'applied_exclude_rules',
                        'applied_size_patterns',
                        'extraction_steps',
                    ],
                ],
            ]);
    }

    /** @test */
    public function 규칙_캐시를_초기화할_수_있다()
    {
        // When: 캐시 초기화
        $response = $this->postJson('/wms/settings/repairs/monitor-rules/clear-cache');

        // Then: 성공적으로 초기화됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
            ]);
    }

    /** @test */
    public function 기본_규칙_데이터를_생성할_수_있다()
    {
        // When: 기본 규칙 생성
        $response = $this->postJson('/wms/settings/repairs/monitor-rules/create-default-rules');

        // Then: 성공적으로 생성됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
            ]);

        // 기본 규칙들이 생성되었는지 확인
        $this->assertDatabaseHas('monitor_rules', [
            'pattern' => 'LG',
            'rule_type' => MonitorRule::RULE_TYPE_BRAND,
        ]);
    }

    /** @test */
    public function 규칙_통계를_조회할_수_있다()
    {
        // Given: 다양한 규칙들이 있음
        MonitorRule::factory()->count(2)->create(['rule_type' => MonitorRule::RULE_TYPE_BRAND]);
        MonitorRule::factory()->count(3)->create(['rule_type' => MonitorRule::RULE_TYPE_EXCLUDE]);
        MonitorRule::factory()->count(1)->create(['rule_type' => MonitorRule::RULE_TYPE_SIZE_PATTERN]);

        // When: 통계 조회
        $response = $this->getJson('/wms/settings/repairs/monitor-rules/statistics');

        // Then: 성공적으로 조회됨
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'total_rules',
                    'active_rules',
                    'inactive_rules',
                    'by_type',
                    'active_by_type',
                ],
            ]);

        $data = $response->json('data');
        $this->assertEquals(6, $data['total_rules']);
        $this->assertEquals(2, $data['by_type']['brand']);
        $this->assertEquals(3, $data['by_type']['exclude']);
        $this->assertEquals(1, $data['by_type']['size_pattern']);
    }

    /** @test */
    public function 잘못된_정규식_패턴으로_규칙_생성시_오류가_발생한다()
    {
        // Given: 잘못된 정규식 패턴
        $invalidRuleData = [
            'rule_type' => MonitorRule::RULE_TYPE_SIZE_PATTERN,
            'pattern' => '/[invalid-regex/',
            'description' => '잘못된 정규식',
            'is_active' => true,
        ];

        // When: 규칙 생성 시도
        $response = $this->postJson('/wms/settings/repairs/monitor-rules', $invalidRuleData);

        // Then: 유효성 검사 오류 발생
        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'error_code',
                'errors',
            ]);
    }

    /** @test */
    public function 존재하지_않는_규칙_수정시_404_오류가_발생한다()
    {
        // When: 존재하지 않는 규칙 수정 시도
        $response = $this->putJson('/wms/settings/repairs/monitor-rules/999', [
            'pattern' => '수정된패턴',
        ]);

        // Then: 404 오류 발생
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
                'error_code',
            ]);
    }

    /** @test */
    public function 빈_상품명으로_크기_추출_테스트시_오류가_발생한다()
    {
        // When: 빈 상품명으로 테스트 시도
        $response = $this->postJson('/wms/settings/repairs/monitor-rules/test-extraction', [
            'product_name' => '',
        ]);

        // Then: 유효성 검사 오류 발생
        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'error_code',
                'errors',
            ]);
    }

    /** @test */
    public function 규칙_유형별_필터링이_작동한다()
    {
        // Given: 다양한 유형의 규칙들이 있음
        MonitorRule::factory()->create(['rule_type' => MonitorRule::RULE_TYPE_BRAND]);
        MonitorRule::factory()->create(['rule_type' => MonitorRule::RULE_TYPE_EXCLUDE]);

        // When: 브랜드 규칙만 필터링
        $response = $this->getJson('/wms/settings/repairs/monitor-rules?rule_type=brand');

        // Then: 브랜드 규칙만 반환됨
        $response->assertStatus(200);
        $data = $response->json('data.rules');
        $this->assertCount(1, $data);
        $this->assertEquals(MonitorRule::RULE_TYPE_BRAND, $data[0]['rule_type']);
    }

    /** @test */
    public function 활성화_상태별_필터링이_작동한다()
    {
        // Given: 활성/비활성 규칙들이 있음
        MonitorRule::factory()->create(['is_active' => true]);
        MonitorRule::factory()->create(['is_active' => false]);

        // When: 활성 규칙만 필터링
        $response = $this->getJson('/wms/settings/repairs/monitor-rules?is_active=true');

        // Then: 활성 규칙만 반환됨
        $response->assertStatus(200);
        $data = $response->json('data.rules');
        $this->assertCount(1, $data);
        $this->assertTrue($data[0]['is_active']);
    }
}
