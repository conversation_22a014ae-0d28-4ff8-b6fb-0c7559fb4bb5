<?php

namespace Tests\Feature\Controllers;

use App\Models\Notification;
use App\Models\NotificationRecipient;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SSEControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }

    public function test_인증된_사용자는_SSE_연결을_할_수_있다()
    {
        // Arrange
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        // Act
        $response = $this->get('/wms/sse/notifications');

        // Assert
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/event-stream; charset=UTF-8');
        $response->assertHeader('Cache-Control', 'no-cache');
        $response->assertHeader('Connection', 'keep-alive');
    }

    public function test_인증되지_않은_사용자는_SSE_연결을_할_수_없다()
    {
        // Act
        $response = $this->get('/wms/sse/notifications');

        // Assert
        $response->assertStatus(401);
    }

    public function test_SSE_연결시_올바른_헤더가_설정됨()
    {
        // Arrange
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        // Act
        $response = $this->get('/wms/sse/notifications');

        // Assert
        $response->assertStatus(200);
        
        // SSE 필수 헤더 확인
        $this->assertEquals('text/event-stream; charset=UTF-8', $response->headers->get('Content-Type'));
        $this->assertEquals('no-cache', $response->headers->get('Cache-Control'));
        $this->assertEquals('keep-alive', $response->headers->get('Connection'));
    }

    public function test_SSE_스트림_응답_타입_확인()
    {
        // Arrange
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        // Act
        $response = $this->get('/wms/sse/notifications');

        // Assert
        $response->assertStatus(200);
        
        // StreamedResponse 타입인지 확인
        $this->assertInstanceOf(
            \Symfony\Component\HttpFoundation\StreamedResponse::class,
            $response->baseResponse
        );
    }

    public function test_미읽음_알림이_있는_사용자의_SSE_연결()
    {
        // Arrange
        $user = User::factory()->create();
        $sender = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        // 미읽음 알림 생성
        $notification = Notification::factory()->create([
            'title' => '테스트 알림',
            'content' => '테스트 내용',
            'sender_id' => $sender->id,
            'sent_at' => now()
        ]);

        NotificationRecipient::factory()->create([
            'notification_id' => $notification->id,
            'user_id' => $user->id,
            'delivered_at' => null
        ]);

        // Act
        $response = $this->get('/wms/sse/notifications');

        // Assert
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/event-stream; charset=UTF-8');
    }

    public function test_미읽음_알림이_없는_사용자의_SSE_연결()
    {
        // Arrange
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        // Act
        $response = $this->get('/wms/sse/notifications');

        // Assert
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/event-stream; charset=UTF-8');
    }

    public function test_다른_사용자의_알림은_전송되지_않음()
    {
        // Arrange
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $sender = User::factory()->create();
        $this->actingAs($user1, 'sanctum');

        // user2에게만 알림 생성
        $notification = Notification::factory()->create([
            'title' => 'User2 전용 알림',
            'content' => 'User2만 받을 알림',
            'sender_id' => $sender->id,
            'sent_at' => now()
        ]);

        NotificationRecipient::factory()->create([
            'notification_id' => $notification->id,
            'user_id' => $user2->id,
            'delivered_at' => null
        ]);

        // Act
        $response = $this->get('/wms/sse/notifications');

        // Assert
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/event-stream; charset=UTF-8');
        
        // user1은 user2의 알림을 받지 않아야 함
        // 실제 스트림 내용 확인은 통합 테스트에서 수행
    }

    public function test_SSE_연결_중_사용자_인증_상태_유지()
    {
        // Arrange
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        // Act
        $response = $this->get('/wms/sse/notifications');

        // Assert
        $response->assertStatus(200);
        
        // 인증된 사용자 정보가 유지되는지 확인
        $this->assertEquals($user->id, auth()->id());
    }

    public function test_SSE_연결시_CORS_헤더_설정()
    {
        // Arrange
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        // Act
        $response = $this->get('/wms/sse/notifications', [
            'Origin' => 'http://localhost:3000'
        ]);

        // Assert
        $response->assertStatus(200);
        
        // CORS 관련 헤더가 있는지 확인 (미들웨어에서 처리됨)
        // 실제 CORS 설정은 config/cors.php에서 관리
    }

    public function test_동시_다중_SSE_연결_지원()
    {
        // Arrange
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        // Act - 첫 번째 사용자 연결
        $this->actingAs($user1, 'sanctum');
        $response1 = $this->get('/wms/sse/notifications');

        // Act - 두 번째 사용자 연결
        $this->actingAs($user2, 'sanctum');
        $response2 = $this->get('/wms/sse/notifications');

        // Assert
        $response1->assertStatus(200);
        $response2->assertStatus(200);
        
        // 두 연결 모두 정상적으로 설정되어야 함
        $response1->assertHeader('Content-Type', 'text/event-stream; charset=UTF-8');
        $response2->assertHeader('Content-Type', 'text/event-stream; charset=UTF-8');
    }
}