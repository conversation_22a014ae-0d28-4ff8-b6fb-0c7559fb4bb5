<?php

namespace Tests\Feature\Controllers;

use App\Models\NotificationGroup;
use App\Models\User;
use App\Services\NotificationSendService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class NotificationSendControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }

    public function test_관리자는_알림을_전송할_수_있다()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $this->actingAs($admin, 'sanctum');

        $requestData = [
            'title' => '테스트 알림',
            'content' => '테스트 내용입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '알림이 성공적으로 전송되었습니다.'
            ])
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'notification_id',
                    'recipients_count'
                ]
            ]);
    }

    public function test_일반_사용자는_알림_전송_권한이_없다()
    {
        // Arrange
        $user = User::factory()->create(['role' => User::ROLE_EMPLOYEE]);
        $this->actingAs($user, 'sanctum');

        $requestData = [
            'title' => '테스트 알림',
            'content' => '테스트 내용입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(403);
    }

    public function test_인증되지_않은_사용자는_접근할_수_없다()
    {
        // Arrange
        $requestData = [
            'title' => '테스트 알림',
            'content' => '테스트 내용입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(401);
    }

    public function test_제목이_비어있으면_유효성_검증_실패()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $this->actingAs($admin, 'sanctum');

        $requestData = [
            'title' => '', // 빈 제목
            'content' => '테스트 내용입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonPath('error.details.validation_errors.title.0', '알림 제목은 필수 입력 항목입니다.');
    }

    public function test_내용이_비어있으면_유효성_검증_실패()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $this->actingAs($admin, 'sanctum');

        $requestData = [
            'title' => '테스트 알림',
            'content' => '', // 빈 내용
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonPath('error.details.validation_errors.content.0', '알림 내용은 필수 입력 항목입니다.');
    }

    public function test_잘못된_대상_타입으로_유효성_검증_실패()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $this->actingAs($admin, 'sanctum');

        $requestData = [
            'title' => '테스트 알림',
            'content' => '테스트 내용입니다.',
            'target_type' => 'invalid_type', // 잘못된 타입
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonPath('error.details.validation_errors.target_type.0', '대상 타입은 all, group, individual 중 하나여야 합니다.');
    }

    public function test_제목이_너무_길면_유효성_검증_실패()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $this->actingAs($admin, 'sanctum');

        $requestData = [
            'title' => str_repeat('a', 201), // 200자 초과
            'content' => '테스트 내용입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonPath('error.details.validation_errors.title.0', '알림 제목은 200자를 초과할 수 없습니다.');
    }

    public function test_내용이_너무_길면_유효성_검증_실패()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $this->actingAs($admin, 'sanctum');

        $requestData = [
            'title' => '테스트 알림',
            'content' => str_repeat('a', 2001), // 2000자 초과
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonPath('error.details.validation_errors.content.0', '알림 내용은 2000자를 초과할 수 없습니다.');
    }

    public function test_그룹_대상_알림_전송_성공()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $this->actingAs($admin, 'sanctum');

        $group = NotificationGroup::factory()->create();

        $requestData = [
            'title' => '그룹 알림',
            'content' => '그룹 대상 알림입니다.',
            'target_type' => 'group',
            'target_ids' => [$group->id]
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '알림이 성공적으로 전송되었습니다.'
            ]);
    }

    public function test_개별_사용자_대상_알림_전송_성공()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $this->actingAs($admin, 'sanctum');

        $targetUser = User::factory()->create(['status' => 1]);

        $requestData = [
            'title' => '개별 알림',
            'content' => '개별 사용자 대상 알림입니다.',
            'target_type' => 'individual',
            'target_ids' => [$targetUser->id]
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '알림이 성공적으로 전송되었습니다.'
            ]);
    }

    public function test_target_ids가_배열이_아니면_유효성_검증_실패()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $this->actingAs($admin, 'sanctum');

        $requestData = [
            'title' => '테스트 알림',
            'content' => '테스트 내용입니다.',
            'target_type' => 'individual',
            'target_ids' => 'not_an_array' // 배열이 아님
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonPath('error.details.validation_errors.target_ids', function ($errors) {
                return is_array($errors) && count($errors) > 0;
            });
    }

    public function test_target_ids_요소가_정수가_아니면_유효성_검증_실패()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $this->actingAs($admin, 'sanctum');

        $requestData = [
            'title' => '테스트 알림',
            'content' => '테스트 내용입니다.',
            'target_type' => 'individual',
            'target_ids' => ['not_integer', 'also_not_integer'] // 정수가 아님
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonPath('error.details.validation_errors', function ($errors) {
                return isset($errors['target_ids.0']) && isset($errors['target_ids.1']);
            });
    }

    public function test_서비스_에러_발생시_500_에러_반환()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $this->actingAs($admin, 'sanctum');

        // NotificationSendService를 모킹하여 예외 발생시키기
        $mockService = $this->createMock(NotificationSendService::class);
        $mockService->method('sendNotification')
            ->willThrowException(new \Exception('Database connection failed'));

        $this->app->instance(NotificationSendService::class, $mockService);

        $requestData = [
            'title' => '테스트 알림',
            'content' => '테스트 내용입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(500)
            ->assertJson([
                'success' => false,
                'message' => '알림 전송 중 오류가 발생했습니다.'
            ]);
    }

    public function test_전송자_정보가_올바르게_기록됨()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $this->actingAs($admin, 'sanctum');

        $requestData = [
            'title' => '테스트 알림',
            'content' => '테스트 내용입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(200);
        
        // 데이터베이스에서 알림을 조회하여 전송자 정보 확인
        $responseData = $response->json('data');
        $notification = \App\Models\Notification::find($responseData['notification_id']);
        
        $this->assertNotNull($notification);
        $this->assertEquals($admin->id, $notification->sender_id);
    }
}