<?php

use App\Models\Product;
use App\Models\ProductLog;
use App\Models\RepairProduct;
use App\Models\User;
use App\Models\WorkStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->create();
    $this->workStatus = WorkStatus::factory()->create();
});

describe('사용자 작업 내역 조회 테스트', function () {
    it('오늘 작업한 RepairProduct 로그들을 페이징과 함께 조회할 수 있다', function () {
        // 오늘 생성된 RepairProduct 로그들 (20개 생성)
        for ($i = 0; $i < 20; $i++) {
            $product = Product::factory()->create();
            $repairProduct = RepairProduct::factory()->create(['product_id' => $product->id]);

            ProductLog::factory()->create([
                'product_id' => $product->id,
                'model_type' => RepairProduct::class,
                'model_id' => $repairProduct->id,
                'work_status_id' => $this->workStatus->id,
                'user_id' => $this->user->id,
                'memo' => "수리 작업 완료 #{$i}",
                'created_at' => now(),
            ]);
        }

        // 첫 번째 페이지 조회 (기본 15개)
        $response = $this->actingAs($this->user)
            ->getJson('/api/user/work-history/today');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'work_logs' => [
                        '*' => [
                            'id',
                            'product_id',
                            'model_type',
                            'model_id',
                            'work_status_id',
                            'user_id',
                            'memo',
                            'created_at',
                            'product' => [
                                'id',
                                'qaid',
                                'name',
                            ],
                            'user' => [
                                'id',
                                'name',
                                'username',
                            ],
                            'work_status' => [
                                'id',
                                'name',
                            ],
                            'related_model' => [
                                'id',
                                'status',
                            ],
                        ],
                    ],
                    'pagination' => [
                        'total',
                        'current_page',
                        'last_page',
                        'per_page',
                        'from',
                        'to',
                        'has_prev',
                        'has_next',
                    ],
                    'summary' => [
                        'total_count',
                        'today_date',
                    ],
                ],
            ]);

        $data = $response->json('data');
        expect($data['work_logs'])->toHaveCount(15); // 첫 페이지는 15개
        expect($data['pagination']['total'])->toBe(20); // 전체 20개
        expect($data['pagination']['current_page'])->toBe(1);
        expect($data['pagination']['last_page'])->toBe(2);
        expect($data['pagination']['has_next'])->toBe(true);
        expect($data['pagination']['has_prev'])->toBe(false);

        // 두 번째 페이지 조회
        $response2 = $this->actingAs($this->user)
            ->getJson('/api/user/work-history/today?page=2');

        $data2 = $response2->json('data');
        expect($data2['work_logs'])->toHaveCount(5); // 두 번째 페이지는 5개
        expect($data2['pagination']['current_page'])->toBe(2);
        expect($data2['pagination']['has_next'])->toBe(false);
        expect($data2['pagination']['has_prev'])->toBe(true);
    });

    it('페이지 크기를 지정할 수 있다', function () {
        // 10개의 로그 생성
        for ($i = 0; $i < 10; $i++) {
            $product = Product::factory()->create();
            $repairProduct = RepairProduct::factory()->create(['product_id' => $product->id]);

            ProductLog::factory()->create([
                'product_id' => $product->id,
                'model_type' => RepairProduct::class,
                'model_id' => $repairProduct->id,
                'work_status_id' => $this->workStatus->id,
                'user_id' => $this->user->id,
                'memo' => "수리 작업 완료 #{$i}",
                'created_at' => now(),
            ]);
        }

        // 페이지 크기를 5로 지정
        $response = $this->actingAs($this->user)
            ->getJson('/api/user/work-history/today?pageSize=5');

        $data = $response->json('data');
        expect($data['work_logs'])->toHaveCount(5);
        expect($data['pagination']['per_page'])->toBe(5);
        expect($data['pagination']['last_page'])->toBe(2);
    });

    it('오늘 작업한 내역이 없으면 빈 배열을 반환한다', function () {
        $response = $this->actingAs($this->user)
            ->getJson('/api/user/work-history/today');

        $response->assertStatus(200);

        $data = $response->json('data');
        expect($data['work_logs'])->toHaveCount(0);
        expect($data['pagination']['total'])->toBe(0);
        expect($data['summary']['total_count'])->toBe(0);
    });

    it('인증되지 않은 사용자는 접근할 수 없다', function () {
        $response = $this->getJson('/api/user/work-history/today');

        $response->assertStatus(401);
    });
});
