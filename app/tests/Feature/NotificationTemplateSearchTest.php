<?php

namespace Tests\Feature;

use App\Models\NotificationTemplate;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NotificationTemplateSearchTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        // 테스트 사용자 생성
        $this->user = User::factory()->create([
            'role' => 'Super-Admin',
        ]);

        // 테스트 템플릿 생성
        NotificationTemplate::create([
            'name' => '시스템점검알림',
            'title' => '시스템 점검 안내',
            'content' => '시스템 점검으로 인해 서비스가 일시 중단됩니다.',
            'priority' => 'high',
            'usage_count' => 5,
            'created_by' => $this->user->id,
        ]);

        NotificationTemplate::create([
            'name' => '긴급시스템오류',
            'title' => '긴급: 시스템 오류 발생',
            'content' => '시스템에 긴급 오류가 발생했습니다.',
            'priority' => 'urgent',
            'usage_count' => 2,
            'created_by' => $this->user->id,
        ]);

        NotificationTemplate::create([
            'name' => '일반공지사항',
            'title' => '일반 공지사항',
            'content' => '일반적인 공지사항입니다.',
            'priority' => 'normal',
            'usage_count' => 10,
            'created_by' => $this->user->id,
        ]);
    }

    /** @test */
    public function it_can_search_templates_by_keyword()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/wms/settings/notifications/templates?search=시스템');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'items',
                    'pagination',
                    'search_meta' => [
                        'applied_filters',
                        'total_results',
                        'filtered_results',
                        'has_filters',
                    ],
                ],
            ]);

        $data = $response->json('data');

        // 검색 결과 검증
        $this->assertCount(2, $data['items']);
        $this->assertTrue($data['search_meta']['has_filters']);
        $this->assertEquals('시스템', $data['search_meta']['applied_filters']['search']);
    }

    /** @test */
    public function it_can_filter_templates_by_priority()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/wms/settings/notifications/templates?priority=high');

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertCount(1, $data['items']);
        $this->assertEquals('high', $data['items'][0]['priority']);
    }

    /** @test */
    public function it_can_sort_templates_by_usage_count()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/wms/settings/notifications/templates?sortBy=usage_count&sortDirection=desc');

        $response->assertStatus(200);

        $data = $response->json('data');
        $usageCounts = array_column($data['items'], 'usage_count');

        // 내림차순으로 정렬되었는지 확인
        $this->assertEquals([10, 5, 2], $usageCounts);
    }

    /** @test */
    public function it_can_sort_templates_by_priority()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/wms/settings/notifications/templates?sortBy=priority&sortDirection=desc');

        $response->assertStatus(200);

        $data = $response->json('data');
        $priorities = array_column($data['items'], 'priority');

        // urgent > high > normal 순으로 정렬되었는지 확인
        $this->assertEquals(['urgent', 'high', 'normal'], $priorities);
    }

    /** @test */
    public function it_can_combine_search_and_filters()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/wms/settings/notifications/templates?search=시스템&priority=high');

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertCount(1, $data['items']);
        $this->assertEquals('시스템점검알림', $data['items'][0]['name']);
        $this->assertEquals('high', $data['items'][0]['priority']);
    }

    /** @test */
    public function it_can_filter_by_usage_range()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/wms/settings/notifications/templates?usage_min=5&usage_max=10');

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertCount(2, $data['items']);

        foreach ($data['items'] as $item) {
            $this->assertGreaterThanOrEqual(5, $item['usage_count']);
            $this->assertLessThanOrEqual(10, $item['usage_count']);
        }
    }

    /** @test */
    public function it_includes_search_metadata_in_response()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/wms/settings/notifications/templates?search=시스템&priority=high&sortBy=name');

        $response->assertStatus(200);

        $searchMeta = $response->json('data.search_meta');

        $this->assertEquals('시스템', $searchMeta['applied_filters']['search']);
        $this->assertEquals('high', $searchMeta['applied_filters']['priority']);
        $this->assertEquals('name', $searchMeta['applied_filters']['sort_by']);
        $this->assertTrue($searchMeta['has_filters']);
        $this->assertIsInt($searchMeta['total_results']);
        $this->assertIsInt($searchMeta['filtered_results']);
    }
}
