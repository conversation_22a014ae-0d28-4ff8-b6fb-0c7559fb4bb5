<?php

namespace Tests\Feature;

use App\Models\ProductLink;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class VidControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('local');
    }

    /**
     * Vid.xlsx 파일 업로드 성공 테스트
     */
    public function test_vid_upload_success(): void
    {
        // 테스트용 Excel 파일 생성 (실제로는 더 복잡한 로직 필요)
        $file = UploadedFile::fake()->create('Vid.xlsx', 100, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        $response = $this->postJson('/api/vid/upload', [
            'vid_file' => $file,
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Vid.xlsx 파일이 성공적으로 업로드되었습니다.',
            ])
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'processed_rows',
                    'created_records',
                    'skipped_records',
                ],
            ]);
    }

    /**
     * 잘못된 파일명 업로드 테스트
     */
    public function test_vid_upload_invalid_filename(): void
    {
        $file = UploadedFile::fake()->create('wrong_file.xlsx', 100, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        $response = $this->postJson('/api/vid/upload', [
            'vid_file' => $file,
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Vid.xlsx 파일만 업로드 가능합니다.',
            ]);
    }

    /**
     * 파일 없이 업로드 테스트
     */
    public function test_vid_upload_no_file(): void
    {
        $response = $this->postJson('/api/vid/upload', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['vid_file']);
    }

    /**
     * 잘못된 파일 타입 업로드 테스트
     */
    public function test_vid_upload_invalid_file_type(): void
    {
        $file = UploadedFile::fake()->create('Vid.txt', 100, 'text/plain');

        $response = $this->postJson('/api/vid/upload', [
            'vid_file' => $file,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['vid_file']);
    }

    /**
     * 파일 크기 초과 테스트
     */
    public function test_vid_upload_file_too_large(): void
    {
        $file = UploadedFile::fake()->create('Vid.xlsx', 11000, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        $response = $this->postJson('/api/vid/upload', [
            'vid_file' => $file,
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['vid_file']);
    }

    /**
     * ProductLink 통계 조회 테스트
     */
    public function test_vid_statistics(): void
    {
        // 테스트 데이터 생성
        ProductLink::create([
            'external_wms_sku_id' => 'EXT-001',
            'vendor_item_id' => 'VENDOR-001',
            'product_id' => 'PROD-001',
            'item_id' => 'ITEM-001',
        ]);

        ProductLink::create([
            'external_wms_sku_id' => 'EXT-002',
            'vendor_item_id' => 'VENDOR-002',
            'product_id' => 'PROD-002',
            'item_id' => null,
        ]);

        ProductLink::create([
            'external_wms_sku_id' => 'EXT-003',
            'vendor_item_id' => 'VENDOR-003',
            'product_id' => null,
            'item_id' => null,
        ]);

        $response = $this->getJson('/api/vid/statistics');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'total_links' => 3,
                    'links_with_product_id' => 2,
                    'links_with_item_id' => 1,
                    'complete_links' => 1,
                    'completion_rate' => 33.33,
                ],
            ]);
    }

    /**
     * 빈 통계 조회 테스트
     */
    public function test_vid_statistics_empty(): void
    {
        $response = $this->getJson('/api/vid/statistics');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'total_links' => 0,
                    'links_with_product_id' => 0,
                    'links_with_item_id' => 0,
                    'complete_links' => 0,
                    'completion_rate' => 0,
                ],
            ]);
    }
}
