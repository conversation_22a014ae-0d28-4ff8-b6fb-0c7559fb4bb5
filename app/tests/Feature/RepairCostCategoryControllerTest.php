<?php

namespace Tests\Feature;

use App\Models\Cate4;
use App\Models\Cate5;
use App\Models\RepairCostCategory;
use App\Models\RepairCostPolicy;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class RepairCostCategoryControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected $system;

    protected $cate4;

    protected $cate5;

    protected function setUp(): void
    {
        parent::setUp();

        // 테스트용 사용자 생성 및 인증
        $this->user = User::factory()->create();
        Sanctum::actingAs($this->user);

        // 테스트용 데이터 생성
        $this->system = RepairCostPolicy::create([
            'name' => 'monitor',
            'display_name' => '모니터',
            'description' => '모니터 수리비 정책',
            'pricing_type' => 'size',
            'is_active' => true,
        ]);

        $this->cate4 = Cate4::create([
            'name' => '테스트 카테고리 4',
        ]);

        $this->cate5 = Cate5::create([
            'cate4_id' => $this->cate4->id,
            'name' => '테스트 카테고리 5',
        ]);
    }

    /** @test */
    public function 카테고리_목록을_조회할_수_있다()
    {
        // Given: 카테고리 생성
        RepairCostCategory::create([
            'repair_cost_policy_id' => $this->system->id,
            'cate4_id' => $this->cate4->id,
            'cate5_id' => null,
            'pricing_criteria' => 'size',
            'is_active' => true,
        ]);

        // When: 목록 조회 요청
        $response = $this->getJson('/wms/settings/repairs/cost-categories');

        // Then: 성공 응답과 데이터 확인
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'repair_cost_policy_id',
                        'cate4_id',
                        'cate5_id',
                        'pricing_criteria',
                        'is_active',
                        'system',
                        'cate4',
                        'cate5',
                        'ranges',
                    ],
                ],
            ])
            ->assertJson([
                'success' => true,
            ]);
    }

    /** @test */
    public function 시스템별로_카테고리를_필터링할_수_있다()
    {
        // Given: 다른 시스템의 카테고리 생성
        $otherSystem = RepairCostPolicy::create([
            'name' => 'general',
            'display_name' => '일반',
            'description' => '일반 수리비 정책',
            'pricing_type' => 'price',
            'is_active' => true,
        ]);

        RepairCostCategory::create([
            'repair_cost_policy_id' => $this->system->id,
            'cate4_id' => $this->cate4->id,
            'pricing_criteria' => 'size',
            'is_active' => true,
        ]);

        RepairCostCategory::create([
            'repair_cost_policy_id' => $otherSystem->id,
            'cate4_id' => $this->cate4->id,
            'pricing_criteria' => 'price',
            'is_active' => true,
        ]);

        // When: 특정 시스템으로 필터링
        $response = $this->getJson("/wms/settings/repairs/cost-categories?system_id={$this->system->id}");

        // Then: 해당 시스템의 카테고리만 반환
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals($this->system->id, $data[0]['repair_cost_policy_id']);
    }

    /** @test */
    public function 새로운_카테고리를_생성할_수_있다()
    {
        // Given: 카테고리 생성 데이터
        $categoryData = [
            'repair_cost_policy_id' => $this->system->id,
            'cate4_id' => $this->cate4->id,
            'cate5_id' => null,
            'pricing_criteria' => 'size',
            'is_active' => true,
        ];

        // When: 카테고리 생성 요청
        $response = $this->postJson('/wms/settings/repairs/cost-categories', $categoryData);

        // Then: 성공 응답과 데이터베이스 확인
        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => '카테고리가 성공적으로 생성되었습니다.',
            ]);

        $this->assertDatabaseHas('repair_cost_categories', [
            'repair_cost_policy_id' => $this->system->id,
            'cate4_id' => $this->cate4->id,
            'pricing_criteria' => 'size',
        ]);
    }

    /** @test */
    public function 중복_매핑_시_오류가_발생한다()
    {
        // Given: 이미 존재하는 카테고리
        RepairCostCategory::create([
            'repair_cost_policy_id' => $this->system->id,
            'cate4_id' => $this->cate4->id,
            'cate5_id' => null,
            'pricing_criteria' => 'size',
            'is_active' => true,
        ]);

        // When: 동일한 매핑으로 카테고리 생성 시도
        $categoryData = [
            'repair_cost_policy_id' => $this->system->id,
            'cate4_id' => $this->cate4->id,
            'cate5_id' => null,
            'pricing_criteria' => 'size',
            'is_active' => true,
        ];

        $response = $this->postJson('/wms/settings/repairs/cost-categories', $categoryData);

        // Then: 중복 오류 응답
        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'error_code' => 'DUPLICATE_CATEGORY_MAPPING',
            ]);
    }

    /** @test */
    public function 시스템과_호환되지_않는_가격_기준_시_오류가_발생한다()
    {
        // Given: 모니터 시스템 (크기 기준만 허용)
        $categoryData = [
            'repair_cost_policy_id' => $this->system->id,
            'cate4_id' => $this->cate4->id,
            'pricing_criteria' => 'price', // 모니터 시스템에서는 허용되지 않음
            'is_active' => true,
        ];

        // When: 호환되지 않는 가격 기준으로 생성 시도
        $response = $this->postJson('/wms/settings/repairs/cost-categories', $categoryData);

        // Then: 호환성 오류 응답
        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'error_code' => 'INCOMPATIBLE_PRICING_CRITERIA',
            ]);
    }

    /** @test */
    public function 카테고리_정보를_조회할_수_있다()
    {
        // Given: 카테고리 생성
        $category = RepairCostCategory::create([
            'repair_cost_policy_id' => $this->system->id,
            'cate4_id' => $this->cate4->id,
            'cate5_id' => $this->cate5->id,
            'pricing_criteria' => 'size',
            'is_active' => true,
        ]);

        // When: 카테고리 조회 요청
        $response = $this->getJson("/wms/settings/repairs/cost-categories/{$category->id}");

        // Then: 성공 응답과 상세 정보 확인
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'id' => $category->id,
                    'repair_cost_policy_id' => $this->system->id,
                    'cate4_id' => $this->cate4->id,
                    'cate5_id' => $this->cate5->id,
                    'pricing_criteria' => 'size',
                ],
            ]);
    }

    /** @test */
    public function 카테고리_정보를_수정할_수_있다()
    {
        // Given: 카테고리 생성
        $category = RepairCostCategory::create([
            'repair_cost_policy_id' => $this->system->id,
            'cate4_id' => $this->cate4->id,
            'pricing_criteria' => 'size',
            'is_active' => true,
        ]);

        // When: 카테고리 수정 요청
        $updateData = [
            'is_active' => false,
        ];

        $response = $this->putJson("/wms/settings/repairs/cost-categories/{$category->id}", $updateData);

        // Then: 성공 응답과 데이터베이스 확인
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '카테고리가 성공적으로 수정되었습니다.',
            ]);

        $this->assertDatabaseHas('repair_cost_categories', [
            'id' => $category->id,
            'is_active' => false,
        ]);
    }

    /** @test */
    public function 카테고리를_삭제할_수_있다()
    {
        // Given: 카테고리 생성
        $category = RepairCostCategory::create([
            'repair_cost_policy_id' => $this->system->id,
            'cate4_id' => $this->cate4->id,
            'pricing_criteria' => 'size',
            'is_active' => true,
        ]);

        // When: 카테고리 삭제 요청
        $response = $this->deleteJson("/wms/settings/repairs/cost-categories/{$category->id}");

        // Then: 성공 응답과 데이터베이스 확인
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '카테고리가 성공적으로 삭제되었습니다.',
            ]);

        $this->assertDatabaseMissing('repair_cost_categories', [
            'id' => $category->id,
        ]);
    }

    /** @test */
    public function 매핑_가능한_카테고리_목록을_조회할_수_있다()
    {
        // When: 매핑 가능한 카테고리 목록 조회
        $response = $this->getJson("/wms/settings/repairs/cost-categories/available-categories?system_id={$this->system->id}");

        // Then: 성공 응답과 구조 확인
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'available_cate4',
                    'available_cate5',
                    'current_mappings',
                ],
            ])
            ->assertJson([
                'success' => true,
            ]);
    }

    /** @test */
    public function 카테고리_일괄_매핑을_설정할_수_있다()
    {
        // Given: 일괄 매핑 데이터
        $mappingData = [
            'repair_cost_policy_id' => $this->system->id,
            'mappings' => [
                [
                    'cate4_id' => $this->cate4->id,
                    'cate5_id' => null,
                    'pricing_criteria' => 'size',
                    'is_active' => true,
                ],
            ],
        ];

        // When: 일괄 매핑 요청
        $response = $this->postJson('/wms/settings/repairs/cost-categories/bulk-mapping', $mappingData);

        // Then: 성공 응답과 데이터베이스 확인
        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => '카테고리 매핑이 성공적으로 설정되었습니다.',
                'data' => [
                    'created_count' => 1,
                ],
            ]);

        $this->assertDatabaseHas('repair_cost_categories', [
            'repair_cost_policy_id' => $this->system->id,
            'cate4_id' => $this->cate4->id,
            'pricing_criteria' => 'size',
        ]);
    }

    /** @test */
    public function 매핑_상태를_조회할_수_있다()
    {
        // Given: 일부 카테고리 매핑 생성
        RepairCostCategory::create([
            'repair_cost_policy_id' => $this->system->id,
            'cate4_id' => $this->cate4->id,
            'pricing_criteria' => 'size',
            'is_active' => true,
        ]);

        // When: 매핑 상태 조회
        $response = $this->getJson("/wms/settings/repairs/cost-categories/mapping-status/{$this->system->id}");

        // Then: 성공 응답과 통계 정보 확인
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'system',
                    'mapping_status' => [
                        'cate4' => [
                            'total',
                            'mapped',
                            'mapping_rate',
                        ],
                        'cate5' => [
                            'total',
                            'mapped',
                            'mapping_rate',
                        ],
                        'active_mappings',
                        'total_mappings',
                    ],
                ],
            ])
            ->assertJson([
                'success' => true,
            ]);
    }

    /** @test */
    public function 존재하지_않는_카테고리_조회_시_404_오류가_발생한다()
    {
        // When: 존재하지 않는 카테고리 조회
        $response = $this->getJson('/wms/settings/repairs/cost-categories/999999');

        // Then: 404 오류 응답
        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'error_code' => 'CATEGORY_NOT_FOUND',
            ]);
    }

    /** @test */
    public function 잘못된_데이터로_카테고리_생성_시_유효성_검사_오류가_발생한다()
    {
        // Given: 잘못된 카테고리 데이터
        $invalidData = [
            'repair_cost_policy_id' => 999999, // 존재하지 않는 시스템
            'cate4_id' => $this->cate4->id,
            'pricing_criteria' => 'invalid_criteria', // 잘못된 가격 기준
        ];

        // When: 잘못된 데이터로 생성 시도
        $response = $this->postJson('/wms/settings/repairs/cost-categories', $invalidData);

        // Then: 유효성 검사 오류 응답
        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'error_code' => 'VALIDATION_FAILED',
            ])
            ->assertJsonStructure(['errors']);
    }
}
