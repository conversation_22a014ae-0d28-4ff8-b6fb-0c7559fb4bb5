<?php

namespace Tests\Feature;

use App\Models\MonitorSizeLookup;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class MonitorSizeLookupControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create();
        Sanctum::actingAs($user);
    }

    /** @test */
    public function 모니터_사이즈_목록을_검색할_수_있다()
    {
        MonitorSizeLookup::factory()->count(5)->create();

        $response = $this->getJson('/wms/settings/repairs/monitor-sizes?brand=brand&unit=INCH');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'items',
                    'pagination' => [
                        'total', 'per_page', 'current_page', 'last_page',
                    ],
                ],
            ]);
    }

    /** @test */
    public function 이름으로_정확히_조회할_수_있다()
    {
        $record = MonitorSizeLookup::factory()->create(['name' => '삼성 32인치 모니터', 'name_hash' => md5('삼성 32인치 모니터')]);

        $response = $this->getJson('/wms/settings/repairs/monitor-sizes?name=삼성 32인치 모니터');

        $response->assertStatus(200);
        $data = $response->json('data.items');
        $this->assertNotEmpty($data);
        $this->assertEquals($record->id, $data[0]['id']);
    }

    /** @test */
    public function 사이즈_범위로_검색할_수_있다()
    {
        MonitorSizeLookup::factory()->create(['size' => 27.00, 'unit' => 'INCH']);
        MonitorSizeLookup::factory()->create(['size' => 32.00, 'unit' => 'INCH']);
        MonitorSizeLookup::factory()->create(['size' => 49.00, 'unit' => 'INCH']);

        $response = $this->getJson('/wms/settings/repairs/monitor-sizes?min_size=28&max_size=40&unit=INCH');

        $response->assertStatus(200);
        $items = $response->json('data.items');
        $this->assertCount(1, $items);
        $this->assertEquals(32.00, $items[0]['size']);
    }

    /** @test */
    public function 브랜드와_unit으로_검색할_수_있다()
    {
        MonitorSizeLookup::factory()->create(['brand' => 'brand', 'unit' => 'INCH']);
        MonitorSizeLookup::factory()->create(['brand' => 'general', 'unit' => 'INCH']);

        $response = $this->getJson('/wms/settings/repairs/monitor-sizes?brand=brand&unit=INCH');

        $response->assertStatus(200);
        $items = $response->json('data.items');
        $this->assertNotEmpty($items);
        foreach ($items as $item) {
            $this->assertEquals('brand', $item['brand']);
            $this->assertEquals('INCH', $item['unit']);
        }
    }

    /** @test */
    public function 브랜드_사이즈_unit만_수정할_수_있다()
    {
        $record = MonitorSizeLookup::factory()->create([
            'name' => '엘지 27인치',
            'name_hash' => md5('엘지 27인치'),
            'brand' => 'general',
            'size' => 27.00,
            'unit' => 'INCH',
        ]);

        $payload = [
            'brand' => 'brand',
            'size' => 27.50,
            'unit' => 'INCH',
        ];

        $response = $this->putJson("/wms/settings/repairs/monitor-sizes/{$record->id}", $payload);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success', 'message', 'data' => ['item'],
            ]);

        $this->assertDatabaseHas('monitor_size_lookups', [
            'id' => $record->id,
            'brand' => 'brand',
            'size' => 27.50,
            'unit' => 'INCH',
            'name' => '엘지 27인치',
            'name_hash' => md5('엘지 27인치'),
        ]);
    }

    /** @test */
    public function name과_name_hash는_수정할_수_없다()
    {
        $record = MonitorSizeLookup::factory()->create();

        $response = $this->putJson("/wms/settings/repairs/monitor-sizes/{$record->id}", [
            'name' => '변경불가',
            'name_hash' => md5('변경불가'),
            'brand' => 'brand',
            'size' => $record->size,
            'unit' => $record->unit,
        ]);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success', 'message', 'error_code', 'errors',
            ]);
    }
}
