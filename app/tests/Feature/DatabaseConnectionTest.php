<?php

use Illuminate\Support\Facades\DB;

test('데이터베이스 연결이 성공적으로 이루어지는지 확인', function () {
    try {
        // 데이터베이스 연결 시도
        $pdo = DB::connection()->getPdo();
        expect($pdo)->toBeObject();
    } catch (Exception $e) {
        $this->fail('데이터베이스 연결 실패: '.$e->getMessage());
    }
});

test('데이터베이스 설정이 올바르게 되어있는지 확인', function () {
    $connection = DB::connection();

    expect($connection->getDatabaseName())->toBe('cornerstone_api')
        ->and(config('database.connections.mysql.host'))->toBe('127.0.0.1')
        ->and(config('database.connections.mysql.port'))->toBe('3306');
});

test('데이터베이스에 간단한 쿼리를 실행할 수 있는지 확인', function () {
    $result = DB::select('SELECT 1 as test');

    expect($result)
        ->toBeArray()
        ->not->toBeEmpty()
        ->and($result[0]->test)->toBe(1);

});
