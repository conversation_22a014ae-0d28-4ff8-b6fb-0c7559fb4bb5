<?php

use App\DTOs\ReqFilterDto;
use App\Http\Controllers\WMS\ReqController;
use App\Jobs\InspectionPassJob;
use App\Jobs\ReqJob;
use App\Models\Req;
use App\Models\User;
use App\Services\CountService;
use App\Services\ReqService;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response;

beforeEach(function () {
    // Mock Auth facade
    $this->user = Mockery::mock(User::class);
    Auth::shouldReceive('user')->andReturn($this->user);

    // Mock ReqService
    $this->reqService = Mockery::mock(ReqService::class);

    // Create controller with mocked service
    $this->controller = new ReqController($this->reqService);
});

afterEach(function () {
    Mockery::close();
});

test('reqList 메서드 검증: 검색필터를 적용하여 요청 목록을 올바르게 반환하는지 확인', function () {
    // Arrange
    $request = new Request([
        'beginAt' => '2025-02-01',
        'endAt' => '2025-02-28',
        'reqType' => 1,
        'pageSize' => 10,
    ]);

    // Builder를 모킹하여 반환
    $queryBuilder = Mockery::mock('Illuminate\Database\Eloquent\Builder');
    $queryBuilder->shouldReceive('paginate')
        ->with(10)
        ->andReturn(['item1', 'item2']);

    // ReqService가 Builder 객체를 반환하도록 수정
    $this->reqService->shouldReceive('getReqList')
        ->once()
        ->withArgs(function (ReqFilterDto $filter) {
            return $filter->beginAt === '2025-02-01' &&
                $filter->endAt === '2025-02-28' &&
                $filter->reqType === 1 &&
                $filter->perPage === 10;
        })
        ->andReturn($queryBuilder);

    // Act
    $response = $this->controller->reqList($request);

    // Assert
    expect($response->getStatusCode())->toBe(200)
        // ->and($response->getContent())->dump() // 결과를 출력합니다
        ->and(json_decode($response->getContent(), true)['data']['items'])->toBe(['item1', 'item2']);
});

test('uncheckedList 메서드 검증: 입고 검수를 해야 할 상품이 있는 req 목록 반환 확인', function () {
    // Arrange
    $request = new Request;
    $expectedItems = collect(['item1', 'item2']);

    $this->reqService->shouldReceive('getUncheckedList')
        ->once()
        ->with(['id', 'req_at'], 'req_at')
        ->andReturn($expectedItems);

    // Act
    $response = $this->controller->uncheckedList($request);

    // Assert
    expect($response->getStatusCode())->toBe(200)
        ->and(json_decode($response->getContent(), true)['data']['items'])->toBe($expectedItems->toArray());
});

// test('store method creates a new request', function () {
//     // Arrange
//     Queue::fake();
//     Storage::fake('local');
//
//     $request = new Request([
//         'req_at' => '2023-01-01',
//         'req_type' => 1,
//         'memo' => 'Test memo'
//     ]);
//
//     $req = new Req();
//     $req->id = 1;
//
//     $this->reqService->shouldReceive('create')
//         ->once()
//         ->with($this->user, [
//             'req_at' => '2023-01-01',
//             'req_type' => 1,
//             'memo' => 'Test memo',
//         ])
//         ->andReturn($req);
//
//     // Act
//     $response = $this->controller->store($request);
//
//     // Assert
//     expect($response->getStatusCode())->toBe(200)
//         ->and(json_decode($response->getContent(), true)['data']['req'])->toBe($req)
//         ->and(json_decode($response->getContent(), true)['data']['memo'])->toBe('메모 없음');
// });
//
// test('store method with excel file dispatches job', function () {
//     // Arrange
//     Queue::fake();
//     Storage::fake('local');
//
//     $file = UploadedFile::fake()->create('test.xlsx', 100);
//
//     $request = new Request([
//         'req_at' => '2023-01-01',
//         'req_type' => 1,
//         'memo' => 'Test memo',
//         'redirect' => 'test-redirect',
//         'start_row' => 2
//     ]);
//     $request->files->set('excel', $file);
//
//     $req = new Req();
//     $req->id = 1;
//
//     $this->reqService->shouldReceive('create')
//         ->once()
//         ->andReturn($req);
//
//     // Act
//     $response = $this->controller->store($request);
//
//     // Assert
//     Queue::assertPushed(ReqJob::class, function ($job) use ($req) {
//         return $job->req->id === $req->id;
//     });
//
//     expect($response->getStatusCode())->toBe(200);
// });
//
// test('update method updates an existing request', function () {
//     // Arrange
//     $reqId = 1;
//     $request = new Request([
//         'req_at' => '2023-01-01',
//         'req_type' => 1,
//         'status' => 10,
//         'memo' => 'Updated memo'
//     ]);
//
//     $req = new Req();
//     $req->id = $reqId;
//
//     $this->reqService->shouldReceive('update')
//         ->once()
//         ->with($reqId, $this->user, [
//             'req_at' => '2023-01-01',
//             'req_type' => 1,
//             'status' => 10,
//             'memo' => 'Updated memo',
//         ])
//         ->andReturn($req);
//
//     // Act
//     $response = $this->controller->update($request, $reqId);
//
//     // Assert
//     expect($response->getStatusCode())->toBe(200)
//         ->and(json_decode($response->getContent(), true)['data']['req'])->toBe($req)
//         ->and(json_decode($response->getContent(), true)['data']['memo'])->toBe('메모 없음');
// });
//
// test('checkUndelivered method dispatches InspectionPassJob', function () {
//     // Arrange
//     Queue::fake();
//
//     $reqId = 1;
//     $req = new Req();
//     $req->id = $reqId;
//     $req->redicret = 'test-redirect';
//
//     Req::shouldReceive('find')
//         ->once()
//         ->with($reqId)
//         ->andReturn($req);
//
//     // Act
//     $response = $this->controller->checkUndelivered($reqId);
//
//     // Assert
//     Queue::assertPushed(InspectionPassJob::class, function ($job) use ($req) {
//         return $job->req->id === $req->id;
//     });
//
//     expect($response->getStatusCode())->toBe(200);
// });
//
// test('destroy method deletes a request', function () {
//     // Arrange
//     $reqId = 1;
//
//     $this->reqService->shouldReceive('destroy')
//         ->once()
//         ->with($reqId, $this->user)
//         ->andReturn(true);
//
//     // Act
//     $response = $this->controller->destroy($reqId);
//
//     // Assert
//     expect($response->getStatusCode())->toBe(200);
// });
//
// test('updateReqCount method updates request counts', function () {
//     // Arrange
//     $reqId = 1;
//     $countService = Mockery::mock(CountService::class);
//
//     // Use a partial mock to replace the CountService instantiation
//     $controller = Mockery::mock(ReqController::class, [$this->reqService])->makePartial();
//     $controller->shouldReceive('__construct')->never();
//
//     app()->instance(CountService::class, $countService);
//
//     $countService->shouldReceive('updateReqCount')
//         ->once()
//         ->with($reqId)
//         ->andReturn(null);
//
//     // Act
//     $response = $controller->updateReqCount($reqId);
//
//     // Assert
//     expect($response->getStatusCode())->toBe(200)
//         ->and(json_decode($response->getContent(), true)['data']['message'])->toBe('카운터가 업데이트 되었습니다.');
// });
//
// test('store method returns error when excel file is invalid', function () {
//     // Arrange
//     Storage::fake('local');
//
//     // Create an invalid file (not an excel)
//     $file = UploadedFile::fake()->create('test.txt', 100);
//
//     $request = new Request([
//         'req_at' => '2023-01-01',
//         'req_type' => 1,
//         'memo' => 'Test memo'
//     ]);
//     $request->files->set('excel', $file);
//
//     $req = new Req();
//     $req->id = 1;
//
//     $this->reqService->shouldReceive('create')
//         ->once()
//         ->andReturn($req);
//
//     // Act
//     $response = $this->controller->store($request);
//
//     // Assert
//     expect($response->getStatusCode())->toBe(Response::HTTP_BAD_REQUEST)
//         ->and(json_decode($response->getContent(), true)['error']['message'])->toContain('엑셀 파일이 아닙니다');
// });
//
// test('store method returns error when excel file is too large', function () {
//     // Arrange
//     Storage::fake('local');
//
//     // Create a file larger than 2MB
//     $file = UploadedFile::fake()->create('test.xlsx', 3000);
//
//     $request = new Request([
//         'req_at' => '2023-01-01',
//         'req_type' => 1,
//         'memo' => 'Test memo'
//     ]);
//     $request->files->set('excel', $file);
//
//     $req = new Req();
//     $req->id = 1;
//
//     $this->reqService->shouldReceive('create')
//         ->once()
//         ->andReturn($req);
//
//     // Act
//     $response = $this->controller->store($request);
//
//     // Assert
//     expect($response->getStatusCode())->toBe(Response::HTTP_BAD_REQUEST)
//         ->and(json_decode($response->getContent(), true)['error']['message'])->toContain('파일 크기는 2MB를 초과할 수 없습니다');
// });
