<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NotificationSendAuthorizationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
    }

    public function test_관리자_역할_사용자는_알림_전송_권한이_있다()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        $this->actingAs($admin, 'sanctum');

        $requestData = [
            'title' => '관리자 알림',
            'content' => '관리자가 전송하는 알림입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '알림이 성공적으로 전송되었습니다.'
            ]);
    }

    public function test_슈퍼_관리자_역할_사용자는_알림_전송_권한이_있다()
    {
        // Arrange
        $superAdmin = User::factory()->create(['role' => User::ROLE_SUPER_ADMIN]);
        $this->actingAs($superAdmin, 'sanctum');

        $requestData = [
            'title' => '슈퍼 관리자 알림',
            'content' => '슈퍼 관리자가 전송하는 알림입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => '알림이 성공적으로 전송되었습니다.'
            ]);
    }

    public function test_일반_사용자_역할은_알림_전송_권한이_없다()
    {
        // Arrange
        $user = User::factory()->create(['role' => User::ROLE_EMPLOYEE]);
        $this->actingAs($user, 'sanctum');

        $requestData = [
            'title' => '일반 사용자 알림',
            'content' => '일반 사용자가 전송하려는 알림입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(403);
    }

    public function test_매니저_역할은_알림_전송_권한이_없다()
    {
        // Arrange
        $manager = User::factory()->create(['role' => User::ROLE_PALLET_MANAGER]);
        $this->actingAs($manager, 'sanctum');

        $requestData = [
            'title' => '매니저 알림',
            'content' => '매니저가 전송하려는 알림입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(403);
    }

    public function test_게스트_사용자는_알림_전송_권한이_없다()
    {
        // Arrange
        $guest = User::factory()->create(['role' => User::ROLE_GUEST]);
        $this->actingAs($guest, 'sanctum');

        $requestData = [
            'title' => '게스트 알림',
            'content' => '게스트가 전송하려는 알림입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(403);
    }

    public function test_인증되지_않은_사용자는_알림_전송_권한이_없다()
    {
        // Arrange - 인증하지 않음
        $requestData = [
            'title' => '비인증 사용자 알림',
            'content' => '비인증 사용자가 전송하려는 알림입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(401);
    }

    public function test_비활성_관리자는_알림_전송_권한이_없다()
    {
        // Arrange
        $inactiveAdmin = User::factory()->create([
            'role' => User::ROLE_ADMIN,
            'status' => 0 // 비활성 상태
        ]);
        $this->actingAs($inactiveAdmin, 'sanctum');

        $requestData = [
            'title' => '비활성 관리자 알림',
            'content' => '비활성 관리자가 전송하려는 알림입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        $response->assertStatus(403);
    }

    public function test_삭제된_관리자는_알림_전송_권한이_없다()
    {
        // Arrange
        $deletedAdmin = User::factory()->create([
            'role' => User::ROLE_ADMIN,
            'status' => User::MEMBER_STATUS_ACTIVE
        ]);
        
        // 사용자를 소프트 삭제
        $deletedAdmin->delete();
        
        $this->actingAs($deletedAdmin, 'sanctum');

        $requestData = [
            'title' => '삭제된 관리자 알림',
            'content' => '삭제된 관리자가 전송하려는 알림입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData);

        // Assert
        // 삭제된 사용자도 Sanctum에서는 인증될 수 있으므로 미들웨어에서 체크해야 함
        $response->assertStatus(403);
    }

    public function test_SSE_연결은_모든_인증된_사용자가_가능하다()
    {
        // Arrange - 다양한 역할의 사용자들
        $users = [
            User::factory()->create(['role' => User::ROLE_ADMIN]),
            User::factory()->create(['role' => User::ROLE_SUPER_ADMIN]),
            User::factory()->create(['role' => User::ROLE_PALLET_MANAGER]),
            User::factory()->create(['role' => User::ROLE_EMPLOYEE]),
            User::factory()->create(['role' => User::ROLE_GUEST])
        ];

        foreach ($users as $user) {
            // Act
            $this->actingAs($user, 'sanctum');
            $response = $this->get('/wms/sse/notifications');

            // Assert
            if ($response->status() !== 200) {
                dump($response->getContent());
            }
            $response->assertStatus(200)
                ->assertHeader('Content-Type', 'text/event-stream; charset=UTF-8');
        }
    }

    public function test_SSE_연결은_인증되지_않은_사용자는_불가능하다()
    {
        // Act
        $response = $this->get('/wms/sse/notifications');

        // Assert
        $response->assertStatus(401);
    }

    public function test_토큰_만료시_알림_전송_권한이_없다()
    {
        // Arrange
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);
        
        // 만료된 토큰으로 인증 시뮬레이션
        $expiredToken = $admin->createToken('test-token', ['*'], now()->subDay())->plainTextToken;

        $requestData = [
            'title' => '만료된 토큰 알림',
            'content' => '만료된 토큰으로 전송하려는 알림입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData, [
            'Authorization' => 'Bearer ' . $expiredToken
        ]);

        // Assert
        $response->assertStatus(401);
    }

    public function test_잘못된_토큰으로_알림_전송_권한이_없다()
    {
        // Arrange
        $requestData = [
            'title' => '잘못된 토큰 알림',
            'content' => '잘못된 토큰으로 전송하려는 알림입니다.',
            'target_type' => 'all',
            'target_ids' => []
        ];

        // Act
        $response = $this->postJson('/wms/notifications/send', $requestData, [
            'Authorization' => 'Bearer invalid-token'
        ]);

        // Assert
        $response->assertStatus(401);
    }
}