<?php

namespace Tests\Feature;

use App\Models\Cate4;
use App\Models\Product;
use App\Models\RepairCost;
use App\Models\RepairCostCategory;
use App\Models\RepairCostPolicy;
use App\Models\RepairCostRange;
use App\Models\RepairCostTypeProcessMapping;
use App\Models\RepairProcess;
use App\Models\Req;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * 수리비 계산 API 컨트롤러 테스트
 */
class RepairCostControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestData();
    }

    /**
     * 테스트 데이터 설정
     */
    private function setupTestData(): void
    {
        // Req 생성
        $req = Req::create([
            'req_type' => Req::TYPE_COUPANG,
            'status' => Req::STATUS_REGISTERED,
            'req_at' => now(),
        ]);

        // Cate4 생성
        $cate4 = Cate4::create([
            'cate4_no' => 1001,
            'name' => '모니터',
        ]);

        // RepairProcess 생성
        $process1 = RepairProcess::create(['name' => '부품교체', 'code' => 'REPAIR_PARTS']);
        $process2 = RepairProcess::create(['name' => '세척', 'code' => 'REPAIR_CLEANING']);
        $process3 = RepairProcess::create(['name' => '소프트웨어', 'code' => 'REPAIR_SOFTWARE']);

        // RepairCostTypeProcessMapping 생성
        RepairCostTypeProcessMapping::create([
            'process_code' => 'REPAIR_PARTS',
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS,
            'is_active' => true,
        ]);
        RepairCostTypeProcessMapping::create([
            'process_code' => 'REPAIR_CLEANING',
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING,
            'is_active' => true,
        ]);
        RepairCostTypeProcessMapping::create([
            'process_code' => 'REPAIR_SOFTWARE',
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_SOFTWARE,
            'is_active' => true,
        ]);

        // 수리비 정책 생성
        $system = RepairCostPolicy::create([
            'name' => 'monitor',
            'display_name' => '모니터',
            'description' => '모니터 수리비 정책',
            'pricing_type' => 'size',
            'is_active' => true,
        ]);

        // 수리비 카테고리 생성
        $category = RepairCostCategory::create([
            'repair_cost_policy_id' => $system->id,
            'cate4_id' => $cate4->id,
            'cate5_id' => null,
            'pricing_criteria' => 'size',
            'is_active' => true,
        ]);

        // 수리비 범위 생성
        $range = RepairCostRange::create([
            'repair_cost_category_id' => $category->id,
            'range_name' => '24인치 미만',
            'min_value' => 0,
            'max_value' => 24,
            'unit' => 'inch',
            'is_active' => true,
        ]);

        // 수리비 생성
        RepairCost::create([
            'repair_cost_range_id' => $range->id,
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_PARTS,
            'amount' => 50000,
        ]);
        RepairCost::create([
            'repair_cost_range_id' => $range->id,
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_CLEANING,
            'amount' => 30000,
        ]);
        RepairCost::create([
            'repair_cost_range_id' => $range->id,
            'repair_type' => RepairCostTypeProcessMapping::REPAIR_TYPE_SOFTWARE,
            'amount' => 40000,
        ]);

        // Product 생성
        Product::create([
            'qaid' => 'TEST001',
            'name' => '삼성 24인치 모니터',
            'amount' => 300000,
            'cate4_id' => $cate4->id,
            'cate5_id' => null,
            'req_id' => $req->id,
            'barcode' => 'TEST001',
        ]);
    }

    /**
     * 수리 프로세스 목록 조회 API 테스트
     */
    public function test_get_available_processes_success(): void
    {
        // 인증 우회
        $this->withoutMiddleware();

        $response = $this->getJson('/wms/repair-costs/processes/TEST001');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'processes' => [
                        '*' => [
                            'process_id',
                            'process_code',
                            'process_name',
                            'repair_type',
                            'repair_type_name',
                            'estimated_cost',
                            'cost_basis',
                        ],
                    ],
                    'total_count',
                ],
            ]);

        $data = $response->json('data');

        // 프로세스 개수 확인
        $this->assertGreaterThan(0, $data['total_count']);
        $this->assertCount($data['total_count'], $data['processes']);

        // 첫 번째 프로세스 데이터 확인
        $firstProcess = $data['processes'][0];
        $this->assertArrayHasKey('process_id', $firstProcess);
        $this->assertArrayHasKey('process_code', $firstProcess);
        $this->assertArrayHasKey('process_name', $firstProcess);
        $this->assertArrayHasKey('repair_type', $firstProcess);
        $this->assertArrayHasKey('repair_type_name', $firstProcess);
        $this->assertArrayHasKey('estimated_cost', $firstProcess);
        $this->assertArrayHasKey('cost_basis', $firstProcess);

        // 예상 수리비가 숫자인지 확인
        $this->assertIsNumeric($firstProcess['estimated_cost']);
        $this->assertGreaterThan(0, $firstProcess['estimated_cost']);
    }

    /**
     * 존재하지 않는 QAID로 수리 프로세스 목록 조회 테스트
     */
    public function test_get_available_processes_product_not_found(): void
    {
        // 인증 우회
        $this->withoutMiddleware();

        $response = $this->getJson('/wms/repair-costs/processes/NONEXISTENT');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => '해당 QAID의 제품을 찾을 수 없습니다.',
            ]);
    }

    /**
     * 수리 유형별 예상 수리비 정보 포함 확인 테스트
     */
    public function test_get_available_processes_includes_estimated_costs(): void
    {
        // 인증 우회
        $this->withoutMiddleware();

        $response = $this->getJson('/wms/repair-costs/processes/TEST001');

        $response->assertStatus(200);

        $processes = $response->json('data.processes');

        foreach ($processes as $process) {
            // 각 프로세스에 예상 수리비 정보가 포함되어 있는지 확인
            $this->assertArrayHasKey('estimated_cost', $process);
            $this->assertArrayHasKey('cost_basis', $process);
            $this->assertArrayHasKey('repair_type_name', $process);

            // 예상 수리비가 유효한 값인지 확인
            $this->assertIsNumeric($process['estimated_cost']);
            $this->assertGreaterThan(0, $process['estimated_cost']);

            // 수리 유형명이 한글로 표시되는지 확인
            $this->assertNotEmpty($process['repair_type_name']);
            $this->assertContains($process['repair_type_name'], [
                '수리_부품교체', '수리_세척', '수리_S/W', '수리_기타', '검수(점검)',
            ]);
        }
    }

    /**
     * 제품 카테고리별 필터링 확인 테스트
     */
    public function test_get_available_processes_filtered_by_category(): void
    {
        // 인증 우회
        $this->withoutMiddleware();

        $response = $this->getJson('/wms/repair-costs/processes/TEST001');

        $response->assertStatus(200);

        $processes = $response->json('data.processes');

        // 활성화된 매핑만 반환되는지 확인
        $this->assertGreaterThan(0, count($processes));

        // 각 프로세스가 유효한 매핑을 가지고 있는지 확인
        foreach ($processes as $process) {
            $this->assertNotEmpty($process['process_code']);
            $this->assertNotEmpty($process['repair_type']);

            // 매핑이 실제로 존재하는지 확인
            $mapping = RepairCostTypeProcessMapping::where('process_code', $process['process_code'])
                ->where('is_active', true)
                ->first();
            $this->assertNotNull($mapping);
        }
    }
}
