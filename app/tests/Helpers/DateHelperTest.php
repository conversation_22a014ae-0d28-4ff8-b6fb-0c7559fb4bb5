<?php

namespace Tests\Helpers;

use App\Helpers\DateHelper;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\TestCase;

class DateHelperTest extends TestCase
{
    /**
     * Tests the createDateFromInput method in DateHelper
     */
    public function test_create_date_from_input(): void
    {
        // Prepare
        $validDateInput = '2020-12-01';
        $invalidDateInput = 'invalid date format';
        $defaultDate = '2020-01-01';

        // Execute and Assert - for valid date input
        $resultWithValidDate = DateHelper::createDateFromInput($validDateInput, $defaultDate);
        $this->assertEquals($validDateInput, $resultWithValidDate);

        // Execute and Assert - for invalid date input
        $resultWithInvalidDate = DateHelper::createDateFromInput($invalidDateInput, $defaultDate);
        $this->assertEquals($defaultDate, $resultWithInvalidDate);

        // Execute and Assert - Empty input should Default is returned
        $resultEmptyInput = DateHelper::createDateFromInput('', $defaultDate);
        $this->assertEquals($defaultDate, $resultEmptyInput);

        // Test with an input that is a date but not in the 'Y-m-d' format
        // The code should consider this input as invalid and returnthe default date
        $dateNotInExpectedFormat = '01-12-2020';
        $resultNotExpectedFormat = DateHelper::createDateFromInput($dateNotInExpectedFormat, $defaultDate);
        $this->assertEquals($defaultDate, $resultNotExpectedFormat);
    }

    /**
     * Tests the getBetweenDate method in DateHelper
     */
    public function test_get_between_date(): void
    {
        // Prepare
        $validDateBegin = '2020-08-01';
        $validDateEnd = '2020-12-31';
        $invalidDateBegin = 'invalid begin date format';
        $invalidDateEnd = 'invalid end date format';

        // Execute and Assert - for valid begin and end dates
        $resultWithValidDates = DateHelper::getBetweenDate($validDateBegin, $validDateEnd);
        $this->assertEquals($validDateBegin, $resultWithValidDates[1]);
        $this->assertEquals($validDateEnd, $resultWithValidDates[2]);

        // Execute and Assert - for invalid begin and end dates
        $resultWithInvalidDates = DateHelper::getBetweenDate($invalidDateBegin, $invalidDateEnd);
        $now = Carbon::now();
        $defaultDateBegin = $now->copy()->subMonths(3)->format('Y-m-d');
        $defaultDateEnd = $now->copy()->format('Y-m-d');
        $this->assertEquals($defaultDateBegin, $resultWithInvalidDates[1]);
        $this->assertEquals($defaultDateEnd, $resultWithInvalidDates[2]);

        // Execute and Assert - for null begin and end dates
        $resultWithNullDates = DateHelper::getBetweenDate(null, null);
        $this->assertEquals($defaultDateBegin, $resultWithNullDates[1]);
        $this->assertEquals($defaultDateEnd, $resultWithNullDates[2]);
    }

    /**
     * Test to check if getBetweenDate method in the DateHelper class returns correct dates
     * when begin and end dates are properly formatted.
     */
    public function test_get_between_date_with_valid_dates(): void
    {
        // Prepare
        $begin = '2020-08-01';
        $end = '2020-12-31';

        // Execute
        $result = DateHelper::getBetweenDate($begin, $end);

        // Assert
        $this->assertEquals($begin, $result[1]);
        $this->assertEquals($end, $result[2]);
    }

    /**
     * Test to check if getBetweenDate method in the DateHelper class returns default dates
     * when begin and end dates are null.
     */
    public function test_get_between_date_with_null_dates(): void
    {
        // Prepare
        $now = Carbon::now();
        $defaultBegin = $now->copy()->subMonths(3)->format('Y-m-d');
        $defaultEnd = $now->copy()->format('Y-m-d');

        // Execute
        $result = DateHelper::getBetweenDate(null, null);

        // Assert
        $this->assertEquals($defaultBegin, $result[1]);
        $this->assertEquals($defaultEnd, $result[2]);
    }

    /**
     * Test to check if getBetweenDate method in the DateHelper class returns default dates
     * when begin and end dates are not properly formatted.
     */
    public function test_get_between_date_with_invalid_dates(): void
    {
        // Prepare
        $now = Carbon::now();
        $defaultBegin = $now->copy()->subMonths(3)->format('Y-m-d');
        $defaultEnd = $now->copy()->format('Y-m-d');
        $invalidBegin = 'invalid begin date format';
        $invalidEnd = 'invalid end date format';

        // Execute
        $result = DateHelper::getBetweenDate($invalidBegin, $invalidEnd);

        // Assert
        $this->assertEquals($defaultBegin, $result[1]);
        $this->assertEquals($defaultEnd, $result[2]);
    }
}
