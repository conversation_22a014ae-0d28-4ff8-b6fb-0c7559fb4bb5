<?php

// use App\Http\Controllers\Api\SSE\SseMonitoringController;
use App\Http\Controllers\WMS\Notification\SSEController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 기본 API 라우트
|--------------------------------------------------------------------------
|
| 애플리케이션의 기본 API 라우트들입니다.
| 사용자 인증 및 시스템 상태 확인 등의 기본 기능을 제공합니다.
|
*/

// 인증된 사용자 정보 조회
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// SSE 실시간 알림 - 쿠키 기반 인증
Route::name('sse.')->prefix('sse')->middleware('auth:sanctum')->group(function () {
    Route::get('/notifications', [SSEController::class, 'connect'])->name('notifications');
    Route::post('/notifications/read', [SSEController::class, 'markAsRead'])->name('notifications.read');
    Route::get('/notifications/unread-count', [SSEController::class, 'getUnreadCount'])->name('notifications.unread-count');
});

// // 공개 상태 확인 엔드포인트 (인증 불필요)
// Route::get('/health', [SseMonitoringController::class, 'healthCheck'])
//     ->name('health.check');
//
// /*
// |--------------------------------------------------------------------------
// | 기능별 라우트 파일 로드
// |--------------------------------------------------------------------------
// |
// | 각 기능별로 분리된 라우트 파일들을 로드합니다.
// |
// */
//
// // SSE 관련 라우트
// require __DIR__ . '/api/sse.php';
//
// // SSE 모니터링 관련 라우트
// require __DIR__ . '/api/monitoring.php';
//
// // SSE 알림 관련 라우트
// require __DIR__ . '/api/notifications.php';
