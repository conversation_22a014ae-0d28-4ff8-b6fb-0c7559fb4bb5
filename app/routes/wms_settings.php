<?php

use App\Http\Controllers\WMS\CostTypeProcessMappingController;
use App\Http\Controllers\WMS\LocationController;
use App\Http\Controllers\WMS\MonitorRuleController;
use App\Http\Controllers\WMS\MonitorSizeLookupController;
use App\Http\Controllers\WMS\Notification\GroupController;
use App\Http\Controllers\WMS\Notification\SendController;
use App\Http\Controllers\WMS\Notification\TemplateController;
use App\Http\Controllers\WMS\RepairCostCategoryController;
use App\Http\Controllers\WMS\RepairCostPolicyController;
use App\Http\Controllers\WMS\RepairCostRangeController;
use App\Http\Controllers\WMS\RepairPartsCategoryController;
use App\Http\Controllers\WMS\RepairPartsController;
use App\Http\Controllers\WMS\Settings\MemberController as MemberSettingController;
use App\Http\Controllers\WMS\Settings\ProcessController;
use App\Http\Controllers\WMS\Settings\RepairFeeController;
use App\Http\Controllers\WMS\Settings\Repairs\GradeController;
use App\Http\Controllers\WMS\Settings\Repairs\ProcessController as RepairProcessController;
use App\Http\Controllers\WMS\Settings\Repairs\SymptomController;
use App\Http\Controllers\WMS\Settings\WorkStatusController;
use App\Http\Controllers\WMS\Settings\WorkStatusManagementController;
use Illuminate\Support\Facades\Route;

Route::name('setting.')->prefix('settings')->group(function () {
    // 직원 관리
    Route::name('member.')->prefix('members')->group(function () {
        // 전체 관리자 및 관리자가 직원 관리
        Route::middleware('admin.access')->group(function () {
            Route::get('/', [MemberSettingController::class, 'list'])->name('list');
            Route::post('/', [MemberSettingController::class, 'store'])->name('store');
            Route::put('/{id}', [MemberSettingController::class, 'update'])
                ->where('id', '[0-9]+')
                ->name('update');
            Route::delete('/{id}', [MemberSettingController::class, 'destroy'])
                ->where('id', '[0-9]+')
                ->name('destroy');
            Route::get('/active', [MemberSettingController::class, 'activeList'])->name('active.list');
            Route::put('/restore/{id}', [MemberSettingController::class, 'restore'])
                ->where('id', '[0-9]+')
                ->name('restore');
        });

        // 근태 등록 및 보기
        Route::get('/attendances', [MemberSettingController::class, 'attendanceList'])->name('attendance.list');
        Route::post('/attendances', [MemberSettingController::class, 'attendanceStore'])->name('attendance.store');

        // 직원 상세 보기
        Route::get('/{id}', [MemberSettingController::class, 'show'])->name('show');
    });

    // --- 신규 수리 설정 관리 ---
    Route::name('repair.')->prefix('repairs')->group(function () {

        /**
         * 1. 수리 등급 관리 (Repair Grades)
         * GET       /wms/settings/repair/grades            (wms.settings.repair.grades.index)
         * POST      /wms/settings/repair/grades            (wms.settings.repair.grades.store)
         * GET       /wms/settings/repair/grades/{grade}    (wms.settings.repair.grades.show)
         * PUT/PATCH /wms/settings/repair/grades/{grade}    (wms.settings.repair.grades.update)
         * DELETE    /wms/settings/repair/grades/{grade}    (wms.settings.repair.grades.destroy)
         */
        Route::get('/grades/{grade}/symptoms', [GradeController::class, 'getSymptoms'])->name('grades.symptoms');
        Route::post('/grades/{grade}/sync-symptoms', [GradeController::class, 'syncSymptoms'])->name('grades.syncSymptoms');
        Route::apiResource('grades', GradeController::class);

        /**
         * 2. 처리 내용 관리 (Repair Processes)
         * GET       /wms/settings/repair/processes              (wms.settings.repair.processes.index)
         * POST      /wms/settings/repair/processes              (wms.settings.repair.processes.store)
         * GET       /wms/settings/repair/processes/{process}    (wms.settings.repair.processes.show)
         * PUT/PATCH /wms/settings/repair/processes/{process}    (wms.settings.repair.processes.update)
         * DELETE    /wms/settings/repair/processes/{process}    (wms.settings.repair.processes.destroy)
         */
        Route::get('/processes/{process}/grades', [RepairProcessController::class, 'getGrades'])->name('repairs.processes.grades');
        Route::post('/processes/{process}/sync-grades', [RepairProcessController::class, 'syncGrades'])->name('repairs.processes.syncGrades');
        Route::apiResource('processes', RepairProcessController::class);

        /**
         * 3. 증상 내용 관리 (Repair Symptoms) & 4. 관계 설정
         */
        Route::get('symptoms/{symptom}/processes', [SymptomController::class, 'getProcesses'])
            ->name('symptoms.processes');
        // 관계 설정 (Sync)
        Route::post('symptoms/{symptom}/sync-processes', [SymptomController::class, 'syncProcesses'])
            ->name('symptoms.sync-processes');
        // 증상 CRUD
        Route::apiResource('symptoms', SymptomController::class);

        /**
         * @todo 삭제 예정
         * 5. 기존 수리비 관리 (Legacy Repair Fees)
         * 기존 시스템과의 호환성을 위해 유지
         */
        Route::name('fee')->prefix('fees')->group(function () {
            Route::get('/', [RepairFeeController::class, 'list'])->name('list');
            Route::post('/', [RepairFeeController::class, 'store'])->name('store');
            Route::put('/', [RepairFeeController::class, 'update'])->name('update');
            Route::delete('/{id}', [RepairFeeController::class, 'destroy'])->name('destroy');

            Route::get('/options', [RepairFeeController::class, 'repairOptions'])->name('options');
        });

        /**
         * 6. 새로운 수리비 정책 관리 (New Repair Cost Policy)
         */
        Route::name('cost.')->prefix('costs')->group(function () {
            // 6.1 수리비 정책 관리
            Route::name('policy.')->prefix('policies')->group(function () {
                Route::get('/', [RepairCostPolicyController::class, 'index'])->name('index');
                Route::get('/statistics', [RepairCostPolicyController::class, 'getStatistics'])->name('statistics');
                Route::post('/', [RepairCostPolicyController::class, 'store'])->name('store');
                Route::get('/{id}', [RepairCostPolicyController::class, 'show'])
                    ->where('id', '[0-9]+')
                    ->name('show');
                Route::put('/{id}', [RepairCostPolicyController::class, 'update'])
                    ->where('id', '[0-9]+')
                    ->name('update');
                Route::patch('/{id}/toggle-active', [RepairCostPolicyController::class, 'toggleActive'])
                    ->where('id', '[0-9]+')
                    ->name('toggle-active');
                Route::post('/{id}/bulk-manage-categories', [RepairCostPolicyController::class, 'bulkManageCategories'])
                    ->where('id', '[0-9]+')
                    ->name('bulk-manage-categories');
                Route::post('/{id}/bulk-manage-ranges', [RepairCostPolicyController::class, 'bulkManageRanges'])
                    ->where('id', '[0-9]+')
                    ->name('bulk-manage-ranges');
                Route::delete('/{id}', [RepairCostPolicyController::class, 'destroy'])
                    ->where('id', '[0-9]+')
                    ->name('destroy');
            });

            // 6.2 수리비 카테고리 관리
            Route::name('category.')->prefix('categories')->group(function () {
                Route::get('/', [RepairCostCategoryController::class, 'index'])->name('index');
                Route::post('/', [RepairCostCategoryController::class, 'store'])->name('store');
                Route::get('/{id}', [RepairCostCategoryController::class, 'show'])
                    ->where('id', '[0-9]+')
                    ->name('show');
                Route::put('/{id}', [RepairCostCategoryController::class, 'update'])
                    ->where('id', '[0-9]+')
                    ->name('update');
                Route::delete('/{id}', [RepairCostCategoryController::class, 'destroy'])
                    ->where('id', '[0-9]+')
                    ->name('destroy');

                // 매핑 관리 관련 라우트
                Route::get('/available-categories', [RepairCostCategoryController::class, 'getAvailableCategories'])
                    ->name('available-categories');
                Route::post('/bulk-mapping', [RepairCostCategoryController::class, 'bulkMapping'])
                    ->name('bulk-mapping');
                Route::get('/mapping-status/{systemId}', [RepairCostCategoryController::class, 'getMappingStatus'])
                    ->where('systemId', '[0-9]+')
                    ->name('mapping-status');
            });

            // 6.3 수리비 범위 관리
            Route::name('range.')->prefix('ranges')->group(function () {
                Route::get('/', [RepairCostRangeController::class, 'index'])->name('index');
                Route::post('/', [RepairCostRangeController::class, 'store'])->name('store');
                Route::get('/{id}', [RepairCostRangeController::class, 'show'])
                    ->where('id', '[0-9]+')
                    ->name('show');
                Route::put('/{id}', [RepairCostRangeController::class, 'update'])
                    ->where('id', '[0-9]+')
                    ->name('update');
                Route::delete('/{id}', [RepairCostRangeController::class, 'destroy'])
                    ->where('id', '[0-9]+')
                    ->name('destroy');

                // 수리비 금액 관리 관련 라우트
                Route::get('/{id}/costs', [RepairCostRangeController::class, 'getCosts'])
                    ->where('id', '[0-9]+')
                    ->name('costs');
                Route::put('/{id}/costs', [RepairCostRangeController::class, 'updateCosts'])
                    ->where('id', '[0-9]+')
                    ->name('update-costs');
                Route::get('/{id}/cost-history', [RepairCostRangeController::class, 'getCostHistory'])
                    ->where('id', '[0-9]+')
                    ->name('cost-history');
                Route::post('/{id}/copy-costs', [RepairCostRangeController::class, 'copyCosts'])
                    ->where('id', '[0-9]+')
                    ->name('copy-costs');
            });
        });

        /**
         * 7. 모니터 규칙 관리 (Monitor Size Extraction Rules)
         */
        Route::name('monitor.rule.')->prefix('monitor-rules')->group(function () {
            Route::get('/', [MonitorRuleController::class, 'index'])->name('index');
            Route::get('/statistics', [MonitorRuleController::class, 'getStatistics'])->name('statistics');
            Route::post('/', [MonitorRuleController::class, 'store'])->name('store');
            Route::get('/{id}', [MonitorRuleController::class, 'show'])
                ->where('id', '[0-9]+')
                ->name('show');
            Route::put('/{id}', [MonitorRuleController::class, 'update'])
                ->where('id', '[0-9]+')
                ->name('update');
            Route::delete('/{id}', [MonitorRuleController::class, 'destroy'])
                ->where('id', '[0-9]+')
                ->name('destroy');

            // 규칙 테스트 및 관리 관련 라우트
            Route::post('/test-extraction', [MonitorRuleController::class, 'testExtraction'])
                ->name('test-extraction');
            Route::post('/reorder', [MonitorRuleController::class, 'reorderRules'])
                ->name('reorder');
            Route::post('/clear-cache', [MonitorRuleController::class, 'clearCache'])
                ->name('clear-cache');
        });

        /**
         * 7-1. 모니터 사이즈 관리 (Monitor Size Lookup Management)
         */
        Route::name('monitor.size.')->prefix('monitor-sizes')->group(function () {
            // 검색: name, name_hash, brand, unit, min_size, max_size
            Route::get('/', [MonitorSizeLookupController::class, 'index'])->name('index');
            // 수정: brand, size, unit만 허용 (name, name_hash는 불변)
            Route::put('/{id}', [MonitorSizeLookupController::class, 'update'])
                ->where('id', '[0-9]+')
                ->name('update');
        });

        /**
         * 8. 프로세스 매핑 관리 (Process Mapping Management)
         */
        Route::name('process.mapping.')->prefix('process-mappings')->group(function () {
            Route::get('/', [CostTypeProcessMappingController::class, 'index'])->name('index');
            Route::get('/statistics', [CostTypeProcessMappingController::class, 'getStatistics'])->name('statistics');
            Route::post('/', [CostTypeProcessMappingController::class, 'store'])->name('store');
            Route::get('/{id}', [CostTypeProcessMappingController::class, 'show'])
                ->where('id', '[0-9]+')
                ->name('show');
            Route::put('/{id}', [CostTypeProcessMappingController::class, 'update'])
                ->where('id', '[0-9]+')
                ->name('update');
            Route::delete('/{id}', [CostTypeProcessMappingController::class, 'destroy'])
                ->where('id', '[0-9]+')
                ->name('destroy');

            // 매핑 관리 관련 라우트
            Route::post('/bulk-update', [CostTypeProcessMappingController::class, 'bulkUpdate'])
                ->name('bulk-update');
            Route::get('/unmapped-processes', [CostTypeProcessMappingController::class, 'getUnmappedProcesses'])
                ->name('unmapped-processes');

            // 매핑 상태 관리 및 대량 처리 관련 라우트
            Route::get('/conflicts', [CostTypeProcessMappingController::class, 'detectConflicts'])
                ->name('conflicts');
            Route::post('/bulk-status-update', [CostTypeProcessMappingController::class, 'bulkStatusUpdate'])
                ->name('bulk-status-update');
            Route::get('/validation-report', [CostTypeProcessMappingController::class, 'generateValidationReport'])
                ->name('validation-report');
            Route::get('/by-repair-type/{repairType}', [CostTypeProcessMappingController::class, 'getMappingsByRepairType'])
                ->name('by-repair-type');
            Route::get('/history', [CostTypeProcessMappingController::class, 'getMappingHistory'])
                ->name('history');
            Route::post('/backup', [CostTypeProcessMappingController::class, 'backupMappings'])
                ->name('backup');
            Route::post('/restore', [CostTypeProcessMappingController::class, 'restoreMappings'])
                ->name('restore');
        });

        /**
         * 9. 구성품 관리 (Parts Management)
         */
        Route::name('parts.')->prefix('parts')->group(function () {
            // 구성품 가격 설정
            Route::get('/', [RepairPartsController::class, 'partsList'])->name('list.parts');
            Route::post('/', [RepairPartsController::class, 'storeParts'])->name('store.parts');
            Route::put('/{id}', [RepairPartsController::class, 'updateParts'])
                ->where(['id' => '[0-9]+'])
                ->name('update.parts');
            Route::delete('/{id}', [RepairPartsController::class, 'destroyParts'])
                ->where(['id' => '[0-9]+'])
                ->name('destroy.parts');

            // // 로그
            // Route::name('log.')->prefix('logs')->group(function () {
            //    Route::get('/', [RepairPartController::class, 'logList'])->name('list');
            // });
            //
            // // 주문
            // Route::name('order.')->prefix('orders')->group(function () {
            //    Route::get('/', [RepairPartController::class, 'orderList'])->name('list');
            //    Route::post('/', [RepairPartController::class, 'orderStore'])->name('store');
            //    Route::put('/', [RepairPartController::class, 'orderUpdate'])->name('update');
            // });
        });

        /**
         * 10. 구성품 카테고리 관리 (Parts Category Management)
         */
        Route::name('parts.category.')->prefix('parts-categories')->group(function () {
            Route::get('/', [RepairPartsCategoryController::class, 'index'])->name('index');
            Route::post('/', [RepairPartsCategoryController::class, 'store'])->name('store');
            Route::put('/{id}', [RepairPartsCategoryController::class, 'update'])->name('update');
            Route::delete('/{id}', [RepairPartsCategoryController::class, 'destroy'])->name('destroy');
        });
    });

    // 점검코드 관리
    Route::name('process.')->prefix('processes')->group(function () {
        // 전체 관리자 및 관리자가 직원 관리
        Route::middleware('admin.access')->group(function () {
            Route::get('/', [ProcessController::class, 'getProcessByAdmin'])->name('admin.process.list');
            Route::post('/', [ProcessController::class, 'store'])->name('store');
            Route::put('/{id}', [ProcessController::class, 'update'])->name('update');
            Route::delete('/{id}', [ProcessController::class, 'destroy'])->name('destroy');
        });
    });

    // Location 설정
    Route::name('location.')->prefix('locations')->group(function () {
        Route::get('/', [LocationController::class, 'list'])->name('list');
        Route::put('/', [LocationController::class, 'update'])->name('update');
    });

    // 작업 상태 설정
    Route::name('status.')->prefix('statuses')->group(function () {
        Route::get('/', [WorkStatusController::class, 'list'])->name('list');
        Route::post('/', [WorkStatusController::class, 'store'])->name('store');
        Route::put('/{id}', [WorkStatusController::class, 'update'])->name('update');
        Route::delete('/{id}', [WorkStatusController::class, 'destroy'])->name('destroy');
    });

    // 알림 설정
    Route::name('notification.')->prefix('notifications')->group(function () {
        // TODO: NotificationController 구현 필요
        // Route::get('/', [NotificationController::class, 'list'])->name('list');
        // Route::post('/', [NotificationController::class, 'store'])->name('store');
        // Route::put('/{id}', [NotificationController::class, 'update'])->name('update');
        // Route::delete('/{id}', [NotificationController::class, 'destroy'])->name('destroy');

        // 그룹
        Route::name('group.')->prefix('groups')->group(function () {
            Route::get('/', [GroupController::class, 'list'])->name('list');
            Route::get('/{id}', [GroupController::class, 'show'])->name('show');
            Route::post('/', [GroupController::class, 'store'])->name('store');
            Route::put('/{id}', [GroupController::class, 'update'])->name('update');
            Route::post('/{groupId}/members', [GroupController::class, 'addMembers'])->name('add.member');
            Route::delete('/{id}', [GroupController::class, 'destroy'])->name('destroy');
            Route::delete('/{groupId}/members/{userId}', [GroupController::class, 'removeMember'])->name('remove.member');
        });

        // 템플릿
        Route::name('template.')->prefix('templates')->middleware('admin.access')->group(function () {
            Route::get('/', [TemplateController::class, 'list'])->name('list');
            Route::get('/all', [TemplateController::class, 'all'])->name('all');
            Route::get('/{id}', [TemplateController::class, 'show'])
                ->where('id', '[0-9]+')
                ->name('show');
            Route::post('/', [TemplateController::class, 'store'])->name('store');
            Route::put('/{id}', [TemplateController::class, 'update'])
                ->where('id', '[0-9]+')
                ->name('update');
            Route::delete('/{id}', [TemplateController::class, 'destroy'])
                ->where('id', '[0-9]+')
                ->name('destroy');
            Route::post('/{id}/use', [TemplateController::class, 'incrementUsage'])
                ->where('id', '[0-9]+')
                ->name('use');
        });

        Route::get('/stats/{notificationId}', [SendController::class, 'stats'])->name('stats');
    });

    // 동적 작업 상태 관리 시스템
    Route::name('dynamic.')->prefix('dynamic-work-status')->group(function () {
        // 카테고리 관리
        Route::name('category.')->prefix('categories')->group(function () {
            Route::get('/', [WorkStatusManagementController::class, 'getCategories'])->name('list');
            Route::post('/', [WorkStatusManagementController::class, 'createCategory'])->name('store');
            Route::put('/{id}', [WorkStatusManagementController::class, 'updateCategory'])
                ->where('id', '[0-9]+')
                ->name('update');
            Route::delete('/{id}', [WorkStatusManagementController::class, 'deleteCategory'])
                ->where('id', '[0-9]+')
                ->name('destroy');

            // 카테고리별 통계
            Route::get('/{code}/stats', [WorkStatusManagementController::class, 'getCategoryStats'])
                ->where('code', '[A-Z_]+')
                ->name('stats');
        });

        // 액션 관리
        Route::name('action.')->prefix('actions')->group(function () {
            Route::get('/{categoryId}', [WorkStatusManagementController::class, 'getActions'])
                ->where('categoryId', '[0-9]+')
                ->name('list');
            Route::post('/', [WorkStatusManagementController::class, 'createAction'])->name('store');
            Route::put('/{id}', [WorkStatusManagementController::class, 'updateAction'])
                ->where('id', '[0-9]+')
                ->name('update');
            Route::delete('/{id}', [WorkStatusManagementController::class, 'deleteAction'])
                ->where('id', '[0-9]+')
                ->name('destroy');
        });

        // 템플릿 관리
        Route::name('template.')->prefix('templates')->group(function () {
            Route::get('/{categoryId}/{actionId}', [WorkStatusManagementController::class, 'getTemplates'])
                ->where(['categoryId' => '[0-9]+', 'actionId' => '[0-9]+'])
                ->name('list');
            Route::post('/', [WorkStatusManagementController::class, 'createTemplate'])->name('store');
            Route::put('/{id}', [WorkStatusManagementController::class, 'updateTemplate'])
                ->where('id', '[0-9]+')
                ->name('update');
            Route::delete('/{id}', [WorkStatusManagementController::class, 'deleteTemplate'])
                ->where('id', '[0-9]+')
                ->name('destroy');

            // 사용되지 않는 템플릿 조회
            Route::get('/unused', [WorkStatusManagementController::class, 'getUnusedTemplates'])->name('unused');
        });

        // 생성된 상태 관리
        Route::name('generated.')->prefix('generated-statuses')->group(function () {
            Route::get('/', [WorkStatusManagementController::class, 'getGeneratedStatuses'])->name('list');
            Route::get('/stats', [WorkStatusManagementController::class, 'getGenerationStats'])->name('stats');
            Route::get('/recent', [WorkStatusManagementController::class, 'getRecentStatuses'])->name('recent');
            Route::get('/frequent', [WorkStatusManagementController::class, 'getFrequentStatuses'])->name('frequent');
        });

        // 시스템 관리
        Route::name('system.')->prefix('system')->group(function () {
            Route::post('/convert-legacy', [WorkStatusManagementController::class, 'convertLegacyConstants'])->name('convert');
            Route::post('/clear-cache', [WorkStatusManagementController::class, 'clearCache'])->name('cache.clear');
        });
    });
});
