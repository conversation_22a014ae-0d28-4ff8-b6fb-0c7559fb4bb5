<?php

use Illuminate\Support\Carbon;
use Monolog\Handler\NullHandler;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\SyslogUdpHandler;
use Monolog\Processor\PsrLogMessageProcessor;

$date = Carbon::today()->format('Y-m-d');

return [

    /*
    |--------------------------------------------------------------------------
    | Default Log Channel
    |--------------------------------------------------------------------------
    |
    | This option defines the default log channel that gets used when writing
    | messages to the logs. The name specified in this option should match
    | one of the channels defined in the "channels" configuration array.
    |
    */

    'default' => env('LOG_CHANNEL', 'stack'),

    /*
    |--------------------------------------------------------------------------
    | Deprecations Log Channel
    |--------------------------------------------------------------------------
    |
    | This option controls the log channel that should be used to log warnings
    | regarding deprecated PHP and library features. This allows you to get
    | your application ready for upcoming major versions of dependencies.
    |
    */

    'deprecations' => [
        'channel' => env('LOG_DEPRECATIONS_CHANNEL', 'null'),
        'trace' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Log Channels
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log channels for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Drivers: "single", "daily", "slack", "syslog",
    |                    "errorlog", "monolog",
    |                    "custom", "stack"
    |
    */

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['single'],
            'ignore_exceptions' => false,
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'replace_placeholders' => true,
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/daily/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 14,
            'replace_placeholders' => true,
        ],

        'papertrail' => [
            'driver' => 'monolog',
            'level' => env('LOG_LEVEL', 'debug'),
            'handler' => env('LOG_PAPERTRAIL_HANDLER', SyslogUdpHandler::class),
            'handler_with' => [
                'host' => env('PAPERTRAIL_URL'),
                'port' => env('PAPERTRAIL_PORT'),
                'connectionString' => 'tls://'.env('PAPERTRAIL_URL').':'.env('PAPERTRAIL_PORT'),
            ],
            'processors' => [PsrLogMessageProcessor::class],
        ],

        'stderr' => [
            'driver' => 'monolog',
            'level' => env('LOG_LEVEL', 'debug'),
            'handler' => StreamHandler::class,
            'formatter' => env('LOG_STDERR_FORMATTER'),
            'with' => [
                'stream' => 'php://stderr',
            ],
            'processors' => [PsrLogMessageProcessor::class],
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level' => env('LOG_LEVEL', 'debug'),
            'facility' => LOG_USER,
            'replace_placeholders' => true,
        ],

        'errorlog' => [
            'driver' => 'errorlog',
            'level' => env('LOG_LEVEL', 'debug'),
            'replace_placeholders' => true,
        ],

        'null' => [
            'driver' => 'monolog',
            'handler' => NullHandler::class,
        ],

        'emergency' => [
            'path' => storage_path('logs/laravel.log'),
        ],

        // 커스텀 채널
        'notify' => [
            'driver' => 'daily',
            'path' => storage_path('logs/notify/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'req' => [
            'driver' => 'daily',
            'path' => storage_path('logs/req/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'product' => [
            'driver' => 'daily',
            'path' => storage_path('logs/product/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'warehouse' => [
            'driver' => 'daily',
            'path' => storage_path('logs/warehouse/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'repair' => [
            'driver' => 'daily',
            'path' => storage_path('logs/repair/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'pallet' => [
            'driver' => 'daily',
            'path' => storage_path('logs/pallet/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'carryout' => [
            'driver' => 'daily',
            'path' => storage_path('logs/carryout/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'setting' => [
            'driver' => 'daily',
            'path' => storage_path('logs/setting/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'work_status' => [
            'driver' => 'daily',
            'path' => storage_path('logs/work-status/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'login' => [
            'driver' => 'daily',
            'path' => storage_path('logs/login/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],
        'logout' => [
            'driver' => 'daily',
            'path' => storage_path('logs/logout/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],
        'board' => [
            'driver' => 'daily',
            'path' => storage_path('logs/board/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],
        'monitor' => [
            'driver' => 'daily',
            'path' => storage_path('logs/monitor/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'excel' => [
            'driver' => 'daily',
            'path' => storage_path('logs/excel/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 30, // SSE 로그는 30일간 보관
        ],

        'sse' => [
            'driver' => 'daily',
            'path' => storage_path('logs/sse/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 30, // SSE 로그는 30일간 보관
        ],

    ],

    /*
     * Custom Log
     */
    'sql' => [
        'enable' => env('LOG_SQL_ENABLE', true),
    ],

];
