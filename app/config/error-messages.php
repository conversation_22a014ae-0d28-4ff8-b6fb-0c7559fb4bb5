<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 에러 메시지 설정
    |--------------------------------------------------------------------------
    |
    | 애플리케이션에서 사용되는 표준화된 에러 메시지를 정의합니다.
    |
    */

    'general' => [
        'internal_server_error' => '서버 내부 오류가 발생했습니다.',
        'validation_error' => '입력 데이터가 유효하지 않습니다.',
        'unauthorized' => '인증이 필요합니다.',
        'forbidden' => '접근 권한이 없습니다.',
        'not_found' => '요청한 리소스를 찾을 수 없습니다.',
        'method_not_allowed' => '허용되지 않은 HTTP 메서드입니다.',
        'unprocessable_entity' => '요청을 처리할 수 없습니다.',
        'conflict' => '이미 존재하는 리소스입니다.',
        'too_many_requests' => '요청 횟수가 제한을 초과했습니다.',
    ],

    'template' => [
        'not_found' => '템플릿을 찾을 수 없습니다.',
        'duplicate_name' => '이미 존재하는 템플릿명입니다.',
        'create_failed' => '템플릿 생성에 실패했습니다.',
        'update_failed' => '템플릿 수정에 실패했습니다.',
        'delete_failed' => '템플릿 삭제에 실패했습니다.',
        'access_denied' => '템플릿에 대한 접근 권한이 없습니다.',
        'validation_failed' => '템플릿 데이터 유효성 검증에 실패했습니다.',
        'invalid_priority' => '유효하지 않은 우선순위입니다.',
        'increment_usage_failed' => '템플릿 사용 횟수 증가에 실패했습니다.',
    ],

    'success' => [
        'general' => '요청이 성공적으로 처리되었습니다.',
        'created' => '리소스가 성공적으로 생성되었습니다.',
        'updated' => '리소스가 성공적으로 수정되었습니다.',
        'deleted' => '리소스가 성공적으로 삭제되었습니다.',
        'retrieved' => '데이터를 성공적으로 조회했습니다.',

        'template' => [
            'created' => '템플릿이 성공적으로 생성되었습니다.',
            'updated' => '템플릿이 성공적으로 수정되었습니다.',
            'deleted' => '템플릿이 성공적으로 삭제되었습니다.',
            'retrieved' => '템플릿을 성공적으로 조회했습니다.',
            'list_retrieved' => '템플릿 목록을 성공적으로 조회했습니다.',
            'usage_incremented' => '템플릿 사용 횟수가 증가되었습니다.',
        ],
    ],

    'validation' => [
        'required' => ':attribute 필드는 필수입니다.',
        'string' => ':attribute 필드는 문자열이어야 합니다.',
        'max' => ':attribute 필드는 :max자를 초과할 수 없습니다.',
        'min' => ':attribute 필드는 최소 :min자 이상이어야 합니다.',
        'unique' => ':attribute 필드는 이미 사용 중입니다.',
        'exists' => '선택한 :attribute가 유효하지 않습니다.',
        'integer' => ':attribute 필드는 정수여야 합니다.',
        'in' => '선택한 :attribute가 유효하지 않습니다.',
        'email' => ':attribute 필드는 유효한 이메일 주소여야 합니다.',
        'confirmed' => ':attribute 확인이 일치하지 않습니다.',
        'numeric' => ':attribute 필드는 숫자여야 합니다.',
        'boolean' => ':attribute 필드는 true 또는 false여야 합니다.',
        'array' => ':attribute 필드는 배열이어야 합니다.',
        'date' => ':attribute 필드는 유효한 날짜여야 합니다.',
        'url' => ':attribute 필드는 유효한 URL이어야 합니다.',
    ],

    'attributes' => [
        'name' => '템플릿명',
        'title' => '제목',
        'content' => '내용',
        'priority' => '우선순위',
        'created_by' => '생성자',
        'user_id' => '사용자 ID',
        'template_id' => '템플릿 ID',
        'page' => '페이지',
        'per_page' => '페이지당 항목 수',
        'search' => '검색어',
        'sort_by' => '정렬 기준',
        'sort_order' => '정렬 순서',
    ],
];
