#!/bin/sh

set -e

APP_DIR="./app/vendor"
if [ -d "$APP_DIR" ] ; then
  rm -rf "$APP_DIR"
fi

git clone https://github.com/laravel/laravel.git

mv laravel app

cd app

rm -rf .git .github

#docker build laravel-app -t laravel-app:0.1.0
docker compose up -d --build --force-recreate

#docker compose exec laravel composer install
#
#cp .env.example .env
#
#docker compose exec laravel php artisan key:generate
#docker compose exec laravel php artisan storage:link
#docker compose exec laravel php artisan migrate
#docker compose exec laravel php artisan db:seed